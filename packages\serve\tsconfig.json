{"extends": "../../packages/typescript-config/base.json", "compilerOptions": {"target": "ESNext", "lib": ["esnext"], "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "paths": {"@/*": ["./src/*"], "@roasmax/database": ["../database/src"], "@roasmax/utils": ["../utils/src"], "@roasmax/utils/*": ["../../packages/utils/src/*"]}}, "include": ["src/**/*", "tsup.config.ts", "typings.d.ts"], "exclude": ["node_modules"]}