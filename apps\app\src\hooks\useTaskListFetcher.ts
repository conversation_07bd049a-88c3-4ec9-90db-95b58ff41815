import { removeMaterials } from '@/services/actions/materials';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { action, ActionResult } from '@/utils/server-action/action';
import { useCallback } from 'react';
import toast from 'react-hot-toast';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface TaskCreatorStore {
  loading: boolean;
  loadingMore: boolean;
  isEnd: boolean;
  hasMore: boolean;
  pagination: { current: number; pageSize: number };
  list: ActionResult<typeof pageVideoGenerationTasks>['list'];
  setLoading: (loading: boolean) => void;
  setLoadingMore: (loadingMore: boolean) => void;
  reset: (data: ActionResult<typeof pageVideoGenerationTasks> | undefined) => void;
  push: (data: ActionResult<typeof pageVideoGenerationTasks>) => void;
  removeMaterials: (materialIds: string[]) => Promise<void>;
  setIsEnd: (isEnd: boolean) => void;
  setHasMore: (hasMore: boolean) => void;
}

const PAGE_SIZE = 10;

/**
 * 任务列表 store
 */
const useTaskListStore = create<TaskCreatorStore>()(
  devtools((set) => {
    return {
      loading: false,
      loadingMore: false,
      isEnd: false,
      list: [],
      hasMore: true,
      pagination: { current: 1, pageSize: PAGE_SIZE },
      setLoading: (loading) => set({ loading }),
      setLoadingMore: (loadingMore) => set({ loadingMore }),
      reset: (data) =>
        set({
          list: data?.list || [],
          pagination: { current: 1, pageSize: PAGE_SIZE },
          hasMore: (data?.list.length || 0) >= PAGE_SIZE,
        }),
      push: (data) =>
        set((pre) => ({
          list: [...pre.list, ...data.list],
          pagination: { current: data.pagination.current, pageSize: data.pagination.pageSize },
          hasMore: data.list.length >= PAGE_SIZE,
        })),
      removeMaterials: async (materialIds) => {
        const res = await action(removeMaterials, { materialIds: materialIds });
        if (!res) return;
        toast.success('删除成功');
        set((pre) => {
          const list = pre.list.map((task) => {
            task.subTasks = task.subTasks.map((subTask) => {
              subTask.generated_materials = subTask.generated_materials.filter((m) => !materialIds.includes(m.id));
              return subTask;
            });
            return task;
          });
          return { list, pagination: pre.pagination };
        });
      },
      setIsEnd: (isEnd) => set({ isEnd }),
      setHasMore: (hasMore) => set({ hasMore }),
    };
  }),
);

const useTaskListFetcher = () => {
  const {
    list,
    isEnd,
    hasMore,
    pagination,
    loading,
    loadingMore,
    setLoading,
    setLoadingMore,
    reset,
    push,
    removeMaterials,
    setIsEnd,
    setHasMore,
  } = useTaskListStore();

  const refresh = useCallback(
    async (filterDate?: string, name?: string, generation_type?: string | undefined) => {
      setIsEnd(false);
      setLoading(true);
      setLoadingMore(true);
      const res = await action(pageVideoGenerationTasks, {
        pagination: { page: 1, limit: PAGE_SIZE },
        filters: { name, tmp_created_at: filterDate, generation_type },
      });
      setLoading(false);
      setLoadingMore(false);
      reset(res);
      setHasMore((res?.list.length || 0) >= PAGE_SIZE);
      if (res?.pagination.total === res?.list.length) setIsEnd(true);
    },
    [setLoading, setLoadingMore, reset, setIsEnd, setHasMore],
  );

  const loadMore = useCallback(
    async (filterDate: string | undefined, name: string | undefined, generation_type: string | undefined) => {
      if (loadingMore || isEnd) return;
      setLoadingMore(true);
      const res = await action(pageVideoGenerationTasks, {
        pagination: { page: pagination.current + 1, limit: pagination.pageSize },
        filters: { name, tmp_created_at: filterDate, generation_type },
      });
      setLoadingMore(false);
      if (res) {
        push(res);
        setHasMore(res.list.length >= PAGE_SIZE);
      }
      if (res?.pagination.total === list.length) setIsEnd(true);
    },
    [isEnd, list.length, loadingMore, pagination, push, setIsEnd, setLoadingMore, setHasMore],
  );
  return { list, loading, loadingMore, refresh, loadMore, removeMaterials, hasMore };
};

export default useTaskListFetcher;
