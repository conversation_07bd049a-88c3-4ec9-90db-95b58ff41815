import { startVideoGenerationTask } from '@/services/actions/video-generation-task';
import { generateRandomBase36, Logger } from '@roasmax/utils';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';

export const POST = async (request: NextRequest) => {
  const logger = new Logger('video-generation', 'api', generateRandomBase36(10));

  try {
    logger._start('开始处理视频生成任务请求');
    const data = await request.json();
    logger.debug('收到请求数据', JSON.stringify(data));

    const taskName = `ADS_${data?.properties?.industry ?? ''}_视频生成`;
    logger._push(taskName);

    if (!data || !data.properties || !data.properties.industry) {
      logger.warn('缺少必要参数');
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    const allADSTasks = await prisma.video_generation_tasks.findMany({
      where: {
        generation_source: 'ADS',
      },
    });

    // 检查是否存在相同来源的任务
    const existingTask = allADSTasks.find((task) => task?.generation_source_info === data?.ads_batch_create_task_id);

    if (existingTask) {
      logger.warn('该批次任务已存在', {
        taskId: existingTask.id,
        adsBatchCreateTaskId: data?.ads_batch_create_task_id,
      });
      return NextResponse.json({ error: '该批次任务已存在' }, { status: 400 });
    }

    logger.info('开始创建视频生成任务');
    const result = await startVideoGenerationTask({
      data: {
        name: taskName,
        sliceType: '300',
        generateCount: data.properties?.generate_round ? Number(data.properties?.generate_round) : 1, // 其实是生成轮次
        materialIds: data.material_ids,
        language: data.properties?.language ?? 'zh-CN',
        industry: data.properties?.industry,
        method: 'normal',
        prompts: data.properties?.prompts ?? ['BZ_attention_60s'],
        accelerate: data.properties?.accelerate ?? '1X',
        subtitles: data.properties?.subtitle ?? false,
        transition_mode: data?.properties?.transition_mode ?? 'fade',
        generationSource: 'ADS',
        generationSourceInfo: JSON.stringify({
          source: 'ADS',
          adsBatchCreateTaskId: data.ads_batch_create_task_id,
          productInfo: data.properties?.product_info ?? [],
        }),
      },
    });

    logger.info('视频生成任务创建成功', { taskId: result.data?.id });
    logger._end('视频生成任务请求处理完成');
    return NextResponse.json(result);
  } catch (error) {
    logger.error('视频生成任务创建失败', (error as Error).message);
    logger._end('视频生成任务请求处理失败');
    return NextResponse.json({ success: false, message: (error as Error).message }, { status: 500 });
  }
};
