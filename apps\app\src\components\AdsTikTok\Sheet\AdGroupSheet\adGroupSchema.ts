import * as z from 'zod';
import moment from 'moment';

export const createAdGroupSchema = (type: string) =>
  z
    .object({
      // 已有的字段
      campaign_id: z.array(z.string()).min(1, '请选择广告系列').optional(),
      adgroup_name: z
        .string({
          required_error: '请输入广告组名称',
        })
        .min(1, '请输入广告组名称')
        .max(200, '最多输入200个字符'),
      operation_status: z.enum(['ENABLE', 'DISABLE']).optional(),
      store_id: z.string().min(1, '请选择商店'),
      shopping_ads_type: z.string().default('VIDEO').optional(),
      budget_mode: z.enum(['BUDGET_MODE_DAY', 'BUDGET_MODE_TOTAL', 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET']).optional(),
      budget: z
        .number({
          required_error: '请输入预算金额',
        })
        .min(10, '预算必须大于等于10'),
      schedule_type: z.enum(['SCHEDULE_FROM_NOW', 'SCHEDULE_START_END']).optional(),
      schedule_start_time: type === 'create' ? z.string().min(1, '请选择开始时间') : z.string().optional(),
      schedule_end_time: z.string().min(1, '请选择结束时间').optional(),
      dayparting: z.string().optional(),
      optimization_goal: z.string().optional(),
      bid_type: z.string().default('BID_TYPE_CUSTOM'),
      bid_price: z.number().optional(),
      conversion_bid_price: z.number().optional(),
      billing_event: z.enum(['CPC', 'OCPM']),
      pacing: z.string().optional(),
      location_ids: z.array(z.string()).min(1, '请选择地域'),
      languages: z.array(z.string()).optional(),
      gender: z.string().optional(),
      age_groups: z.array(z.string()).optional(),
      audience_ids: z.array(z.string()).optional(),
      interest_category_ids: z.array(z.string()).optional(),
      network_types: z.array(z.string()).optional(),

      // 需要添加的新字段
      tags: z.array(z.string()).optional(),
      is_template: z.boolean().optional(),
      name: z.string().optional(),
      advertiser_id: z.string().optional(),
      placements: z.array(z.string()).optional(),
      comment_disabled: z.boolean().optional(),
      video_download_disabled: z.boolean().optional(),
      frequency: z.number().optional(),
      frequency_schedule: z.number().optional(),
      promotion_type: z.string().optional(),
      product_source: z.string().optional(),
      placement_type: z.string().optional(),

      // 已有的互动行为相关字段
      optimization_event: z.string().optional(),
      video_user_actions: z.array(z.any()).optional(),
      // deep_bid_type: z.string().optional(),
      A_few_days_of_behavior: z.number().optional(),
      creator_user_actions: z.array(z.any()).optional(),
      hashtag_user_actions: z.array(z.any()).optional(),
      // 商店相关
      store_type: z.string().optional(),
      store_authorized_bc_id: z.string().optional(),
      // 互动行为分类
      action_category_ids1: z.array(z.string()).optional(),
      action_category_ids2: z.array(z.string()).optional(),
      action_category_ids3: z.array(z.string()).optional(),

      // 转化目标相关

      // actions数组
      actions: z
        .array(
          z.object({
            action_scene: z.string().optional(),
            action_period: z.number().optional(),
            video_user_actions: z.array(z.string()).optional(),
            action_category_ids: z.array(z.string()).optional(),
          }),
        )
        .optional(),
    })
    .superRefine((data, ctx) => {
      if (type === 'create' && data.schedule_start_time && data.schedule_end_time) {
        if (moment(data.schedule_start_time).isAfter(moment(data.schedule_end_time))) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: '开始时间不能大于结束时间',
            path: ['schedule_start_time'],
          });
        }
      }

      // 根据billing_event验证bid_price或conversion_bid_price
      if (data.billing_event === 'CPC' && data.bid_type === 'BID_TYPE_CUSTOM' && data.optimization_goal !== 'VALUE') {
        if (!data.bid_price || data.bid_price <= 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: '当计费方式为CPC时，请输入有效的出价金额',
            path: ['bid_price'],
          });
        }
      } else if (data.bid_type === 'BID_TYPE_CUSTOM' && data.optimization_goal !== 'VALUE') {
        if (!data.conversion_bid_price || data.conversion_bid_price <= 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: '当计费方式为OCPM时，请输入有效的转化出价',
            path: ['conversion_bid_price'],
          });
        }
      }
    });
// 导出类型
export type AdGroupFormSchema = ReturnType<typeof createAdGroupSchema>;
export const getLocalTimeByTimezone = (timezone: string | undefined) => {
  const utcDate = new Date();
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  };
  const formatter = new Intl.DateTimeFormat([], options);
  const localTime = formatter.format(utcDate);
  return localTime;
};
