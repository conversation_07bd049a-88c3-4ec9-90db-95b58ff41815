import * as React from 'react';

import { cn } from '@/utils/cn';

const RainbowBorder = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & { background?: string }>(
  ({ background, children, className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn('rainbow-border border', className)} {...props}>
        <div className={cn('rainbow-border-content bg', className)} style={{ background: background }}>
          {children}
        </div>
      </div>
    );
  },
);
RainbowBorder.displayName = 'RainbowBorder';

export { RainbowBorder as RainbowBorder };
