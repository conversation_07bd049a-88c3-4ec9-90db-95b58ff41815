import { RadiusClose } from '@/components/icon';
import { ProFormField } from '@/components/pro/pro-form';
import { ProSelect } from '@/components/pro/pro-select';
import { ProToggleGroup } from '@/components/pro/pro-toggle-group';
import { TaskCreatorMaterialGallery } from '@/components/TaskCreatorMaterialGallery';
import { Form, FormLabel, Input, Switch } from '@/components/ui';
import { TagInput } from '@/components/ui/TagInput';
import { GENERATE_ROUND_OPTIONS, GENERATE_SPEED_OPTIONS, useTaskCreator } from '@/hooks/useTaskCreator';
import { cn } from '@/utils/cn';
import dayjs from 'dayjs';
import { useEffect } from 'react';

// 添加音色选项常量
const VOICE_TYPE_OPTIONS = [
  { value: 'en_female', label: '英语女声(肯豆)' },
  { value: 'en_female_mature', label: '英语女声(成熟)' },
  { value: 'en_male_vitality', label: '英语男声（活力）' },
  { value: 'en_female_2', label: '英语男声(讲述)' },
  { value: 'en_male', label: '英语男声' },
  { value: 'es_female', label: '西班牙语女声' },
  { value: 'es_male', label: '西班牙语男声' },
];

interface VideoFormProps {
  onClickMaterialImport: () => void;
  setShowTitle?: (showTitle: boolean) => void;
}

export const VideoForm = (props: VideoFormProps) => {
  const { form, formValues, selectedMaterials, setSelectedMaterials, removeSelectedMaterials } = useTaskCreator();

  useEffect(() => {
    props.setShowTitle?.(false);
    setSelectedMaterials(() => []);
    form.setValue('name', `爆款克隆-${dayjs().format('YYYYMMDDHHmmss')}`);
    form.setValue('method', 'gc_imitate' as const);
    form.setValue('generationType', '大卖推荐' as const);
    form.setValue('targetVideoDuration', 30);
    form.setValue('sceneImplantation', false);
    form.setValue('festiveAtmosphere', false);
    form.setValue('subtitle', true);
    form.setValue('transition_mode', 'null' as const);
  }, []);

  return (
    <Form {...form}>
      <form className="space-y-6">
        {/* 上传模块 */}
        <div>
          <div className="flex flex-col items-start justify-start gap-3">
            <div className="flex w-full flex-col items-start justify-start gap-3">
              <FormLabel className="font-[PingFang SC] w-1/5 text-[14px] font-medium leading-normal text-white">
                底板视频上传
              </FormLabel>
              <TaskCreatorMaterialGallery
                className="w-full"
                materials={selectedMaterials}
                add={props.onClickMaterialImport}
                remove={(m) => removeSelectedMaterials(m.id)}
              />
            </div>
          </div>
        </div>
        {/* 任务名称 */}
        <ProFormField
          name="name"
          label="任务名称"
          form={form}
          renderFormControl={(field) => (
            <div className="relative">
              <Input
                value={field.value}
                onChange={(e) => {
                  const v = e.target.value.slice(0, 20);
                  field.onChange(v);
                }}
                className={cn(
                  'rounded-[8px] bg-[#CCDDFF1A] font-normal file:border-0 placeholder:font-normal placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A]',
                )}
                placeholder="请输入批次名称"
              />
              {formValues.name?.length !== 0 && (
                <div
                  onClick={() => form.setValue('name', '')}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500"
                >
                  <RadiusClose />
                </div>
              )}
            </div>
          )}
          extra={
            <div className={cn('text-xs', (formValues.name?.length || 0) >= 20 ? 'text-[#FF4D4D]' : 'text-[#BEC5D6]')}>
              {formValues.name?.length || 0} / 20
            </div>
          }
        />
        <ProFormField
          name="targetVideoDuration"
          label="目标视频时长"
          form={form}
          renderFormControl={(field) => (
            <div className="flex items-center gap-4 text-xs">
              <ProToggleGroup
                type="single"
                className="flex-wrap justify-start gap-4"
                value={String(field.value)}
                defaultValue={String(30)}
                onValueChange={(v) => {
                  form.setValue('targetVideoDuration', Number(v));
                }}
                toggles={[
                  { value: '30', label: '30秒' },
                  { value: '60', label: '60秒' },
                ]}
                toggleItemProps={{
                  className: cn(
                    'border h-9 border-transparent bg-[#CCDDFF1A] text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-[#FFF]',
                    'text-xs data-[state=on]:text-[#FFF] data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#CCDDFF0F] px-4 py-2',
                  ),
                }}
              />
            </div>
          )}
        />
        <ProFormField label="视频风格">
          {/* 自定义提示词 */}
          <ProFormField
            name="customPrompt"
            label="核心突出点"
            form={form}
            subField
            renderFormControl={(field) => (
              <TagInput
                value={field.value ? field.value.split('\n').map((v) => ({ id: v, label: v })) : []}
                onChange={(v) => field.onChange(v ? v.map((v) => v.label).join('\n') : '')}
                placeholder="请选择核心突出点或手动输入（按回车键确认）"
                className="bg-[#CCDDFF1A]"
                suggestions={[
                  '不出现橱窗指向',
                  '不出现商品链接',
                  '不出现商品名称',
                  '不出现商品价格',
                  '支持免费邮寄',
                  '支持免费退货',
                  '支持免费换货',
                  '支持免费维修',
                  '支持免费安装',
                  '支持免费保养',
                ].map((v) => ({ id: v, label: v }))}
              />
            )}
          />
          {/* 目标音色 */}
          <ProFormField
            name="targetVoice"
            label="目标音色"
            form={form}
            subField
            renderFormControl={(field) => (
              <ProSelect
                value={field.value}
                onValueChange={field.onChange}
                placeholder={<span className="font-normal text-[#9FA4B2]">请选择目标音色</span>}
                options={VOICE_TYPE_OPTIONS.map(({ value, label }) => ({ value, label }))}
                className="bg-[#CCDDFF1A]"
              />
            )}
          />
          {/* 场景植入 */}
          <ProFormField
            name="sceneImplantation"
            label="场景植入"
            form={form}
            subField
            layout="horizontal"
            renderFormControl={(field) => (
              <Switch checked={field.value} onCheckedChange={field.onChange} thumbClassName="bg-[#FFF]" />
            )}
          />
          {/* 节日氛围 */}
          <ProFormField
            name="festiveAtmosphere"
            label="节日氛围"
            form={form}
            subField
            layout="horizontal"
            renderFormControl={(field) => (
              <Switch checked={field.value} onCheckedChange={field.onChange} thumbClassName="bg-[#FFF]" />
            )}
          />
        </ProFormField>
        {/* 生成轮次和视频加速 */}
        <div className="flex justify-between gap-2">
          <ProFormField
            name="generateRound"
            label="生成轮次"
            tooltip="生成视频数量翻倍值"
            form={form}
            className="flex-1"
            renderFormControl={(field) => (
              <>
                <ProSelect
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder={<span className="font-normal text-[#9FA4B2]">请选择生成轮次</span>}
                  options={GENERATE_ROUND_OPTIONS.map((v) => ({ value: v, label: `${v} 轮` }))}
                  className="bg-[#CCDDFF1A]"
                />
              </>
            )}
          />
          <ProFormField
            name="speed"
            label="视频加速"
            tooltip="生成视频加速倍率"
            form={form}
            className="flex-1"
            renderFormControl={(field) => (
              <ProSelect
                value={field.value}
                onValueChange={field.onChange}
                placeholder={<span className="font-normal text-[#9FA4B2]">请选择视频加速</span>}
                options={GENERATE_SPEED_OPTIONS.map((v) => ({ value: v, label: `${v}` }))}
                className="bg-[#CCDDFF1A]"
              />
            )}
          />
        </div>
        {/* 字幕 */}
        <ProFormField
          name="subtitle"
          label="生成字幕"
          form={form}
          layout="horizontal"
          renderFormControl={(field) => (
            <Switch checked={field.value} onCheckedChange={field.onChange} thumbClassName="bg-[#FFF]" />
          )}
        />
        {/* 叠化
        <ProFormField
          name="transition_mode"
          label="叠化效果"
          form={form}
          layout="horizontal"
          renderFormControl={(field) => (
            <Switch
              checked={field.value === 'fade'}
              onCheckedChange={(checked) => field.onChange(checked ? 'fade' : 'null')}
              thumbClassName="bg-[#FFF]"
            />
          )}
        /> */}
      </form>
    </Form>
  );
};
