import { prisma } from '@/utils/prisma';
import { Logger } from '@roasmax/utils';
import { NextRequest, NextResponse } from 'next/server';

// 获取最新的小红书 cookie 服务函数
const getLatestRednoteCookie = async () => {
  return await prisma.rednote_cookies.findFirst({
    orderBy: {
      tmp_created_at: 'desc',
    },
  });
};

export const GET = async (request: NextRequest) => {
  const logger = new Logger('rednote-cookie', 'api', request.headers.get('x-request-id') || 'unknown');

  try {
    logger._start('开始处理获取最新小红书 Cookie 请求');

    logger.info('开始查询最新小红书 Cookie');
    const result = await getLatestRednoteCookie();

    if (!result) {
      logger.warn('未找到小红书 Cookie 记录');
      return NextResponse.json({ error: 'No cookie found' }, { status: 404 });
    }

    logger.info('获取最新小红书 Cookie 成功', { id: result.id });
    logger._end('获取最新小红书 Cookie 请求处理完成');
    return NextResponse.json(result);
  } catch (error) {
    logger.error('获取最新小红书 Cookie 失败', (error as Error).message);
    logger._end('获取最新小红书 Cookie 请求处理失败');
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
};
