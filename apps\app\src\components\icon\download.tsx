import React from 'react';

export const Download = () => {
  return (
    <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="56" height="56" rx="6" fill="#373F4D" />
      <path
        d="M34.9798 39.5598L33.4484 40.4875H35.2389H39.6038C39.7381 40.4875 39.8668 40.5408 39.9618 40.6358C40.0567 40.7307 40.1101 40.8595 40.1101 40.9937C40.1101 41.128 40.0567 41.2568 39.9618 41.3517L40.3153 41.7053L39.9618 41.3517C39.8668 41.4467 39.7381 41.5 39.6038 41.5H28C20.5442 41.5 14.5 35.4558 14.5 28C14.5 20.5442 20.5442 14.5 28 14.5C35.4558 14.5 41.5 20.5442 41.5 28C41.5 32.9007 38.8891 37.1918 34.9798 39.5598ZM19.1673 36.8306L19.1671 36.8308C21.4733 39.1448 24.5975 40.4583 27.8643 40.4875L27.8643 40.4875H27.8688H28.175V40.4876L28.182 40.4875C31.0685 40.4469 33.852 39.4092 36.0606 37.5504C38.2692 35.6916 39.7669 33.126 40.2996 30.2889C40.8323 27.4518 40.3673 24.5177 38.9835 21.9843C37.5996 19.4509 35.382 17.4742 32.7069 16.3894C30.0318 15.3046 27.0637 15.1784 24.3062 16.0324C21.5487 16.8863 19.1714 18.6678 17.5776 21.0747C15.9838 23.4815 15.2715 26.3655 15.5617 29.2376C15.8517 32.1088 17.1255 34.7913 19.1673 36.8306Z"
        fill="url(#paint0_linear_1698_3386)"
        stroke="url(#paint1_linear_1698_3386)"
      />
      <path
        d="M27.6614 33.1519L27.6632 33.1509L32.838 30.1632L32.838 30.1632C34.4567 29.2286 34.4567 26.8923 32.838 25.9577L32.838 25.9577L27.6632 22.97L27.6609 22.9687L27.661 22.9687C27.2944 22.7545 26.8777 22.6412 26.4532 22.6402C25.1786 22.6405 24.0225 23.6608 24.0225 25.0714V31.0477C24.0225 32.4608 25.181 33.4787 26.4532 33.4789L27.6614 33.1519ZM27.6614 33.1519C27.2946 33.3657 26.8778 33.4785 26.4533 33.4789L27.6614 33.1519ZM26.4562 23.654L26.4562 23.654H26.4524C25.7815 23.654 25.0336 24.1838 25.0336 25.0728V30.7632L25.0315 30.7645V31.0477C25.0315 31.9363 25.7793 32.4678 26.4537 32.4678L26.4579 32.4677C26.705 32.4657 26.947 32.3983 27.1596 32.2724L32.3316 29.288L32.3317 29.2879C32.5469 29.1637 32.7256 28.985 32.8498 28.7698C32.9741 28.5546 33.0395 28.3105 33.0395 28.062C33.0395 27.8135 32.9741 27.5694 32.8498 27.3542C32.7256 27.139 32.5469 26.9603 32.3317 26.836L32.3317 26.836L27.1598 23.85C26.9468 23.7235 26.704 23.6559 26.4562 23.654Z"
        fill="url(#paint2_linear_1698_3386)"
        stroke="url(#paint3_linear_1698_3386)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1698_3386"
          x1="15.0769"
          y1="33.9231"
          x2="42"
          y2="40.9231"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0EF" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
        <linearGradient id="paint1_linear_1698_3386" x1="28" y1="14" x2="28" y2="42" gradientUnits="userSpaceOnUse">
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0EF" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1698_3386"
          x1="23.9467"
          y1="30.5639"
          x2="34.6415"
          y2="33.1545"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0EF" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1698_3386"
          x1="29.0373"
          y1="22.1402"
          x2="29.0373"
          y2="33.9789"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0EF" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
      </defs>
    </svg>
  );
};
