export default function Loading() {
  return (
    <div className="loadingio-spinner-dual-ball-2by998twmg8">
      <div className="ldio-yzaezf3dcmj">
        <div></div>
        <div></div>
        <div></div>
      </div>
      <style jsx>{`
        @keyframes ldio-yzaezf3dcmj-o {
          0% {
            opacity: 1;
            transform: translate(-50%, -50%) rotate(135deg);
          }
          49.99% {
            opacity: 1;
            transform: translate(-50%, calc(-50% + 12px)) rotate(135deg);
          }
          50% {
            opacity: 0;
            transform: translate(-50%, calc(-50% + 12px)) rotate(135deg);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) rotate(135deg);
          }
        }
        @keyframes ldio-yzaezf3dcmj {
          0% {
            transform: translate(-50%, -50%) rotate(135deg);
          }
          50% {
            transform: translate(-50%, calc(-50% + 12px)) rotate(135deg);
          }
          100% {
            transform: translate(-50%, -50%) rotate(135deg);
          }
        }
        .ldio-yzaezf3dcmj div {
          position: absolute;
          width: 10.462px;
          height: 10.462px;
          border-radius: 1px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          aspect-ratio: 10.46/10.46;
        }
        .ldio-yzaezf3dcmj div:nth-child(1) {
          background: linear-gradient(0deg, rgba(0, 225, 255, 0.8) 0%, rgba(0, 225, 255, 0.8) 100%);
          animation: ldio-yzaezf3dcmj 0.8s linear infinite;
          animation-delay: -0.4s;
        }
        .ldio-yzaezf3dcmj div:nth-child(2) {
          background: #54ffe0;
          animation: ldio-yzaezf3dcmj 0.8s linear infinite;
          animation-delay: 0s;
        }
        .ldio-yzaezf3dcmj div:nth-child(3) {
          background: linear-gradient(0deg, rgba(0, 225, 255, 0.8) 0%, rgba(0, 225, 255, 0.8) 100%);
          animation: ldio-yzaezf3dcmj-o 0.8s linear infinite;
          animation-delay: -0.4s;
        }
        .loadingio-spinner-dual-ball-2by998twmg8 {
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          background: transparent;
        }
        .ldio-yzaezf3dcmj {
          width: 100%;
          height: 100%;
          position: relative;
          transform: translateZ(0) scale(1);
          backface-visibility: hidden;
          transform-origin: 0 0;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .ldio-yzaezf3dcmj div {
          box-sizing: content-box;
        }
      `}</style>
    </div>
  );
}
