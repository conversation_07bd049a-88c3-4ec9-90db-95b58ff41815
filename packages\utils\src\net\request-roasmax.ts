import * as crypto from 'crypto';
import { request } from './request';

export class RequestRoasmax {
  private host: string;
  private tenantId: string;
  private secret: string;
  constructor(config: { host: string; tenantId: string; secret: string }) {
    this.host = config.host;
    this.tenantId = config.tenantId;
    this.secret = config.secret;
  }

  async post<R, T>(path: string, data: T) {
    return await request.post<R>(`${this.host}${path}`, data, {
      headers: {
        AccessToken: this.generateToken(),
      },
    });
  }

  private generateToken(): string {
    const timestamp = Date.now();
    const data = `${this.tenantId}:${timestamp}`;
    const hmac = crypto.createHmac('sha256', this.secret);
    hmac.update(data);
    const signature = hmac.digest('hex');
    const token = Buffer.from(`${data}:${signature}`).toString('base64');
    return token;
  }
}
