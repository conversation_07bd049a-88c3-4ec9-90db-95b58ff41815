import { AuthenticationClient } from 'authing-node-sdk';
import { ActionContextPluginLoader } from '../../types';

type AuthingPlugin = AuthenticationClient;

const authingPlugin: ActionContextPluginLoader = (context) => {
  if (!process.env.APPID || !process.env.APPSECRET || !process.env.APPHOST) {
    throw new Error('APPID, APPSECRET, or APPHOT is not set');
  }
  const authClient = new AuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  authClient.setAccessToken(context.token);

  return {
    name: 'authing',
    plugin: authClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * authing AuthenticationClient 操作API
     */
    authing: AuthingPlugin;
  }
}

export default authingPlugin;
