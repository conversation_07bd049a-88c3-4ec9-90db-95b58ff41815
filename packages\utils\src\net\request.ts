import axios from 'axios';
import chalk from 'chalk';
export const request = axios.create();

request.interceptors.request.use(async (config) => {
  const hash = Math.random().toString(36).substring(2);
  (config as any).__hash = hash;
  (config as any).__requestTime = Date.now();
  console.log('Request ', hash, config.url, JSON.stringify(config.data));
  return config;
});

request.interceptors.response.use(async (response) => {
  const hash = (response.config as any).__hash;
  const requestTime = (response.config as any).__requestTime;
  const cost = Date.now() - requestTime;
  console.log('Response', hash, response.status, cc(cost), response.config.url);
  return response;
});

const cc = (cost: number) => (cost < 500 ? chalk.green(`${cost}ms`) : chalk.red(`${cost}ms`));
