import { feishuRobot } from './feishu';
import { prisma } from './prisma';
import { vod } from './vod';

export const onFileUploaded = async (params: {
  forceUpdate: boolean;
  tenantId: string;
  info: { list: { name?: string; vodFileId: string }[]; directoryId: string };
}) => {
  if (!params.info.list.length) {
    throw new Error('文件ID不能为空');
  }
  if (params.info.list.some((v) => !v.vodFileId)) {
    throw new Error('文件ID不能为空');
  }
  console.log('VOD文件信息同步', JSON.stringify(params.info.list));

  const tenant = { id: params.tenantId };
  const sourceConfigs = await prisma.source_configs.findUnique({
    where: { tenant_id: params.tenantId },
  });
  if (!sourceConfigs) {
    throw new Error('未找到租户配置信息');
  }

  // 只获取基础信息和元数据就够了
  const vodFileInfos = await vod.DescribeMediaInfos({
    FileIds: params.info.list.map((v) => v.vodFileId),
    SubAppId: Number(sourceConfigs.vod_sub_app_id),
    Filters: ['basicInfo', 'metaData'],
  });
  if (vodFileInfos.NotExistFileIdSet?.length) {
    await feishuRobot.error('未找到对应的视频', ['同步VOD文件时', ...vodFileInfos.NotExistFileIdSet]);
  }

  // 查找对应fileId的素材库记录
  const materials = await prisma.materials.findMany({
    where: { vod_file_id: { in: params.info.list.map((v) => v.vodFileId) } },
  });

  const resList = [];
  for (const { name, vodFileId } of params.info.list) {
    const material = materials.find((m) => m.vod_file_id === vodFileId);
    const mediaInfo = vodFileInfos.MediaInfoSet?.find((info) => info.FileId === vodFileId);
    if (!mediaInfo) {
      await feishuRobot.error('未找到对应视频的媒体信息', ['同步VOD文件时', vodFileId]);
      console.error(`未找到对应视频的媒体信息: ${vodFileId}`);
      continue;
    }

    const record = {
      tenant_id: tenant.id,
      name: name ?? mediaInfo.BasicInfo!.Name!,
      size: Math.round(mediaInfo.MetaData!.Size / 1024),
      video_duration: mediaInfo.MetaData!.Duration,
      type: 'VIDEO',
      vod_sub_app_id: sourceConfigs.vod_sub_app_id,
      vod_category_id: String(mediaInfo.BasicInfo!.ClassId!),
      vod_file_id: mediaInfo.FileId!,
      vod_media_url: mediaInfo.BasicInfo!.MediaUrl!,
      vod_cover_url: mediaInfo.BasicInfo!.CoverUrl!,
      directory_id: params.info.directoryId,
    };

    if (!material) {
      // 不存在则创建
      const res = await prisma.materials.create({
        data: record,
      });
      // TODO: 暂时关闭，还没有接入新环境
      // 创建预切分任务
      // const task = await ctx.execute(createSplitTask, { name: record.name, materialId: res.id, slice_duration: 300 });
      // 执行预切分任务
      // await ctx.execute(splitTaskEmitter, { taskId: task.id });
      resList.push(res);
      continue;
    }
    if (params.forceUpdate) {
      // 强制更新
      const res = await prisma.materials.update({ where: { id: material.id }, data: record });
      resList.push(res);
      continue;
    }
  }
  await refreshStoreSize({ tenantId: tenant.id });
  return resList;
};

/**
 * 刷新存储空间
 * @param params
 * @returns
 */
export const refreshStoreSize = async (params: { tenantId: string }) => {
  const storageList = await prisma.materials.findMany({
    where: { tenant_id: params.tenantId, tmp_deleted_at: null },
    select: { size: true },
  });
  const totalSize = storageList.reduce((acc, item) => acc + item.size, 0);
  const totalSizeFloor = Math.floor(Number(totalSize) / 1024);
  await prisma.source_configs.update({
    where: { tenant_id: params.tenantId },
    data: { used_storage_size: totalSizeFloor },
  });
  return totalSizeFloor;
};

/**
 * 创建素材目录
 */
export const makeMaterialDirectory = async (params: {
  tenantId: string;
  data: { name: string; parentId: string | undefined };
}) => {
  if (!params.data.name) {
    throw new Error('目录名不能为空');
  }

  const tenant = { id: params.tenantId };

  // 如果有父目录,检查父目录是否存在
  if (params.data.parentId) {
    const parent = await prisma.material_directories.findUnique({
      where: { id: params.data.parentId, tenant_id: tenant.id },
    });
    if (!parent) {
      throw new Error('未找到父级目录');
    }
  }

  const directory = await prisma.material_directories.create({
    data: {
      name: params.data.name,
      tenant_id: tenant.id,
      parent_id: params.data.parentId,
    },
  });
  return directory;
};
