import React from 'react';

export const EmptyStorage = () => {
  return (
    <svg width="64" height="65" viewBox="0 0 64 65" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_1059_2682" fill="white">
        <path d="M0.38483 54.5075C-0.584519 39.9237 0.242147 25.5002 3.26636 11.2148C4.16118 7.8843 7.42753 4.81129 10.3687 4.48164C21.6885 3.28314 33.0083 3.00918 44.3283 3.65974C47.2703 3.83903 50.2338 6.90302 50.8127 10.3776C50.6717 12.6803 50.4764 14.9793 50.2269 17.2728" />
      </mask>
      <path
        d="M0.38483 54.5075C-0.584519 39.9237 0.242147 25.5002 3.26636 11.2148C4.16118 7.8843 7.42753 4.81129 10.3687 4.48164C21.6885 3.28314 33.0083 3.00918 44.3283 3.65974C47.2703 3.83903 50.2338 6.90302 50.8127 10.3776C50.6717 12.6803 50.4764 14.9793 50.2269 17.2728"
        fill="#D9D9D9"
        fillOpacity="0.1"
      />
      <path
        d="M3.26636 11.2148L2.30061 10.9554L2.29362 10.9814L2.28804 11.0077L3.26636 11.2148ZM10.3687 4.48164L10.2634 3.48717L10.2573 3.48786L10.3687 4.48164ZM44.3283 3.65974L44.3891 2.66159L44.3857 2.66139L44.3283 3.65974ZM50.8127 10.3776L51.8109 10.4387L51.8178 10.3253L51.7991 10.2132L50.8127 10.3776ZM1.38263 54.4412C0.418731 39.9394 1.24137 25.6086 4.24467 11.4219L2.28804 11.0077C-0.757078 25.3918 -1.58777 39.908 -0.612968 54.5738L1.38263 54.4412ZM4.23211 11.4743C4.62377 10.0165 5.55175 8.57066 6.73728 7.44945C7.93068 6.32079 9.29021 5.60878 10.4801 5.47541L10.2573 3.48786C8.50604 3.68415 6.76181 4.67347 5.36303 5.99637C3.95638 7.32671 2.80377 9.0826 2.30061 10.9554L4.23211 11.4743ZM10.474 5.47608C21.74 4.28328 33.0053 4.01066 44.2709 4.6581L44.3857 2.66139C33.0112 2.0077 21.637 2.283 10.2634 3.48719L10.474 5.47608ZM44.2675 4.65789C45.4214 4.72821 46.6768 5.37962 47.7374 6.47765C48.7908 7.56832 49.5731 9.02228 49.8263 10.5419L51.7991 10.2132C51.4734 8.2583 50.4845 6.443 49.1759 5.08821C47.8745 3.74079 46.1772 2.77056 44.3891 2.6616L44.2675 4.65789ZM49.8146 10.3164C49.6746 12.6034 49.4806 14.8868 49.2327 17.1646L51.221 17.381C51.4722 15.0718 51.6689 12.7571 51.8109 10.4387L49.8146 10.3164Z"
        fill="url(#paint0_linear_1059_2682)"
        mask="url(#path-1-inside-1_1059_2682)"
      />
      <g filter="url(#filter0_b_1059_2682)">
        <path
          d="M36.8958 16.9785C44.074 17.0427 51.2527 17.2602 58.4312 17.6304C61.9251 17.8336 64.5109 20.6689 63.9142 23.9592C62.1105 33.6293 58.8463 43.7869 55.0124 54.2575C53.6948 57.8323 49.3366 61.0713 45.4157 61.2705C32.2699 61.9314 19.1242 61.5927 5.9779 60.2542C2.05621 59.8288 -0.36199 56.7478 0.765956 53.4536C4.00207 43.9277 8.07572 33.9465 12.2186 23.7531C13.6847 20.2056 17.0043 17.1904 19.5902 17.1201C22.1988 17.0538 24.3332 17.0225 24.3332 17.0223"
          fill="#D9D9D9"
          fillOpacity="0.1"
        />
      </g>
      <path
        d="M33.9263 52.6565C33.2731 51.0793 32.9369 49.3888 32.937 47.6817V47.6816C32.9369 45.9744 33.2731 44.284 33.9263 42.7068C34.5795 41.1296 35.537 39.6965 36.7441 38.4893C37.9512 37.2821 39.3842 36.3245 40.9614 35.6712C42.5386 35.0179 44.229 34.6816 45.9361 34.6816C47.6433 34.6816 49.3337 35.0179 50.9109 35.6712C52.488 36.3245 53.9211 37.2821 55.1282 38.4893C56.3353 39.6965 57.2928 41.1296 57.946 42.7068C58.5992 44.284 58.9354 45.9744 58.9352 47.6816V47.6817C58.9354 49.3888 58.5992 51.0793 57.946 52.6565C57.2928 54.2337 56.3353 55.6668 55.1282 56.874C53.9211 58.0812 52.488 59.0387 50.9109 59.6921C49.3337 60.3454 47.6433 60.6816 45.9361 60.6816C44.229 60.6816 42.5386 60.3454 40.9614 59.6921C39.3842 59.0387 37.9512 58.0812 36.7441 56.874C35.537 55.6668 34.5795 54.2337 33.9263 52.6565Z"
        fill="#0D1320"
        stroke="#00E1FF"
        strokeWidth="2"
      />
      <path
        d="M44.1863 47.4872C44.1863 46.5207 44.9698 45.7372 45.9363 45.7372C46.9028 45.7372 47.6863 46.5207 47.6863 47.4872V52.9316C47.6863 53.8981 46.9028 54.6816 45.9363 54.6816C44.9698 54.6816 44.1863 53.8981 44.1863 52.9316V47.4872ZM44.1863 42.4316C44.1863 41.4651 44.9698 40.6816 45.9363 40.6816C46.9028 40.6816 47.6863 41.4651 47.6863 42.4316C47.6863 43.3981 46.9028 44.1816 45.9363 44.1816C44.9698 44.1816 44.1863 43.3981 44.1863 42.4316Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_b_1059_2682"
          x="-24.512"
          y="-8.02148"
          width="113.512"
          height="94.6197"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="12.5" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1059_2682" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1059_2682" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_1059_2682"
          x1="25.4064"
          y1="3.31641"
          x2="25.4064"
          y2="54.5075"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EFEDFD" stopOpacity="0.1" />
          <stop offset="1" stopColor="#EFEDFD" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
