#!/usr/bin/env node

/**
 * 认证模式切换脚本
 * 在 Authing 和本地认证之间切换
 */

const fs = require('fs');
const path = require('path');

const PLUGIN_DIR = path.join(__dirname, '../packages/serve/src/context/plugins');
const AUTHING_PLUGIN = path.join(PLUGIN_DIR, 'authing.ts');
const AUTHING_MANAGE_PLUGIN = path.join(PLUGIN_DIR, 'authing-manage.ts');
const LOCAL_AUTH_PLUGIN = path.join(PLUGIN_DIR, 'local-auth.ts');
const LOCAL_AUTH_MANAGE_PLUGIN = path.join(PLUGIN_DIR, 'local-auth-manage.ts');

const BACKUP_SUFFIX = '.authing-backup';

function log(level, message) {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  }[level] || '📋';
  
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

function fileExists(filePath) {
  return fs.existsSync(filePath);
}

function backupFile(filePath) {
  if (fileExists(filePath)) {
    const backupPath = filePath + BACKUP_SUFFIX;
    fs.copyFileSync(filePath, backupPath);
    log('info', `备份文件: ${path.basename(filePath)} -> ${path.basename(backupPath)}`);
    return true;
  }
  return false;
}

function restoreFile(filePath) {
  const backupPath = filePath + BACKUP_SUFFIX;
  if (fileExists(backupPath)) {
    fs.copyFileSync(backupPath, filePath);
    log('info', `恢复文件: ${path.basename(backupPath)} -> ${path.basename(filePath)}`);
    return true;
  }
  return false;
}

function switchToLocal() {
  log('info', '切换到本地认证模式...');

  try {
    // 备份原有的 Authing 插件
    backupFile(AUTHING_PLUGIN);
    backupFile(AUTHING_MANAGE_PLUGIN);

    // 检查本地认证插件是否存在
    if (!fileExists(LOCAL_AUTH_PLUGIN)) {
      log('error', '本地认证插件不存在，请先创建');
      return false;
    }

    if (!fileExists(LOCAL_AUTH_MANAGE_PLUGIN)) {
      log('error', '本地管理插件不存在，请先创建');
      return false;
    }

    // 复制本地认证插件到原位置
    fs.copyFileSync(LOCAL_AUTH_PLUGIN, AUTHING_PLUGIN);
    fs.copyFileSync(LOCAL_AUTH_MANAGE_PLUGIN, AUTHING_MANAGE_PLUGIN);

    log('success', '已切换到本地认证模式');
    log('info', '请重启应用以使更改生效');
    
    return true;

  } catch (error) {
    log('error', `切换失败: ${error.message}`);
    return false;
  }
}

function switchToAuthing() {
  log('info', '切换到 Authing 认证模式...');

  try {
    // 恢复原有的 Authing 插件
    const authingRestored = restoreFile(AUTHING_PLUGIN);
    const authingManageRestored = restoreFile(AUTHING_MANAGE_PLUGIN);

    if (!authingRestored || !authingManageRestored) {
      log('error', '未找到 Authing 插件备份文件');
      return false;
    }

    log('success', '已切换到 Authing 认证模式');
    log('info', '请重启应用以使更改生效');
    
    return true;

  } catch (error) {
    log('error', `切换失败: ${error.message}`);
    return false;
  }
}

function getCurrentMode() {
  if (!fileExists(AUTHING_PLUGIN)) {
    return 'unknown';
  }

  const content = fs.readFileSync(AUTHING_PLUGIN, 'utf8');
  
  if (content.includes('LocalAuthenticationClient')) {
    return 'local';
  } else if (content.includes('AuthenticationClient')) {
    return 'authing';
  } else {
    return 'unknown';
  }
}

function showStatus() {
  log('info', '=== 认证模式状态 ===');
  
  const currentMode = getCurrentMode();
  
  switch (currentMode) {
    case 'local':
      log('success', '当前模式: 本地认证');
      break;
    case 'authing':
      log('success', '当前模式: Authing 认证');
      break;
    default:
      log('warning', '当前模式: 未知');
      break;
  }

  // 检查文件状态
  log('info', '文件状态:');
  log('info', `  - authing.ts: ${fileExists(AUTHING_PLUGIN) ? '存在' : '不存在'}`);
  log('info', `  - authing-manage.ts: ${fileExists(AUTHING_MANAGE_PLUGIN) ? '存在' : '不存在'}`);
  log('info', `  - local-auth.ts: ${fileExists(LOCAL_AUTH_PLUGIN) ? '存在' : '不存在'}`);
  log('info', `  - local-auth-manage.ts: ${fileExists(LOCAL_AUTH_MANAGE_PLUGIN) ? '存在' : '不存在'}`);
  
  // 检查备份文件
  const hasBackups = fileExists(AUTHING_PLUGIN + BACKUP_SUFFIX) && fileExists(AUTHING_MANAGE_PLUGIN + BACKUP_SUFFIX);
  log('info', `  - Authing 备份: ${hasBackups ? '存在' : '不存在'}`);
}

function showHelp() {
  console.log(`
认证模式切换脚本

用法: node ${path.basename(__filename)} [命令]

命令:
  local     切换到本地认证模式
  authing   切换到 Authing 认证模式
  status    显示当前认证模式状态
  help      显示此帮助信息

示例:
  node ${path.basename(__filename)} local    # 切换到本地认证
  node ${path.basename(__filename)} authing  # 切换到 Authing 认证
  node ${path.basename(__filename)} status   # 查看当前状态

注意事项:
1. 切换模式后需要重启应用
2. 首次切换到本地认证前，请确保已创建本地认证插件
3. 切换会自动备份原有插件文件
`);
}

function main() {
  const command = process.argv[2];

  switch (command) {
    case 'local':
      if (switchToLocal()) {
        process.exit(0);
      } else {
        process.exit(1);
      }
      break;

    case 'authing':
      if (switchToAuthing()) {
        process.exit(0);
      } else {
        process.exit(1);
      }
      break;

    case 'status':
      showStatus();
      break;

    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;

    default:
      log('error', `未知命令: ${command || '(空)'}`);
      showHelp();
      process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  switchToLocal,
  switchToAuthing,
  getCurrentMode,
  showStatus
};
