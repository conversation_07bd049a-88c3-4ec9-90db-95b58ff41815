import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/Pagination';

export const DEFAULT_PAGE_SIZE = 100;
export const DEFAULT_PAGINATION = { page: 1, pageSize: DEFAULT_PAGE_SIZE };

export const ProPagination = ({
  pagination,
  onPaginationChange: setPagination,
}: {
  pagination: { page: number; pageSize: number; total?: number };
  onPaginationChange: (pagination: { page: number; pageSize: number }) => void;
}) => {
  return (
    <Pagination>
      <PaginationContent>
        共 {pagination.total} 条
        <PaginationItem>
          <PaginationPrevious
            href="#"
            size={'sm'}
            onClick={(e) => {
              e.preventDefault();
              setPagination({ ...pagination, page: pagination.page - 1 });
            }}
          />
        </PaginationItem>
        {(() => {
          const totalPages = Math.ceil((pagination.total || 0) / pagination.pageSize);
          const currentPage = pagination.page;
          const pages = [];

          // 计算需要显示的页码范围
          const start = Math.max(1, currentPage - 2);
          const end = Math.min(totalPages, currentPage + 2);

          // 处理开头的页码
          if (start > 1) {
            pages.push(1);
            if (start > 2) {
              pages.push('ellipsis_start');
            }
          }

          // 添加中间的页码
          for (let i = start; i <= end; i++) {
            pages.push(i);
          }

          // 处理结尾的页码
          if (end < totalPages) {
            if (end < totalPages - 1) {
              pages.push('ellipsis_end');
            }
            pages.push(totalPages);
          }

          return pages.map((page, index) => {
            if (page === 'ellipsis_start' || page === 'ellipsis_end') {
              return (
                <PaginationItem key={page}>
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            return (
              <PaginationItem key={index}>
                <PaginationLink
                  size={'sm'}
                  href="#"
                  isActive={pagination.page === page}
                  onClick={(e) => {
                    e.preventDefault();
                    setPagination({ ...pagination, page: page as number });
                  }}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            );
          });
        })()}
        <PaginationItem>
          <PaginationNext
            href="#"
            size={'sm'}
            onClick={(e) => {
              e.preventDefault();
              setPagination({ ...pagination, page: pagination.page + 1 });
            }}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};
