import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { generateRandomBase36, Logger } from '@roasmax/utils';

export interface DifyBatchResponse {
  task_id: string;
  workflow_run_id: string;
  data: {
    id: string;
    workflow_id: string;
    status: 'succeeded' | 'failed' | 'pending';
    outputs: {
      data: string;
    };
    error: null | string;
    elapsed_time: number;
    total_tokens: number;
    total_steps: number;
    created_at: number;
    finished_at: number;
  };
}

export const POST = async (request: NextRequest) => {
  const logger = new Logger('batch-generate', 'api', request.headers.get('x-request-id') || generateRandomBase36(10));
  logger._start('开始批量生成产品描述');

  try {
    if (!process.env.DIFY_API_URL || !process.env.DIFY_API_KEY) {
      logger.error('环境变量未设置：DIFY_API_URL 或 DIFY_API_KEY');
      throw new Error('DIFY_API_URL or DIFY_API_KEY is not set');
    }

    const body = await request.json();
    const { productName = '', count = 20, language = 'en' } = body;

    if (!productName) {
      logger.error('缺少必要参数：productName');
      throw new Error('Product name is required');
    }

    logger.info(`请求参数：productName=${productName}, count=${count}, language=${language}`);

    logger._push('调用 Dify API');
    const response = await fetch(process.env.DIFY_API_URL, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${process.env.DIFY_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: {
          product_name: productName,
          count: count,
          language: language,
        },
        response_mode: 'blocking',
        user: 'system',
      }),
    });
    logger._pop();

    const data: DifyBatchResponse = await response.json();
    logger.info('成功获取响应', { taskId: data.task_id });
    logger._end('批量生成完成');

    return NextResponse.json(data);
  } catch (error) {
    logger.error('处理请求时发生错误', error);
    throw error;
  }
};
