import tiktokService from '@/services/tiktokService';
import { to } from '@roasmax/utils';
import Cookies from 'js-cookie';
import { cloneDeep } from 'lodash';
import { toast } from 'react-hot-toast';
import type { StoreApi } from 'zustand';
import { defaultAdRightDrawerValue } from '../defaultStores';
import type { AdStore } from '../storeTypes';
import type { StoreSlice } from './types';
import { recursiveCamelToUnderscore } from '@/utils/camel';
import { AdFormValues } from '@/components/AdsTikTok/Sheet/AdSheet/adFormSchema';
import { AdItem } from '@/types/ads';
import emitter from '@/utils/mitt';

export interface AdSlice
  extends StoreSlice<
    | 'setCurrentAd'
    | 'createOrUpdateAd'
    | 'uploadVideoToTiktok'
    | 'uploadImageToTiktok'
    | 'setAdRightDrawer'
    | 'clearAdRightDrawer'
    | 'setCloudSheetOpen'
    | 'generateAdText'
    | 'cancelGenerateAdText'
    | 'updateAdStatus'
  > {}

export const createAdSlice = (set: StoreApi<AdStore>['setState'], get: StoreApi<AdStore>['getState']): AdSlice => ({
  actions: {
    setCurrentAd: (ad) => {
      set({ currentAd: ad });
    },
    setCloudSheetOpen: (open) => {
      set({ cloudSheetOpen: open });
    },
    setAdRightDrawer: (drawer) => {
      // @ts-ignore
      set((state) => ({
        adRightDrawer: {
          ...state.adRightDrawer,
          ...drawer,
        },
      }));
    },

    clearAdRightDrawer: () => {
      set({
        adRightDrawer: cloneDeep(defaultAdRightDrawerValue),
      });
    },
    updateAdStatus: async (params) => {
      const isDelete = params.operationStatus === 'DELETE';
      try {
        const data = recursiveCamelToUnderscore(params);
        const [error, response] = await to(tiktokService.updateAdStatus(data));

        if (error || !response) {
          toast.error(isDelete ? '删除广告失败' : '更新广告状态失败');
          throw error;
        }

        toast.success(isDelete ? '删除广告成功' : '更新广告状态成功');

        const currentAdvertiser = get().currentAdvertiser;
        const currentAdGroup = get().currentAdGroup;

        emitter.emit('REFRESH_AD_LIST', {
          page: 1,
          pageSize: 10,
          advertiserIds: currentAdvertiser?.map((advertiser) => advertiser.advertiserId).join(','),
          groupIds: currentAdGroup?.map((adGroup) => adGroup.groupId).join(','),
        });
      } catch (error) {
        toast.error(isDelete ? '删除广告失败' : '更新广告状态失败');
        throw error;
      }
    },
    createOrUpdateAd: async (adData: AdFormValues, record?: AdItem) => {
      set({ createAdStatus: { loading: true, error: false } });

      try {
        const currentAdvertiser = get().currentAdvertiser;
        const currentAdGroup = get().currentAdGroup;

        // 非编辑模式下, 创建必须指定广告主和广告组
        if ((!currentAdvertiser || !currentAdGroup) && adData.sheetType !== 'edit') {
          throw new Error('未选择广告主或广告组');
        }

        // @ts-ignore
        const { adgroupIds, selectedVideoItem = [], videoId, imageIds = [], ...restData } = adData;
        const { type } = get().adRightDrawer;

        // 1. 处理选择的视频
        // 处理单个 video_id 的情况
        const videoIdCreatives = videoId
          ? [
              {
                ...restData,
                video_id: videoId,
                ...(imageIds?.length > 0 ? { image_ids: imageIds } : {}),
              },
            ]
          : [];

        // 处理多个选中视频的情况
        const selectedVideoCreatives = selectedVideoItem.map((item: any) => ({
          ...(item.properties?.map_platform?.toLowerCase() === 'tiktok'
            ? {
                video_id: item.properties?.map_platform_info?.tk_video_id ?? '',
                image_ids: [item.properties?.map_platform_info?.tk_image_id ?? ''],
              }
            : {}),
          vod_url: item.vod_media_url,
          cover_url: item.vod_cover_url,
          material_id: item.id,
        }));

        const baseCreatives = [...videoIdCreatives, ...selectedVideoCreatives];

        // 检查创意数量限制
        if (baseCreatives.length > 20) {
          set({ createAdStatus: { loading: false, error: true } });
          toast.error('创意数量不能超过20个');
          return;
        }

        // 2. 生成请求入参
        // 根据广告组和创意生成最终的请求数据
        let requestData: any = [];

        if (adData.sheetType === 'edit') {
          if (!record) {
            throw new Error('record is undefined');
          }
          requestData = {
            advertiser_id: record.advertiserId,
            adgroup_id: record.groupId,
            patch_update: true,
            id: record.id,
            creatives: [
              {
                ad_id: record.adId,
                ad_name: adData.adName,
              },
            ],
          };
        } else if (adData.sheetType === 'normal') {
          requestData = currentAdGroup
            ?.filter((each) => each.source === 'ADS')
            .map((group) => ({
              adgroup_id: group.groupId,
              creatives: baseCreatives.map((creative) => {
                // not xxx type
                if (typeof group.jsonDate === 'string') {
                  toast.error('group.jsonDate is not a valid object');
                  throw new Error('group.jsonDate is not a valid object');
                }

                return {
                  ...creative,
                  ad_format: 'SINGLE_VIDEO',
                  vertical_video_strategy: 'SINGLE_VIDEO',
                  ...(adData.sheetType === 'normal' && {
                    ad_name: adData.adName,
                    ad_text: adData.adText,
                  }),
                  identity_id: adData.identityId,
                  identity_type: adData.identityType,
                  identity_authorized_bc_id: adData.identityAuthorizedBcId,
                  item_group_ids: adData.itemGroupIds,
                  dark_post_status: adData.darkPostStatus ? 'ON' : 'OFF',
                  operation_status: adData?.operationStatus ? 'ENABLE' : 'DISABLE',
                };
              }),
            }));
        } else {
          requestData = {
            adgroup_ids: adgroupIds,
            properties: {
              industry: adData?.industry ?? '电商',
              accelerate: adData.speed,
              subtitle: !!adData?.subtitle,
              transition_mode: adData?.transitionMode ? 'fade' : 'null',
              generate_round: Number(adData?.generateRound) ?? 1,
              language: adData.language,
              product_info: currentAdGroup?.map((group) => {
                return {
                  group_id: group?.groupId ?? '',
                  product_names: adData?.productNames ?? [],
                  creatives: Array.from({ length: adData.expectVideoCount }, (_, index) => ({
                    identity_id: adData.identityId,
                    identity_type: adData.identityType,
                    identity_authorized_bc_id: adData.identityAuthorizedBcId,
                    item_group_ids: adData.itemGroupIds,
                    operation_status: adData?.operationStatus ? 'ENABLE' : 'DISABLE',
                    dark_post_status: adData?.darkPostStatus ? 'ON' : 'OFF',
                    ad_format: 'SINGLE_VIDEO',
                    vertical_video_strategy: 'SINGLE_VIDEO',
                  })),
                };
              }),
            },
            material_ids: selectedVideoItem.map((item) => item.id),
            create_num: adData.expectVideoCount,
            operation_status: adData?.operationStatus ? 'ENABLE' : 'DISABLE',
            dark_post_status: adData?.darkPostStatus ? 'ON' : 'OFF',
          };
        }

        const underScoreRequestData = recursiveCamelToUnderscore(requestData);

        // 执行创建或更新操作
        if (type === 'edit') {
          const [error, response] = await to(tiktokService.updateAd(underScoreRequestData));

          if (error || !response) {
            set({ createAdStatus: { loading: false, error: true } });
            toast.error('更新广告失败');
            return;
          }
          toast.success('更新广告成功');
        } else {
          let error: any;
          let response: any;
          if (adData.sheetType === 'normal') {
            [error, response] = await to(tiktokService.createAd(underScoreRequestData));
          } else {
            [error, response] = await to(tiktokService.fastCreateAd(underScoreRequestData));
          }

          if (error || !response) {
            set({ createAdStatus: { loading: false, error: true } });
            toast.error(`${adData.sheetType === 'normal' ? '创建' : '快速创建'}广告失败`);
            return;
          }

          toast.success(`${adData.sheetType === 'normal' ? '创建' : '快速创建'}创建广告成功`);
        }

        // 清理表单状态
        get().actions.clearAdRightDrawer();
        get().actions.setAdRightDrawer({
          ...defaultAdRightDrawerValue,
          type: 'create',
          formValue: {},
        });

        set({ createAdStatus: { loading: false, error: false } });
        // 触发广告列表更新，支持传入分页参数
        emitter.emit('REFRESH_AD_LIST', {
          page: 1,
          pageSize: 10,
          advertiserIds: currentAdvertiser?.map((advertiser) => advertiser.advertiserId).join(','),
          groupIds: currentAdGroup?.map((adGroup) => adGroup.groupId).join(','),
        });
      } catch (error) {
        set({ createAdStatus: { loading: false, error: true } });
        const { type } = get().adRightDrawer;
        console.error(type === 'edit' ? '更新广告失败:' : '创建广告失败:', error);
        toast.error(type === 'edit' ? '更新广告失败' : '创建广告失败');
        throw error;
      }
    },

    uploadVideoToTiktok: async (params) => {
      const currentAdvertiser = get().currentAdvertiser;
      if (!currentAdvertiser) {
        throw new Error('未选择广告主');
      }

      try {
        const response = await tiktokService.uploadVideoToTikTokMaterialLibrary({
          advertiser_ids: currentAdvertiser.map((advertiser) => advertiser.advertiserId).join(','),
          file_name: params.file_name,
          upload_type: 'UPLOAD_BY_URL',
          video_url: params.video_url,
          is_third_party: true,
          flaw_detect: true,
          auto_fix_enabled: true,
          auto_bind_enabled: true,
        });
        return response;
      } catch (error) {
        console.error('上传到抖音失败:', error);
        toast.error('上传到抖音失败');
        throw error;
      }
    },

    uploadImageToTiktok: async (params) => {
      const currentAdvertiser = get().currentAdvertiser;
      if (!currentAdvertiser) {
        throw new Error('未选择广告主');
      }

      try {
        const response = await tiktokService.uploadImageToTikTokMaterialLibrary({
          advertiser_ids: currentAdvertiser.map((advertiser) => advertiser.advertiserId).join(','),
          file_name: params?.file_name ?? '',
          upload_type: 'UPLOAD_BY_URL',
          image_url: params.image_url,
        });
        return response;
      } catch (error) {
        console.error('上传图片到抖音失败:', error);
        toast.error('上传图片到抖音失败');
        throw error;
      }
    },

    generateAdText: async (productName: string, callback: (text: string) => void, abortSignal?: AbortSignal) => {
      set({ generateAdTextStatus: { loading: true, error: false } });

      try {
        const response = await fetch('/api/llm/chat/generate-product-description', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            AccessToken: Cookies.get('Authorization') || '',
          },
          body: JSON.stringify({
            productName,
            language: 'zh',
          }),
          signal: abortSignal,
        });

        if (!response.ok) {
          throw new Error('生成文案失败');
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('无法读取响应流');
        }

        let accumulatedText = '';
        let displayText = '';
        const { actions, adRightDrawer } = get();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = new TextDecoder().decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonData = JSON.parse(line.slice(6));

                // 处理 text_chunk 事件
                if (jsonData.event === 'text_chunk' && jsonData.data?.text) {
                  const newChar = jsonData.data.text;
                  accumulatedText += newChar;

                  // 如果是JSON的开始或结束符号，先不显示
                  if (newChar === '{' || newChar === '}' || newChar === '"' || newChar === ':' || newChar === ' ') {
                    continue;
                  }

                  // 如果是title字段的开始，跳过
                  if (newChar === 't' && accumulatedText.endsWith('{"t')) {
                    continue;
                  }
                  if (accumulatedText.endsWith('{"title')) {
                    continue;
                  }

                  // 将新字符添加到显示文本中
                  displayText += newChar;
                  callback(displayText);
                }

                if (jsonData.event === 'workflow_finished' && jsonData.data?.outputs?.result) {
                  try {
                    const finalResult = JSON.parse(jsonData.data.outputs.result);
                    if (finalResult.title) {
                      callback(finalResult?.title ?? '');
                    }
                    // 关闭流
                    reader?.cancel();
                  } catch (e) {
                    console.error('解析最终结果失败:', e);
                  }
                }
              } catch (e) {
                // 解析单行数据失败，继续处理下一行
                continue;
              }
            }
          }
        }

        set({ generateAdTextStatus: { loading: false, error: false } });
        return displayText;
      } catch (error: unknown) {
        if (error instanceof Error && error.name === 'AbortError') {
          set({ generateAdTextStatus: { loading: false, error: false } });
          return;
        }
        console.error('生成文案错误:', error);
        set({ generateAdTextStatus: { loading: false, error: true } });
        throw error;
      }
    },

    cancelGenerateAdText: () => {
      set({ generateAdTextStatus: { loading: false, error: false } });
    },
  },
});
