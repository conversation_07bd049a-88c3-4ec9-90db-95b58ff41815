'use client';

import { But<PERSON>, Checkbox, Panel } from '@/components/ui';
import { useBatchDownload } from '@/hooks/useBatchDownload';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { TaskStatus } from '@/types/task';
import { ActionResult } from '@/utils/server-action/action';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import SubTaskCard from './SubTaskCard';
import SubTaskPreviewDialog, { SubTaskPreviewDialogRef } from './SubTaskPreviewDialog';
import { cn } from '@/utils/cn';
import { useTaskCreator, TaskCreatorFormValues } from '@/hooks/useTaskCreator';
import { UserAvatar } from '@/components/UserAvatar';
import { useRouter } from 'next/navigation';
import { useTaskCreatorStore } from '@/hooks/useTaskCreator';
import { ProCheckbox } from '../pro/pro-checkbox';

type TaskType = ArrayItemType<ActionResult<typeof pageVideoGenerationTasks>['list']>;

/**
 * 任务
 * 一个任务包含了多个子任务，每一个子任务以卡片的形式展示
 * @param props
 * @returns
 */
const TaskItem: React.FC<{
  data: TaskType;
}> = ({ data }) => {
  const [selectedSubTasks, setSelectedSubTasks] = useState<TaskType['subTasks']>([]);
  const { promptList, getPromptByValue, updateFormValues, setSelectedMaterials } = useTaskCreator();
  const { downloadAndZipVideos } = useBatchDownload();
  const { setFormData } = useTaskCreatorStore();

  const subTaskPreviewDialog = useRef<SubTaskPreviewDialogRef>(null);

  /** 全选状态 */
  const checkedAll = useMemo(() => {
    if (selectedSubTasks.length === 0) {
      return false;
    }
    if (selectedSubTasks.length === data.subTasks.length) {
      return true;
    }
    return 'indeterminate';
  }, [data.subTasks.length, selectedSubTasks.length]);

  /** 全选按钮点击事件 */
  const handleSelectAll = useCallback(() => {
    if (checkedAll === true) {
      setSelectedSubTasks([]);
    } else {
      setSelectedSubTasks(data.subTasks || []);
    }
  }, [checkedAll, data.subTasks]);

  /** 子任务项选择事件 */
  const handleSelectSubTask = useCallback(
    (subTaskId: string, checked: boolean) => {
      if (checked) {
        setSelectedSubTasks((prev) => [...prev, data.subTasks.find((t) => t.id === subTaskId)!]);
      } else {
        setSelectedSubTasks((prev) => prev.filter((t) => t.id !== subTaskId));
      }
    },
    [data.subTasks],
  );
  const router = useRouter();

  const handleRegenerate = async (subTasks: TaskType['subTasks']) => {
    const matchedPrompts = promptList?.find((p) => (data.prompts as string[]).includes(p.name));
    const { industry, duration: referenceDuration } = getPromptByValue(matchedPrompts?.name);
    console.log(data);
    const formData = {
      name: data.name,
      subtitle: !!data.subtitle,
      transition_mode: data.transition_mode,
      generateRound: String(data.generate_round),
      speed: data.video_speed,
      sliceType: data.slice_duration,
      method: data.method,
      language: data.video_language,

      industry: data.method === 'normal' ? data.industry || industry : undefined,
      referenceDuration: data.method === 'normal' ? referenceDuration : undefined,
      prompts: data.method === 'normal' ? ((data.prompts || []) as string[]) : [],
      dify: data.method === 'normal' && data.dify_workflow_key ? true : false,

      productUrl: data.method === 'gc_imitate' ? data.product_url : undefined,
      sceneImplantation: data.method === 'gc_imitate' ? data.scene_implantation : undefined,
      festiveAtmosphere: data.method === 'gc_imitate' ? data.festive_atmosphere : undefined,
      generationType: data.method === 'gc_imitate' ? data.generation_type : undefined,
      industryId: data.method === 'gc_imitate' && data.generation_type === '大V严选' ? data.industry_id : industry,
      kolStyle: data.method === 'gc_imitate' && data.generation_type === '大V严选' ? data.kol_style : undefined,
      targetLanguage:
        data.method === 'gc_imitate' && data.generation_type === '大V严选' ? data.target_language : undefined,

      templateVideoVodId:
        data.method === 'gc_imitate' && data.generation_type === 'KOL转移' ? data.template_video_vod_id : undefined,
      templateVideoLanguage:
        data.method === 'gc_imitate' && data.generation_type === 'KOL转移' ? data.template_video_language : undefined,
      targetVoice: data.method === 'gc_imitate' && data.generation_type === 'KOL转移' ? data.target_voice : undefined,
    } as TaskCreatorFormValues;
    setFormData(formData);
    await updateFormValues(formData);
    setSelectedMaterials(() => subTasks.filter((t) => t.origin_material).map((t) => t.origin_material!));
    // 等待表单值更新完成后再跳转
    await new Promise((resolve) => setTimeout(resolve, 100));
    router.push(`/originality`);
  };

  /** 批量下载 */
  const handleDownload = useCallback(
    (subTasks: TaskType['subTasks']) => {
      const selected_generated_materials = subTasks.reduce(
        (all, t) => all.concat(t.generated_materials),
        [] as TaskType['subTasks'][0]['generated_materials'],
      );
      downloadAndZipVideos(selected_generated_materials);
    },
    [downloadAndZipVideos],
  );
  return (
    <div className="flex items-start justify-between pl-4 pr-4">
      <div className="item mb-2 flex w-full gap-3">
        <div className="h-10 w-10">
          <UserAvatar />
        </div>
        <div className="flex-1">
          <div>
            <div className="flex justify-between">
              <TaskHead data={data} />
              <div className="flex items-center space-x-2 text-nowrap">
                <div className="text-xs">全选</div>
                <ProCheckbox checked={checkedAll} onCheckedChange={handleSelectAll} />
              </div>
            </div>
            <div className="flex flex-wrap gap-3">
              {data.subTasks.map((subTask) => {
                const notEmpty = !!subTask.origin_material || !!subTask.generated_materials?.length;
                return (
                  <SubTaskCard
                    key={subTask.id}
                    data={subTask}
                    checked={selectedSubTasks.some((s) => s.id === subTask.id)}
                    onCheckedChange={(checked) => handleSelectSubTask(subTask.id, checked)}
                    onClick={() => {
                      if (!notEmpty) return;
                      subTaskPreviewDialog.current?.show(subTask);
                    }}
                    className={notEmpty ? '' : 'cursor-not-allowed'}
                  />
                );
              })}
              <SubTaskPreviewDialog ref={subTaskPreviewDialog} />
            </div>
          </div>
          {selectedSubTasks.length > 0 && (
            <OperationBar
              task={data}
              data={selectedSubTasks}
              onRegenerate={handleRegenerate}
              onDownload={handleDownload}
              onCancel={() => setSelectedSubTasks([])}
            />
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * 任务头部信息
 * @param props
 * @returns
 */
const TaskHead: React.FC<{
  data: TaskType;
}> = (props) => {
  return (
    <div className="mb-4 mt-1">
      <div className="mb-1.5">
        <div className="font-medium text-[#FFFFFF]">
          {props.data.subTasks.some((st) => st.status !== TaskStatus.SUCCESS && st.status !== TaskStatus.FAILED)
            ? '我正在为你努力生成中...'
            : `我已为你生成了 ${props.data.subTasks.reduce((all, t) => all + t.generated_materials?.length, 0) || 0} 条视频，你可以选择视频保存`}
        </div>
      </div>
      <div className="flex text-xs font-normal text-[#9FA4B2]">
        <div>任务名称：{props.data.name}</div>
        <div className="mx-2">|</div>
        <div>
          类型：
          {props.data.method === 'gc_imitate'
            ? props.data.generation_type === '大卖推荐'
              ? 'AI搜索爆款克隆'
              : '自定义视频裂变'
            : 'AI视频精剪'}
        </div>
        <div className="mx-2">|</div>
        <div>{dayjs(props.data.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
        <div className="mx-2">|</div>
        <div>任务ID: {props.data.id}</div>
      </div>
    </div>
  );
};

const OperationBar: React.FC<{
  task: TaskType;
  data: TaskType['subTasks'];
  onRegenerate?: (data: TaskType['subTasks']) => void;
  onDownload?: (data: TaskType['subTasks']) => void;
  onCancel?: () => void;
}> = (props) => {
  const originMaterials = props.data?.map((t) => t.origin_material).filter(Boolean);
  const generatedMaterials = props.data?.reduce(
    (all, t) => all.concat(t.generated_materials),
    [] as TaskType['subTasks'][0]['generated_materials'],
  );

  console.log('🚀 =>>>>>>> ~ index.tsx:255 ~ task:', props.task);
  return (
    <Panel className="mt-6 flex h-12 items-center justify-between rounded-[12px] px-3">
      <div className="px-1">
        已选择 {originMaterials.length} 条原始视频， {generatedMaterials.length} 条生成视频
      </div>
      <div className="space-x-2">
        <Button variant="link" className="h-[32px] text-[#9FA4B2]" onClick={props.onCancel}>
          取消
        </Button>
        {props.task.method === 'normal' && (
          <Button
            variant="secondary"
            disabled={!originMaterials.length}
            className={cn(
              !originMaterials.length ? 'cursor-not-allowed disabled:pointer-events-auto' : '',
              'h-[32px] w-[108px] rounded-[8px] bg-[#FFFFFF1A] hover:bg-[#CCDDFF33]',
            )}
            onClick={() => {
              if (!originMaterials.length) return;
              props.onRegenerate?.(props.data);
            }}
          >
            再次生成
          </Button>
        )}
        <Button
          variant="default"
          disabled={!generatedMaterials.length}
          className={cn(
            !generatedMaterials.length ? 'cursor-not-allowed disabled:pointer-events-auto' : '',
            'h-[32px] w-[108px] rounded-[8px] text-[#050A1C]',
          )}
          onClick={() => {
            if (!generatedMaterials.length) return;
            props.onDownload?.(props.data);
          }}
        >
          下载
        </Button>
      </div>
    </Panel>
  );
};

export default TaskItem;
