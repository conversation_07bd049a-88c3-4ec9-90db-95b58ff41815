// @ts-nocheck
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { ChevronsDown, ChevronsUp, Clock } from 'lucide-react';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { format } from 'date-fns';
import { createAdGroupSchema, getLocalTimeByTimezone, type AdGroupFormSchema } from './adGroupSchema';
import { FormMultiSelect } from '@/components/ui/FormMultiSelect';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/ToggleGroup';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Switch } from '@/components/ui/Switch';
import { FormTreeSelect } from '@/components/ui/FormTreeSelect';
import { RadioGroup, RadioGroupItem } from '@/components/ui/RadioGroup';
import { Checkbox } from '@/components/ui/Checkbox';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetFooter } from '@/components/ui/Sheet';
import { InteractionPanel } from '../fields/InteractionPanel';
import { AnswerIcon, TikTok } from '@/components/icon';
import { Label } from '@/components/ui/Label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { DatePicker } from '@/components/ui/DatePicker';
import DateRangePicker from '@/components/ui/DateRangePicker';
import tiktokService from '@/services/tiktokService';
import { useCurrentAdvertiser, useCurrentCampaign } from '@/store/ads/adStore';
import { ProMask } from '@/components/pro/pro-mask';
import { useCallback, useEffect, useState } from 'react';
import {
  type Action,
  type CreateRightDrawerProps,
  type TargetingType,
  type Topic,
  ageOptions,
  languageOptions,
  networktypes,
} from '@/types/enum';
import { AdGroupNameSection } from './AdGroupNameSection';
import {
  transformToTreeData,
  buildRegionTree,
  convertToLocalTime,
  convertToLocalTime1,
  validateScheduleTime,
  formatToUTCString,
} from '@/hooks/usedebounce';
import { buildActions, validateFormData, prepareCreateData, cleanupFormData } from '@/hooks/useGroupSheeForm';
const GroupSheet = ({ open, setOpen, DEFAULT_DAYPARTING, type, onConfirm, editingGroup }: CreateRightDrawerProps) => {
  const [isEditAdGroup, setIsEditAdGroup] = useState(false);
  const currentAdvertiser = useCurrentAdvertiser(); // 获取选择的广告主
  const advertiser = currentAdvertiser?.[0]; // 使用可选链
  const [selectedStore, setSelectedStore] = useState<any>(null);
  // const [isModalOpen, setIsModalOpen] = useState(false);
  const campaign = useCurrentCampaign(); // 获取选择的
  const [storeType, setStoreType] = useState('STORE');
  const [isDirectional, setIsDirectional] = useState(true);
  const [setup, setSetup] = useState(true);
  const [customize, setCustomize] = useState(false);
  const [customTagInput, setCustomTagInput] = useState('');
  // 默认值设置
  const defaultValues = {
    campaign_id: campaign?.map((item) => item.campaignId),
    operation_status: 'ENABLE',
    shopping_ads_type: 'VIDEO',
    budget_mode: 'BUDGET_MODE_DAY',
    schedule_type: 'SCHEDULE_FROM_NOW',
    dayparting: DEFAULT_DAYPARTING,
    bid_type: 'BID_TYPE_CUSTOM',
    gender: 'GENDER_UNLIMITED',
    age_groups: ageOptions.filter((opt) => opt.value !== 'ALL').map((opt) => opt.value),
    network_types: ['WIFI', '4G', '5G'],
    optimization_goal: 'VALUE',
    optimization_event: undefined,
    billing_event: 'OCPM',
  };
  const form = useForm<AdGroupFormSchema>({
    resolver: zodResolver(createAdGroupSchema(type)),
    defaultValues: type !== 'create' ? { ...editingGroup } : defaultValues,
  });
  const [budgetMode, setBudgetMode] = useState<
    'BUDGET_MODE_DAY' | 'BUDGET_MODE_TOTAL' | 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET'
  >('BUDGET_MODE_DAY');
  const [isVideoInteractionExpanded, setIsVideoInteractionExpanded] = useState(false);
  const [isCreatorInteractionExpanded, setIsCreatorInteractionExpanded] = useState(false);
  const [isTopicInteractionExpanded, setIsTopicInteractionExpanded] = useState(false);
  const [customAudienceList, setCustomAudienceList] = useState<any[]>([]); // 包含受众
  const [scheduleType, setScheduleType] = useState('SCHEDULE_FROM_NOW');
  const [operationStatus, setOperationStatus] = useState<'ENABLE' | 'DISABLE'>('ENABLE'); //广告组状态
  const [videoInteraction, setVideoInteraction] = useState<any[]>([]); //视频互动
  const [creatorInteraction, setCreatorInteraction] = useState<any[]>([]); //创作者互动
  const [purchaseIntention, setPurchaseIntention] = useState<any[]>([]); //购买意向
  const [actions, setActions] = useState<Action[]>([]); // 时间
  const [adsIdentity, setAdsIdentity] = useState<any[]>([]); // 广告身份
  const [storeList, setStoreList] = useState<any[]>([]); // 商店
  const [locationData, setLocationData] = useState<any[]>([]); // 地域
  const [targetingRegionCodes, setTargetingRegionCodes] = useState<string[]>([]); //商店可用于定向的地域代码。
  const [displayOptimizationGoal, setDisplayOptimizationGoal] = useState<string>('VALUE');
  const [utcStartTime, setUtcStartTime] = useState<string>();
  const [utcEndTime, setUtcEndTime] = useState<string>();
  // const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 选中的商品
  const [adgroupNameTags, setAdgroupNameTags] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const handleCancel = () => {
    // 关闭抽屉
    setOpen(false);
    // 重置表单
    form.reset({
      ...defaultValues,
    });
    // 重置所有状态
    setIsEditAdGroup(false);
    setOperationStatus('ENABLE');
    setDisplayOptimizationGoal('VALUE');
    setTemplateName('');
    setActions([]);
    setSelectedStore(null);
    setStoreType('STORE');
    setIsDirectional(true);
    setSetup(true);
    setCustomize(false);
    setCustomTagInput('');
    setAdgroupNameTags([]);
    setIsVideoInteractionExpanded(false);
    setIsCreatorInteractionExpanded(false);
    setIsTopicInteractionExpanded(false);
    setUtcStartTime(undefined);
    setUtcEndTime(undefined);
    setScheduleType('SCHEDULE_FROM_NOW');
    setBudgetMode('BUDGET_MODE_DAY');
  };
  const replacePlaceholders = (name: string, campaignId: string) => {
    // 只替换用户选择的标签
    if (!adgroupNameTags.length) {
      return name;
    }
    let replacedName = name;
    adgroupNameTags.forEach((tag) => {
      switch (tag) {
        case '系列名称':
          const selectedCampaign = campaign?.find((item) => item.campaignId === campaignId);
          replacedName = replacedName.replace('系列名称', selectedCampaign?.campaignName || '');
          break;
        case '商品数据源':
          const currentStoreName = 'STORE';
          replacedName = replacedName.replace('商品数据源', currentStoreName);
          break;
        case '优化目标':
          const optimizationMap: { [key: string]: string } = {
            VALUE: '价值(总收入)',
            CLICK: '点击数',
            SHOPPING: '付费数',
            INITIATE_ORDER: '开始结账数',
          };
          replacedName = replacedName.replace('优化目标', optimizationMap[displayOptimizationGoal] || '');
          break;
        case '是否出价':
          const currentBidType = form.getValues('bid_type');
          const bidValue = currentBidType === 'BID_TYPE_NO_BID' ? '不出价' : '出价';
          replacedName = replacedName.replace('是否出价', bidValue);
          break;
      }
    });

    return replacedName;
  };
  const handleConfirm = async (values: any) => {
    try {
      setIsSubmitting(true);
      values.actions = buildActions(values);
      if (type === 'create') {
        if (!validateFormData(values, form, displayOptimizationGoal)) {
          return;
        }
        // 准备创建数据
        const createData = prepareCreateData(
          // values,
          {
            ...values,
            schedule_start_time: formatToUTCString(values.schedule_start_time),
            schedule_end_time: formatToUTCString(values.schedule_end_time),
          },
          // selectedRowKeys,
          displayOptimizationGoal,
          adgroupNameTags,
          replacePlaceholders,
        );
        cleanupFormData(createData);
        const baseParams = {
          ...createData,
          advertiser_id: advertiser?.advertiserId,
          shopping_ads_retargeting_actions_days: 180,
        };

        if (values.is_template) {
          baseParams.is_template = true;
          baseParams.name = templateName;
        }

        const params = values.campaign_id.map((item: string) => ({
          ...baseParams,
          campaign_id: item,
        }));
        // 创建广告组;
        await tiktokService.createAdGroup(params);
        toast.success('创建成功');
      } else {
        const updateData: UpdateAdGroupRequest = {
          id: editingGroup.id,
          advertiser_id: editingGroup.jsonDate?.advertiserId,
          adgroup_id: editingGroup.groupId,
          adgroup_name: values.adgroup_name,
          location_ids: values.location_ids,
          languages: values.languages,
          gender: values.gender,
          age_groups: values.age_groups,
          network_types: values.network_types,
          audience_ids: values.audience_ids,
          purchase_intention_keyword_ids: values.purchase_intention_keyword_ids,
          interest_category_ids: values.interest_category_ids,
          actions: values.actions,
          // operation_status: values.operation_status,
          shopping_ads_retargeting_actions_days: 180,
          budget: values.budget,
          tags: adgroupNameTags,
        };
        // 更新广告组
        await tiktokService.updateAdGroup(updateData);
        toast.success('更新成功');
      }

      setTemplateName('');
      onConfirm();
      handleCancel();
    } catch (error) {
      console.error('Submit error:', error);
      return error;
    } finally {
      setIsSubmitting(false);
    }
  };
  const fetchTargetingData = useCallback(
    async (advertiser_id: string | undefined, { type, responseKey, setState, search_keywords }: TargetingType) => {
      if (!advertiser_id) return;
      try {
        const res = await tiktokService.getInterestList({
          advertiser_id,
          sub_targeting_types: type,
          search_keywords: search_keywords,
        });
        const treeData = transformToTreeData(res[responseKey]?.listResult || []);
        setState(treeData);
      } catch (error) {
        return;
      }
    },
    [],
  );
  const handleOptimizationGoalChange = (value: string) => {
    setDisplayOptimizationGoal(value);
    // 根据不同的优化目标设置相应的 billing_event
    switch (value) {
      case 'VALUE':
        form.setValue('optimization_goal', 'VALUE');
        form.setValue('optimization_event', undefined);
        form.setValue('billing_event', 'OCPM');
        form.setValue('bid_price', undefined);
        break;
      case 'CLICK':
        form.setValue('optimization_goal', 'CLICK');
        form.setValue('optimization_event', undefined);
        form.setValue('billing_event', 'CPC');
        form.setValue('conversion_bid_price', undefined);
        break;
      case 'SHOPPING':
        form.setValue('optimization_goal', 'CONVERT');
        form.setValue('optimization_event', 'SHOPPING');
        form.setValue('billing_event', 'OCPM');
        form.setValue('bid_price', undefined);
        break;
      case 'INITIATE_ORDER':
        form.setValue('optimization_goal', 'CONVERT');
        form.setValue('optimization_event', 'INITIATE_ORDER');
        form.setValue('billing_event', 'OCPM');
        form.setValue('bid_price', undefined);
        break;
    }
  };
  useEffect(() => {
    if (open && advertiser?.advertiserId) {
      const getProjectList = async () => {
        try {
          const res = await tiktokService.getProjectList({
            advertiser_id: advertiser?.advertiserId,
          });
          setStoreList(res.data.stores || []);
          const res1 = await tiktokService.getTargetingRegionLists({
            advertiser_id: advertiser?.advertiserId,
            placements: ['PLACEMENT_TIKTOK'],
            objective_type: 'PRODUCT_SALES',
            level_range: 'TO_PROVINCE',
            language: 'zh',
            shopping_ads_type: 'VIDEO',
            promotion_type: 'VIDEO_SHOPPING',
          });
          const regionTree = buildRegionTree(res1.regionInfo || []);
          setLocationData(regionTree);
        } catch (error) {
          return;
        }
      };
      getProjectList();
    }
  }, [open, advertiser?.advertiserId]);

  useEffect(() => {
    if (open && advertiser?.advertiserId) {
      const getCustomList = async () => {
        try {
          const res = await tiktokService.getCustomAudience({
            advertiser_id: advertiser?.advertiserId,
            page: 1,
            page_size: 100,
          });
          setCustomAudienceList(res.data.list || []);
        } catch (error) {
          return;
        }
      };
      getCustomList();
    }
  }, [open, advertiser?.advertiserId]);
  useEffect(() => {
    if (open && advertiser?.advertiserId) {
      const targetingTypes: TargetingType[] = [
        {
          type: 'VIDEO_INTERACTION',
          responseKey: 'videoInteraction',
          errorMessage: '视频互动受众类别',
          setState: setVideoInteraction,
        },
        {
          type: 'CREATOR_INTERACTION',
          responseKey: 'creatorInteraction',
          errorMessage: '创作者互动受众类别',
          setState: setCreatorInteraction,
        },
        {
          type: 'PURCHASE_INTENTION',
          responseKey: 'purchaseIntention',
          errorMessage: '购买意向受众类别',
          setState: setPurchaseIntention,
        },
      ];
      Promise.all(targetingTypes.map((config) => fetchTargetingData(advertiser?.advertiserId, config)));
      if (editingGroup && type !== 'create') {
        setIsEditAdGroup(true);
        if (editingGroup.jsonDate?.optimizationGoal === 'CONVERT') {
          setDisplayOptimizationGoal(editingGroup.jsonDate?.optimizationEvent);
        } else {
          setDisplayOptimizationGoal(editingGroup.jsonDate?.optimizationGoal);
        }
        // 处理互动行为数据
        const hashtagActions = editingGroup.jsonDate?.actions?.find((a: any) => a.actionScene === 'HASHTAG_RELATED');
        const utcStartTime = convertToLocalTime1(
          editingGroup.jsonDate?.scheduleStartTime,
          advertiser?.jsonDate?.displayTimezone,
        );
        const utcEndTime = convertToLocalTime1(
          editingGroup.jsonDate?.scheduleEndTime,
          advertiser?.jsonDate?.displayTimezone,
        );
        setUtcStartTime(utcStartTime);
        setUtcEndTime(utcEndTime);
        // 设置表单初始值
        form.reset({
          adgroup_name: editingGroup.jsonDate?.adgroupName,
          operation_status: editingGroup.jsonDate?.operationStatus,
          store_id: editingGroup.jsonDate?.storeId,
          shopping_ads_type: editingGroup.jsonDate?.shoppingAdsType || 'VIDEO',
          placements: editingGroup.jsonDate?.placements,
          comment_disabled: editingGroup.jsonDate?.commentDisabled,
          video_download_disabled: editingGroup.jsonDate?.videoDownloadDisabled,
          budget_mode: editingGroup.jsonDate?.budgetMode,
          budget: editingGroup.jsonDate?.budget,
          schedule_type: editingGroup.jsonDate?.scheduleType,
          dayparting: editingGroup.jsonDate?.dayparting,
          optimization_goal: editingGroup.jsonDate?.optimizationGoal,
          bid_type: editingGroup.jsonDate?.bidType,
          bid_price: editingGroup.jsonDate?.bidPrice,
          conversion_bid_price: editingGroup.jsonDate?.conversionBidPrice,
          billing_event: editingGroup.jsonDate?.billingEvent,
          pacing: editingGroup.jsonDate?.pacing,
          location_ids: editingGroup.jsonDate?.locationIds,
          languages: editingGroup.jsonDate?.languages,
          gender: editingGroup.jsonDate?.gender,
          age_groups: editingGroup.jsonDate?.ageGroups,
          audience_ids: editingGroup.jsonDate?.audienceIds,
          interest_category_ids: editingGroup.jsonDate?.interestCategoryIds,
          network_types: editingGroup.jsonDate?.networkTypes,

          // action_category_ids3: hashtagActions?.actionCategoryIds,
        });
        form.setValue(
          'action_category_ids1',
          editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'VIDEO_RELATED')?.actionCategoryIds || [],
        );
        form.setValue(
          'action_category_ids2',
          editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'CREATOR_RELATED')?.actionCategoryIds ||
            [],
        );
        setAdgroupNameTags(editingGroup.tagNames || []);
        setScheduleType(editingGroup.jsonDate?.scheduleType);
        setOperationStatus(editingGroup.jsonDate?.operationStatus);
        setDisplayOptimizationGoal(editingGroup.jsonDate?.optimizationGoal);
        setBudgetMode(editingGroup.jsonDate?.budgetMode);
        setActions(editingGroup.jsonDate?.actions || []);
      } else {
        // 创建模式：重置表单和状态
        form.reset();
        setIsEditAdGroup(false);
        setOperationStatus('ENABLE');
        setDisplayOptimizationGoal('VALUE');
        form.setValue('optimization_goal', 'VALUE');
        form.setValue('billing_event', 'OCPM');
        form.setValue('bid_price', 0);
        form.setValue('video_user_actions', []);
        form.setValue(
          'campaign_id',
          campaign?.map((item) => item.campaignId),
        );
        setActions([]);
      }
    }
  }, [editingGroup, type, open, form, currentAdvertiser, fetchTargetingData, campaign?.length]);
  // 添加验证函数
  const validateForm = async (isTemplate = false) => {
    try {
      await form.trigger();
      const values = form.getValues();
      const {
        video_user_actions,
        action_category_ids1,
        A_few_days_of_behavior,
        creator_user_actions,
        action_category_ids2,
        hashtag_user_actions,
        action_category_ids3,
      } = values;

      // 检查是否有任何一组行为参数被填写
      const hasVideoActions = video_user_actions?.length > 0;
      const hasVideoCategories = action_category_ids1?.length > 0;
      const hasVideoDays = !!A_few_days_of_behavior;
      const hasCreatorActions = creator_user_actions?.length > 0;
      const hasCreatorCategories = action_category_ids2?.length > 0;
      const hasHashtagActions = hashtag_user_actions?.length > 0;
      const hasHashtagCategories = action_category_ids3?.length > 0;

      // 检查各组行为参数的完整性
      if (hasVideoActions || hasVideoCategories || hasVideoDays) {
        if (!hasVideoActions || !hasVideoCategories || !hasVideoDays) {
          toast.error('请完整填写视频互动行为的所有选项');
          return false;
        }
      }

      if (hasCreatorActions || hasCreatorCategories) {
        if (!hasCreatorActions || !hasCreatorCategories) {
          toast.error('请完整填写创作者互动行为的所有选项');
          return false;
        }
      }

      if (hasHashtagActions || hasHashtagCategories) {
        if (!hasHashtagActions || !hasHashtagCategories) {
          toast.error('请完整填写话题互动行为的所有选项');
          return false;
        }
      }
      // 模板名称验证
      if (isTemplate && !templateName.trim()) {
        toast.error('请输入模板名称');
        return false;
      }
      const errors = form.formState.errors;
      if (Object.keys(errors).length > 0) {
        // 收集所有错误信息
        const errorMessages = [];
        const errorFields = {
          campaign_id: '广告系列',
          adgroup_name: '广告组名称',
          store_id: '商店',
          budget: '预算',
          schedule_start_time: '开始时间',
          location_ids: '地域',
          // interest_category_ids: '兴趣',
          bid_price: '出价金额',
          conversion_bid_price: '转化出价',
        };
        Object.entries(errorFields).forEach(([key, value]) => {
          if (errors[key]) errorMessages.push(value);
        });
        if (errorMessages.length > 0) {
          toast.error(`请检查以下必填项: ${errorMessages.join('、')}`, {
            duration: 3000,
            position: 'top-center',
          });
        }
        return false;
      }
      // 预算验证
      const budget = form.getValues('budget');
      const budgetMode = form.getValues('budget_mode');
      if (budget < 10.01) {
        toast.error('预算金额必须大于等于10');
        return false;
      }
      if (budgetMode === 'BUDGET_MODE_TOTAL' && budget < 150) {
        toast.error('总预算必须大于等于150');
        return false;
      }

      return true;
    } catch (error) {
      console.error('表单验证错误:', error);
      toast.error('表单验证失败，请检查必填项');
      return false;
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleCancel}>
      <SheetContent className="flex min-w-[800px] flex-col bg-[#151C29]">
        <ProMask loading={isSubmitting} />
        <SheetHeader>
          <SheetTitle>
            {type === 'create'
              ? '新建广告组'
              : type === 'template'
                ? '广告组模板'
                : type === 'edit'
                  ? '编辑广告组'
                  : ''}
          </SheetTitle>
        </SheetHeader>
        <div className="m-0 flex-1 overflow-y-auto pl-4 pr-[112px]">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleConfirm)}>
              {type === 'create' && (
                <div>
                  <div className="flex items-center gap-3">
                    <div className="h-4 w-1 bg-[#00E1FF]"></div>
                    <div className="text-base font-semibold">已选系列</div>
                  </div>
                  <div className="pl-10">
                    <FormMultiSelect
                      name="campaign_id"
                      control={form.control}
                      label="广告系列"
                      options={
                        campaign?.map((item) => ({
                          label: item.campaignName,
                          value: item.campaignId,
                        })) ?? []
                      }
                      defaultValue={campaign?.map((item) => item.campaignId)}
                      placeholder="选择广告系列"
                      placeholderTitle="广告系列"
                      rules={{ required: '请选择广告系列' }}
                      required={true}
                      onValueChange={(value) => {
                        if (!value || value.length === 0) {
                          toast.error('请选择至少一个广告系列');
                          return;
                        } else {
                          form.clearErrors('campaign_id');
                        }
                      }}
                    />
                  </div>
                </div>
              )}
              <AdGroupNameSection
                form={form}
                isEditAdGroup={isEditAdGroup}
                type={type}
                customize={customize}
                customTagInput={customTagInput}
                setCustomize={setCustomize}
                setCustomTagInput={setCustomTagInput}
                adgroupNameTags={adgroupNameTags}
                setAdgroupNameTags={setAdgroupNameTags}
                storeType={storeType}
                setStoreType={setStoreType}
                storeList={storeList}
                locationData={locationData}
                advertiser={advertiser}
                templateName={templateName}
                setTemplateName={setTemplateName}
              />
              <div className="mt-10 flex items-center gap-3">
                <div className="h-4 w-1 bg-[#00E1FF]"></div>
                <div className="text-base font-semibold">预算&优化</div>
              </div>
              <div className="pl-10">
                <FormField
                  control={form.control}
                  name="budget_mode"
                  rules={{ required: '请选择预算类型' }}
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">
                        广告组预算 <span className="ml-2 text-red-500">*</span>
                      </FormLabel>
                      <FormControl className="w-4/5">
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value);
                            setBudgetMode(
                              value as 'BUDGET_MODE_DAY' | 'BUDGET_MODE_TOTAL' | 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET',
                            );
                          }}
                          value={field.value}
                          className="flex space-x-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="BUDGET_MODE_DAY" id="BUDGET_MODE_DAY" disabled={isEditAdGroup} />
                            <Label htmlFor="BUDGET_MODE_DAY" className="text-sm">
                              日预算
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem
                              value="BUDGET_MODE_DYNAMIC_DAILY_BUDGET"
                              id="BUDGET_MODE_DYNAMIC_DAILY_BUDGET"
                              disabled={isEditAdGroup}
                            />
                            <Label htmlFor="BUDGET_MODE_DYNAMIC_DAILY_BUDGET" className="text-sm">
                              动态日预算
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="BUDGET_MODE_TOTAL" id="BUDGET_MODE_TOTAL" disabled={isEditAdGroup} />
                            <Label htmlFor="BUDGET_MODE_TOTAL" className="text-sm">
                              总预算
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="budget"
                  rules={{
                    required: '请输入预算',
                    validate: (value) => {
                      if (value < 10.01) {
                        return '请输入大于等于10的预算';
                      }
                      if (budgetMode === 'BUDGET_MODE_TOTAL' && value < 150) {
                        return '请输入大于等于150的预算';
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => {
                    const [isHovered, setIsHovered] = useState(false);

                    return (
                      <FormItem className="mt-4 flex items-center">
                        <FormLabel className="w-1/5 text-sm text-white"></FormLabel>
                        <FormControl className="w-4/5">
                          <div className="relative w-[360px]">
                            <Input
                              className="h-10 border border-[#363D54] bg-transparent text-sm placeholder:text-[#9FA4B2] focus-visible:ring-0 focus-visible:ring-offset-0"
                              type="number"
                              min={10.0}
                              step={0.01}
                              {...field}
                              placeholder={budgetMode === 'BUDGET_MODE_TOTAL' ? '150.00以上' : '10.00以上'}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                              disabled={type === 'template'}
                              onMouseEnter={() => setIsHovered(true)}
                              onMouseLeave={() => {
                                if (!document.activeElement.matches('input[type="number"]')) {
                                  setIsHovered(false);
                                }
                              }}
                              onFocus={() => setIsHovered(true)}
                              onBlur={() => setIsHovered(false)}
                            />
                            {/* <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"> */}
                            <div
                              className={`pointer-events-none absolute inset-y-0 ${isHovered ? 'right-5' : 'right-0'} flex items-center pr-3`}
                            >
                              <span className="text-[#9FA4B2]">USD</span>
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="optimization_goal"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="flex w-1/5 items-center gap-2 text-sm text-white">
                        <div>优化目标</div>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                              }}
                            >
                              <AnswerIcon />
                            </TooltipTrigger>
                            <TooltipContent>具体可选的优化目标以各个账号已解锁的优化目标为准</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl className="w-4/5">
                        {isEditAdGroup || type === 'template' ? (
                          <div className="text-sm text-white">
                            {displayOptimizationGoal === 'VALUE' && '价值(总收入)'}
                            {displayOptimizationGoal === 'CLICK' && '点击数'}
                            {displayOptimizationGoal === 'CONVERT' &&
                              editingGroup?.jsonDate?.optimizationEvent === 'SHOPPING' &&
                              '付费数'}
                            {displayOptimizationGoal === 'CONVERT' &&
                              editingGroup?.jsonDate?.optimizationEvent === 'INITIATE_ORDER' &&
                              '开始结账数'}
                          </div>
                        ) : (
                          <div>
                            <Select onValueChange={handleOptimizationGoalChange} value={displayOptimizationGoal}>
                              <SelectTrigger className="w-[360px] rounded border-[#363D54] bg-transparent text-sm">
                                <SelectValue placeholder="请选择优化目标" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="VALUE" className="text-sm">
                                  价值(总收入)
                                </SelectItem>
                                <SelectItem value="CLICK" className="text-sm">
                                  点击数
                                </SelectItem>
                                <SelectItem value="SHOPPING" className="text-sm">
                                  付费数
                                </SelectItem>
                                <SelectItem value="INITIATE_ORDER" className="text-sm">
                                  开始结账数
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {displayOptimizationGoal !== 'VALUE' && (
                  <FormField
                    control={form.control}
                    name={displayOptimizationGoal === 'CLICK' ? 'bid_price' : 'conversion_bid_price'}
                    rules={{
                      validate: (value) => {
                        if (!value || value <= 0) {
                          return displayOptimizationGoal === 'CLICK' ? '请输入有效的出价金额' : '请输入有效的转化出价';
                        }
                        const budget = form.getValues('budget');
                        if (value >= budget) {
                          return '出价不能大于等于预算金额';
                        }
                        return true;
                      },
                    }}
                    render={({ field }) => (
                      <FormItem className="mt-6 flex items-center">
                        <FormLabel className="w-1/5 text-sm text-white">
                          {displayOptimizationGoal === 'CLICK' ? (
                            <div className="flex items-center gap-2">
                              <div>USD/点击</div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                    }}
                                  >
                                    <AnswerIcon />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    目标广告支出回报率 (ROAS)
                                    为选填项。要采用"最高总收入"出价策略，请将此项留空；要控制你的 ROAS，请填写此项。
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <div>
                                {displayOptimizationGoal === 'SHOPPING' || displayOptimizationGoal === 'INITIATE_ORDER'
                                  ? 'USD/转化'
                                  : '目标ROAS'}
                              </div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                    }}
                                  >
                                    <AnswerIcon />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    目标广告支出回报率 (ROAS)
                                    为选填项。要采用"最高总收入"出价策略，请将此项留空；要控制你的 ROAS，请填写此项。
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          )}
                        </FormLabel>
                        <FormControl className="w-4/5">
                          <Input
                            className="h-10 w-[360px] border border-[#363D54] bg-transparent text-sm placeholder:text-[#9FA4B2] focus-visible:ring-0 focus-visible:ring-offset-0"
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            disabled={isEditAdGroup}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                            placeholder={
                              displayOptimizationGoal === 'CLICK' ? '请输入每次点击的出价' : '请输入目标ROAS值'
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                <div className="mt-3 flex">
                  <div className="w-1/5"></div>
                  <p className="w-[360px] text-xs text-[#9FA4B2]">
                    {displayOptimizationGoal === 'CLICK'
                      ? `采用"量大投放量"出价策略时，我们将尽量消耗你的全部预算，以取得量多成效。如果你希望广告的平均成效成本保持在某个特定值，请输入平均成效出价`
                      : displayOptimizationGoal === 'SHOPPING' || displayOptimizationGoal === 'INITIATE_ORDER'
                        ? `采用"最大投放量"出价策略时，我们将尽量消耗你的全部预算，以取得最多成效。如果你希望广告的平均成效成本保持在某个特定值，请输入平均或效出价`
                        : `采用"最高价值"出价策略时，我们将尽量消耗你的全部预算，以获得最多的总收入。如果你希望广告的平均 ROAS 保持在某个特定值，请输入你的目标 ROAS 值`}
                  </p>
                </div>
              </div>
              <div className="mt-10 flex items-center gap-3" onClick={() => setIsDirectional(!isDirectional)}>
                <div className="h-4 w-1 bg-[#00E1FF]"></div>
                <div className="text-base font-semibold">定向</div>
                <div className="">{isDirectional ? <ChevronsUp /> : <ChevronsDown />}</div>
              </div>
              <div
                className="pl-10"
                style={{
                  display: isDirectional ? 'block' : 'none',
                }}
              >
                <FormMultiSelect
                  name="location_ids"
                  control={form.control}
                  rules={{ required: '请选择地域' }}
                  label="地域"
                  options={
                    locationData?.map((item) => ({
                      label: item.name,
                      value: item.locationId,
                    })) ?? []
                  }
                  defaultValue={editingGroup?.jsonDate?.locationIds}
                  placeholder="选择地域"
                  placeholderTitle="地域"
                  disabled={type === 'template'}
                  required={true}
                />
                {/* 语言选择 */}
                <FormMultiSelect
                  name="languages"
                  control={form.control}
                  label="语言"
                  options={
                    languageOptions?.map((item) => ({
                      label: item.label,
                      value: item.value,
                    })) ?? []
                  }
                  defaultValue={editingGroup?.jsonDate?.languages}
                  placeholder="选择语言"
                  placeholderTitle="语言"
                  disabled={type === 'template'}
                  onValueChange={(value) => {
                    if (!value || value.length === 0) {
                      toast.error('请选择至少一个语言');
                      return;
                    } else {
                      form.clearErrors('languages');
                    }
                  }}
                />
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">性别</FormLabel>
                      <FormControl className="w-4/5">
                        <ToggleGroup
                          type="single"
                          value={field.value}
                          className="gap-0"
                          onValueChange={(values) => {
                            if (values.length === 0) {
                              form.setValue('gender', 'GENDER_UNLIMITED');
                            } else {
                              form.setValue('gender', values);
                            }
                          }}
                        >
                          <ToggleGroupItem
                            className="h-8 rounded-none rounded-l border border-[#363D54] bg-transparent text-sm data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#00E1ff1A]"
                            value="GENDER_UNLIMITED"
                            disabled={type === 'template'}
                          >
                            不限
                          </ToggleGroupItem>
                          <ToggleGroupItem
                            className="h-8 rounded-none border border-[#363D54] bg-transparent text-sm data-[state=on]:border data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#00E1ff1A]"
                            value="GENDER_MALE"
                            disabled={type === 'template'}
                          >
                            男
                          </ToggleGroupItem>
                          <ToggleGroupItem
                            className="h-8 rounded-none rounded-r border border-[#363D54] bg-transparent text-sm data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#00E1ff1A]"
                            value="GENDER_FEMALE"
                            disabled={type === 'template'}
                          >
                            女
                          </ToggleGroupItem>
                        </ToggleGroup>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="age_groups"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">年龄</FormLabel>
                      <FormControl className="w-4/5">
                        <ToggleGroup
                          type="multiple" // 多选
                          value={field.value}
                          className="gap-0"
                          onValueChange={(values) => {
                            if (values.length === 0) {
                              form.setValue('age_groups', ['AGE_18_24']);
                            } else {
                              form.setValue('age_groups', values);
                            }
                          }}
                        >
                          {ageOptions.map((option, index) => (
                            <ToggleGroupItem
                              className={`h-8 border border-[#363D54] bg-transparent text-sm data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#00E1ff1A] ${
                                index === 0
                                  ? 'rounded-none rounded-l'
                                  : index === ageOptions.length - 1
                                    ? 'rounded-none rounded-r'
                                    : 'rounded-none'
                              }`}
                              key={option.value}
                              value={option.value}
                              aria-label={option.label}
                              disabled={type === 'template'}
                            >
                              {option.label}
                            </ToggleGroupItem>
                          ))}
                        </ToggleGroup>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormMultiSelect
                  name="audience_ids"
                  control={form.control}
                  label="包含受众"
                  options={customAudienceList.map((item) => ({
                    label: item.audienceType,
                    value: item.audienceId,
                  }))}
                  defaultValue={editingGroup?.jsonDate?.audienceIds}
                  placeholder="请选择受众"
                  placeholderTitle="受众"
                  disabled={type === 'template'}
                />
                <FormTreeSelect
                  name="interest_category_ids"
                  control={form.control}
                  label="添加兴趣"
                  options={purchaseIntention.map((item) => ({
                    label: item.label,
                    value: item.key,
                    children: item.children?.map((child) => ({
                      label: child.label,
                      value: child.key,
                    })),
                  }))}
                  test={true}
                  // rules={{ required: '请选择兴趣' }}
                  defaultValue={editingGroup?.jsonDate?.interestCategoryIds}
                  placeholder="请选择兴趣"
                  disabled={type === 'template'}
                  layout="horizontal"
                />
                {/* TODO */}
                <div className="flex">
                  <div className="mt-6 w-1/5 text-sm text-white">添加行为</div>
                  <div className="mt-2 w-4/5">
                    <InteractionPanel
                      type={type}
                      title="视频互动"
                      description="根据用户在Tiktok上与视频互动行为进行定向"
                      isExpanded={isVideoInteractionExpanded}
                      onToggle={() => setIsVideoInteractionExpanded(!isVideoInteractionExpanded)}
                      panelType="video"
                      control={form.control}
                      videoInteraction={videoInteraction}
                      editingGroup={editingGroup}
                    />

                    <InteractionPanel
                      type={type}
                      title="创作者互动"
                      description="根据用户在TikTok上与创作者的互动行为进行定向"
                      isExpanded={isCreatorInteractionExpanded}
                      onToggle={() => setIsCreatorInteractionExpanded(!isCreatorInteractionExpanded)}
                      panelType="creator"
                      control={form.control}
                      creatorInteraction={creatorInteraction}
                      editingGroup={editingGroup}
                    />

                    <InteractionPanel
                      type={type}
                      title="话题互动"
                      description="根据用户与TikTok上的话题的互动情况定向用户"
                      isExpanded={isTopicInteractionExpanded}
                      onToggle={() => setIsTopicInteractionExpanded(!isTopicInteractionExpanded)}
                      panelType="topic"
                      control={form.control}
                      advertiser={advertiser}
                      editingGroup={editingGroup}
                    />
                  </div>
                </div>
                <FormField
                  control={form.control}
                  name="network_types"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">网络情况</FormLabel>
                      <FormControl className="w-1/5">
                        <ToggleGroup
                          type="multiple" // 多选
                          value={field.value}
                          className="gap-0"
                          onValueChange={(values) => {
                            if (values.length === 0) {
                              form.setValue('network_types', ['WIFI']);
                            } else {
                              form.setValue('network_types', values);
                            }
                          }}
                          disabled={type === 'template'}
                        >
                          {networktypes.map((option, index) => (
                            <ToggleGroupItem
                              className={`h-8 border border-[#363D54] bg-transparent data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#00E1ff1A] ${
                                index === 0
                                  ? 'rounded-none rounded-l'
                                  : index === networktypes.length - 1
                                    ? 'rounded-none rounded-r'
                                    : 'rounded-none'
                              }`}
                              key={option.value}
                              value={option.value}
                              aria-label={option.label}
                              disabled={type === 'template'}
                            >
                              {option.label}
                            </ToggleGroupItem>
                          ))}
                        </ToggleGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="mt-10 flex items-center gap-3" onClick={() => setSetup(!setup)}>
                <div className="h-4 w-1 bg-[#00E1FF]"></div>
                <div className="text-base font-semibold">高级设置</div>
                <div>{setup ? <ChevronsUp /> : <ChevronsDown />}</div>
              </div>
              <div
                className="pl-10"
                style={{
                  display: setup ? 'block' : 'none',
                }}
              >
                <FormField
                  control={form.control}
                  name="shopping_ads_type"
                  defaultValue="VIDEO"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">购物广告类型</FormLabel>
                      <FormControl className="w-4/5">
                        <RadioGroup onValueChange={field.onChange} defaultValue={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="VIDEO" id="video" disabled={isEditAdGroup} />
                            <Label htmlFor="video">视频购物广告</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="placements"
                  defaultValue={['PLACEMENT_TIKTOK']}
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">版位</FormLabel>
                      <FormControl className="w-4/5">
                        {/* <RadioGroup onValueChange={field.onChange} defaultValue={field.value}> */}
                        <div className="flex items-center space-x-2">
                          {/* <RadioGroupItem value="PLACEMENT_TIKTOK" id="placement_tiktok" disabled={isEditAdGroup} /> */}
                          <TikTok />
                          <Label htmlFor="placement_tiktok" className="text-sm font-normal">
                            TikTok
                          </Label>
                        </div>
                        {/* </RadioGroup> */}
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="mt-6 flex">
                  <div className="w-1/5 text-sm text-white">高级设置</div>
                  <div className="flex w-4/5 items-center gap-10">
                    <FormField
                      control={form.control}
                      name="comment_disabled"
                      defaultValue={false}
                      render={({ field }) => (
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-white"> 用户评论</span>
                          <Switch
                            className="h-[16px] w-[28px]"
                            thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
                            checked={!field.value} // 保持取反显示
                            onCheckedChange={(checked) => {
                              field.onChange(!checked);
                            }}
                            disabled={isEditAdGroup}
                          />
                        </div>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="video_download_disabled"
                      defaultValue={false}
                      render={({ field }) => (
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-white">视频下载</span>
                          <Switch
                            className="h-[16px] w-[28px]"
                            thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
                            checked={!field.value} // 保持取反显示
                            onCheckedChange={(checked) => {
                              field.onChange(!checked);
                            }}
                            disabled={isEditAdGroup}
                          />
                        </div>
                      )}
                    />
                  </div>
                </div>
                <FormField
                  control={form.control}
                  name="schedule_type"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">
                        排期
                        <span className="ml-2 text-red-500">*</span>
                      </FormLabel>
                      <FormControl className="w-4/5">
                        {isEditAdGroup ? (
                          <div className="text-sm text-white">
                            {editingGroup?.jsonDate?.scheduleType === 'SCHEDULE_FROM_NOW'
                              ? '从排定的开始时间起持续投放'
                              : '在某个时间范围内投放'}
                          </div>
                        ) : (
                          <RadioGroup
                            defaultValue={scheduleType}
                            onValueChange={(value: string) => {
                              field.onChange(value);
                              setScheduleType(value);
                            }}
                            disabled={type === 'template'}
                          >
                            <div className="flex items-center justify-between space-x-1">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem
                                  value="SCHEDULE_FROM_NOW"
                                  id="SCHEDULE_FROM_NOW"
                                  disabled={budgetMode === 'BUDGET_MODE_TOTAL'}
                                />
                                <Label htmlFor="SCHEDULE_FROM_NOW" className="text-sm">
                                  从排定的开始时间起持续投放广告组
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="SCHEDULE_START_END" id="SCHEDULE_START_END" />
                                <Label htmlFor="SCHEDULE_START_END" className="text-sm">
                                  在某个时间范围内投放广告组
                                </Label>
                              </div>
                            </div>
                          </RadioGroup>
                        )}
                      </FormControl>
                    </FormItem>
                  )}
                />
                {scheduleType === 'SCHEDULE_FROM_NOW' ? (
                  <FormField
                    control={form.control}
                    name="schedule_start_time"
                    rules={{
                      required: type === 'create' ? '请选择开始时间' : false,
                      validate: (value) => {
                        return validateScheduleTime(value, form.getValues('schedule_end_time'));
                      },
                    }}
                    render={({ field }) => {
                      const formattedUtcStartTime = utcStartTime
                        ? format(new Date(utcStartTime), 'yyyy/MM/dd HH:mm:ss')
                        : undefined;
                      return (
                        <FormItem className="mt-4 flex items-center">
                          <FormLabel className="w-1/5 text-sm text-white"></FormLabel>
                          <FormControl className="w-4/5">
                            <div className="relative">
                              <DatePicker
                                defaultValue={formattedUtcStartTime}
                                disabled={isEditAdGroup}
                                onValueChange={(value) => {
                                  form.setValue('schedule_start_time', value);
                                  const tenYearsLater = new Date(value);
                                  tenYearsLater.setFullYear(tenYearsLater.getFullYear() + 10);
                                  form.setValue('schedule_end_time', tenYearsLater.toISOString());
                                }}
                                test={false}
                              />
                              <div className="absolute right-3 top-3">
                                <Clock className="h-4 w-4 text-[#9FA4B2]" />
                              </div>
                              <div className="mt-4 flex items-center">
                                <div className="text-sm text-[#9FA4B2]">
                                  时区 {advertiser?.jsonDate?.displayTimezone}
                                </div>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                      }}
                                    >
                                      <span className="ml-4 text-sm text-[#9FA4B2]">此刻</span>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      {`账户所在的时区${advertiser?.jsonDate?.displayTimezone}的当前时刻:${getLocalTimeByTimezone(
                                        advertiser?.jsonDate?.displayTimezone,
                                      )}`}
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                ) : (
                  <FormField
                    control={form.control}
                    name="schedule_start_time"
                    rules={{
                      required: type === 'create' ? '请选择时间范围' : false,
                      validate: (value) => {
                        const endTime = form.getValues('schedule_end_time');
                        return validateScheduleTime(value, endTime);
                      },
                    }}
                    render={({ field }) => {
                      const formattedUtcStartTime = utcStartTime
                        ? format(new Date(utcStartTime), 'yyyy/MM/dd HH:mm:ss')
                        : undefined;
                      const formattedUtcEndTime = utcEndTime
                        ? format(new Date(utcEndTime), 'yyyy/MM/dd HH:mm:ss')
                        : undefined;
                      return (
                        <FormItem className="mt-4 flex items-center">
                          <FormLabel className="w-1/5 text-sm text-white"></FormLabel>
                          <FormControl className="w-4/5">
                            <div className="relative">
                              <DateRangePicker
                                defaultValue={
                                  formattedUtcStartTime && formattedUtcEndTime
                                    ? {
                                        startTime: formattedUtcStartTime,
                                        endTime: formattedUtcEndTime,
                                      }
                                    : undefined
                                }
                                onChange={(value) => {
                                  form.setValue('schedule_start_time', value?.startTime);
                                  form.setValue('schedule_end_time', value?.endTime);
                                }}
                                disabled={isEditAdGroup}
                                test={false}
                              />
                              <div className="absolute right-3 top-3">
                                <Clock className="h-4 w-4 text-[#9FA4B2]" />
                              </div>
                              <div className="mt-4 flex items-center">
                                <div className="text-sm text-[#9FA4B2]">
                                  时区 {advertiser?.jsonDate?.displayTimezone}
                                </div>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                      }}
                                    >
                                      <span className="ml-4 text-sm text-[#9FA4B2]">此刻</span>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      {`账户所在的时区${advertiser?.jsonDate?.displayTimezone}的当前时刻:${getLocalTimeByTimezone(
                                        advertiser?.jsonDate?.displayTimezone,
                                      )}`}
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                )}
                <FormField
                  control={form.control}
                  name="dayparting"
                  defaultValue={DEFAULT_DAYPARTING}
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">分时段</FormLabel>
                      <FormControl className="w-4/5">
                        <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-10">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={DEFAULT_DAYPARTING}
                              id={DEFAULT_DAYPARTING}
                              disabled={isEditAdGroup}
                            />
                            <Label htmlFor={DEFAULT_DAYPARTING} className="text-sm">
                              全天
                            </Label>
                          </div>
                          <div className="tetx-[#9FA4B2] flex items-center space-x-2">
                            <RadioGroupItem disabled value="2" id="2" />
                            <Label htmlFor="2" className="text-sm">
                              特定时间段
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="billing_event"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">计费点</FormLabel>
                      <FormControl className="w-4/5">
                        {isEditAdGroup ? (
                          <div className="text-sm text-white">
                            {editingGroup?.jsonDate?.billingEvent === 'OCPM' && '展现(oCPM)'}
                            {editingGroup?.jsonDate?.billingEvent === 'CPC' && '点击(CPC)'}
                          </div>
                        ) : (
                          <div className="text-sm text-white">
                            {displayOptimizationGoal === 'CLICK' ? '点击(CPC)' : '展现(oCPM)'}
                          </div>
                        )}
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="pacing"
                  defaultValue="PACING_MODE_SMOOTH"
                  render={({ field }) => (
                    <FormItem className="mt-6 flex items-center">
                      <FormLabel className="w-1/5 text-sm text-white">投放速度</FormLabel>
                      <FormControl className="w-4/5">
                        <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-10">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem
                              value="PACING_MODE_SMOOTH"
                              id="PACING_MODE_SMOOTH"
                              disabled={isEditAdGroup}
                            />
                            <Label htmlFor="PACING_MODE_SMOOTH" className="flex items-center gap-1 text-sm">
                              <div>标准</div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                    }}
                                  >
                                    <AnswerIcon />
                                  </TooltipTrigger>
                                  <TooltipContent>在预定的时间内平均分配预算</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="PACING_MODE_FAST" id="PACING_MODE_FAST" disabled={isEditAdGroup} />
                            <Label htmlFor="PACING_MODE_FAST" className="flex items-center gap-1 text-sm">
                              <div>加速</div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                    }}
                                  >
                                    <AnswerIcon />
                                  </TooltipTrigger>
                                  <TooltipContent>尽快消耗预算并产出结果</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
        <SheetFooter>
          {type === 'template' ? (
            <Button variant="outline" className="bg-[#CCDDFF1A]" onClick={handleCancel}>
              关闭
            </Button>
          ) : (
            <>
              <Button variant="outline" className="h-8 bg-[#CCDDFF1A]" onClick={handleCancel}>
                取消
              </Button>
              {!isEditAdGroup && (
                <Button
                  variant="outline"
                  className="h-8"
                  onClick={async () => {
                    if (await validateForm(true)) {
                      form.handleSubmit((values) => handleConfirm({ ...values, is_template: true }))();
                    }
                  }}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '提交中...' : '保存模板并发布'}
                </Button>
              )}
              <Button
                className="h-8 text-black"
                onClick={async () => {
                  if (await validateForm(false)) {
                    form.handleSubmit((values) => handleConfirm({ ...values, is_template: false }))();
                  }
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? '提交中...' : '发布'}
              </Button>
            </>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
export default GroupSheet;
