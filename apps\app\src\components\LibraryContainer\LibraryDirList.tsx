import React, { useMemo } from 'react';
import DirBox from '../Box/DirBox';
import { getBoxSizeByMedia } from '../Waterfall';
import { DirItemType } from '@/types/material';
import GridList from '../GridList';
import { material_directories } from '@roasmax/database';
import { Loader2 } from 'lucide-react';

const LibraryDirList = ({
  containerWidth,
  dirList,
  onClickDir,
  batchMode,
  onSelect,
  loading,
  updateDir,
}: {
  containerWidth: number;
  dirList: DirItemType[];
  onClickDir: (data: material_directories) => void;
  batchMode: boolean;
  onSelect: (data: DirItemType) => void;
  loading: boolean;
  updateDir: (data: { name: string; id: string | undefined }) => Promise<void>;
}) => {
  const { waterfallList, column } = useMemo(() => {
    const { column: col, width, height } = getBoxSizeByMedia('DirBox', containerWidth, 32, 0);
    const newList = [...dirList];

    return {
      waterfallList: newList?.map((each: any) => {
        return {
          w: width,
          h: height,
          data: each,
        };
      }),
      column: col,
    };
  }, [dirList, containerWidth]);
  if (!loading && !dirList.length) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex h-[100px] items-center justify-center text-sm text-[#81889D]">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      </div>
    );
  }

  return (
    <div className="h-auto w-full">
      <div className="mb-4 text-sm text-[#81889D]">我的文件夹</div>
      <GridList
        list={waterfallList}
        column={column}
        component={DirBox}
        itemPadding={32}
        itemPaddingY={32}
        containerId="bwai-library-container"
        componentProps={{
          onClick: (item: material_directories) => {
            onClickDir(item);
          },
          batchMode,
          onSelect,
          updateDir,
        }}
      />
    </div>
  );
};

export default LibraryDirList;
