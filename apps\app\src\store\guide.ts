import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';

interface stepsArray {
  title: string;
  message: string;
  target: string;
  isNextStep: boolean;
}
interface AuthState {
  tipOpen: boolean;
  currentStep: number;
  steps: stepsArray[];
  setSteps: (list: stepsArray[]) => void;
  setCurrentStep: (value: number) => void;
  setTipOpen: (value: boolean) => void;
  handleHighlight: (elementId: string, step: number, currentStep: number) => void;
}

export const guide = create<AuthState>()(
  devtools(
    persist(
      (set) => ({
        tipOpen: true,
        currentStep: 0,
        steps: [
          {
            title: '您好，欢迎来到博望AI',
            message: '点击“导入素材”，可选择你所需的导入方式。',
            target: '#next1',
            isNextStep: false,
          },
          { title: '', message: '请先本地上传视频，上传成功后即可一键导入素材', target: '#next4', isNextStep: false },
          {
            title: '',
            message: '上传成功的视频会保存在素材库，也可在此筛选导入素材',
            target: '#next3',
            isNextStep: false,
          },
          { title: '', message: '云盘中心包括素材库和生成库，请注意内存使用量', target: '#next5', isNextStep: false },
          {
            title: '',
            message: '在此处确保您选择的语言与视频内容相匹配，否则会导致视频生成错误',
            target: '#next2',
            isNextStep: false,
          },
          // { title: '', message: '素材库可对上传的视频文件进行管理', target: '#next6', isNextStep: false },
          // { title: '', message: '生成库可对生成的视频文件进行管理', target: '#next7', isNextStep: false },
        ],
        setSteps: (list) => set({ steps: list }),
        setCurrentStep: (value: number) => set({ currentStep: value }),
        setTipOpen: (value: boolean) => set({ tipOpen: value }),
        handleHighlight: (elementId: string, step: number, currentStep: number) => {
          const element = document.getElementById(elementId);
          if (element) {
            if (step === currentStep) {
              element.classList.add('highlight');
              set({ tipOpen: true });
            } else {
              element.classList.remove('highlight');
              element.classList.remove('highlight2');
            }
          }
        },
      }),
      {
        name: `BWAI_USER_GUIDE`,
        storage: createJSONStorage(() => localStorage),
      },
    ),
  ),
);
