{"$schema": "https://json.schemastore.org/tsconfig", "extends": "../../packages/typescript-config/nextjs.json", "compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "esnext", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@roasmax/utils": ["../../packages/utils/src"], "@roasmax/utils/*": ["../../packages/utils/src/*"], "@roasmax/serve": ["../../packages/serve/src"], "@roasmax/database": ["../../packages/database/src"]}}, "include": ["next-env.d.ts", "typings.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.mjs", "../../packages/database/typings.d.ts"], "exclude": ["node_modules"]}