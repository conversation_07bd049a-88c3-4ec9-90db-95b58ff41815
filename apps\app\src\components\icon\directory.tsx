export const Directory = ({ className }: { className?: string }) => {
  return (
    <svg
      className={className}
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.740234" y="0.587891" width="40" height="40" rx="6" fill="#1F2434" />
      <path
        opacity="0.2"
        d="M11.7411 28.1975H10.3564V14.3513C10.3564 13.8005 10.5753 13.2722 10.9648 12.8827C11.3543 12.4932 11.8825 12.2744 12.4334 12.2744H20.0488V13.659H12.4334C12.2498 13.659 12.0737 13.732 11.9438 13.8618C11.814 13.9916 11.7411 14.1677 11.7411 14.3513V28.1975Z"
        fill="url(#paint0_linear_738_788)"
      />
      <path
        d="M14.5087 24.0498H17.2779C17.7395 24.0498 17.9703 24.2806 17.9703 24.7421C17.9703 25.2037 17.7395 25.4344 17.2779 25.4344H14.5087C14.0472 25.4344 13.8164 25.2037 13.8164 24.7421C13.8164 24.2806 14.0472 24.0498 14.5087 24.0498Z"
        fill="white"
      />
      <path
        d="M29.74 17.1264H29.0477V15.0494C29.0477 14.4986 28.8289 13.9703 28.4394 13.5808C28.0499 13.1913 27.5216 12.9725 26.9708 12.9725H20.2415L20.0255 12.1127C19.9882 11.9631 19.902 11.8302 19.7806 11.7351C19.6592 11.6401 19.5096 11.5882 19.3554 11.5879H11.74C11.1892 11.5879 10.6609 11.8067 10.2714 12.1962C9.8819 12.5857 9.66309 13.114 9.66309 13.6648V27.511C9.66309 28.0618 9.8819 28.5901 10.2714 28.9796C10.6609 29.3691 11.1892 29.5879 11.74 29.5879H29.74C30.2908 29.5879 30.8191 29.3691 31.2086 28.9796C31.5981 28.5901 31.8169 28.0618 31.8169 27.511V19.2033C31.8169 18.6524 31.5981 18.1242 31.2086 17.7347C30.8191 17.3452 30.2908 17.1264 29.74 17.1264ZM26.9708 14.3571C27.1544 14.3571 27.3305 14.4301 27.4603 14.5599C27.5901 14.6897 27.6631 14.8658 27.6631 15.0494V17.1264H21.2772L20.5849 14.3571H26.9708ZM30.4323 27.511C30.4323 27.6946 30.3594 27.8707 30.2295 28.0005C30.0997 28.1303 29.9236 28.2033 29.74 28.2033H11.74C11.5564 28.2033 11.3803 28.1303 11.2505 28.0005C11.1206 27.8707 11.0477 27.6946 11.0477 27.511V13.6648C11.0477 13.4812 11.1206 13.3051 11.2505 13.1753C11.3803 13.0454 11.5564 12.9725 11.74 12.9725H18.814L20.0685 17.9876C20.1061 18.1372 20.1926 18.2699 20.3143 18.3647C20.4359 18.4595 20.5858 18.511 20.74 18.511H29.74C29.9236 18.511 30.0997 18.5839 30.2295 18.7137C30.3594 18.8436 30.4323 19.0197 30.4323 19.2033V27.511Z"
        fill="url(#paint1_linear_738_788)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_738_788"
          x1="15.2026"
          y1="12.2744"
          x2="15.2026"
          y2="28.1975"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#54FFE0" />
          <stop offset="0.2" stopColor="#2AF0F0" />
          <stop offset="1" stopColor="#9D81FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_738_788"
          x1="9.66309"
          y1="15.6953"
          x2="29.7492"
          y2="29.5879"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0F0" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
      </defs>
    </svg>
  );
};
