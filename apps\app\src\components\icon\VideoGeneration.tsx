export const VideoGeneration = ({ className }: { className?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask id="mask0_4192_692" maskUnits="userSpaceOnUse" x="6" y="6" width="21" height="21">
        <path d="M6.7 6.7H26.3V26.3H6.7V6.7Z" fill="white" stroke="white" strokeWidth="1.4" />
      </mask>
      <g mask="url(#mask0_4192_692)">
        <path
          d="M16.4992 25.4C21.4248 25.4 25.4242 21.4205 25.4242 16.5023C25.4242 11.5841 21.4248 7.60352 16.4992 7.60352C11.5737 7.60352 7.57422 11.5841 7.57422 16.5023C7.57422 21.4205 11.5737 25.4 16.4992 25.4Z"
          stroke="currentColor"
          strokeWidth="1.4"
        />
        <path d="M12.625 14V17.2812" stroke="currentColor" strokeWidth="1.4" strokeLinecap="round" />
        <path
          d="M18.9495 14L17.0879 15.6401L18.9506 17.2812"
          stroke="currentColor"
          strokeWidth="1.4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};
