import { Button } from '@/components/ui';
import { MaterialItemType } from '@/hooks/useMaterial';
import { Dispatch, SetStateAction } from 'react';
const SelectModeFooter = ({
  selectedMaterialList,
  setSelectedMaterialList,
  onConfirm,
  setMaterialList,
}: {
  selectedMaterialList: MaterialItemType[];
  setSelectedMaterialList: (list: MaterialItemType[]) => void;
  onConfirm: (list: MaterialItemType[]) => void;
  setMaterialList: Dispatch<SetStateAction<MaterialItemType[]>>;
}) => {
  const onCancel = () => {
    setSelectedMaterialList([]);
    setMaterialList((prev) => prev.map((item) => ({ ...item, checked: false })));
  };
  const handleConfirm = () => {
    onConfirm(selectedMaterialList);
  };
  return (
    <div className="absolute bottom-0 left-0 right-2 h-[65px]">
      <div className="bg-background mr-6 flex h-12 w-auto items-center justify-between rounded-lg px-3">
        <div>已选择 {selectedMaterialList.length} 项视频</div>
        <div className="flex items-center gap-4">
          <Button variant="link" className="text-[#9FA4B2] hover:text-white hover:no-underline" onClick={onCancel}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedMaterialList.length}
            className="h-8 w-[108px] text-sm font-medium text-[#050A1C]"
          >
            使用素材
          </Button>
        </div>
      </div>
    </div>
  );
};
export default SelectModeFooter;
