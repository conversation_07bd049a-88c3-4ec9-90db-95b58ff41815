import React from 'react';

export const CrossOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="8" height="7" viewBox="0 0 8 7" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.98171 6.34082C6.92194 6.34082 6.86448 6.31783 6.8185 6.27416L1.2811 0.734461C1.19146 0.644814 1.19146 0.497702 1.2811 0.408055C1.37075 0.318409 1.51556 0.318409 1.60521 0.408055L7.14491 5.94775C7.20927 6.01441 7.22996 6.11326 7.19318 6.19831C7.1587 6.28335 7.07595 6.34082 6.98171 6.34082Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
      <path
        d="M1.44227 6.33986C1.35033 6.33986 1.26528 6.28469 1.2285 6.19734C1.19172 6.11229 1.21241 6.01345 1.27907 5.94679L6.81877 0.407094C6.91071 0.326642 7.04633 0.333538 7.13368 0.418587C7.21873 0.503636 7.22333 0.641554 7.14517 0.733499L1.60547 6.2732C1.5618 6.31457 1.50433 6.33986 1.44227 6.33986Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
    </svg>
  );
};
