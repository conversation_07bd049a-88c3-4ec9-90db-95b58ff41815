import { isEmpty } from 'lodash';

/**
 * 从原始数据中解析出 header 和 data
 * @param raw 原始数据
 * @param skipStartLines 跳过起始行数
 * @returns
 */
const parseHeaderAndDataFromRaw = (raw: string, skipStartLines: number = 0): { header: string[]; body: string[][] } => {
  // 根据 schema 解析原始数据。原始数据以 csv 格式输入，/t 作为分隔符，/n 作为换行符。第一行为标题行。
  const lines = raw.split('\n').slice(skipStartLines);
  const header = lines[0]?.split('\t') || [];
  const body = lines.slice(1).map((line) => line.split('\t'));
  return { header, body };
};

/**
 * 从一行原始数据中解析出对象数据
 * 将数据转换为 json 格式，header的值为column中title的值，然后再把对应title的key作为对象的key
 * @param line 一行数据
 * @param header 标题行
 * @param columns 列定义
 * @returns
 */
const parseObjectDataFromRaw = (
  line: string[],
  header: string[],
  columns: PrismaJson.DataImportTemplateContent['columns'],
) => {
  return line.reduce((acc, cur, index) => {
    const h = header[index];
    const column = columns.find((c) => c.title === h);

    if (!column) {
      // 如果列定义不存在，忽略此列值
      return acc;
    }

    if (column.type === 'images' || column.type === 'tags') {
      // 如果列定义为 images / tags，按照分号分割
      acc[column.key!] = cur ? cur.split(';') : column.defaultValue || [];
      return acc;
    }

    if (column.type === 'number') {
      // 如果列定义为 number，转换为数字
      const { format } = column.config || {};
      const num = Number(format ? cur.match(new RegExp(format))?.[1] : cur.replace(/[^\d.]/g, '')) || null;
      acc[column.key!] = num || column.defaultValue || null;
      return acc;
    }

    if (column.type === 'currency') {
      // 如果列定义为 currency，解析货币
      const { format, unit } = column.config || {};
      const num =
        Number(format ? cur.match(new RegExp(format))?.[1] : cur.replace(/[^\d.]/g, '')) || column.defaultValue || null;
      if (typeof num == 'number') {
        acc[column.key!] = num * (unit || 1);
      }
      return acc;
    }

    if (column.type === 'percent') {
      // 如果列定义为 percent，解析百分比
      const { format, unit } = column.config || {};
      const num = Number(format ? cur.match(new RegExp(format))?.[1] : cur) || column.defaultValue || null;
      if (typeof num == 'number') {
        acc[column.key!] = num * (unit || 1);
      }
      return acc;
    }

    // 其他类型，直接赋值
    acc[column.key!] = cur.trim() || column.defaultValue || null;
    return acc;
  }, {} as any);
};

/**
 * 从原始数据中解析出新增、更新和不变的数据
 * @param raw 原始数据
 * @param schema 数据导入模板内容
 * @param request 请求函数 用于获取线上数据以进行对比
 * @returns
 */
export const parseDataFromRawString = async (
  raw: string,
  schema: PrismaJson.DataImportTemplateContent,
  request: (data: any[], schema: PrismaJson.DataImportTemplateContent) => Promise<any[]>,
): Promise<{ new: any[]; update: any[]; noChange: any[] }> => {
  const { header, body } = parseHeaderAndDataFromRaw(raw, schema.skipStartLines || 0);
  // 只取唯一键值有值的数据，若是联合唯一键，至少有一个有值即可
  const parsed = body
    .map((line) => parseObjectDataFromRaw(line, header, schema.columns))
    .filter((item) =>
      schema.uniqueKeys.some((key) => {
        const v = item[key];
        if (!v) console.log(item);
        return v;
      }),
    );

  const originData = await request?.(parsed, schema);

  // 线上数据和解析出的数据进行对比，得到新增和更新的数据
  const data: { new: any[]; update: any[]; noChange: any[] } = { new: [], update: [], noChange: [] };

  for (const item of parsed) {
    const origin = originData?.find((originItem) => schema.uniqueKeys.every((key) => originItem[key] === item[key]));
    if (!origin) {
      // 如果线上数据不存在，则新增, 使用默认值
      data.new.push({ ...(schema.defaultValues || {}), ...item });
      continue;
    }

    const diff = (schema.shouldUpdateKeys || Object.keys(item)).filter((key) => {
      if (item[key] === undefined || item[key] === null || (typeof item[key] === 'object' && isEmpty(item[key]))) {
        return false;
      }
      return JSON.stringify(item[key]) !== JSON.stringify(origin[key]);
    });
    if (diff.length > 0) {
      // 如果出现差异，则更新，只渲染差异的列和唯一值列
      const updateItem = diff.reduce((acc, key) => ({ ...acc, [key]: item[key] }), {} as any);
      data.update.push({
        ...origin,
        ...schema.uniqueKeys.reduce((acc, key) => ({ ...acc, [key]: origin[key] }), {} as any),
        ...updateItem,
        __diff__: diff,
      });
      continue;
    }

    data.noChange.push({ ...item, id: origin.id });
  }

  return data;
};
