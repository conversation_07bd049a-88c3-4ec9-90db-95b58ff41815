export const GoodsIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="20"
      height="18"
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M12.0918 15.0572H8.40155C7.82073 15.0542 7.26476 14.8303 6.85516 14.4343C6.44555 14.0383 6.21563 13.5025 6.21564 12.9441V5.0339C6.2218 4.47934 6.45445 3.94933 6.86339 3.55823C7.27233 3.16713 7.82478 2.94629 8.40155 2.94336H12.0918C12.6726 2.94635 13.2285 3.1703 13.6381 3.56627C14.0478 3.96223 14.2777 4.49801 14.2777 5.0565V12.9667C14.2715 13.5212 14.0389 14.0512 13.6299 14.4423C13.221 14.8334 12.6685 15.0543 12.0918 15.0572ZM8.40155 3.91518C8.08986 3.91518 7.79094 4.03423 7.57054 4.24615C7.35015 4.45807 7.22633 4.7455 7.22633 5.0452V12.9554C7.22633 13.2551 7.35015 13.5425 7.57054 13.7544C7.79094 13.9663 8.08986 14.0854 8.40155 14.0854H12.0918C12.4034 14.0854 12.7024 13.9663 12.9228 13.7544C13.1432 13.5425 13.267 13.2551 13.267 12.9554V5.0452C13.267 4.7455 13.1432 4.45807 12.9228 4.24615C12.7024 4.03423 12.4034 3.91518 12.0918 3.91518H8.40155ZM5.38123 14.3227C5.1055 14.3348 4.83001 14.2943 4.57062 14.2036C4.31123 14.1129 4.07307 13.9737 3.86988 13.7941C3.66669 13.6145 3.50247 13.398 3.3867 13.157C3.27092 12.9161 3.20588 12.6555 3.19531 12.3903V5.61021C3.20588 5.34501 3.27092 5.08444 3.3867 4.84351C3.50247 4.60259 3.66669 4.38608 3.86988 4.20646C4.07307 4.02684 4.31123 3.88765 4.57062 3.79694C4.83001 3.70622 5.1055 3.66575 5.38123 3.67787C5.45324 3.66722 5.5268 3.67167 5.59686 3.69091C5.66691 3.71015 5.73182 3.74373 5.78712 3.78935C5.84241 3.83496 5.8868 3.89154 5.91723 3.95519C5.94766 4.01885 5.96341 4.08808 5.96341 4.15813C5.96341 4.22819 5.94766 4.29742 5.91723 4.36108C5.8868 4.42473 5.84241 4.4813 5.78712 4.52692C5.73182 4.57254 5.66691 4.60612 5.59686 4.62536C5.5268 4.6446 5.45324 4.64905 5.38123 4.63839C5.09859 4.61606 4.81799 4.70024 4.5987 4.87314C4.37941 5.04604 4.23859 5.29413 4.206 5.56501V12.3903C4.23859 12.6612 4.37941 12.9093 4.5987 13.0822C4.81799 13.2551 5.09859 13.3393 5.38123 13.317C5.45324 13.3063 5.5268 13.3108 5.59686 13.33C5.66691 13.3492 5.73182 13.3828 5.78712 13.4284C5.84241 13.4741 5.8868 13.5306 5.91723 13.5943C5.94766 13.6579 5.96341 13.7272 5.96341 13.7972C5.96341 13.8673 5.94766 13.9365 5.91723 14.0002C5.8868 14.0638 5.84241 14.1204 5.78712 14.166C5.73182 14.2116 5.66691 14.2452 5.59686 14.2644C5.5268 14.2837 5.45324 14.2881 5.38123 14.2775V14.3227ZM15.1121 14.3227C15.0401 14.3333 14.9665 14.3289 14.8964 14.3096C14.8264 14.2904 14.7615 14.2568 14.7062 14.2112C14.6509 14.1656 14.6065 14.109 14.5761 14.0454C14.5456 13.9817 14.5299 13.9125 14.5299 13.8424C14.5299 13.7724 14.5456 13.7031 14.5761 13.6395C14.6065 13.5758 14.6509 13.5193 14.7062 13.4736C14.7615 13.428 14.8264 13.3944 14.8964 13.3752C14.9665 13.356 15.0401 13.3515 15.1121 13.3622C15.3947 13.3845 15.6753 13.3003 15.8946 13.1274C16.1139 12.9545 16.2547 12.7064 16.2873 12.4355V5.61021C16.2547 5.33934 16.1139 5.09124 15.8946 4.91834C15.6753 4.74544 15.3947 4.66126 15.1121 4.68359C15.0401 4.69425 14.9665 4.6898 14.8964 4.67056C14.8264 4.65132 14.7615 4.61774 14.7062 4.57212C14.6509 4.52651 14.6065 4.46993 14.5761 4.40628C14.5456 4.34262 14.5299 4.27339 14.5299 4.20333C14.5299 4.13328 14.5456 4.06405 14.5761 4.00039C14.6065 3.93674 14.6509 3.88016 14.7062 3.83455C14.7615 3.78893 14.8264 3.75535 14.8964 3.73611C14.9665 3.71687 15.0401 3.71242 15.1121 3.72307C15.6612 3.69834 16.1984 3.88247 16.6075 4.23562C17.0166 4.58878 17.2646 5.08257 17.298 5.61021V12.3903C17.2874 12.6555 17.2224 12.9161 17.1066 13.157C16.9908 13.398 16.8266 13.6145 16.6234 13.7941C16.4202 13.9737 16.1821 14.1129 15.9227 14.2036C15.6633 14.2943 15.3878 14.3348 15.1121 14.3227Z"
        fill="white"
      />
    </svg>
  );
};
