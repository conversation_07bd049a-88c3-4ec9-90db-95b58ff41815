export const I18nText = () => {
  return (
    <svg width="131" height="119" viewBox="0 0 131 119" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M103.125 50L130.625 118.75H117.158L109.65 100H84.0917L76.5917 118.75H63.1333L90.625 50H103.125ZM50 0V12.5H87.5V25H75.2C70.3792 39.5138 62.6846 52.9079 52.575 64.3833C57.0833 68.4051 61.9731 71.9776 67.175 75.05L62.4833 86.7917C55.7667 82.975 49.4833 78.45 43.75 73.2833C32.5855 83.3836 19.3657 90.9458 5 95.45L1.64167 83.4C13.952 79.4754 25.2962 72.9983 34.9333 64.3917C27.8006 56.3148 21.8599 47.2592 17.2917 37.5H31.2917C34.775 43.9333 38.9583 49.9583 43.75 55.4833C51.5625 46.4749 57.7186 36.1545 61.9333 25H0V12.5H37.5V0H50ZM96.875 68.0333L89.0833 87.5H104.658L96.875 68.0333Z"
        fill="url(#paint0_linear_2651_487)"
        fillOpacity="0.1"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2651_487"
          x1="65.3125"
          y1="0"
          x2="65.3125"
          y2="185.475"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#929394" />
          <stop offset="1" stopColor="#929394" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
