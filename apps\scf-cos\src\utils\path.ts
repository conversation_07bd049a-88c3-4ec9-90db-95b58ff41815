import { EventPayload } from '..';

/**
 * 根据文件路径解析相关信息
 */
interface ParsedFileInfo {
  appName: string;
  tenantId: string;
  workflowName: string;
  stageName: string;
  remainingPath: string[];
}
export const parseFilePath = (record: EventPayload['Records'][0]): Partial<ParsedFileInfo> | null => {
  // 获取文件相对于bucket下的key
  const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

  const keyArr = key.split('/');
  if (keyArr.length < 5) return null;

  const [appName, tenantId, workflowName, stageName, ...remainingPath] = keyArr;

  return {
    appName,
    tenantId,
    workflowName,
    stageName,
    remainingPath,
  };
};
