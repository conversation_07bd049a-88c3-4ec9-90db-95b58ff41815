import plimit from 'p-limit';
import { useCallback, useEffect, useState, useRef } from 'react';

const useHoverDisplay = (
  url: string,
): {
  onMouseLeave: (videoElement: any) => void;
  onMouseOver: (videoElement: any) => void;
  loading: boolean;
} => {
  const [queue, setQueue] = useState<Promise<unknown>[]>([]);
  const [loading, setLoading] = useState(false);
  const limit = plimit(1);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const onMouseOver = useCallback(
    (videoElement: HTMLVideoElement) => {
      if (!videoElement) return;

      timerRef.current = setTimeout(() => {
        setLoading(true);
        setQueue((prevQueue) => [
          ...prevQueue,
          limit(() => {
            return new Promise((resolve) => {
              videoElement.src = url;
              videoElement.addEventListener('canplay', () => {
                if (videoElement && videoElement.paused) {
                  videoElement
                    .play()
                    .then(() => {
                      setLoading(false);
                      resolve('');
                    })
                    .catch((e) => {
                      console.error(e);
                      setLoading(false);
                      resolve('');
                    });
                }
              });
            });
          }),
        ]);
      }, 200);
    },
    [limit, url],
  );

  const onMouseLeave = useCallback(
    (videoElement: HTMLVideoElement) => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      setQueue(() => [
        limit(() => {
          return new Promise(() => {
            videoElement.pause();
            videoElement.src = '';
            videoElement.removeAttribute('src');
            videoElement.load();
            setLoading(false);
          });
        }),
      ]);
    },
    [limit],
  );

  useEffect(() => {
    async function func() {
      if (!queue.length) return;
      await Promise.all(queue).then(() => {
        setQueue([]);
      });
    }
    func();
  }, [queue]);

  return { onMouseOver, onMouseLeave, loading };
};

export { useHoverDisplay };
