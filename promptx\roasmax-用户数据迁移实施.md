# Roasmax - 用户数据迁移实施

## 📋 任务概述

**任务ID**: 4kLumbac9PC9g8hvQhCGx9  
**任务名称**: 用户数据迁移实施  
**优先级**: 高  
**预估工时**: 2-3天  
**前置依赖**: 本地认证适配器开发  

## 🎯 任务目标

安全、完整地将 Authing 用户数据迁移到本地数据库，确保数据完整性和业务连续性。

## 📊 详细任务分解

### 1. 数据导出实施

#### 1.1 Authing 用户数据导出
```typescript
// scripts/migration/export-authing-users.ts
import { ManagementClient } from 'authing-node-sdk';
import fs from 'fs/promises';
import path from 'path';

interface AuthingUserExport {
  userId: string;
  email: string;
  nickname: string;
  username: string;
  phone?: string;
  tenantId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  customData?: Record<string, any>;
}

interface AuthingRoleExport {
  userId: string;
  tenantId: string;
  roles: Array<{
    code: string;
    name: string;
    description?: string;
  }>;
}

class AuthingDataExporter {
  private managementClient: ManagementClient;
  private exportDir: string;
  
  constructor() {
    this.managementClient = new ManagementClient({
      accessKeyId: process.env.PRIVATE_ACCESSKEYID_ID!,
      accessKeySecret: process.env.PRIVATE_ACCESSKEYSECRET_ID!,
    });
    
    this.exportDir = path.join(process.cwd(), 'migration-data');
  }
  
  async exportAllUsers(): Promise<void> {
    console.log('开始导出 Authing 用户数据...');
    
    // 创建导出目录
    await fs.mkdir(this.exportDir, { recursive: true });
    
    const users: AuthingUserExport[] = [];
    const userRoles: AuthingRoleExport[] = [];
    let page = 1;
    const limit = 100;
    
    while (true) {
      try {
        console.log(`正在导出第 ${page} 页用户数据...`);
        
        // 获取用户列表
        const result = await this.managementClient.listUsers({
          page,
          limit
        });
        
        if (!result.data?.list?.length) {
          console.log('用户数据导出完成');
          break;
        }
        
        // 处理每个用户
        for (const user of result.data.list) {
          // 导出用户基础信息
          users.push({
            userId: user.userId,
            email: user.email,
            nickname: user.nickname,
            username: user.username,
            phone: user.phone,
            tenantId: user.tenantId || '',
            status: user.status,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            customData: user.customData
          });
          
          // 导出用户角色信息
          try {
            const rolesResult = await this.managementClient.getUserRoles({
              userId: user.userId,
              userIdType: 'user_id'
            });
            
            if (rolesResult.data?.list?.length) {
              userRoles.push({
                userId: user.userId,
                tenantId: user.tenantId || '',
                roles: rolesResult.data.list.map(role => ({
                  code: role.code,
                  name: role.name,
                  description: role.description
                }))
              });
            }
          } catch (roleError) {
            console.warn(`获取用户 ${user.userId} 角色失败:`, roleError);
          }
          
          // 添加延迟避免 API 限流
          await this.delay(100);
        }
        
        page++;
      } catch (error) {
        console.error(`导出第 ${page} 页数据失败:`, error);
        throw error;
      }
    }
    
    // 保存导出数据
    await this.saveExportData(users, userRoles);
    
    console.log(`导出完成: 用户 ${users.length} 个，角色关系 ${userRoles.length} 个`);
  }
  
  private async saveExportData(users: AuthingUserExport[], userRoles: AuthingRoleExport[]): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 保存用户数据
    await fs.writeFile(
      path.join(this.exportDir, `authing-users-${timestamp}.json`),
      JSON.stringify(users, null, 2)
    );
    
    // 保存角色数据
    await fs.writeFile(
      path.join(this.exportDir, `authing-user-roles-${timestamp}.json`),
      JSON.stringify(userRoles, null, 2)
    );
    
    // 保存导出摘要
    const summary = {
      exportTime: new Date().toISOString(),
      userCount: users.length,
      roleRelationCount: userRoles.length,
      tenants: [...new Set(users.map(u => u.tenantId))],
      files: [
        `authing-users-${timestamp}.json`,
        `authing-user-roles-${timestamp}.json`
      ]
    };
    
    await fs.writeFile(
      path.join(this.exportDir, `export-summary-${timestamp}.json`),
      JSON.stringify(summary, null, 2)
    );
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 执行导出
async function main() {
  try {
    const exporter = new AuthingDataExporter();
    await exporter.exportAllUsers();
  } catch (error) {
    console.error('导出失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
```

#### 1.2 租户和角色数据导出
```typescript
// scripts/migration/export-authing-tenants.ts
class AuthingTenantExporter {
  private managementClient: ManagementClient;
  
  async exportTenants(): Promise<void> {
    console.log('开始导出租户数据...');
    
    const tenants = [];
    const roles = [];
    const permissions = [];
    
    // 导出租户列表
    const tenantResult = await this.managementClient.listTenants();
    
    for (const tenant of tenantResult.data.list) {
      tenants.push({
        id: tenant.id,
        name: tenant.name,
        description: tenant.description,
        createdAt: tenant.createdAt
      });
      
      // 导出租户下的角色
      const rolesResult = await this.managementClient.listRoles({
        namespace: tenant.id
      });
      
      for (const role of rolesResult.data.list) {
        roles.push({
          tenantId: tenant.id,
          code: role.code,
          name: role.name,
          description: role.description
        });
        
        // 导出角色权限
        try {
          const permissionsResult = await this.managementClient.getRoleAuthorizedResources({
            code: role.code,
            namespace: tenant.id
          });
          
          if (permissionsResult.data?.list?.length) {
            permissions.push({
              tenantId: tenant.id,
              roleCode: role.code,
              resources: permissionsResult.data.list
            });
          }
        } catch (permError) {
          console.warn(`获取角色 ${role.code} 权限失败:`, permError);
        }
      }
    }
    
    // 保存数据
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await fs.writeFile(
      path.join(this.exportDir, `authing-tenants-${timestamp}.json`),
      JSON.stringify({ tenants, roles, permissions }, null, 2)
    );
    
    console.log(`租户数据导出完成: ${tenants.length} 个租户，${roles.length} 个角色`);
  }
}
```

### 2. 数据转换和清洗

#### 2.1 用户数据转换
```typescript
// scripts/migration/transform-user-data.ts
interface LocalUserData {
  id: string;
  tenant_id: string;
  user_id: string;
  email: string;
  nickname: string;
  account: string;
  user_status: string;
  admin: number;
  password: string; // 临时空值
  authing_user_id: string;
  password_reset_required: boolean;
  is_migrated: boolean;
  migration_date: Date;
  email_verified: boolean;
}

class UserDataTransformer {
  async transformUsers(authingUsers: AuthingUserExport[]): Promise<LocalUserData[]> {
    console.log('开始转换用户数据...');
    
    const localUsers: LocalUserData[] = [];
    
    for (const authingUser of authingUsers) {
      // 数据验证
      if (!authingUser.email || !authingUser.userId) {
        console.warn(`跳过无效用户数据: ${JSON.stringify(authingUser)}`);
        continue;
      }
      
      // 生成本地用户ID
      const localUserId = this.generateLocalUserId();
      
      localUsers.push({
        id: this.generateUUID(),
        tenant_id: authingUser.tenantId || 'default',
        user_id: localUserId,
        email: authingUser.email,
        nickname: authingUser.nickname || authingUser.email.split('@')[0],
        account: authingUser.username || authingUser.email,
        user_status: this.mapUserStatus(authingUser.status),
        admin: this.determineAdminStatus(authingUser),
        password: '', // 临时空密码，等待用户重置
        authing_user_id: authingUser.userId,
        password_reset_required: true,
        is_migrated: false,
        migration_date: new Date(),
        email_verified: true // 假设 Authing 用户邮箱已验证
      });
    }
    
    console.log(`用户数据转换完成: ${localUsers.length} 个用户`);
    return localUsers;
  }
  
  private generateLocalUserId(): string {
    // 生成本地用户ID，格式: usr_xxxxxxxxxx
    return 'usr_' + Math.random().toString(36).substr(2, 10);
  }
  
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  private mapUserStatus(authingStatus: string): string {
    const statusMap: Record<string, string> = {
      'Activated': 'active',
      'Suspended': 'suspended',
      'Deactivated': 'inactive'
    };
    
    return statusMap[authingStatus] || 'active';
  }
  
  private determineAdminStatus(user: AuthingUserExport): number {
    // 根据用户数据判断是否为管理员
    // 这里可以根据实际业务逻辑调整
    return user.customData?.isAdmin ? 1 : 0;
  }
}
```

#### 2.2 角色数据转换
```typescript
// scripts/migration/transform-role-data.ts
interface LocalRoleData {
  id: string;
  tenant_id: string;
  code: string;
  name: string;
  description?: string;
  is_system: boolean;
}

interface LocalUserRoleData {
  id: string;
  tenant_id: string;
  user_id: string;
  role_code: string;
}

class RoleDataTransformer {
  async transformRoles(
    authingRoles: AuthingRoleExport[],
    userMapping: Map<string, string> // Authing userId -> Local userId
  ): Promise<{ roles: LocalRoleData[]; userRoles: LocalUserRoleData[] }> {
    console.log('开始转换角色数据...');
    
    const localRoles: LocalRoleData[] = [];
    const localUserRoles: LocalUserRoleData[] = [];
    const processedRoles = new Set<string>();
    
    for (const authingUserRole of authingRoles) {
      const localUserId = userMapping.get(authingUserRole.userId);
      if (!localUserId) {
        console.warn(`找不到用户 ${authingUserRole.userId} 的本地映射`);
        continue;
      }
      
      for (const role of authingUserRole.roles) {
        const roleKey = `${authingUserRole.tenantId}_${role.code}`;
        
        // 添加角色定义（去重）
        if (!processedRoles.has(roleKey)) {
          localRoles.push({
            id: this.generateUUID(),
            tenant_id: authingUserRole.tenantId,
            code: role.code,
            name: role.name,
            description: role.description,
            is_system: this.isSystemRole(role.code)
          });
          processedRoles.add(roleKey);
        }
        
        // 添加用户角色关系
        localUserRoles.push({
          id: this.generateUUID(),
          tenant_id: authingUserRole.tenantId,
          user_id: localUserId,
          role_code: role.code
        });
      }
    }
    
    console.log(`角色数据转换完成: ${localRoles.length} 个角色，${localUserRoles.length} 个用户角色关系`);
    return { roles: localRoles, userRoles: localUserRoles };
  }
  
  private isSystemRole(roleCode: string): boolean {
    const systemRoles = ['admin', 'super_admin', 'system_admin'];
    return systemRoles.some(sr => roleCode.toLowerCase().includes(sr));
  }
  
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}
```

### 3. 数据导入实施

#### 3.1 批量数据导入
```typescript
// scripts/migration/import-local-data.ts
import { PrismaClient } from '@roasmax/database';

class LocalDataImporter {
  private prisma: PrismaClient;
  private batchSize = 100;
  
  constructor() {
    this.prisma = new PrismaClient();
  }
  
  async importUsers(users: LocalUserData[]): Promise<void> {
    console.log(`开始导入 ${users.length} 个用户...`);
    
    // 分批导入
    for (let i = 0; i < users.length; i += this.batchSize) {
      const batch = users.slice(i, i + this.batchSize);
      
      try {
        await this.prisma.members.createMany({
          data: batch,
          skipDuplicates: true
        });
        
        console.log(`已导入用户 ${i + batch.length}/${users.length}`);
      } catch (error) {
        console.error(`导入用户批次 ${i}-${i + batch.length} 失败:`, error);
        
        // 逐个导入以识别问题数据
        for (const user of batch) {
          try {
            await this.prisma.members.create({ data: user });
          } catch (userError) {
            console.error(`导入用户 ${user.email} 失败:`, userError);
          }
        }
      }
    }
    
    console.log('用户数据导入完成');
  }
  
  async importRoles(roles: LocalRoleData[]): Promise<void> {
    console.log(`开始导入 ${roles.length} 个角色...`);
    
    for (let i = 0; i < roles.length; i += this.batchSize) {
      const batch = roles.slice(i, i + this.batchSize);
      
      try {
        await this.prisma.roles.createMany({
          data: batch,
          skipDuplicates: true
        });
        
        console.log(`已导入角色 ${i + batch.length}/${roles.length}`);
      } catch (error) {
        console.error(`导入角色批次失败:`, error);
      }
    }
    
    console.log('角色数据导入完成');
  }
  
  async importUserRoles(userRoles: LocalUserRoleData[]): Promise<void> {
    console.log(`开始导入 ${userRoles.length} 个用户角色关系...`);
    
    for (let i = 0; i < userRoles.length; i += this.batchSize) {
      const batch = userRoles.slice(i, i + this.batchSize);
      
      try {
        await this.prisma.user_roles.createMany({
          data: batch,
          skipDuplicates: true
        });
        
        console.log(`已导入用户角色关系 ${i + batch.length}/${userRoles.length}`);
      } catch (error) {
        console.error(`导入用户角色关系批次失败:`, error);
      }
    }
    
    console.log('用户角色关系导入完成');
  }
  
  async verifyImportedData(): Promise<void> {
    console.log('开始验证导入数据...');
    
    const userCount = await this.prisma.members.count({
      where: { authing_user_id: { not: null } }
    });
    
    const roleCount = await this.prisma.roles.count();
    const userRoleCount = await this.prisma.user_roles.count();
    
    console.log(`验证结果:`);
    console.log(`- 迁移用户数: ${userCount}`);
    console.log(`- 角色数: ${roleCount}`);
    console.log(`- 用户角色关系数: ${userRoleCount}`);
    
    // 检查数据完整性
    const usersWithoutRoles = await this.prisma.members.findMany({
      where: {
        authing_user_id: { not: null },
        user_roles: { none: {} }
      },
      select: { email: true, user_id: true }
    });
    
    if (usersWithoutRoles.length > 0) {
      console.warn(`发现 ${usersWithoutRoles.length} 个用户没有角色分配`);
    }
  }
}
```

### 4. 迁移执行脚本

#### 4.1 完整迁移流程
```typescript
// scripts/migration/run-migration.ts
async function runMigration() {
  console.log('=== 开始 Authing 数据迁移 ===');
  
  try {
    // 1. 导出 Authing 数据
    console.log('\n1. 导出 Authing 数据');
    const exporter = new AuthingDataExporter();
    await exporter.exportAllUsers();
    
    // 2. 加载导出数据
    console.log('\n2. 加载导出数据');
    const exportFiles = await fs.readdir('./migration-data');
    const latestUserFile = exportFiles
      .filter(f => f.startsWith('authing-users-'))
      .sort()
      .pop();
    
    if (!latestUserFile) {
      throw new Error('找不到用户导出文件');
    }
    
    const authingUsers = JSON.parse(
      await fs.readFile(`./migration-data/${latestUserFile}`, 'utf-8')
    );
    
    // 3. 转换数据
    console.log('\n3. 转换数据格式');
    const transformer = new UserDataTransformer();
    const localUsers = await transformer.transformUsers(authingUsers);
    
    // 4. 导入数据
    console.log('\n4. 导入本地数据库');
    const importer = new LocalDataImporter();
    await importer.importUsers(localUsers);
    
    // 5. 验证数据
    console.log('\n5. 验证导入结果');
    await importer.verifyImportedData();
    
    console.log('\n=== 迁移完成 ===');
    
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  runMigration();
}
```

## ✅ 验收标准

1. **数据完整性**
   - [ ] 所有 Authing 用户数据已导出
   - [ ] 用户角色关系完整迁移
   - [ ] 数据转换准确无误
   - [ ] 导入数据验证通过

2. **迁移安全性**
   - [ ] 原始数据备份完整
   - [ ] 迁移过程可回滚
   - [ ] 敏感数据处理安全
   - [ ] 迁移日志详细记录

3. **业务连续性**
   - [ ] 用户可正常登录
   - [ ] 权限验证正确
   - [ ] 业务功能不受影响

## 🔧 Augment Code 提示词

```
请帮我实施 Roasmax 项目的 Authing 用户数据迁移：

1. 开发 Authing 数据导出脚本，包括用户、角色、权限数据
2. 实现数据转换和清洗逻辑
3. 开发批量数据导入功能
4. 创建数据验证和完整性检查
5. 提供完整的迁移执行脚本
6. 实现迁移回滚机制

要求：
- 确保数据迁移的安全性和完整性
- 提供详细的迁移日志和进度显示
- 支持分批处理大量数据
- 包含完整的错误处理和恢复机制
- 提供数据验证和质量检查

请提供完整的 TypeScript 迁移脚本。
```

## 📅 时间安排

- **第1天**: 数据导出脚本开发和测试
- **第2天**: 数据转换和导入功能开发
- **第3天**: 完整迁移流程测试和验证

## 🚨 风险提示

1. **数据丢失风险**: 迁移过程中可能出现数据丢失
2. **业务中断风险**: 迁移期间可能影响业务正常运行
3. **数据一致性风险**: 迁移后数据可能存在不一致

## 📞 协作要求

- 需要与 DBA 协调数据库操作
- 需要与运维团队协调迁移时间窗口
- 需要与测试团队验证迁移结果
