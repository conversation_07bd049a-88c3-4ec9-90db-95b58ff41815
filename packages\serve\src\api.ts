import { Logger, to } from '@roasmax/utils';
import { AuthenticationClient } from 'authing-node-sdk';
import chalk from 'chalk';
import { NextRequest, NextResponse } from 'next/server';
import { ActionContext } from './types';
import { createContext } from './context';
import { getLoginSessionInfo } from './authing';
import { prisma } from './prisma';

// 添加 SSE 响应类型
type ApiResponse<R> = Response | NextResponse;

export const api = <R, T>(
  handler: (context: ActionContext<T>, request: NextRequest) => Promise<R>,
  options?: {
    sse?: boolean;
  },
) => {
  return async (request: NextRequest): Promise<ApiResponse<R>> => {
    const [error, body] = await to(request.json().catch(() => ({})));
    const payload = error ? undefined : body;
    const token = request.headers.get('AccessToken') || request.headers.get('Authorization');
    const requestIp = request.headers.get('x-forwarded-for');
    const logger = new Logger(`${request.url ?? '-'}`, requestIp ?? '-', generateRandomBase36(10));
    const now = Date.now();

    logger._start('Start', JSON.stringify(payload));

    let ctx: ActionContext<any>;
    try {
      // 鉴权
      if (!token) {
        throw new Error('Unauthorized: no token');
      }
      const sessionInfo = await getLoginSessionInfo(token);
      if (!sessionInfo?.data?.id && !sessionInfo?.sub) {
        throw new Error('Unauthorized: invalid token');
      }
      const tenant = await fetchTenant(token);
      const tenantConfig = await fetchTenantSourceConfig(tenant.id);

      ctx = await createContext(
        { data: payload },
        {
          token,
          user: { id: sessionInfo?.data?.id ?? sessionInfo?.sub ?? '' },
          tenant: { id: tenant.id, name: tenant.name, config: tenantConfig },
          logger,
        },
      );

      // 处理 SSE 请求
      if (options?.sse) {
        const encoder = new TextEncoder();
        const stream = new TransformStream();
        const writer = stream.writable.getWriter();

        const send = (data: any) => {
          const message = `${data}`;
          writer.write(encoder.encode(message));
        };

        const response = new Response(stream.readable, {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            Connection: 'keep-alive',
          },
        });

        // @ts-ignore
        ctx.send = send;

        handler(ctx, request);
        logger._end('SSE Started', cc(Date.now() - now));
        return response;
      }

      // 处理普通请求
      const data = await handler(ctx, request);
      logger._end('Success', cc(Date.now() - now));
      return NextResponse.json({ success: true, data });
    } catch (e: any) {
      logger._end('Error', cc(Date.now() - now), e);
      return NextResponse.json({ success: false, code: 500, message: e.message, data: null });
    }
  };
};

const cc = (cost: number) => (cost < 500 ? chalk.green(`${cost}ms`) : chalk.red(`${cost}ms`));

function generateRandomBase36(length: number): string {
  let result = '';
  while (result.length < length) {
    result += Math.random().toString(36).substring(2);
  }
  return result.substring(0, length);
}

/**
 * 每一个用户只有一个租户，就意味着每次登录的token只会对应一个租户
 * 这里做一层简单的缓存，避免频繁远程请求
 */
const TOKEN_TENANT_MAP: Record<string, { id: string; name: string; expired: number }> = {};

const fetchTenant = async (token: string) => {
  // 这里做一层简单的缓存，使得可以跨请求缓存每一个登录态对应的租户信息
  if (TOKEN_TENANT_MAP[token]) {
    if (TOKEN_TENANT_MAP[token].expired > Date.now()) {
      const { id, name } = TOKEN_TENANT_MAP[token];
      return { id, name };
    }
    delete TOKEN_TENANT_MAP[token];
  }

  if (!process.env.APPID || !process.env.APPSECRET || !process.env.APPHOST) {
    throw new Error('Unauthorized');
  }

  const authClient = new AuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  authClient.setAccessToken(token);

  const result = await authClient.getTenantList();
  if (result.statusCode !== 200 || !result.data.length) {
    throw new Error('Unauthorized');
  }
  const data = { id: result.data[0]!.tenantId, name: result.data[0]!.tenantName };
  TOKEN_TENANT_MAP[token] = { ...data, expired: Date.now() + 30000 };
  return data;
};

const fetchTenantSourceConfig = async (tenantId: string) => {
  const sourceConfig = await prisma.source_configs.findUnique({
    where: { tenant_id: tenantId },
  });
  if (!sourceConfig) {
    throw new Error('Unauthorized');
  }
  return sourceConfig;
};
