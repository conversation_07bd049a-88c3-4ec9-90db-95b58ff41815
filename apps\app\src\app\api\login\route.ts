import { AuthenticationClient } from 'authing-node-sdk';
import { NextResponse } from 'next/server';
import { getLoginSessionInfo } from '@/utils/authing';
import * as jose from 'jose';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { account: email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ message: '邮箱和密码不能为空' }, { status: 400 });
    }
    const authenticationClient = new AuthenticationClient({
      appId: process.env.APPID,
      appSecret: process.env.APPSECRET,
      appHost: process.env.APPHOST,
    });

    const result = await authenticationClient.signInByEmailPassword({
      email,
      password,
    });

    if (result.statusCode === 200) {
      const { data: loginData } = result;
      const { id_token } = loginData;
      const idTokenDecoded = jose.decodeJwt(id_token!);

      const signedNewObj = await new jose.SignJWT(idTokenDecoded)
        .setProtectedHeader({ alg: 'HS256' })
        .sign(new TextEncoder().encode(process.env.APPSECRET));
      const newObjDecoded = await getLoginSessionInfo(signedNewObj);
      const response = NextResponse.json({ data: newObjDecoded, code: 200, message: '' });
      if (newObjDecoded) {
        response.cookies.set('token', signedNewObj);
        response.cookies.set('Authorization', signedNewObj);
      }
      return response;
    } else {
      return NextResponse.json(
        { message: result.message || '登录失败', code: result.statusCode },
        { status: result.statusCode },
      );
    }
  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json({ message: '登录失败，请稍后重试', code: 500 }, { status: 500 });
  }
}
