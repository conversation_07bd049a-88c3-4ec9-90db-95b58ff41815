import { feishuRobot } from '@/utils/feishu';
import { prisma } from '@/utils/prisma';
import { materials, source_configs, video_slice_tasks } from '@prisma/client';
import Langfuse from 'langfuse';
import { refundQuota, TaskStatus } from '../utils';
import generationTask from '../generation-task';
type SliceTraceOutput = {
  originVideo: {
    fileId: string;
    fileMediaUrl: string;
    fileAudioId: string;
    fileAudioMediaUrl: string;
    fileCoverUrl: string;
  };
  sliceVideos: {
    fileId: string;
    fileAudioId: string;
    fileCoverUrl: string;
    fileMediaUrl: string;
    fileAudioMediaUrl: string;
  }[];
};

/**
 * 处理进行中的切分任务
 */
const handleProcessingSliceTask = async (params: {
  task: video_slice_tasks;
  originMaterial: materials;
  sourceConfig: source_configs;
}) => {
  const { task, originMaterial, sourceConfig } = params;
  if (task.status !== TaskStatus.PROCESSING) {
    console.log('任务不是处理中状态，任务id：' + task.id);
    return;
  }

  const langfuse = new Langfuse({
    baseUrl: process.env.LANGFUSE_HOST,
    publicKey: sourceConfig.langfuse_public_key,
    secretKey: sourceConfig.langfuse_secret_key,
  });

  const trace = await langfuse.fetchTrace(task.trace_id!);
  const latestObservation = trace.data.observations?.[trace.data.observations.length - 1];
  const latestObservationName = latestObservation?.name || '空';

  // 如果有输出，说明任务已经完成
  if (trace.data.output && latestObservation && latestObservation.level !== 'ERROR') {
    console.log('任务完成', task.id, latestObservationName);
    console.log('任务输出', JSON.stringify(trace.data.output));
    // 如果有输出，说明任务已经完成 更新素材和任务
    // 输出的产物有切分后的视频和音频
    const output = trace.data.output as SliceTraceOutput;
    const splitIds = output.sliceVideos.map((o) => ({ video: o.fileId, audio: o.fileAudioId }));

    await prisma.materials.update({
      where: { id: originMaterial.id },
      data: { vod_audio_file_id: output.originVideo.fileAudioId, split_materials: splitIds },
    });

    await prisma.video_slice_tasks.update({
      where: { id: task.id },
      data: {
        status: trace.data.output ? TaskStatus.SUCCESS : undefined,
        status_desc: latestObservationName,
        split_material_ids: splitIds,
      },
    });
    await onSliceTaskSuccess({ data: { materialId: originMaterial.id } });
    return;
  }

  // 如果有错误信息，说明任务失败
  if (latestObservation?.level === 'ERROR') {
    // 任务未完成
    console.log('任务失败', task.id, latestObservationName, latestObservation.statusMessage);
    const reason = `${latestObservationName} ${latestObservation.statusMessage}`;
    await prisma.video_slice_tasks.update({
      where: { id: task.id },
      data: { status: TaskStatus.FAILED, status_desc: reason },
    });
    await onSliceTaskFailed({ data: { materialId: originMaterial.id, reason: reason } });
    return;
  }

  // 如果超时（> 90min），则认为任务失败，这个超时是从任务创建开始计算的
  if (new Date().getTime() - new Date(task.tmp_created_at).getTime() > 90 * 60 * 1000) {
    console.log('任务超时', task.id, latestObservationName);
    const reason = `${latestObservationName} 任务超时`;
    await prisma.video_slice_tasks.update({
      where: { id: task.id },
      data: { status: TaskStatus.FAILED, status_desc: reason },
    });
    await onSliceTaskFailed({ data: { materialId: originMaterial.id, reason: reason } });
    return;
  }

  // 剩余表示任务未完成
  console.log('任务进行中', task.id, latestObservationName);
  await prisma.video_slice_tasks.update({
    where: { id: task.id },
    data: { status_desc: latestObservationName },
  });
};

/**
 * 生成任务触发器
 * 当一个预切片任务完成时，触发对应等待的生成任务
 * @param ctx
 * @returns
 */
const onSliceTaskSuccess = async (ctx: { data: { materialId: string } }) => {
  try {
    await generationTask.slice.handleProcessing({ materialId: ctx.data.materialId });
  } catch (e: any) {
    const m = `切分任务完成回调失败 ${e.message}, 素材ID: ${ctx.data.materialId}`;
    await feishuRobot.error('切分任务完成回调失败', [m]);
    console.error(m);
  }
};

/**
 * 生成任务触发失败
 * 当一个预切片任务失败时，触发对应等待的生成任务为失败
 * @param ctx
 */
const onSliceTaskFailed = async (ctx: { data: { materialId: string; reason: string } }) => {
  const originMaterial = await prisma.materials.findUnique({ where: { id: ctx.data.materialId } });
  if (!originMaterial) {
    await feishuRobot.error('原始素材不存在', ['通过素材触发失败子任务时', ctx.data.materialId]);
    console.error('原始素材不存在', ctx.data.materialId);
    return;
  }

  // 获取预切片类型的子任务
  const subSliceTasks = await prisma.video_generation_sub_tasks.findMany({
    where: { origin_material_id: originMaterial.id, sub_task_type: 'slice', status: TaskStatus.PENDING },
  });
  console.log(
    '切片后素材存在子任务待执行',
    subSliceTasks.length,
    originMaterial.id,
    subSliceTasks.map((t) => t.id),
  );

  // 将所有的预切分任务设置为失败
  for (const subSliceTask of subSliceTasks) {
    await prisma.video_generation_sub_tasks.update({
      where: { id: subSliceTask.id },
      data: { status: TaskStatus.FAILED, status_desc: `预切分任务失败 ${ctx.data.reason}` },
    });
    const task = await prisma.video_generation_tasks.findUnique({ where: { id: subSliceTask.task_id } });
    if (!task) {
      console.error('等待预切分子任务对应的生成任务不存在', subSliceTask.task_id);
      feishuRobot.error('等待预切分子任务对应的生成任务不存在', [
        '通过素材触发失败子任务时',
        `素材ID: ${ctx.data.materialId}`,
        `子任务ID: ${subSliceTask.task_id}`,
      ]);
      continue;
    }
    // 退点计算 每个预切分任务，退 轮询次数 * prompts数 * 10
    const quota = task.generate_round * (task.method === 'gc_imitate' ? 1 : task.prompts.length) * 10;
    await refundQuota({
      tenantId: originMaterial.tenant_id,
      quota: quota,
      reason: `生成任务失败返点`,
    });
  }
};

export default {
  handleProcessing: handleProcessingSliceTask,
};
