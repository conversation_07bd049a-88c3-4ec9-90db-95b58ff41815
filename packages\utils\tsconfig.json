{"extends": "../../packages/typescript-config/base.json", "compilerOptions": {"target": "ESNext", "lib": ["dom", "esnext"], "types": ["node"], "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "tsup.config.ts"], "exclude": ["node_modules"]}