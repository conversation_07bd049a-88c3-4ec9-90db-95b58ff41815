// src/components/multi-select.tsx

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { CheckIcon, ChevronDown, ChevronUp, XIcon } from 'lucide-react';

import { cn } from '@/utils/cn';
import { Separator } from '@/components/ui/Separator';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/Command';

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
export const multiSelectVariants = cva(
  'mr-2 transition ease-in-out delay-150 hover:translate-y-0 hover:scale-110 duration-300',
  {
    variants: {
      variant: {
        default: 'border-foreground/10 text-foreground bg-card hover:bg-card/80',
        secondary: 'border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        inverted: 'inverted',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Props for MultiSelect component
 */
interface MultiSelectProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof multiSelectVariants> {
  /**
   * An array of option objects to be displayed in the multi-select component.
   * Each option object has a label, value, and an optional icon.
   */
  options: {
    /** The text to display for the option. */
    label: string;
    /** The unique value associated with the option. */
    value: string;
    /** Optional icon component to display alongside the option. */
    icon?: React.ComponentType<{ className?: string }>;
  }[];

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange: (value: string[]) => void;

  /** The default selected values when the component mounts. */
  defaultValue?: string[];

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string;

  /**
   * Animation duration in seconds for the visual effects (e.g., bouncing badges).
   * Optional, defaults to 0 (no animation).
   */
  animation?: number;

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number;

  /**
   * The modality of the popover. When set to true, interaction with outside elements
   * will be disabled and only popover content will be visible to screen readers.
   * Optional, defaults to false.
   */
  modalPopover?: boolean;

  /**
   * If true, renders the multi-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean;

  /**
   * Additional class names to apply custom styles to the multi-select component.
   * Optional, can be used to add custom styles.
   */
  className?: string;
  placeholderTitle?: string;
  test?: boolean;

  /**
   * If true, the component will be read-only and not allow any changes
   * Optional, defaults to false
   */
  disabled?: boolean;
}

export const MultiSelect = React.forwardRef<HTMLButtonElement, MultiSelectProps>(
  (
    {
      options,
      onValueChange,
      variant,
      defaultValue = [],
      placeholder = 'Select options',
      animation = 0,
      maxCount = 3,
      modalPopover = false,
      asChild = false,
      className,
      placeholderTitle,
      test,
      disabled = false,
      ...props
    },
    ref,
  ) => {
    const [selectedValues, setSelectedValues] = React.useState<string[]>(defaultValue);
    const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
    const [isAnimating, setIsAnimating] = React.useState(false);

    // 添加一个 ref 用于获取 CommandList 元素
    const commandListRef = React.useRef<HTMLDivElement>(null);

    // 添加自动滚动到选中项的效果
    React.useEffect(() => {
      if (isPopoverOpen && defaultValue?.length > 0) {
        // 给一个小延时确保 DOM 已经渲染完成
        setTimeout(() => {
          const commandList = commandListRef.current;
          const selectedItem = commandList?.querySelector(`[data-scroll-value="${defaultValue[0]}"]`);
          selectedItem?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }, [isPopoverOpen, defaultValue]);

    const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        setIsPopoverOpen(true);
      } else if (event.key === 'Backspace' && !event.currentTarget.value) {
        const newSelectedValues = [...selectedValues];
        newSelectedValues.pop();
        setSelectedValues(newSelectedValues);
        onValueChange(newSelectedValues);
      }
    };

    const toggleOption = (option: string) => {
      const newSelectedValues = selectedValues.includes(option)
        ? selectedValues.filter((value) => value !== option)
        : [...selectedValues, option];
      setSelectedValues(newSelectedValues);
      onValueChange(newSelectedValues);
    };

    const handleClear = () => {
      setSelectedValues([]);
      onValueChange([]);
    };

    const handleTogglePopover = () => {
      setIsPopoverOpen((prev) => !prev);
    };

    const clearExtraOptions = () => {
      const newSelectedValues = selectedValues.slice(0, maxCount);
      setSelectedValues(newSelectedValues);
      onValueChange(newSelectedValues);
    };

    const toggleAll = () => {
      if (disabled) return;
      if (selectedValues.length === options.length) {
        handleClear();
      } else {
        const allValues = options.map((option) => option.value);
        setSelectedValues(allValues);
        onValueChange(allValues);
      }
    };

    return (
      <div
        className={cn(
          'mr-4 flex h-8 items-center border text-white',
          selectedValues.length > 1 ? 'w-[380px]' : 'w-[290px]',
          test ? 'h-8 rounded border-[rgb(28,42,63)] text-xs' : 'h-10 w-[360px] rounded border-[#363D54] text-sm',
          disabled && 'cursor-not-allowed opacity-70',
        )}
      >
        {test && <div className="w-[80px] border-r border-gray-700 text-center text-white">{placeholderTitle}</div>}
        <Popover open={isPopoverOpen} onOpenChange={disabled ? undefined : setIsPopoverOpen} modal={modalPopover}>
          <PopoverTrigger asChild>
            <Button
              ref={ref}
              {...props}
              onClick={handleTogglePopover}
              className={cn(
                'flex w-[204px] items-center justify-between border-none bg-inherit p-1 hover:bg-inherit [&_svg]:pointer-events-auto',
                className,
              )}
            >
              {selectedValues.length > 0 ? (
                <div className="flex h-8 w-full items-center justify-between">
                  <div className="flex flex-nowrap items-center">
                    {selectedValues.slice(0, maxCount).map((value) => {
                      const option = options.find((o) => o.value === value);
                      return (
                        <Badge
                          key={value}
                          className={cn(
                            'rounded bg-[#CCDDFF0D] text-[#9FA4B2] hover:bg-transparent',
                            isAnimating ? 'animate-bounce' : '',
                            multiSelectVariants({ variant }),
                            'max-w-[110px] rounded bg-[#CCDDFF0D] text-[#9FA4B2]',
                            test ? 'h-6 text-xs' : 'text-sm',
                          )}
                          style={{ animationDuration: `${animation}s` }}
                        >
                          <span className="truncate">{option?.label}</span>
                          <div className="max-w-[20px]">
                            <XIcon
                              className={cn(test ? 'mx-1 h-4 w-3' : 'w-4', 'mx-2 cursor-pointer text-[#9FA4B2]')}
                              onClick={(event) => {
                                event.stopPropagation();
                                toggleOption(value);
                              }}
                            />
                          </div>
                        </Badge>
                      );
                    })}
                    {selectedValues.length > maxCount && (
                      <Badge
                        className={cn(
                          'text-foreground rounded bg-[#CCDDFF0D] text-[#9FA4B2] hover:bg-transparent',
                          isAnimating ? 'animate-bounce' : '',
                          multiSelectVariants({ variant }),
                          'max-w-[120px] truncate',
                          test ? 'text-xs' : 'text-sm',
                        )}
                        style={{ animationDuration: `${animation}s` }}
                      >
                        {`已选 ${selectedValues.length}`}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    {!disabled && (
                      <>
                        <XIcon
                          className="mx-2 h-4 cursor-pointer text-[#9FA4B2]"
                          onClick={(event) => {
                            event.stopPropagation();
                            handleClear();
                          }}
                        />
                        <Separator orientation="vertical" className="flex h-full min-h-6" />
                        {isPopoverOpen ? (
                          <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                        ) : (
                          <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                        )}
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div className="mx-auto flex w-full items-center justify-between">
                  <span className={cn('mx-3 text-[#9FA4B2]', test ? 'text-xs' : 'text-sm')}>{placeholder}</span>
                  {!disabled && isPopoverOpen ? (
                    <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                  ) : (
                    <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                  )}
                </div>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[350px] p-0" align="start" onEscapeKeyDown={() => setIsPopoverOpen(false)}>
            <Command>
              <CommandInput
                placeholder="搜索选项"
                className={cn('placeholder:text-gray-500', test ? 'text-xs' : 'text-sm')}
                onKeyDown={handleInputKeyDown}
              />
              <CommandList className="overflow-y-auto" ref={commandListRef}>
                <CommandEmpty>暂无数据</CommandEmpty>
                <CommandGroup>
                  <CommandItem disabled={disabled} key="all" onSelect={toggleAll} className="cursor-pointer px-5">
                    <div className={cn('flex w-full items-center justify-between', test ? 'text-xs' : 'text-sm')}>
                      <div className="text-left text-[#9FA4B2]">全部选项({options.length})</div>
                      <div className="flex justify-end">
                        <div
                          className={cn(
                            'border-primary',
                            selectedValues.length === options?.length
                              ? 'text-[#00E1FF]'
                              : 'text-[#00E1FF] opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <span>全选</span>
                        </div>
                      </div>
                    </div>
                  </CommandItem>
                  {options?.map((option) => {
                    const isSelected = selectedValues.includes(option.value);
                    return (
                      <CommandItem
                        disabled={disabled}
                        data-scroll-value={option.value}
                        key={option.value}
                        onSelect={() => toggleOption(option.value)}
                        className={cn('cursor-pointer px-5 py-2', test ? 'text-xs' : 'text-sm')}
                      >
                        <div className="flex w-full items-center justify-between">
                          <div>
                            {option.icon && <option.icon className="mr-2 h-4 w-4 text-[#9FA4B2]" />}
                            <div>{option.label.length > 30 ? option.label.slice(0, 30) + '...' : option.label}</div>
                            {test && (
                              <div className="mt-1 text-[#9FA4B2]">
                                {option.value.length > 30 ? option.value.slice(0, 30) + '...' : option.value}
                              </div>
                            )}
                          </div>
                          <div
                            className={cn(
                              'border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border',
                              isSelected ? 'bg-primary text-primary-foreground' : 'opacity-50 [&_svg]:invisible',
                            )}
                          >
                            <CheckIcon className="h-4 w-4" />
                          </div>
                        </div>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup>
                  <div className="flex items-center justify-between">
                    {selectedValues.length > 0 && (
                      <>
                        <CommandItem
                          disabled={disabled}
                          onSelect={handleClear}
                          className="flex-1 cursor-pointer justify-center"
                        >
                          清空
                        </CommandItem>
                        <Separator orientation="vertical" className="flex h-full min-h-6" />
                      </>
                    )}
                    <CommandItem
                      onSelect={() => setIsPopoverOpen(false)}
                      className="max-w-full flex-1 cursor-pointer justify-center"
                    >
                      取消
                    </CommandItem>
                  </div>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    );
  },
);

MultiSelect.displayName = 'MultiSelect';
