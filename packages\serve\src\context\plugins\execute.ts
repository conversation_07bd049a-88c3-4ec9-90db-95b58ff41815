import { ActionContextPluginLoader, ServerAction } from '../../types';
import chalk from 'chalk';

type ExecutePlugin = <R, P>(action: ServerAction<R, P>, data: P) => Promise<R>;

const executePlugin: ActionContextPluginLoader<'execute', ExecutePlugin> = (context) => {
  return {
    name: 'execute',
    plugin: async (action, data) => {
      context.logger._push(action.name);
      const now = Date.now();
      context.logger.log(chalk.cyan(`Start ${JSON.stringify(data)}`));
      const originData = context.data;
      context.data = data as any;
      const res = await action(context as any);
      context.logger.log(chalk.cyan(`End ${Date.now() - now}ms`));
      context.logger._pop();
      context.data = originData;
      return res;
    },
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 执行一个 action
     */
    execute: ExecutePlugin;
  }
}

export default executePlugin;
