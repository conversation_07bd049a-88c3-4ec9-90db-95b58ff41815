'use server';

import { getQuota, updateQuota } from '@/services/domains/pricing_plans';
import { getCache, setCache } from '@/utils/cache';
import { ActionContext, server } from '@roasmax/serve';
import { UpdateQuotaInput } from '@/types/wallet';
/**
 * 获取钱包
 */
export const getQuotas = server('获取钱包', async (ctx) => {
  const now = new Date();
  const res = await ctx.execute(getQuota, {});

  if (!res.grade && !res.latestExpired) {
    throw new Error('未能获取有效的配额信息');
  }

  const plan = (res.grade || res.latestExpired)!;
  const isExpired = new Date(plan.end_time) < now;

  return {
    quota: isExpired ? 0 : res.quota,
    grade: {
      ...plan,
      plan_name: isExpired ? '已过期' : plan.plan_name,
    },
  };
});
/**
 * 获取钱包变更记录
 * @param {Object} ctx - 上下文对象
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页记录数
 */
export const getQuotaChangeLogs = server(
  '获取钱包变更记录',
  async (ctx: ActionContext<{ pagination: { page: number; limit: number; type?: string } }>) => {
    const { pagination } = ctx.data;

    if (!ctx.tenant.id) {
      throw new Error('租户id不能为空');
    }

    const cacheKey = `quotaChangeLogs:${ctx.tenant.id}:${pagination.page}:${pagination.limit}:${pagination.type}`;
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    let changeTypeCondition;
    switch (pagination.type) {
      case 'obtain':
        changeTypeCondition = { in: ['CHARGE', 'REFUND', 'SET_MENU_IN', 'GIVE'] };
        break;
      case 'consume':
        changeTypeCondition = { notIn: ['CHARGE', 'REFUND', 'SET_MENU_IN', 'GIVE'] };
        break;
      case 'all':
        changeTypeCondition = undefined;
        break;
      default:
        throw new Error('无效的变更类型');
    }

    const whereCondition: any = {
      tenant_id: ctx.tenant.id,
    };

    if (changeTypeCondition) {
      whereCondition.change_type = changeTypeCondition;
    }

    const [logs, totalCount] = await Promise.all([
      ctx.db.quota_changelogs.findMany({
        where: whereCondition,
        orderBy: {
          tmp_created_at: 'desc',
        },
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit,
      }),
      ctx.db.quota_changelogs.count({
        where: {
          tenant_id: ctx.tenant.id,
        },
      }),
    ]);

    const result = {
      data: logs,
      current: pagination.page,
      pageSize: pagination.limit,
      total: totalCount,
    };

    setCache(cacheKey, result);

    return result;
  },
);

/**
 * 更新钱包额度
 */
export const updateQuotas = server('更新钱包额度', async (ctx: ActionContext<UpdateQuotaInput>) => {
  return await ctx.execute(updateQuota, ctx.data);
});
