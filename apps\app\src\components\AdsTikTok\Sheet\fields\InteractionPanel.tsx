import { Checkbox, Label, RadioGroup, RadioGroupItem } from '@/components/ui';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form';
import { FormTreeSelect } from '@/components/ui/FormTreeSelect';
import { SearchableSelect } from '@/components/ui/SearchableSelect';
import { cn } from '@/utils/cn';
import { Plus } from 'lucide-react';
import { Control } from 'react-hook-form';

interface InteractionPanelProps {
  type: string;
  title: string;
  description: string;
  isExpanded: boolean;
  onToggle: () => void;
  panelType: 'video' | 'creator' | 'topic';
  control: Control<any>;
  advertiser?: any;
  videoInteraction?: any[];
  creatorInteraction?: any[];
  editingGroup?: any;
}

export const InteractionPanel = ({
  type,
  title,
  description,
  isExpanded,
  onToggle,
  panelType,
  control,
  advertiser,
  videoInteraction,
  creatorInteraction,
  editingGroup,
}: InteractionPanelProps) => {
  const videoActions = editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'VIDEO_RELATED');

  const creatorActions = editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'CREATOR_RELATED');

  const hashtagActions = editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'HASHTAG_RELATED');

  const renderVideoContent = () => (
    <>
      <FormField
        control={control}
        name="video_user_actions"
        defaultValue={videoActions?.videoUserActions ?? []}
        render={({ field }) => (
          <FormItem className="flex items-end gap-3">
            <FormLabel className="text-sm">定义他们与视频的互动方式: </FormLabel>
            <FormControl>
              <div className="flex flex-wrap gap-4">
                {[
                  { value: 'WATCHED_TO_END', label: '完播' },
                  { value: 'LIKED', label: '点赞' },
                  { value: 'COMMENTED', label: '评论' },
                  { value: 'SHARED', label: '分享' },
                ].map((action) => (
                  <div key={action.value} className="flex items-center space-x-1">
                    <Checkbox
                      className="border-[#9FA4B2]"
                      checked={field.value?.includes(action.value)}
                      onCheckedChange={(checked) => {
                        const currentValue = field.value || [];
                        const newValue = checked
                          ? [...currentValue, action.value]
                          : currentValue.filter((v: string) => v !== action.value);
                        field.onChange(newValue);
                      }}
                      disabled={type === 'template'}
                    />
                    <Label className="text-sm">{action.label}</Label>
                  </div>
                ))}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormTreeSelect
        name="action_category_ids1"
        control={control}
        label="用户互动的视频类别:"
        options={
          videoInteraction?.map((item) => ({
            label: item.label,
            value: item.key,
            children: item.children?.map((child: any) => ({
              label: child.label,
              value: child.key,
            })),
          })) || []
        }
        test={false}
        defaultValue={
          editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'VIDEO_RELATED')?.actionCategoryIds || []
        }
        placeholder="请选择视频类别"
        disabled={type === 'template'}
      />
      <FormField
        control={control}
        name="A_few_days_of_behavior"
        defaultValue={videoActions?.actionPeriod}
        render={({ field }) => (
          <FormItem className="mt-4 flex items-center gap-3">
            <FormLabel className="text-sm text-white">选择n天内发生的行为:</FormLabel>
            <FormControl className="gap-3">
              <RadioGroup
                onValueChange={(value) => field.onChange(Number(value))}
                value={field.value?.toString()}
                className="flex space-x-1"
                disabled={type === 'template'}
              >
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="7" id="7days" />
                  <Label htmlFor="7days" className="text-sm">
                    7天
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="15" id="15days" />
                  <Label htmlFor="15days" className="text-sm">
                    15天
                  </Label>
                </div>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );

  const renderCreatorContent = () => (
    <>
      <FormField
        control={control}
        name="creator_user_actions"
        defaultValue={creatorActions?.videoUserActions ?? []}
        render={({ field }) => (
          <FormItem className="flex items-end gap-3">
            <FormLabel>创作者互动行为:</FormLabel>
            <FormControl>
              <div className="flex flex-wrap gap-4">
                {[
                  { value: 'FOLLOWING', label: '关注' },
                  { value: 'VIEW_HOMEPAGE', label: '浏览主页' },
                ].map((action) => (
                  <div key={action.value} className="flex items-center space-x-1">
                    <Checkbox
                      className="border-[#9FA4B2]"
                      checked={field.value?.includes(action.value)}
                      onCheckedChange={(checked) => {
                        const currentValue = field.value || [];
                        const newValue = checked
                          ? [...currentValue, action.value]
                          : currentValue.filter((v: string) => v !== action.value);
                        field.onChange(newValue);
                      }}
                      disabled={type === 'template'}
                    />
                    <Label>{action.label}</Label>
                  </div>
                ))}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormTreeSelect
        name="action_category_ids2"
        control={control}
        label="用户互动的创作者类别:"
        options={
          creatorInteraction?.map((item) => ({
            label: item.label,
            value: item.key,
            children: item.children?.map((child: any) => ({
              label: child.label,
              value: child.key,
            })),
          })) || []
        }
        test={false}
        defaultValue={
          editingGroup?.jsonDate?.actions?.find((a: any) => a.actionScene === 'CREATOR_RELATED')?.actionCategoryIds ??
          []
        }
        placeholder="请选择创作者类别"
        disabled={type === 'template'}
      />
    </>
  );

  const renderTopicContent = () => (
    <>
      <FormField
        control={control}
        name="hashtag_user_actions"
        defaultValue={hashtagActions?.videoUserActions ?? []}
        render={({ field }) => (
          <FormItem className="flex items-end gap-3">
            <FormLabel>话题互动行为:</FormLabel>
            <FormControl>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center space-x-1">
                  <Checkbox
                    className="border-[#9FA4B2]"
                    checked={field.value?.includes('VIEW_HASHTAG')}
                    onCheckedChange={(checked) => {
                      const currentValue = field.value || [];
                      const newValue = checked
                        ? [...currentValue, 'VIEW_HASHTAG']
                        : currentValue.filter((v: string) => v !== 'VIEW_HASHTAG');
                      field.onChange(newValue);
                    }}
                    disabled={type === 'template'}
                  />
                  <Label>浏览话题标签</Label>
                </div>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="action_category_ids3"
        defaultValue={hashtagActions?.actionCategoryIds ?? []}
        render={({ field }) => (
          <FormItem className="mt-4 flex items-end">
            <FormLabel className="text-xs"></FormLabel>
            <FormControl>
              <SearchableSelect
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                }}
                advertiser_id={advertiser?.advertiserId}
                multiple={true}
                disabled={type === 'template'}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );

  const renderContent = () => {
    switch (panelType) {
      case 'video':
        return renderVideoContent();
      case 'creator':
        return renderCreatorContent();
      case 'topic':
        return renderTopicContent();
      default:
        return null;
    }
  };

  return (
    <div
      className={`mt-4 w-[100%] rounded-lg border border-solid border-[#363D54] px-4 py-5 ${
        isExpanded ? ' ' : 'border-[#333]'
      }`}
    >
      <div className="cursor-pointer" onClick={onToggle}>
        <div className="mb-[10px] flex items-center justify-between">
          <div className="flex items-center">
            <span className="mr-2">
              {isExpanded ? <div className="ml-[2px]">▼</div> : <Plus className="w-4 flex-shrink-0" />}
            </span>
            <span className="text-sm">{title}</span>
          </div>
        </div>
        <div className="text-xs text-[#9FA4B2]">{description}</div>
      </div>
      <div style={{ display: isExpanded ? 'block ' : 'none' }} className={cn(isExpanded ? 'mt-5 text-sm' : '')}>
        {renderContent()}
      </div>
    </div>
  );
};
