'use server';
import { ActionContext, server } from '@roasmax/serve';
import { v4 as uuidv4 } from 'uuid';
import { addDays } from 'date-fns';

/**
 * 保存产品分析记录
 */
export const saveProductAnalysisRecord = server(
  '保存产品分析记录',
  async (
    ctx: ActionContext<{
      productUrl: string;
      analysisResult: {
        product_title?: string;
        product_analysis?: string;
        video_ids?: string[];
        template_info?: any[];
        price?: string;
        description?: string;
        suggestion?: string;
        picturesUrl?: string[];
      };
      completedSteps: number;
      status: string;
      errorMessage?: string | null;
      playbackSteps: any[];
    }>,
  ) => {
    const shareId = uuidv4();
    const shareExpiredAt = addDays(new Date(), 360); // 分享链接360天后过期

    const record = await ctx.db.product_analysis_records.create({
      data: {
        id: uuidv4(),
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        product_url: ctx.data.productUrl,
        share_id: shareId,
        share_expired_at: shareExpiredAt,
        status: ctx.data.status,
        error_message: ctx.data.errorMessage || null,
        completed_steps: ctx.data.completedSteps,
        total_steps: 4, // 固定为4个步骤
        product_title: ctx.data.analysisResult.product_title || null,
        product_analysis: ctx.data.analysisResult.product_analysis || null,
        video_ids: ctx.data.analysisResult.video_ids || [],
        template_info: ctx.data.analysisResult.template_info || [],
        price: ctx.data.analysisResult.price || null,
        description: ctx.data.analysisResult.description || null,
        suggestion: ctx.data.analysisResult.suggestion || null,
        playback_steps: ctx.data.playbackSteps,
        picturesUrl: ctx.data.analysisResult.picturesUrl || [],
      },
    });

    return {
      success: true,
      shareId,
      record,
    };
  },
);

/**
 * 获取产品分析记录列表
 */
export const getProductAnalysisRecords = server(
  '获取产品分析记录列表',
  async (
    ctx: ActionContext<{
      page?: number;
      pageSize?: number;
      status?: string;
      keyword?: string;
    }>,
  ) => {
    const page = ctx.data.page || 1;
    const pageSize = ctx.data.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = {
      tenant_id: ctx.tenant.id,
      user_id: ctx.user.id,
      tmp_deleted_at: null,
      ...(ctx.data.status ? { status: ctx.data.status } : {}),
      ...(ctx.data.keyword
        ? {
            OR: [{ product_title: { contains: ctx.data.keyword } }, { product_url: { contains: ctx.data.keyword } }],
          }
        : {}),
    };

    // 查询总数
    const total = await ctx.db.product_analysis_records.count({ where });

    // 查询记录
    const records = await ctx.db.product_analysis_records.findMany({
      where,
      orderBy: { tmp_created_at: 'desc' },
      skip,
      take: pageSize,
      select: {
        id: true,
        product_title: true,
        product_url: true,
        status: true,
        completed_steps: true,
        total_steps: true,
        tmp_created_at: true,
        share_id: true,
        share_expired_at: true,
      },
    });

    return {
      records,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  },
);

/**
 * 获取产品分析记录详情
 */
export const getProductAnalysisRecord = server('获取产品分析记录详情', async (ctx: ActionContext<{ id: string }>) => {
  const record = await ctx.db.product_analysis_records.findFirst({
    where: {
      id: ctx.data.id,
      tenant_id: ctx.tenant.id,
      user_id: ctx.user.id,
      tmp_deleted_at: null,
    },
  });

  if (!record) {
    return {
      success: false,
      message: '记录不存在',
    };
  }

  return {
    success: true,
    record,
  };
});

/**
 * 通过分享ID获取产品分析记录
 */
export const getProductAnalysisByShareId = server(
  '通过分享ID获取产品分析记录',
  async (ctx: ActionContext<{ shareId: string }>) => {
    const record = await ctx.db.product_analysis_records.findFirst({
      where: {
        share_id: ctx.data.shareId,
        tmp_deleted_at: null,
        share_expired_at: {
          gt: new Date(), // 确保分享链接未过期
        },
      },
    });

    if (!record) {
      return {
        success: false,
        message: '分享链接不存在或已过期',
      };
    }

    return {
      success: true,
      record,
    };
  },
);

/**
 * 更新产品分析记录
 */
export const updateProductAnalysisRecord = server(
  '更新产品分析记录',
  async (
    ctx: ActionContext<{
      id: string;
      status?: string;
      errorMessage?: string | null;
      completedSteps?: number;
      analysisResult?: {
        product_title?: string;
        product_analysis?: string;
        video_ids?: string[];
        template_info?: any[];
        price?: string;
        description?: string;
        suggestion?: string;
      };
      playbackSteps?: any[];
      playbackVersion?: string;
    }>,
  ) => {
    // 检查记录是否存在
    const existingRecord = await ctx.db.product_analysis_records.findFirst({
      where: {
        id: ctx.data.id,
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        tmp_deleted_at: null,
      },
    });

    if (!existingRecord) {
      return {
        success: false,
        message: '记录不存在',
      };
    }

    // 构建更新数据
    const updateData: any = {};

    if (ctx.data.status !== undefined) {
      updateData.status = ctx.data.status;
    }

    if (ctx.data.errorMessage !== undefined) {
      updateData.error_message = ctx.data.errorMessage;
    }

    if (ctx.data.completedSteps !== undefined) {
      updateData.completed_steps = ctx.data.completedSteps;
    }

    if (ctx.data.analysisResult) {
      if (ctx.data.analysisResult.product_title !== undefined) {
        updateData.product_title = ctx.data.analysisResult.product_title;
      }

      if (ctx.data.analysisResult.product_analysis !== undefined) {
        updateData.product_analysis = ctx.data.analysisResult.product_analysis;
      }

      if (ctx.data.analysisResult.video_ids !== undefined) {
        updateData.video_ids = ctx.data.analysisResult.video_ids;
      }

      if (ctx.data.analysisResult.template_info !== undefined) {
        updateData.template_info = ctx.data.analysisResult.template_info;
      }

      if (ctx.data.analysisResult.price !== undefined) {
        updateData.price = ctx.data.analysisResult.price;
      }

      if (ctx.data.analysisResult.description !== undefined) {
        updateData.description = ctx.data.analysisResult.description;
      }

      if (ctx.data.analysisResult.suggestion !== undefined) {
        updateData.suggestion = ctx.data.analysisResult.suggestion;
      }
    }

    if (ctx.data.playbackSteps !== undefined) {
      updateData.playback_steps = ctx.data.playbackSteps;
    }

    if (ctx.data.playbackVersion !== undefined) {
      updateData.playback_version = ctx.data.playbackVersion;
    }

    // 执行更新
    const updatedRecord = await ctx.db.product_analysis_records.update({
      where: { id: ctx.data.id },
      data: updateData,
    });

    return {
      success: true,
      record: updatedRecord,
    };
  },
);

/**
 * 删除产品分析记录
 */
export const deleteProductAnalysisRecord = server('删除产品分析记录', async (ctx: ActionContext<{ id: string }>) => {
  // 检查记录是否存在
  const existingRecord = await ctx.db.product_analysis_records.findFirst({
    where: {
      id: ctx.data.id,
      tenant_id: ctx.tenant.id,
      user_id: ctx.user.id,
      tmp_deleted_at: null,
    },
  });

  if (!existingRecord) {
    return {
      success: false,
      message: '记录不存在',
    };
  }

  // 软删除记录
  await ctx.db.product_analysis_records.update({
    where: { id: ctx.data.id },
    data: { tmp_deleted_at: new Date() },
  });

  return {
    success: true,
  };
});

/**
 * 批量删除产品分析记录
 */
export const batchDeleteProductAnalysisRecords = server(
  '批量删除产品分析记录',
  async (ctx: ActionContext<{ ids: string[] }>) => {
    // 批量软删除记录
    await ctx.db.product_analysis_records.updateMany({
      where: {
        id: { in: ctx.data.ids },
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        tmp_deleted_at: null,
      },
      data: { tmp_deleted_at: new Date() },
    });

    return {
      success: true,
    };
  },
);

/**
 * 刷新分享链接
 */
export const refreshShareLink = server(
  '刷新分享链接',
  async (ctx: ActionContext<{ id: string; expirationDays?: number }>) => {
    const expirationDays = ctx.data.expirationDays || 7;
    const shareId = uuidv4();
    const shareExpiredAt = addDays(new Date(), expirationDays);

    // 检查记录是否存在
    const existingRecord = await ctx.db.product_analysis_records.findFirst({
      where: {
        id: ctx.data.id,
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        tmp_deleted_at: null,
      },
    });

    if (!existingRecord) {
      return {
        success: false,
        message: '记录不存在',
      };
    }

    // 更新分享ID和过期时间
    const updatedRecord = await ctx.db.product_analysis_records.update({
      where: { id: ctx.data.id },
      data: {
        share_id: shareId,
        share_expired_at: shareExpiredAt,
      },
    });

    return {
      success: true,
      shareId,
      shareExpiredAt,
      record: updatedRecord,
    };
  },
);
