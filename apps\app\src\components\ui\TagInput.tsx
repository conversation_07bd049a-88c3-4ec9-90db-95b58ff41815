import * as React from 'react';
import { X } from 'lucide-react';
import { cn } from '@/utils/cn';

interface Tag {
  id: string;
  label: string;
}

interface TagInputProps {
  value: Tag[];
  onChange: (tags: Tag[]) => void;
  suggestions?: Tag[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function TagInput({
  value = [],
  onChange,
  suggestions = [],
  placeholder = '输入标签...',
  className,
  disabled = false,
}: TagInputProps) {
  const [inputValue, setInputValue] = React.useState('');
  const [isOpen, setIsOpen] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 添加点击外部关闭功能
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 过滤已选择的标签
  const getFilteredSuggestions = React.useCallback(
    (searchValue: string) => {
      return suggestions.filter(
        (tag) => tag.label.toLowerCase().includes(searchValue.toLowerCase()) && !value.some((t) => t.id === tag.id),
      );
    },
    [suggestions, value],
  );

  const [filteredSuggestions, setFilteredSuggestions] = React.useState<Tag[]>(getFilteredSuggestions(''));

  // 当value或suggestions变化时更新过滤后的建议
  React.useEffect(() => {
    setFilteredSuggestions(getFilteredSuggestions(inputValue));
  }, [value, suggestions, getFilteredSuggestions, inputValue]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setIsOpen(true);
    setFilteredSuggestions(getFilteredSuggestions(newValue));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue) {
      e.preventDefault();
      const trimmedValue = inputValue.trim();
      addTag({
        id: trimmedValue,
        label: trimmedValue,
      });
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      const lastTag = value[value.length - 1];
      if (lastTag) {
        removeTag(lastTag.id);
      }
    }
  };

  const addTag = (tag: Tag) => {
    if (!value.some((t) => t.id === tag.id)) {
      onChange([...value, tag]);
      setInputValue('');
      setIsOpen(false);
    }
  };

  const removeTag = (tagId: string) => {
    onChange(value.filter((tag) => tag.id !== tagId));
  };

  return (
    <div className="relative" ref={containerRef}>
      <div
        className={cn(
          'bg-background flex min-h-10 w-full flex-wrap items-center gap-2 rounded-md border-none px-3 py-2 outline-none',
          className,
        )}
        onClick={() => inputRef.current?.focus()}
      >
        {value.map((tag) => (
          <span
            key={tag.id}
            className="text-secondary-foreground flex items-center gap-1 rounded-md bg-[#CCDDFF1A] px-2 py-1 text-sm"
          >
            {tag.label}
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                removeTag(tag.id);
              }}
              className="hover:text-destructive focus:text-destructive"
            >
              <X className="h-3 w-3" />
            </button>
          </span>
        ))}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="flex-1 bg-transparent text-sm outline-none placeholder:font-normal placeholder:text-[#9FA4B2]"
          disabled={disabled}
        />
      </div>

      {isOpen && filteredSuggestions.length > 0 && (
        <div className="bg-popover text-popover-foreground absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md p-1 shadow-md">
          {filteredSuggestions.map((suggestion) => (
            <button
              key={suggestion.id}
              className="hover:bg-accent hover:text-accent-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none"
              onClick={() => addTag(suggestion)}
            >
              {suggestion.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
