# Roasmax - 数据库设计与迁移准备

## 📋 任务概述

**任务ID**: k5VRoAih3rtz9uKm38wmHP  
**任务名称**: 数据库设计与迁移准备  
**优先级**: 高  
**预估工时**: 2-3天  
**前置依赖**: 现状分析与资产盘点  

## 🎯 任务目标

设计本地认证系统的数据库结构，准备完整的数据迁移脚本，确保从 Authing 到本地系统的平滑过渡。

## 📊 详细任务分解

### 1. 数据库结构设计

#### 1.1 扩展现有 members 表
```sql
-- 添加本地认证相关字段
ALTER TABLE members ADD COLUMN password_hash VARCHAR(255) NULL COMMENT '密码哈希值';
ALTER TABLE members ADD COLUMN salt VARCHAR(255) NULL COMMENT '密码盐值';
ALTER TABLE members ADD COLUMN is_migrated BOOLEAN DEFAULT FALSE COMMENT '是否已迁移';
ALTER TABLE members ADD COLUMN password_reset_required BOOLEAN DEFAULT FALSE COMMENT '是否需要重置密码';
ALTER TABLE members ADD COLUMN migration_date DATETIME NULL COMMENT '迁移时间';
ALTER TABLE members ADD COLUMN authing_user_id VARCHAR(255) NULL COMMENT '原 Authing 用户ID';
ALTER TABLE members ADD COLUMN last_login_at DATETIME NULL COMMENT '最后登录时间';
ALTER TABLE members ADD COLUMN login_attempts INT DEFAULT 0 COMMENT '登录尝试次数';
ALTER TABLE members ADD COLUMN locked_until DATETIME NULL COMMENT '锁定到期时间';
ALTER TABLE members ADD COLUMN email_verified BOOLEAN DEFAULT TRUE COMMENT '邮箱是否已验证';
```

#### 1.2 创建角色管理表
```sql
-- 角色定义表
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY COMMENT '角色ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  code VARCHAR(255) NOT NULL COMMENT '角色代码',
  name VARCHAR(255) NOT NULL COMMENT '角色名称',
  description TEXT COMMENT '角色描述',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted_at DATETIME NULL COMMENT '删除时间',
  UNIQUE KEY unique_tenant_code (tenant_id, code),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_code (code)
) COMMENT='角色定义表';
```

#### 1.3 创建用户角色关联表
```sql
-- 用户角色关联表
CREATE TABLE user_roles (
  id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
  role_code VARCHAR(255) NOT NULL COMMENT '角色代码',
  assigned_by VARCHAR(255) NULL COMMENT '分配者ID',
  assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  expires_at DATETIME NULL COMMENT '过期时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY unique_user_role (tenant_id, user_id, role_code),
  INDEX idx_tenant_user (tenant_id, user_id),
  INDEX idx_role_code (role_code)
) COMMENT='用户角色关联表';
```

#### 1.4 创建权限资源表
```sql
-- 权限资源表
CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY COMMENT '权限ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  role_code VARCHAR(255) NOT NULL COMMENT '角色代码',
  resource_type ENUM('MENU', 'API', 'DATA') NOT NULL COMMENT '资源类型',
  resource_code VARCHAR(255) NOT NULL COMMENT '资源代码',
  actions JSON COMMENT '允许的操作',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_tenant_role (tenant_id, role_code),
  INDEX idx_resource (resource_type, resource_code)
) COMMENT='权限资源表';
```

#### 1.5 创建会话管理表
```sql
-- 会话管理表
CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY COMMENT '会话ID',
  user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  token_hash VARCHAR(255) NOT NULL COMMENT 'Token哈希值',
  device_info JSON COMMENT '设备信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  last_active_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  expires_at DATETIME NOT NULL COMMENT '过期时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
  INDEX idx_user_id (user_id),
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires_at (expires_at)
) COMMENT='用户会话表';
```

### 2. 数据迁移脚本设计

#### 2.1 Authing 数据导出脚本
```typescript
// scripts/export-authing-data.ts
interface AuthingExportData {
  users: AuthingUser[];
  roles: AuthingRole[];
  userRoles: AuthingUserRole[];
  permissions: AuthingPermission[];
}

async function exportAuthingData(): Promise<AuthingExportData> {
  // 导出用户数据
  // 导出角色数据
  // 导出用户角色关系
  // 导出权限配置
}
```

#### 2.2 数据清洗和转换脚本
```typescript
// scripts/transform-data.ts
interface TransformConfig {
  userMapping: Record<string, string>;
  roleMapping: Record<string, string>;
  permissionMapping: Record<string, string>;
}

async function transformAuthingData(
  authingData: AuthingExportData,
  config: TransformConfig
): Promise<LocalAuthData> {
  // 用户数据转换
  // 角色数据转换
  // 权限数据转换
  // 数据验证
}
```

#### 2.3 数据导入脚本
```typescript
// scripts/import-local-data.ts
async function importLocalData(localData: LocalAuthData): Promise<void> {
  // 批量导入用户数据
  // 批量导入角色数据
  // 批量导入用户角色关系
  // 批量导入权限配置
  // 数据完整性验证
}
```

### 3. 数据库索引优化

#### 3.1 性能关键索引
```sql
-- members 表索引优化
CREATE INDEX idx_members_email_tenant ON members(email, tenant_id);
CREATE INDEX idx_members_authing_user_id ON members(authing_user_id);
CREATE INDEX idx_members_migration_status ON members(is_migrated, password_reset_required);
CREATE INDEX idx_members_login_status ON members(last_login_at, login_attempts);

-- 复合索引优化
CREATE INDEX idx_user_roles_lookup ON user_roles(tenant_id, user_id, role_code);
CREATE INDEX idx_permissions_lookup ON permissions(tenant_id, role_code, resource_type);
```

#### 3.2 查询性能优化
```sql
-- 分区表设计（如果数据量大）
ALTER TABLE user_sessions PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
  PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
  PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
  -- 更多分区...
);
```

### 4. 数据备份和恢复策略

#### 4.1 备份脚本
```bash
#!/bin/bash
# scripts/backup-database.sh

BACKUP_DIR="/backup/roasmax"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="roasmax"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump --single-transaction --routines --triggers \
  $DB_NAME > $BACKUP_DIR/roasmax_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/roasmax_backup_$DATE.sql

echo "Database backup completed: roasmax_backup_$DATE.sql.gz"
```

#### 4.2 恢复脚本
```bash
#!/bin/bash
# scripts/restore-database.sh

BACKUP_FILE=$1
DB_NAME="roasmax"

if [ -z "$BACKUP_FILE" ]; then
  echo "Usage: $0 <backup_file>"
  exit 1
fi

# 解压备份文件
gunzip -c $BACKUP_FILE | mysql $DB_NAME

echo "Database restore completed from: $BACKUP_FILE"
```

## 🔧 Prisma Schema 更新

### 1. 更新 schema.prisma
```prisma
// 扩展 members 模型
model members {
  id                     String    @id @default(uuid())
  tmp_created_at         DateTime  @default(now())
  tmp_updated_at         DateTime  @updatedAt
  tmp_deleted_at         DateTime?
  tenant_id              String
  user_id                String
  user_status            String
  nickname               String
  email                  String
  account                String
  admin                  Int
  password               String
  
  // 新增字段
  password_hash          String?
  salt                   String?
  is_migrated            Boolean   @default(false)
  password_reset_required Boolean  @default(false)
  migration_date         DateTime?
  authing_user_id        String?
  last_login_at          DateTime?
  login_attempts         Int       @default(0)
  locked_until           DateTime?
  email_verified         Boolean   @default(true)

  @@unique([tenant_id, user_id])
  @@index([email, tenant_id])
  @@index([authing_user_id])
}

// 新增角色模型
model roles {
  id          String    @id @default(uuid())
  tenant_id   String
  code        String
  name        String
  description String?
  is_system   Boolean   @default(false)
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  deleted_at  DateTime?

  @@unique([tenant_id, code])
  @@index([tenant_id])
}
```

## ✅ 验收标准

1. **数据库结构完整性**
   - [ ] 所有新表创建成功
   - [ ] 现有表字段扩展完成
   - [ ] 索引创建正确
   - [ ] 约束设置合理

2. **迁移脚本可用性**
   - [ ] 数据导出脚本测试通过
   - [ ] 数据转换脚本验证正确
   - [ ] 数据导入脚本执行成功
   - [ ] 回滚脚本准备完成

3. **性能优化效果**
   - [ ] 关键查询性能满足要求
   - [ ] 索引设计合理有效
   - [ ] 数据库连接池配置优化

## 🔧 Augment Code 提示词

```
请帮我设计 Roasmax 项目的本地认证数据库结构：

1. 分析现有的 members 表结构，设计扩展字段
2. 设计角色权限管理的相关表结构
3. 创建用户会话管理表
4. 编写数据迁移脚本，从 Authing 迁移到本地数据库
5. 优化数据库索引和查询性能
6. 更新 Prisma schema 文件

要求：
- 保持与现有系统的兼容性
- 确保数据迁移的安全性和完整性
- 优化查询性能
- 支持多租户架构
- 提供完整的备份恢复机制

请提供详细的 SQL 脚本和 TypeScript 迁移代码。
```

## 📅 时间安排

- **第1天**: 数据库结构设计和 SQL 脚本编写
- **第2天**: 数据迁移脚本开发和测试
- **第3天**: 性能优化和文档整理

## 🚨 风险提示

1. **数据丢失风险**: 迁移过程中可能出现数据丢失
2. **性能影响**: 新表结构可能影响查询性能
3. **兼容性问题**: 可能与现有代码存在兼容性问题
