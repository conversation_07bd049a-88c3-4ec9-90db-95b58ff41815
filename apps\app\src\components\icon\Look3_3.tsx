export const Look3_3 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <path
        d="M16.5 13.9062C15.6213 13.9062 14.9062 14.6213 14.9062 15.5C14.9062 16.3787 15.6213 17.0938 16.5 17.0938C17.3787 17.0938 18.0938 16.3787 18.0938 15.5C18.0938 14.6213 17.3787 13.9062 16.5 13.9062ZM16.5 16.0312C16.3591 16.0312 16.224 15.9753 16.1243 15.8757C16.0247 15.776 15.9688 15.6409 15.9688 15.5C15.9688 15.3591 16.0247 15.224 16.1243 15.1243C16.224 15.0247 16.3591 14.9688 16.5 14.9688C16.6409 14.9688 16.776 15.0247 16.8757 15.1243C16.9753 15.224 17.0312 15.3591 17.0312 15.5C17.0312 15.6409 16.9753 15.776 16.8757 15.8757C16.776 15.9753 16.6409 16.0312 16.5 16.0312Z"
        fill="#9FA4B2"
      />
      <path
        d="M24.4688 14.9688H23.3797C23.2513 13.3278 22.5412 11.7866 21.3773 10.6227C20.2134 9.45879 18.6722 8.74871 17.0312 8.62031V7.53125C17.0312 7.39035 16.9753 7.25523 16.8757 7.1556C16.776 7.05597 16.6409 7 16.5 7C16.3591 7 16.224 7.05597 16.1243 7.1556C16.0247 7.25523 15.9688 7.39035 15.9688 7.53125V8.62031C14.3278 8.74871 12.7866 9.45879 11.6227 10.6227C10.4588 11.7866 9.74871 13.3278 9.62031 14.9688H8.53125C8.39035 14.9688 8.25523 15.0247 8.1556 15.1243C8.05597 15.224 8 15.3591 8 15.5C8 15.6409 8.05597 15.776 8.1556 15.8757C8.25523 15.9753 8.39035 16.0312 8.53125 16.0312H9.62031C9.74871 17.6722 10.4588 19.2134 11.6227 20.3773C12.7866 21.5412 14.3278 22.2513 15.9688 22.3797V23.4688C15.9688 23.6096 16.0247 23.7448 16.1243 23.8444C16.224 23.944 16.3591 24 16.5 24C16.6409 24 16.776 23.944 16.8757 23.8444C16.9753 23.7448 17.0312 23.6096 17.0312 23.4688V22.3797C18.6722 22.2513 20.2134 21.5412 21.3773 20.3773C22.5412 19.2134 23.2513 17.6722 23.3797 16.0312H24.4688C24.6096 16.0312 24.7448 15.9753 24.8444 15.8757C24.944 15.776 25 15.6409 25 15.5C25 15.3591 24.944 15.224 24.8444 15.1243C24.7448 15.0247 24.6096 14.9688 24.4688 14.9688ZM17.0312 21.3172V20.2812C17.0312 20.1404 16.9753 20.0052 16.8757 19.9056C16.776 19.806 16.6409 19.75 16.5 19.75C16.3591 19.75 16.224 19.806 16.1243 19.9056C16.0247 20.0052 15.9688 20.1404 15.9688 20.2812V21.3172C14.6093 21.1913 13.3367 20.594 12.3714 19.6286C11.406 18.6633 10.8087 17.3907 10.6828 16.0312H11.7188C11.8596 16.0312 11.9948 15.9753 12.0944 15.8757C12.194 15.776 12.25 15.6409 12.25 15.5C12.25 15.3591 12.194 15.224 12.0944 15.1243C11.9948 15.0247 11.8596 14.9688 11.7188 14.9688H10.6828C10.8087 13.6093 11.406 12.3367 12.3714 11.3714C13.3367 10.406 14.6093 9.80866 15.9688 9.68281V10.7188C15.9688 10.8596 16.0247 10.9948 16.1243 11.0944C16.224 11.194 16.3591 11.25 16.5 11.25C16.6409 11.25 16.776 11.194 16.8757 11.0944C16.9753 10.9948 17.0312 10.8596 17.0312 10.7188V9.68281C18.3907 9.80866 19.6633 10.406 20.6286 11.3714C21.594 12.3367 22.1913 13.6093 22.3172 14.9688H21.2812C21.1404 14.9688 21.0052 15.0247 20.9056 15.1243C20.806 15.224 20.75 15.3591 20.75 15.5C20.75 15.6409 20.806 15.776 20.9056 15.8757C21.0052 15.9753 21.1404 16.0312 21.2812 16.0312H22.3172C22.1913 17.3907 21.594 18.6633 20.6286 19.6286C19.6633 20.594 18.3907 21.1913 17.0312 21.3172Z"
        fill="#9FA4B2"
      />
    </svg>
  );
};
