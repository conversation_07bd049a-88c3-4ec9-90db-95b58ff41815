import type { BoxMedia } from './getBoxSizes';

import { useMemo } from 'react';

import { getBoxSizeByMedia } from './getBoxSizes';

type BoxType = keyof typeof BoxMedia;

export const useMedia = (list: any[], w: number, type: BoxType) => {
  return useMemo(() => {
    const { column } = getBoxSizeByMedia(type, w);

    return {
      waterfallList: list.map((data) => {
        const { imgWidth, imgHeight } = data;
        const ratio = imgHeight / imgWidth;

        const { width, height } = getBoxSizeByMedia(type, w, 16, 16, (boxWidth) => {
          return (boxWidth * ratio) / boxWidth;
        });

        return {
          w: width,
          h: height,
          data,
        };
      }),
      column,
    };
  }, [list, type, w]);
};
