import React from 'react';

export const Look1_3 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <path
        d="M21.4295 22H10.499C9.67301 22 9 21.3266 9 20.5V10.5C9 9.67344 9.67301 9 10.499 9H21.4295C22.2556 9 22.9286 9.67344 22.9286 10.5V20.5C22.9286 21.3266 22.2556 22 21.4295 22ZM10.499 10C10.2227 10 9.99936 10.225 9.99936 10.5V20.5C9.99936 20.7766 10.2242 21 10.499 21H21.4295C21.7059 21 21.9292 20.775 21.9292 20.5V10.5C21.9292 10.2234 21.7044 10 21.4295 10H10.499Z"
        fill="#9FA4B2"
      />
      <path d="M9.89648 12.9971H22.3572V13.9971H9.89648V12.9971Z" fill="#9FA4B2" />
      <path
        d="M10.9707 11.5156C10.9707 11.6482 11.0234 11.7754 11.1171 11.8692C11.2109 11.9629 11.3381 12.0156 11.4707 12.0156C11.6033 12.0156 11.7305 11.9629 11.8243 11.8692C11.918 11.7754 11.9707 11.6482 11.9707 11.5156C11.9707 11.383 11.918 11.2558 11.8243 11.1621C11.7305 11.0683 11.6033 11.0156 11.4707 11.0156C11.3381 11.0156 11.2109 11.0683 11.1171 11.1621C11.0234 11.2558 10.9707 11.383 10.9707 11.5156Z"
        fill="#9FA4B2"
      />
      <path
        d="M12.9707 11.5156C12.9707 11.6482 13.0234 11.7754 13.1171 11.8692C13.2109 11.9629 13.3381 12.0156 13.4707 12.0156C13.6033 12.0156 13.7305 11.9629 13.8243 11.8692C13.918 11.7754 13.9707 11.6482 13.9707 11.5156C13.9707 11.383 13.918 11.2558 13.8243 11.1621C13.7305 11.0683 13.6033 11.0156 13.4707 11.0156C13.3381 11.0156 13.2109 11.0683 13.1171 11.1621C13.0234 11.2558 12.9707 11.383 12.9707 11.5156Z"
        fill="#9FA4B2"
      />
      <path
        d="M14.9688 11.5156C14.9687 11.5813 14.9817 11.6463 15.0068 11.707C15.0319 11.7676 15.0688 11.8227 15.1152 11.8692C15.1616 11.9156 15.2167 11.9524 15.2774 11.9776C15.3381 12.0027 15.4031 12.0156 15.4688 12.0156C15.5344 12.0156 15.5994 12.0027 15.6601 11.9776C15.7208 11.9524 15.7759 11.9156 15.8223 11.8692C15.8687 11.8227 15.9056 11.7676 15.9307 11.707C15.9558 11.6463 15.9688 11.5813 15.9688 11.5156C15.9688 11.383 15.9161 11.2558 15.8223 11.1621C15.7285 11.0683 15.6014 11.0156 15.4688 11.0156C15.3361 11.0156 15.209 11.0683 15.1152 11.1621C15.0214 11.2558 14.9688 11.383 14.9688 11.5156Z"
        fill="#9FA4B2"
      />
    </svg>
  );
};
