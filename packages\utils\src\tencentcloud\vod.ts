// import { Client } from 'tencentcloud-sdk-nodejs-vod/tencentcloud/services/vod/v20180717/vod_client';
import { vod } from 'tencentcloud-sdk-nodejs-vod';

const { VodUploadClient, VodUploadRequest } = require('vod-node-sdk');
export class VodClient extends vod.v20180717.Client {
  constructor(options: ConstructorParameters<typeof vod.v20180717.Client>[0]) {
    super(options);
  }
}

/**
 * 上传文件，仅服务端可用
 * @param options
 * @returns
 *
 * @see https://www.tencentcloud.com/zh/document/product/266/33918#.E6.8E.A5.E5.8F.A3.E6.8F.8F.E8.BF.B0
 */
export const uploadFileToVod = async (options: {
  secretId: string;
  secretKey: string;
  MediaName: string;
  MediaFilePath: string;
  SubAppId: string;
  ClassId: string;
  region: string;
}) => {
  const uploader = new VodUploadClient(options.secretId, options.secretKey);
  const req = new VodUploadRequest();
  req.MediaName = options.MediaName;
  req.MediaFilePath = options.MediaFilePath;
  req.SubAppId = Number(options.SubAppId);
  req.ClassId = Number(options.ClassId);
  const promise = await new Promise<{ FileId: string; MediaUrl: string; CoverUrl: string; RequestId: string }>(
    (resolve, reject) => {
      uploader.upload(
        options.region,
        req,
        function (err: any, data: { FileId: string; MediaUrl: string; CoverUrl: string; RequestId: string }) {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        },
      );
    },
  );
  return promise;
};
