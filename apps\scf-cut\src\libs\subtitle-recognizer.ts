import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import { withRetry } from '@roasmax/utils';

interface VoiceToTextResult {
  id: string;
  utterances: Array<{ start_time: number; end_time: number; text: string }>;
}

interface SubtitleRecognizerConfig {
  appid: string;
  accessToken: string;
  language: string;
  segmentDuration: number;
}

/**
 * 字幕识别器
 * 按照预定时间间隔将视频切割为音频切片 -> 进行语音转写 -> 生成srt格式字幕
 */
class SubtitleRecognizer {
  private baseUrl: string;
  private readonly config: SubtitleRecognizerConfig;
  constructor(config: SubtitleRecognizerConfig) {
    this.baseUrl = 'https://openspeech.bytedance.com/api/v1/vc';
    this.config = config;
  }

  /**
   * 识别字幕
   * @param videoPath 视频路径
   * @returns 字幕内容（srt格式）
   */
  public async recognize(videoPath: string): Promise<string> {
    try {
      console.log('开始提取音频...');
      const audioSegments = await this.extractAudioSegments(videoPath);

      const subtitleContents: string[] = [];

      for (let i = 0; i < audioSegments.length; i++) {
        console.log(`处理第 ${i + 1}/${audioSegments.length} 段...`);
        const audioPath = audioSegments[i];
        // 读取音频文件
        const audioData = fs.readFileSync(audioPath!);
        // 语言转写
        const queryResponse = await this.voiceToText(audioData);
        // 生成SRT格式字幕
        const srtOutput = this.convertToSrtFormat(queryResponse);
        subtitleContents.push(srtOutput);
      }

      // 合并所有字幕内容
      const mergedContent = subtitleContents.join('\n');

      // 清理临时文件
      const tempDir = path.join(path.dirname(videoPath), 'temp_audio');
      fs.readdirSync(tempDir).forEach((file) => {
        fs.unlinkSync(path.join(tempDir, file));
      });
      fs.rmdirSync(tempDir);

      console.log('字幕生成完成！');
      return mergedContent;
    } catch (error) {
      console.error('生成字幕失败:', error);
      throw error;
    }
  }

  /**
   * 语音转写
   * @param data 音频数据
   * @returns 转写结果
   */
  private async voiceToText(data: Buffer<ArrayBufferLike>) {
    const response = await withRetry(() =>
      axios.post<{ id: string }>(`${this.baseUrl}/submit`, data, {
        params: {
          appid: this.config.appid,
          language: this.config.language,
          use_itn: 'True',
          use_capitalize: 'True',
          max_lines: 1,
          words_per_line: 12,
          with_speaker_info: 'True',
        },
        headers: {
          'content-type': 'audio/mpeg',
          Authorization: `Bearer; ${this.config.accessToken}`,
        },
      }),
    );

    // 查询结果
    const queryResponse = await withRetry(() =>
      axios.get<VoiceToTextResult>(`${this.baseUrl}/query`, {
        params: { appid: this.config.appid, id: response.data.id },
        headers: { Authorization: `Bearer; ${this.config.accessToken}` },
      }),
    );
    return queryResponse.data;
  }

  /**
   * 提取音频切片
   * @param videoPath 视频路径
   * @returns 音频切片路径 <视频路径>/temp_audio/segment_${i}.mp3
   */
  private async extractAudioSegments(videoPath: string): Promise<string[]> {
    const tempDir = path.join(path.dirname(videoPath), 'temp_audio');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }

    console.log('开始获取视频时长...');
    const duration = await this.getVideoDuration(videoPath);
    console.log(`视频时长获取完成！视频时长：${duration}`);

    const segments: string[] = [];
    for (let i = 0; i < Math.ceil(duration / this.config.segmentDuration); i++) {
      const startTime = i * this.config.segmentDuration;
      const outputPath = path.join(tempDir, `segment_${i}.mp3`);
      console.log(`开始切割第 ${i + 1} 段...`);
      console.log(`开始时间：${startTime}; 结束时间：${startTime + this.config.segmentDuration}`);
      await new Promise<void>((resolve, reject) => {
        ffmpeg(videoPath)
          .setStartTime(startTime)
          .setDuration(this.config.segmentDuration)
          .noVideo()
          .output(outputPath)
          .on('end', () => resolve())
          .on('error', (err) => {
            console.error(`第 ${i + 1} 段切割失败！`);
            console.error(err);
            reject(err);
          })
          .run();
      });
      console.log(`第 ${i + 1} 段切割完成！`);
      segments.push(outputPath);
    }
    console.log('音频切割完成！');
    return segments;
  }

  /**
   * 获取视频时长
   * @param videoPath 视频路径
   * @returns 视频时长（秒）
   */
  private async getVideoDuration(videoPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(videoPath, (err, metadata) => {
        if (err) reject(err);
        resolve(metadata.format.duration || 0);
      });
    });
  }

  /**
   * 格式化时间
   * @param timeInSeconds 时间（秒）
   * @returns 时间（srt格式）
   */
  private formatTimeSrt(timeInSeconds: number): string {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    const milliseconds = Math.floor((timeInSeconds * 1000) % 1000);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
  }

  /**
   * 转换为srt格式
   * @param data 接口返回数据
   * @returns srt格式字幕
   */
  private convertToSrtFormat(data: VoiceToTextResult): string {
    let srtContent = '';
    data.utterances.forEach((utterance, i) => {
      const startTime = this.formatTimeSrt(utterance.start_time / 1000);
      const endTime = this.formatTimeSrt(utterance.end_time / 1000);
      srtContent += `${i + 1}\n${startTime} --> ${endTime}\n${utterance.text}\n\n`;
    });
    return srtContent;
  }
}

export default SubtitleRecognizer;
