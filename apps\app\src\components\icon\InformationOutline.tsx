import React from 'react';

export const InformationOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.74023 0.86084C11.6061 0.86084 14.7402 3.99495 14.7402 7.86084C14.7402 11.7267 11.6061 14.8608 7.74023 14.8608C3.87435 14.8608 0.740234 11.7267 0.740234 7.86084C0.740234 3.99495 3.87435 0.86084 7.74023 0.86084ZM7.74023 1.97434C4.48934 1.97434 1.85374 4.60994 1.85374 7.86084C1.85374 11.1117 4.48934 13.7473 7.74023 13.7473C10.9911 13.7473 13.6267 11.1117 13.6267 7.86084C13.6267 4.60994 10.9911 1.97434 7.74023 1.97434Z"
        fill="currentColor"
      />
      <path
        d="M7.74017 10.0338C7.39013 10.0338 7.10645 10.3175 7.10645 10.6675C7.10645 11.0176 7.39013 11.3013 7.74017 11.3013C8.09021 11.3013 8.37389 11.0176 8.37389 10.6675C8.37389 10.3175 8.09021 10.0338 7.74017 10.0338Z"
        fill="currentColor"
      />
      <path
        d="M8.46374 4.95312L8.21659 9.15312C8.20114 9.4162 7.97538 9.61682 7.7123 9.60137C7.46827 9.587 7.27779 9.39153 7.26406 9.15312L7.0169 4.95312C6.99333 4.55359 7.29825 4.21073 7.69762 4.18732C8.09715 4.16374 8.44001 4.46866 8.46343 4.86803C8.4653 4.8952 8.46515 4.92643 8.46374 4.95312Z"
        fill="currentColor"
      />
    </svg>
  );
};
