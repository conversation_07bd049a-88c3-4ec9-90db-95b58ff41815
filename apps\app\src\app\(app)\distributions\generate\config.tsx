import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { Button } from '@/components/ui';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { cn } from '@/utils/cn';
import { ActionParams, ActionResult } from '@/utils/server-action/action';
import dayjs from 'dayjs';
import { useMemo } from 'react';

export type GenerationTaskType = ActionResult<typeof pageVideoGenerationTasks>['list'][0];

export const useColumns = (config: {
  onShowTaskPreview?: (id: string) => void;
}): ProTableColumnType<GenerationTaskType>[] => {
  const columns = useMemo(() => {
    const inbuilt: ProTableColumnType<GenerationTaskType>[] = [
      {
        dataIndex: 'name',
        title: '任务名称',
        render: (v, record) => (
          <div>
            <div className="mb-1">{v}</div>
            <div className="text-xs text-gray-500">ID: {record.id}</div>
          </div>
        ),
      },
      {
        dataIndex: 'tmp_created_at',
        title: '创建时间',
        render: (_, record) => <div>{dayjs(record.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>,
      },
      {
        key: 'process',
        title: '进度',
        render: (_, record) => {
          const total = record.subTasks.length;
          const processing = record.subTasks.filter((subTask) => subTask.status === 'PROCESSING').length;
          const success = record.subTasks.filter((subTask) => subTask.status === 'SUCCESS').length;
          const failed = record.subTasks.filter((subTask) => subTask.status === 'FAILED').length;
          return (
            <div className="grid grid-cols-4 gap-1 text-right">
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">进行中</div>
                <div className="text-sm">{processing}</div>
              </div>
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">成功</div>
                <div className="text-sm">{success}</div>
              </div>
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">失败</div>
                <div className={cn('text-sm', failed > 0 ? 'text-[#FF4D4D]' : '')}>{failed}</div>
              </div>
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">总数</div>
                <div className="text-sm">{total}</div>
              </div>
            </div>
          );
        },
      },
      {
        key: 'options',
        title: <div className="ml-4">操作</div>,
        fixed: 'right' as const,
        render: (_, record) => {
          return (
            <div>
              <Button variant="link" className="text-xs" onClick={() => config.onShowTaskPreview?.(record.id)}>
                明细
              </Button>
            </div>
          );
        },
      },
    ];

    return inbuilt;
  }, []);

  return columns;
};

export const filterColumns: ProFilterColumnType<
  NonNullable<ActionParams<typeof pageVideoGenerationTasks>['filters']>
>[] = [
  {
    key: 'name',
    title: '任务名称',
    type: 'text',
  },
];
