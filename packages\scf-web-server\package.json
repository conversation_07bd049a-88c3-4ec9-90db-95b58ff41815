{"name": "@roasmax/scf-web-server", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup src/index.ts", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"dotenv": "^16.4.7", "koa": "^2.15.3", "koa-bodyparser": "^4.4.1"}, "devDependencies": {"@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.12", "cross-env": "^7.0.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}}