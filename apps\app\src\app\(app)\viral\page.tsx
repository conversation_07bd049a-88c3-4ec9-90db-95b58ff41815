'use client';

import { asyncConfirm } from '@/components/ConfirmDialog';
import { ViralIcon, WarningFullfil } from '@/components/icon/index';
import { StarsFullfil } from '@/components/icon/StartsFullfil';
import MaterialDialog from '@/components/MaterialDialog';
import { <PERSON><PERSON>, Card } from '@/components/ui';
import { useProductAnalysis } from '@/hooks/useProductAnalysis';
import { TaskCreatorFormValues, useTaskCreator } from '@/hooks/useTaskCreator';
import { useWallet } from '@/hooks/useWallet';
import { startVideoGenerationTask } from '@/services/actions/video-generation-task';
import useMaterialStore from '@/store/materialStore';
import { cn } from '@/utils/cn';
import { action } from '@/utils/server-action/action';
import { calcCostQuota, calcGenerateCount, to } from '@roasmax/utils';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { AnalysisPanel } from './components/AnalysisPanel';
import ErrorPrompt from './components/ErrorPrompt';
import { LinkInput } from './components/LinkInput';
import { VideoForm } from './components/TaskCreator';
import { Share2 } from 'lucide-react';
import { ProgressSharing } from './components/ProgressSharing';

export interface GenerateViralVideoFormValues {
  taskName: string;
  language: string;
  voiceType: string;
  generateRound: string;
  speed: string;
  sceneImplantation: boolean;
  festiveAtmosphere: boolean;
  subtitle: boolean;
  transitionMode: boolean;
  materialIds: string[];
  targetVideoDuration: number;
}

const Viral = () => {
  const router = useRouter();

  const { refresh: refreshWallet, quota } = useWallet();
  const {
    error,
    isLoading,
    isVideoLoading,
    isFloating,
    setIsFloating,
    analysisResult,
    handleAnalyze,
    handleCancelAnalyze,
    completedSteps,
    errorNavigator,
    errorAi,
    playbackSteps,
    shareId,
    fiilterCoverage,
    setFilterCoverage,
    setIsLoading,
    videoIds,
    picturesUrl,
  } = useProductAnalysis();

  const { setMaterialDrawerOpen } = useMaterialStore();

  const { form, formValues, selectedMaterials } = useTaskCreator();
  const [link, setLink] = useState('');
  const [showTitle, setShowTitle] = useState(true);
  const [currentStep, setCurrentStep] = useState<'analysis' | 'generate'>('analysis');
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);
  const [savedSelectedTemplates, setSavedSelectedTemplates] = useState<string[]>([]); // 新增状态保存已选择的模板

  const handleNextStep = () => {
    if (selectedTemplates.length === 0) {
      toast.error('请选择模板');
      return;
    }
    setSavedSelectedTemplates(selectedTemplates);
    setCurrentStep('generate');
    setShowTitle(false);
  };
  const handleAnalyzeWithAnimation = async (link: string) => {
    setShowTitle(true);
    setIsFloating(true);
    setFilterCoverage(null);
    setSelectedTemplates([]);
    await handleAnalyze(link);
  };

  const shareContent = async () => {
    if (!shareId) {
      toast.error('分享ID不存在，无法分享');
      return;
    }

    const shareUrl = `${window.location.origin}/share/product-analysis/${shareId}`;

    try {
      // 优先使用新的 Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(shareUrl);
        toast.success('分享链接已复制到剪贴板');
      } else {
        // 回退到旧的 document.execCommand 方法
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;

        // 确保文本区域不可见
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          toast.success('分享链接已复制到剪贴板');
        } else {
          toast.error('复制失败，请手动复制链接');
        }
      }
    } catch (err) {
      console.error('复制到剪贴板失败:', err);
      toast.error('复制失败，请手动复制链接');
    }
  };

  useEffect(() => {
    if (currentStep === 'generate') {
      const scrollContainer = document.getElementById('form-scroll-container');
      if (scrollContainer) {
        scrollContainer.scrollTop = 0;
      }
    }
  }, [currentStep]);
  useEffect(() => {
    if (currentStep === 'analysis') {
      setSelectedTemplates(savedSelectedTemplates); // 返回analysis步骤时，设置已保存的模板
    }
  }, [currentStep, savedSelectedTemplates]);
  /**
   * 计算生成视频数量和消耗点数
   */
  const { expectVideoCount, expectCostQuota } = useMemo(() => {
    const p = {
      method: 'gc_imitate' as const,
      sliceDuration: 300,
      materialDurations: selectedMaterials.map((each) => each.video_duration),
      generateRound: Number(formValues.generateRound),
      prompts: [],
      generationType: '大卖推荐',
      templateCount: selectedTemplates.length,
    };
    const expectVideoCount = calcGenerateCount(p);
    const expectCostQuota = calcCostQuota(p);
    return { expectVideoCount, expectCostQuota };
  }, [selectedMaterials, formValues.generateRound, selectedTemplates]);

  /**
   * 异步提交表单的函数
   */
  const submit = async () => {
    await form.trigger();
    console.log('formValues', form.getValues());
    // transition_mode: 'null';

    const values = form.getValues() as TaskCreatorFormValues<'gc_imitate'>;

    if (selectedMaterials.length === 0) {
      toast.error('请选择素材');
      return;
    }

    if (!values.targetVoice) {
      toast.error('请选择目标音色');
      return;
    }

    await asyncConfirm({
      content: (
        <div className="mb-2 flex gap-[8px]">
          <div className="mt-[1px]">
            <WarningFullfil className="h-[18px] w-[18px]" />
          </div>
          <div className="w-[300px]">
            <div className="mb-2 text-sm">生成视频确认</div>
            <div className="text-[13px] font-normal leading-[22px] text-[#95A0AA]">
              <div>
                预计扣除：<span className="text-[#00E1FF]">{expectCostQuota} 点数</span>
                ；预计生成 <span className="text-[#00E1FF]">{expectVideoCount} 条视频</span>
              </div>
            </div>
          </div>
        </div>
      ),
      buttonText: { cancel: '返回修改', confirm: '确定生成' },
      onConfirm: async () => {
        if (expectCostQuota > (quota || 0)) {
          toast.error('点数不足');
          return;
        }

        const [err, result] = await to(
          action(
            startVideoGenerationTask,
            {
              name: values.name,
              sliceType: '300',
              method: 'gc_imitate',
              generateCount: Number(values.generateRound),
              accelerate: values.speed,
              subtitles: values.subtitle,
              transition_mode: 'null',
              materialIds: selectedMaterials.map((material) => material.id),
              productUrl: link || '',
              productAnalysis: analysisResult?.product_analysis ?? '',
              targetLanguage: '',
              customPrompt: values.customPrompt,
              targetVoice: values.targetVoice,
              generationType: '大卖推荐',
              sceneImplantation: values.sceneImplantation,
              festiveAtmosphere: values.festiveAtmosphere,
              templateVideoTiktokIds: selectedTemplates || [],
              targetVideoDuration: values.targetVideoDuration,
            },
            { errorType: 'return' },
          ),
        );
        if (err || !result.success) {
          toast.error('生成视频失败');
          return;
        }

        if (result.data) {
          toast.success('创建视频生成任务成功');
          await refreshWallet();
          router.push('/video-generation-tasks');
        }
      },
    });
  };

  const onClickCancel = () => {
    setShowTitle(true);
    setIsFloating(false);
    setLink('');
    setSelectedTemplates([]);
    setSavedSelectedTemplates([]);
    handleCancelAnalyze();
    setFilterCoverage(null);
  };
  console.log('window.innerHeight', window.innerHeight);

  return (
    <div
      className="flex h-screen w-full flex-col overflow-auto pb-6"
      style={{
        backgroundImage: 'url(/background/bgAi1.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center -100px',
      }}
    >
      <div
        className={cn(
          'flex flex-col items-center transition-all duration-500',
          isFloating ? 'pt-[56px] xl:pt-[88px]' : 'mb-0 min-h-[calc(100vh-250px)] justify-start pt-[25vh]',
        )}
      >
        <div className="z-10 flex w-full flex-col items-center px-6">
          {showTitle || error || errorNavigator ? (
            <div className="flex flex-col items-center">
              <ViralIcon />
              <p className="mt-4 text-base font-normal leading-normal tracking-[0.8px] text-[#9FA4B2] lg:mb-5 xl:mb-[26px] 2xl:mb-[56px]">
                您可以提供商品链接，一键分析视频
              </p>
            </div>
          ) : null}
          <div id="link-input-container" className="w-full max-w-[1500px]">
            <LinkInput
              value={link}
              onChange={setLink}
              onSubmit={handleAnalyzeWithAnimation}
              loading={isLoading}
              disabled={isFloating}
              className="w-full max-w-[1500px]"
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center px-6">
        {isVideoLoading && (
          <>
            <Card
              className={cn(
                'w-full max-w-[1500px] flex-1 overflow-hidden rounded-2xl bg-[#1B243580]',
                currentStep === 'analysis' ? '-mt-14 pt-14' : 'mt-4',
              )}
            >
              {isLoading ? (
                <ProgressSharing
                  isLoading={isLoading}
                  completedSteps={completedSteps}
                  playbackSteps={playbackSteps}
                  fiilterCoverage={fiilterCoverage} // 传递给ProgressSharing
                  link={link}
                  setIsLoading={setIsLoading}
                  videoIds={videoIds}
                  setShowTitle={setShowTitle}
                  picturesUrl={picturesUrl}
                />
              ) : error ? (
                <ErrorPrompt
                  imgComponent={'icons/ai/DaMaiSvg.svg'}
                  errorMessage="链接无法识别，请输入正确的链接"
                  handleCancelAnalyze={handleCancelAnalyze}
                  setShowTitle={setShowTitle}
                  handleAnalyzeWithAnimation={handleAnalyzeWithAnimation}
                  link={link}
                />
              ) : errorAi ? (
                <ErrorPrompt
                  imgComponent={'icons/ai/ErrorAiSvg.svg'}
                  errorMessage="Ai睡着啦，请再次分析唤醒它"
                  handleCancelAnalyze={handleCancelAnalyze}
                  setShowTitle={setShowTitle}
                  handleAnalyzeWithAnimation={handleAnalyzeWithAnimation}
                  link={link}
                />
              ) : errorNavigator ? (
                <ErrorPrompt
                  imgComponent={'icons/ai/OnlineSvg.svg'}
                  errorMessage="网络出错了，请检查您的网络"
                  handleCancelAnalyze={handleCancelAnalyze}
                  setShowTitle={setShowTitle}
                  handleAnalyzeWithAnimation={handleAnalyzeWithAnimation}
                  link={link}
                />
              ) : (
                <div
                  className={cn(
                    'flex flex-col',
                    currentStep === 'analysis'
                      ? 'lg:h-[calc(100vh-200px)] xl:h-[calc(100vh-224px)] 2xl:h-[calc(100vh-244px)]'
                      : 'h-[calc(100vh-204px)]',
                    errorNavigator && 'lg:h-[calc(100vh-290px)] xl:h-[calc(100vh-314px)] 2xl:h-[calc(100vh-344px)]',
                  )}
                >
                  {currentStep === 'analysis' ? (
                    <div className="flex-1 overflow-y-auto p-6">
                      <AnalysisPanel
                        selectedTemplates={selectedTemplates}
                        setSelectedTemplates={setSelectedTemplates}
                        isLoading={isLoading}
                        analysisResult={analysisResult}
                        videoIds={videoIds}
                        setShowTitle={setShowTitle}
                      />
                    </div>
                  ) : (
                    <div className="flex-1 overflow-y-auto p-6" id="form-scroll-container">
                      <VideoForm
                        onClickMaterialImport={() => setMaterialDrawerOpen(true)}
                        setShowTitle={setShowTitle}
                      />
                    </div>
                  )}
                  <div className="flex h-[88px] items-center justify-center gap-3">
                    {currentStep === 'analysis' ? (
                      <>
                        <Button variant="outline" className="w-[120px] bg-[#ccddff]/10" onClick={onClickCancel}>
                          取消
                        </Button>
                        {analysisResult?.suggestion &&
                          analysisResult?.template_info &&
                          analysisResult?.product_analysis && (
                            <div className="flex items-center gap-3">
                              <Button
                                className="w-[120px] bg-[linear-gradient(90.59deg,_#54FFE0_4.36%,_#00E1FF_22.69%,_#9D81FF_96%)] px-7 py-4 text-black transition duration-150 ease-in-out hover:text-black"
                                onClick={handleNextStep}
                              >
                                使用模板
                              </Button>
                              {shareId && (
                                <Button
                                  variant="outline"
                                  className="h-[40px] w-[40px] rounded-full bg-[#ccddff]/10 p-0 text-white"
                                  onClick={shareContent}
                                >
                                  <Share2 size={18} />
                                </Button>
                              )}
                            </div>
                          )}
                      </>
                    ) : (
                      <div className="flex w-full items-center justify-between gap-2 px-2">
                        <Button
                          variant="outline"
                          className="w-[120px] bg-[#ccddff]/10"
                          onClick={() => {
                            setCurrentStep('analysis');
                          }}
                        >
                          返回
                        </Button>
                        <Button
                          size="xl"
                          className={cn(
                            'h-[48px] w-full rounded-xl',
                            'bg-[linear-gradient(90.59deg,_#54FFE0_4.36%,_#00E1FF_22.69%,_#9D81FF_96%)]',
                          )}
                          onClick={() => {
                            submit();
                          }}
                        >
                          <div>
                            <div className={cn('mb-[2px] flex h-[20px] gap-2 text-[#050A1C]')}>
                              <StarsFullfil />
                              <div>立即生成</div>
                            </div>
                            <div className={cn('text-xs text-[#050A1CCC]')}>消耗 {expectCostQuota || 0} 点数</div>
                          </div>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </Card>
          </>
        )}
      </div>
      <MaterialDialog />
    </div>
  );
};

export default Viral;
