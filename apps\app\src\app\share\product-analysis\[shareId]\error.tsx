'use client';

import { Button } from '@/components/ui';
import { useRouter } from 'next/navigation';

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  const router = useRouter();

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center">
      <div className="flex flex-col items-center gap-6 text-center">
        <h1 className="text-4xl font-bold text-[#00E1FF]">出错了</h1>
        <p className="max-w-md text-lg text-[#9FA4B2]">分享链接可能已过期或不存在，请联系分享者获取新的链接。</p>
        <div className="flex gap-4">
          <Button
            variant="outline"
            onClick={() => {
              router.push('/');
            }}
          >
            返回首页
          </Button>
          <Button onClick={() => reset()}>重试</Button>
        </div>
      </div>
    </div>
  );
}
