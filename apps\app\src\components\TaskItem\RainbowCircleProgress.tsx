import React, { useRef, useEffect, useCallback } from 'react';

const SIZE = 32;
const STROKE_WIDTH = 3;
const SCALE = 2;

const GradientCircleProgressBar: React.FC<{
  progress: number;
  rollingSpeed?: number;
  className?: string;
}> = (props) => {
  const { progress, rollingSpeed = 0.02, className } = props;
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const drawProgress = useCallback(
    (rollingOffset: number) => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext('2d')!;
      canvas.width = SIZE * SCALE;
      canvas.height = SIZE * SCALE;
      canvas.style.width = `${SIZE}px`;
      canvas.style.height = `${SIZE}px`;
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = ((SIZE - STROKE_WIDTH) * SCALE) / 2;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.strokeStyle = 'lightgray';
      ctx.lineWidth = STROKE_WIDTH * SCALE;
      ctx.stroke();

      ctx.beginPath();
      ctx.arc(
        centerX,
        centerY,
        radius,
        rollingOffset * Math.PI + -Math.PI / 2,
        rollingOffset * Math.PI + (2 * Math.PI * progress - Math.PI / 2),
      );
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);

      const colors = [
        { color: '#9D81FF', offset: 0.2 },
        { color: '#FBFD70', offset: 0.4 },
        { color: '#54FFE0', offset: 0.6 },
        { color: '#00E1FF', offset: 0.8 },
      ];

      colors.forEach((color) => {
        gradient.addColorStop(color.offset, color.color);
      });

      ctx.strokeStyle = gradient;
      ctx.lineWidth = STROKE_WIDTH * SCALE;
      ctx.stroke();
    },
    [progress],
  );

  const animate = useCallback(() => {
    let rollingOffset = 0;
    const animate = () => {
      rollingOffset += rollingSpeed;
      drawProgress(rollingOffset);
      requestAnimationFrame(animate);
    };
    animate();
  }, [drawProgress, rollingSpeed]);

  useEffect(() => {
    animate();
  }, [animate]);

  return <canvas ref={canvasRef} className={className}></canvas>;
};

export default GradientCircleProgressBar;
