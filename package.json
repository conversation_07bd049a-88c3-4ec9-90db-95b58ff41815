{"name": "roasmax", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "turbo clean && rm -rf node_modules", "rebase": "git fetch origin master && git rebase origin/master"}, "devDependencies": {"@types/node": "^20", "@vercel/ncc": "^0.38.3", "prettier": "^3.2.5", "tsup": "^8.3.5", "tsx": "^4.19.2", "turbo": "^2.3.3", "typescript": "^5.7.2"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@9.15.0", "workspaces": ["apps/*", "packages/*"]}