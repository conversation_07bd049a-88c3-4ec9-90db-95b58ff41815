import { LangfuseOpenApi } from '@roasmax/utils/langfuse';
import { ActionContextPluginLoader } from '../../types';

type FetchLangfuseOpenApiPlugin = LangfuseOpenApi;

const fetchLangfuseOpenPlugin: ActionContextPluginLoader<'langfuseOpenApi', FetchLangfuseOpenApiPlugin> = (context) => {
  if (!process.env.LANGFUSE_BASEURL) {
    throw new Error('LANGFUSE_BASEURL is not set');
  }
  return {
    name: 'langfuseOpenApi',
    plugin: new LangfuseOpenApi({
      host: process.env.LANGFUSE_BASEURL,
      basicAuth: {
        username: context.tenant.config.langfuse_public_key,
        password: context.tenant.config.langfuse_secret_key,
      },
    }),
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * langfuse 实例
     */
    langfuseOpenApi: LangfuseOpenApi;
  }
}

export default fetchLangfuseOpenPlugin;
