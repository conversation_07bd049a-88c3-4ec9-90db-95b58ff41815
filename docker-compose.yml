services:
  mysql:
    image: mysql:8.0
    container_name: roasmax-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: roasmax_dev
      MYSQL_USER: roasmax_user
      MYSQL_PASSWORD: roasmax_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/init:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
    networks:
      - roasmax-network

  # 可选：添加 phpMyAdmin 用于数据库管理
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: roasmax-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: roasmax_user
      PMA_PASSWORD: roasmax_password
      MYSQL_ROOT_PASSWORD: rootpassword
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - roasmax-network

volumes:
  mysql_data:
    driver: local

networks:
  roasmax-network:
    driver: bridge
