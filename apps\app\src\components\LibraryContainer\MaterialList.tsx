import React, { Dispatch, SetStateAction, useMemo } from 'react';
import MaterialBox from '../Box/MaterialBox';
import { getBoxSizeByMedia, Waterfall } from '../Waterfall';
import { materials } from '@roasmax/database';
import useMaterialStore from '@/store/materialStore';
import { MaterialItemType } from '@/hooks/useMaterial';
import { Loader2 } from 'lucide-react';
import { useTaskCreator } from '@/hooks/useTaskCreator';

const MaterialList = ({
  containerWidth,
  dirId,
  materialList,
  setMaterialList,
  loading,
  onScroll,
  selectMode,
  hasMore,
  waterfallTopHeight,
  updateMaterials,
}: {
  containerWidth: number;
  dirId: string;
  materialList: materials[];
  setMaterialList: Dispatch<SetStateAction<MaterialItemType[]>>;
  loading: boolean;
  onScroll: () => void;
  waterfallTopHeight: number;
  selectMode: boolean;
  hasMore: boolean;
  updateMaterials: (data: { name: string; id: string }) => void;
}) => {
  const { setSelectedMaterialList, selectedMaterialList, batchMode } = useMaterialStore();
  const { selectedMaterials: taskMaterialList = [] } = useTaskCreator();
  const onSelect = (item: MaterialItemType) => {
    setMaterialList((prev) => {
      return prev.map((each) => {
        return { ...each, ...(item.id === each.id ? { checked: !each.checked } : {}) };
      });
    });

    if (selectMode) {
      if (item.checked) {
        const newList = selectedMaterialList.filter((material) => material.id !== item.id);
        setSelectedMaterialList(newList);
      } else {
        const newList = [...selectedMaterialList];
        newList.push(item);
        setSelectedMaterialList(newList);
      }
    }
  };

  const { waterfallList, column } = useMemo(() => {
    const { column: col, width, height } = getBoxSizeByMedia('MaterialBox', containerWidth, 32, 0);
    const newList = [...materialList];

    return {
      waterfallList: newList?.map((each: any) => {
        return {
          w: width,
          h: height,
          data: each,
        };
      }),
      column: col,
    };
  }, [materialList, containerWidth, taskMaterialList?.length]);

  if (!dirId) return null;

  if (loading) {
    if (!materialList.length) {
      return (
        <div className="flex h-[100px] items-center justify-center text-sm text-[#81889D]">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        </div>
      );
    }
  }

  if (!materialList.length) return null;
  console.log(waterfallList, 'waterfallList');

  return (
    <div className="h-auto w-full">
      <Waterfall
        containerId="bwai-library-container"
        topHeight={waterfallTopHeight}
        beforeSlot={<div className="mb-4 text-sm text-[#81889D]">我的视频素材</div>}
        afterSlot={
          <div className="flex h-[50px] items-center justify-center text-sm text-[#81889D]">
            {hasMore ? '加载中...' : '没有更多了'}
          </div>
        }
        className="h-full"
        list={waterfallList}
        column={column}
        getData={onScroll}
        itemPadding={32}
        itemPaddingY={32}
        component={MaterialBox}
        componentProps={{
          batchMode,
          selectMode,
          onSelect,
          updateMaterials,
        }}
      />
    </div>
  );
};

export default MaterialList;
