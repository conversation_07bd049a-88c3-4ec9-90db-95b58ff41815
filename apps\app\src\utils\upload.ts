import { getUploadSignature } from '@/services/actions/vod';
import TcVod from 'vod-js-sdk-v6';
import { action } from './server-action/action';
import { UploaderOptions } from 'vod-js-sdk-v6/lib/src/uploader';

interface ProgressEvent {
  loaded: number;
  total: number;
  speed: number;
  percent: number;
}

interface UploadEvent {
  UploadId: string;
  Location: string;
  Bucket: string;
  Key: string;
  ETag: string;
  StatusCode: number;
  headers: {
    'content-length': string;
    'content-type': string;
    'x-cos-request-id': string;
  };
  RequestId: string;
}

interface UploadDoneInfo {
  video: {
    url: string;
    verify_content: string;
  };
  fileId: string;
}

let vod: TcVod | null = null;

export const getVodClient = () => {
  if (vod) return vod;
  vod = new TcVod({ getSignature: () => action(getUploadSignature, undefined, { errorType: 'throw' }) });
  return vod;
};

export const uploadToVod = async (options: {
  params: Omit<UploaderOptions, 'getSignature'>;
  onMediaProgress?: (info: ProgressEvent) => void;
  onMediaUpload?: (info: UploadEvent) => void;
}) => {
  const { params, onMediaProgress, onMediaUpload } = options;
  return new Promise<UploadDoneInfo>((resolve) => {
    const vod = getVodClient();
    const uploader = vod.upload(params);
    uploader.on('media_progress', (info) => {
      onMediaProgress?.(info);
    });
    uploader.on('media_upload', (info) => {
      onMediaUpload?.(info);
    });
    uploader.done().then((info) => {
      resolve(info);
    });
  });
};
