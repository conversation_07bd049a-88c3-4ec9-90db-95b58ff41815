import { Button, Calendar, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { cn } from '@/utils/cn';
import dayjs from 'dayjs';
import { Calendar as CalendarIcon } from 'lucide-react';
export const QuickDataPicker: React.FC<{
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
}> = ({ date, setDate }) => {
  return (
    <div className="flex items-center gap-4">
      <Button
        variant="link"
        className={cn(
          'text-xs',
          date && dayjs(date).isSame(dayjs().subtract(2, 'day').toDate(), 'day') && 'text-[#00e1ff]',
        )}
        onClick={() => setDate(dayjs().subtract(2, 'day').toDate())}
      >
        前天({dayjs().subtract(2, 'day').format('MM-DD')})
      </Button>
      <Button
        variant="link"
        className={cn(
          'text-xs',
          date && dayjs(date).isSame(dayjs().subtract(1, 'day').toDate(), 'day') && 'text-[#00e1ff]',
        )}
        onClick={() => setDate(dayjs().subtract(1, 'day').toDate())}
      >
        昨天({dayjs().subtract(1, 'day').format('MM-DD')})
      </Button>
      <Button
        variant="link"
        className={cn('text-xs', date && dayjs(date).isSame(dayjs().toDate(), 'day') && 'text-[#00e1ff]')}
        onClick={() => setDate(dayjs().toDate())}
      >
        今天({dayjs().format('MM-DD')})
      </Button>
      <Button
        variant="link"
        className={cn('text-xs', date && dayjs(date).isSame(dayjs().add(1, 'day').toDate(), 'day') && 'text-[#00e1ff]')}
        onClick={() => setDate(dayjs().add(1, 'day').toDate())}
      >
        明天({dayjs().add(1, 'day').format('MM-DD')})
      </Button>
      <Button
        variant="link"
        className={cn('text-xs', date && dayjs(date).isSame(dayjs().add(2, 'day').toDate(), 'day') && 'text-[#00e1ff]')}
        onClick={() => setDate(dayjs().add(2, 'day').toDate())}
      >
        后天({dayjs().add(2, 'day').format('MM-DD')})
      </Button>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn('h-[32px] w-[280px] justify-start text-left font-normal', !date && 'text-muted-foreground')}
          >
            <CalendarIcon className="mr-2 w-4" />
            {date ? dayjs(date).format('YYYY-MM-DD') : <span>选择日期</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto bg-[#262F40] p-0">
          <Calendar mode="single" selected={date} onSelect={setDate} />
        </PopoverContent>
      </Popover>
    </div>
  );
};
