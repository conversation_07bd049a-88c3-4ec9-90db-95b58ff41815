import React from 'react';
import {
  <PERSON><PERSON><PERSON>rumb,
  Bread<PERSON><PERSON>b<PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
  BreadcrumbPage,
  DropdownMenuItem,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  Button,
} from '@/components/ui';
import { DirItemType } from '@/types/material';
import { cn } from '@/utils/cn';

// todo: 改成pro-breadcrumb
const BreadCrumbs = ({
  breadcrumbItems,
  handleBreadcrumbClick,
  disabled = false,
}: {
  breadcrumbItems: DirItemType[];
  handleBreadcrumbClick: (item: DirItemType, index: number) => void;
  disabled: boolean;
}) => {
  const renderBreadcrumbItem = (item: DirItemType, index: number) => {
    const isRoot = index === 0;
    const isLast = index === breadcrumbItems.length - 1;
    const showEllipsis = breadcrumbItems.length > 3 && index === 1;

    if (breadcrumbItems.length > 3 && !isRoot && !isLast && !showEllipsis) {
      return null;
    }

    if (showEllipsis) {
      return (
        <BreadcrumbItem key="ellipsis" className={cn({ 'cursor-pointer hover:text-white': !disabled })}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-5 p-0">
                <BreadcrumbEllipsis />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {breadcrumbItems.slice(1, -1).map((dropdownItem, dropdownIndex) => (
                <DropdownMenuItem
                  className={cn({ 'cursor-pointer hover:text-white': !disabled })}
                  key={dropdownIndex}
                  onClick={() => {
                    if (disabled) return;
                    handleBreadcrumbClick(dropdownItem, dropdownIndex + 1);
                  }}
                >
                  {dropdownItem.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </BreadcrumbItem>
      );
    }

    if (isLast) {
      return (
        <BreadcrumbItem key={index} className={cn({ 'cursor-pointer text-white hover:text-white': !disabled })}>
          <BreadcrumbPage>{item.name}</BreadcrumbPage>
        </BreadcrumbItem>
      );
    }

    return (
      <BreadcrumbItem key={index} className={cn({ 'cursor-pointer hover:text-white': !disabled })}>
        <span
          onClick={() => {
            if (disabled) return;
            handleBreadcrumbClick(item, index);
          }}
        >
          {item.name}
        </span>
      </BreadcrumbItem>
    );
  };

  return (
    <Breadcrumb className={cn({ 'cursor-not-allowed': disabled })}>
      <BreadcrumbList className="h-5">
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={index}>
            {renderBreadcrumbItem(item, index)}
            {index < breadcrumbItems.length - 1 && renderBreadcrumbItem(item, index) && <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumbs;
