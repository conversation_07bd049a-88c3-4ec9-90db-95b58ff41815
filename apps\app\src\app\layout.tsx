import dynamic from 'next/dynamic';
import React from 'react';
import './globals.css';
import { SidebarProvider } from '@/components/ui/Sidebar';

const SwrProvider = dynamic(() => import('@/components/SwrProvider'), { ssr: false });

const isDev = process.env.NODE_ENV === 'development';
const bodyWidth = isDev ? '1024px' : '1440px';

const RootLayout = ({ children }: React.PropsWithChildren) => {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <title>Roasmax | 不忘智能</title>
        <meta name="robots" content="noindex, nofollow" />
        <meta name="googlebot" content="noindex, nofollow" />
      </head>
      <body
        className="text dark"
        style={{
          minWidth: bodyWidth,
          backgroundImage: 'url(/background/gradient.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center 30%',
        }}
      >
        <SwrProvider>
          <SidebarProvider>{children}</SidebarProvider>
        </SwrProvider>
      </body>
    </html>
  );
};

export default RootLayout;
