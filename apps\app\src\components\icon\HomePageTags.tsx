export const HomePageTags = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="6" fill="#00E1FF" />
      <g filter="url(#filter0_f_4798_364)">
        <circle cx="10" cy="10" r="6" fill="#00E1FF" />
      </g>
      <circle cx="10" cy="10" r="4" fill="#1B2435" />
      <defs>
        <filter
          id="filter0_f_4798_364"
          x="0"
          y="0"
          width="20"
          height="20"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_4798_364" />
        </filter>
      </defs>
    </svg>
  );
};
