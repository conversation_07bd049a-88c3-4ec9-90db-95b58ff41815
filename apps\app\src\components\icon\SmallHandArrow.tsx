export const SmallHandArrow = () => {
  return (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M1.71963 11.6958C1.85421 11.698 1.98535 11.6525 2.09058 11.567C2.19582 11.4816 2.2686 11.3615 2.29646 11.2275C2.32433 11.0934 2.30553 10.9536 2.2433 10.8321C2.18108 10.7106 2.07928 10.6149 1.95537 10.5614C1.83145 10.5079 1.69312 10.5 1.56407 10.5389C1.43502 10.5778 1.32329 10.6612 1.24801 10.7749C1.17273 10.8885 1.13859 11.0252 1.15144 11.1616C1.16429 11.298 1.22333 11.4256 1.31844 11.5226C1.3703 11.5776 1.4326 11.6214 1.50157 11.6511C1.57053 11.6809 1.64472 11.6961 1.71963 11.6958ZM8.56071 12.8632C9.68885 12.8632 10.2529 12.3556 10.2529 11.3404C10.2518 11.1701 10.237 11.0001 10.2088 10.8323C10.3979 10.724 10.5472 10.5559 10.6341 10.3535C10.7364 10.1454 10.7912 9.91638 10.7945 9.68363C10.7962 9.46324 10.7408 9.24629 10.6341 9.05457C10.7842 8.91838 10.9042 8.75119 10.9859 8.56413C11.0677 8.37706 11.1094 8.17441 11.1083 7.96966C11.1047 7.79732 11.0744 7.62665 11.0184 7.46397C10.9729 7.30588 10.8968 7.15867 10.7945 7.03098H13.7585C14.0573 7.02293 14.3411 6.89645 14.5496 6.67847C14.7581 6.46049 14.8748 6.16824 14.8748 5.86396C14.8748 5.55969 14.7581 5.26744 14.5496 5.04946C14.3411 4.83148 14.0573 4.705 13.7585 4.69695H8.59762C8.60747 4.54093 8.65339 4.3895 8.73161 4.25498C8.82085 4.08234 8.91943 3.91488 9.02689 3.75337C9.14985 3.55975 9.24901 3.3515 9.32216 3.1333C9.41068 2.88644 9.45603 2.6257 9.45616 2.36291C9.46958 2.19218 9.43974 2.02081 9.3695 1.86518C9.29925 1.70955 9.19096 1.57489 9.05497 1.47407C8.74734 1.27577 8.38773 1.17747 8.02392 1.19222C7.88056 1.19222 7.6131 1.61458 7.22154 2.45931C7.07818 2.72673 6.96772 2.92416 6.89015 3.0516C6.59214 3.51957 6.25667 3.96169 5.88718 4.37343C5.6064 4.71498 5.30435 5.03779 4.9829 5.33989C4.63859 5.659 4.19411 5.84315 3.72878 5.85947H3.43832V11.695H3.72477C4.23543 11.7101 4.7404 11.8086 5.22041 11.9866C5.78742 12.1811 6.36486 12.3755 6.95274 12.5699C7.4694 12.7519 8.01089 12.8504 8.5575 12.8616L8.56071 12.8632ZM8.60564 14.0306C7.61618 13.99 6.64086 13.7772 5.72189 13.4016C5.08406 13.1283 4.41186 12.9471 3.72477 12.8632H1.14673C0.842698 12.863 0.551179 12.7399 0.336196 12.521C0.121213 12.3022 0.000342893 12.0053 0.000130356 11.6958V5.86029C-0.00218837 5.70666 0.0264485 5.55418 0.0842727 5.41226C0.142097 5.27035 0.227881 5.14201 0.336327 5.03516C0.441262 4.92475 0.567314 4.83741 0.706699 4.77853C0.846084 4.71966 0.995844 4.6905 1.14673 4.69286H3.72557C3.7917 4.69157 3.85701 4.67772 3.91814 4.65201C3.99403 4.62037 4.06488 4.57742 4.12837 4.52457L4.32976 4.36118C4.40546 4.29732 4.47727 4.22885 4.5448 4.15612L4.72855 3.96006C4.77027 3.91485 4.82697 3.84949 4.89865 3.76399C4.97033 3.67848 5.01205 3.62674 5.02382 3.60877C5.35083 3.24002 5.65016 2.84678 5.91928 2.43235C5.99684 2.30491 6.09527 2.11646 6.21455 1.86702C6.33384 1.61758 6.4443 1.39972 6.54593 1.21346C6.65453 1.01504 6.77568 0.824016 6.90861 0.641592C7.03856 0.456273 7.2067 0.302089 7.40127 0.189817C7.59141 0.081362 7.80597 0.0250597 8.02392 0.0264264C8.69063 0.000997262 9.34458 0.217247 9.86939 0.63669C10.1136 0.84683 10.3066 1.11182 10.4332 1.41079C10.5597 1.70977 10.6164 2.03458 10.5987 2.35964C10.6013 2.75761 10.5344 3.15287 10.4014 3.52707H13.7505C14.3509 3.53851 14.923 3.7894 15.3437 4.2258C15.7643 4.66219 16 5.24923 16 5.8607C16 6.47216 15.7643 7.0592 15.3437 7.4956C14.923 7.93199 14.3509 8.18288 13.7505 8.19432H12.2444C12.2188 8.57737 12.1053 8.94896 11.913 9.27924C11.9313 9.40912 11.9404 9.54017 11.9403 9.67137C11.9491 10.2591 11.7597 10.8321 11.4035 11.2947C11.4245 11.6643 11.3672 12.0342 11.2354 12.3792C11.1036 12.7243 10.9004 13.0365 10.6397 13.2946C10.0825 13.801 9.35256 14.065 8.60644 14.0298L8.60564 14.0306Z"
        fill="#050A1C"
      />
    </svg>
  );
};
