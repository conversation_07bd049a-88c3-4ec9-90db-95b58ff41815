WITH recursive `numbers` AS (
  SELECT
    1 AS `n`
  UNION
  ALL
  SELECT
    (`numbers`.`n` + 1) AS `n + 1`
  FROM
    `numbers`
  WHERE
    (`numbers`.`n` < 100)
),
`json_array_elements` AS (
  SELECT
    `g`.`name` AS `name`,
    json_unquote(
      json_extract(
        `g`.`images`,
        concat('$[',(`numbers`.`n` - 1), ']')
      )
    ) AS `image_url`,
    json_unquote(
      json_extract(`g`.`tags`, concat('$[',(`numbers`.`n` - 1), ']'))
    ) AS `tag`,
    json_unquote(
      json_extract(
        `g`.`live_rooms`,
        concat('$[',(`numbers`.`n` - 1), ']')
      )
    ) AS `live_room`,
    json_unquote(
      json_extract(`g`.`alias`, concat('$[',(`numbers`.`n` - 1), ']'))
    ) AS `alias`
  FROM
    (
      `slice_bak`.`goods` `g`
      JOIN `numbers` ON(
        (
          (
            json_unquote(
              json_extract(
                `g`.`images`,
                concat('$[',(`numbers`.`n` - 1), ']')
              )
            ) IS NOT NULL
          )
          OR (
            json_unquote(
              json_extract(`g`.`tags`, concat('$[',(`numbers`.`n` - 1), ']'))
            ) IS NOT NULL
          )
          OR (
            json_unquote(
              json_extract(
                `g`.`live_rooms`,
                concat('$[',(`numbers`.`n` - 1), ']')
              )
            ) IS NOT NULL
          )
          OR (
            json_unquote(
              json_extract(`g`.`alias`, concat('$[',(`numbers`.`n` - 1), ']'))
            ) IS NOT NULL
          )
        )
      )
    )
  WHERE
    (`g`.`tmp_deleted_at` IS NULL)
),
`grouped_goods` AS (
  SELECT
    `slice_bak`.`goods`.`name` AS `name`,
    `slice_bak`.`goods`.`tenant_id` AS `tenant_id`,
    count(0) AS `goods_count`
  FROM
    `slice_bak`.`goods`
  WHERE
    (`slice_bak`.`goods`.`tmp_deleted_at` IS NULL)
  GROUP BY
    `slice_bak`.`goods`.`name`,
    `slice_bak`.`goods`.`tenant_id`
  HAVING
    (count(0) > 1)
)
SELECT
  concat(`g`.`name`, '_', `g`.`tenant_id`) AS `id`,
  `g`.`name` AS `name`,
  `g`.`tenant_id` AS `tenant_id`,
  `gg`.`goods_count` AS `goods_count`,
  json_arrayagg(`g`.`id`) AS `ids`,
  json_arrayagg(`g`.`tenant_id`) AS `tenant_ids`,
  json_arrayagg(`jae`.`image_url`) AS `images`,
  json_arrayagg(`jae`.`tag`) AS `tags`,
  json_arrayagg(`jae`.`live_room`) AS `live_rooms`,
  json_arrayagg(`jae`.`alias`) AS `aliases`,
  json_arrayagg(`g`.`properties`) AS `properties`,
  json_arrayagg(`g`.`ip`) AS `ips`,
  json_arrayagg(`g`.`platform`) AS `platforms`,
  json_arrayagg(`g`.`short_sn`) AS `short_sns`,
  json_arrayagg(`g`.`product_identity`) AS `product_identities`,
  json_arrayagg(`g`.`product_url`) AS `product_urls`,
  json_arrayagg(`g`.`hot_product`) AS `hot_products`,
  json_arrayagg(`g`.`shop_name`) AS `shop_names`,
  json_arrayagg(`g`.`commission_rate`) AS `commission_rates`,
  json_arrayagg(`g`.`remark`) AS `remarks`,
  json_arrayagg(`g`.`price`) AS `prices`,
  json_arrayagg(`g`.`stock_amount`) AS `stock_amounts`,
  json_arrayagg(`g`.`link`) AS `links`
FROM
  (
    (
      `slice_bak`.`goods` `g`
      LEFT JOIN `json_array_elements` `jae` ON((`g`.`name` = `jae`.`name`))
    )
    JOIN `grouped_goods` `gg` ON(
      (
        (`g`.`name` = `gg`.`name`)
        AND (`g`.`tenant_id` = `gg`.`tenant_id`)
      )
    )
  )
WHERE
  (`g`.`tmp_deleted_at` IS NULL)
GROUP BY
  `g`.`name`,
  `g`.`tenant_id`,
  `gg`.`goods_count`
ORDER BY
  `gg`.`goods_count` DESC