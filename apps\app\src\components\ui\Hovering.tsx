import { cn } from '@/utils/cn';
import React from 'react';

export const Hovering = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => {
  return (
    <div ref={ref} {...props} className={cn('hovering relative', props.className)}>
      {props.children}
    </div>
  );
});

Hovering.displayName = 'Hovering';

export const HoveringMaskContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div ref={ref} {...props} className={cn('mask-content absolute w-full h-full bg-black bg-opacity-50', className)}>
        {props.children}
      </div>
    );
  },
);

HoveringMaskContent.displayName = 'HoveringMaskContent';
