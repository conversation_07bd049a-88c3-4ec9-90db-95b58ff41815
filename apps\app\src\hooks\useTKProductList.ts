import { ProductListResponse, ProductListResponseWrapper } from '@/services/interfaces/ads/res';
import tiktokService from '@/services/tiktokService';
import { useCurrentAdvertiser } from '@/store/ads/adStore';
import { ToCamelCase } from '@/utils/camel';
import { useEffect, useState } from 'react';
import useSWR from 'swr';
import emitter from '@/utils/mitt';

// 抽离 fetcher 函数
export const productFetcher = async (params: {
  page: number;
  pageSize: number;
  bc_id: string;
  store_id: string;
  advertiser_id: string;
}) => {
  const { page, pageSize, bc_id, store_id, advertiser_id } = params;
  const data = await tiktokService.getProductList({
    page,
    page_size: pageSize,
    bc_id,
    store_id,
    advertiser_id,
  });

  return data;
};

interface PaginationState {
  page: number;
  pageSize: number;
}

export function useTKProductList(bc_id: string, store_id: string, initialPage = 1, initialPageSize = 10) {
  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
  });
  const currentAdvertiser = useCurrentAdvertiser();
  const advertiserId = currentAdvertiser?.[0]?.advertiserId;

  const swrKey = store_id
    ? ['/api/ads/products', { ...pagination, bc_id, store_id, advertiser_id: advertiserId }]
    : null;

  const { data, error, isLoading, isValidating, mutate } = useSWR<ToCamelCase<ProductListResponseWrapper>>(
    swrKey,
    ([_, params]) => productFetcher(params as any),
    {
      keepPreviousData: false,
      revalidateOnFocus: false,
      revalidateIfStale: true,
      revalidateOnMount: true,
      revalidateOnReconnect: true,
    },
  );

  useEffect(() => {
    const handleRefresh = (params: { page?: number; pageSize?: number }) => {
      const { page, pageSize } = params;
      if (page !== undefined || pageSize !== undefined) {
        setPagination((prev) => ({
          page: page ?? prev.page,
          pageSize: pageSize ?? prev.pageSize,
        }));
      }
      mutate();
    };

    // 使用 mitt
    emitter.on('REFRESH_PRODUCT_LIST', handleRefresh);
    return () => {
      emitter.off('REFRESH_PRODUCT_LIST', handleRefresh);
    };
  }, [mutate]);

  return {
    products: data?.data,
    isLoading: isLoading || isValidating,
    isError: error,
    refresh: () => mutate(),
    mutate: (newPage?: number, newPageSize?: number) => {
      if (newPage !== undefined || newPageSize !== undefined) {
        setPagination((prev) => ({
          page: newPage ?? prev.page,
          pageSize: newPageSize ?? prev.pageSize,
        }));
      }
    },
  };
}
