'use client';

import React, { memo, useEffect, useRef, useState } from 'react';
import { cn } from '@/utils/cn';
import { Directory } from '../icon/directory';
import { Input } from '@/components/ui';
import { INPUT_PLACEHOLDER } from '@/common/statics/zh_cn';
import { DirItemType } from '@/types/material';

interface IDirItemProps {
  each: DirItemType;
  handleClick: (item: DirItemType) => void;
  cancelCreating: () => void;
  submitCreating: (name: string, parentId?: string) => void;
  isSubmitting: boolean;
}

const DirItem: React.FC<IDirItemProps> = ({ handleClick, each, cancelCreating, submitCreating, isSubmitting }) => {
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (each.status === 'creating' && inputRef.current) {
      inputRef.current.focus();
    }
  }, [each.status]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isSubmitting) {
      if (e.key === 'Enter' && inputValue.trim()) {
        submitCreating(inputValue.trim(), each?.parent_id || '');
      } else if (e.key === 'Escape') {
        cancelCreating();
      }
    }
  };

  return (
    <div
      onClick={() => {
        if (each.status !== 'creating') {
          handleClick(each);
        }
      }}
      className={cn(
        'px-5.5 flex h-11 w-full flex-shrink-0 cursor-pointer flex-nowrap items-center justify-between gap-1.5 rounded py-2.5 text-black transition-colors duration-200 ease-in',
        {
          'pointer-events-none opacity-50': isSubmitting && each.status === 'creating',
        },
      )}
    >
      <div className="flex w-[70%] items-center gap-3">
        <Directory className="h-10 w-10" />
        {each.status === 'creating' ? (
          <div className="relative flex w-4/5 items-center">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={INPUT_PLACEHOLDER}
              maxLength={20}
              disabled={isSubmitting}
              className="flex h-10 items-center border border-[#292E3E] bg-transparent placeholder:text-sm placeholder:text-[#81889d] focus:border-[#00E1FF]"
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500">
              {inputValue.length}/20
            </span>
          </div>
        ) : (
          <span className="text-sm text-white">{each.name}</span>
        )}
      </div>
      <div className="flex-shrink-0 whitespace-nowrap">
        {each.status === 'creating' ? (
          <div className="flex items-center gap-4">
            {!isSubmitting && (
              <div
                className="text-xs text-[#00e1ff]"
                onClick={(e) => {
                  e.stopPropagation();
                  if (isSubmitting) return;
                  cancelCreating();
                }}
              >
                取消
              </div>
            )}
            <div
              className={cn('text-xs text-[#00e1ff]', {
                'pointer-events-none opacity-50': inputValue.trim().length === 0,
              })}
              onClick={(e) => {
                e.stopPropagation();
                if (isSubmitting) return;
                submitCreating(inputValue.trim(), each?.parent_id || '');
              }}
            >
              {isSubmitting ? '创建中...' : '确认'}
            </div>
          </div>
        ) : (
          <span className="text-xs font-medium text-[#81889d]">
            创建时间: {new Date(each?.tmp_created_at ?? '').toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
          </span>
        )}
      </div>
    </div>
  );
};

export default memo(DirItem);
