import { generateCloudTerminalShareLink } from '@/services/domains/social';
import { prisma } from '@/utils/prisma';
import { createContext } from '@roasmax/serve';
import { Logger } from '@roasmax/utils';
import jwt from 'jsonwebtoken';
import { NextResponse } from 'next/server';

export const POST = async (request: Request) => {
  try {
    const requestIp = request.headers.get('x-forwarded-for');
    // 从请求中获取 token
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: '缺少 token' }, { status: 400 });
    }

    // 解析 token
    const { tenant_id, social_account_id } = jwt.verify(token, process.env.WEBHOOK_SECRET!) as {
      tenant_id: string;
      social_account_id: string;
      social_account_secret: string;
    };
    // 验证租户
    const source_config = await prisma.source_configs.findUnique({ where: { tenant_id: tenant_id } });
    if (!source_config) {
      throw new Error('Unauthorized');
    }

    const logger = new Logger(`${request.url ?? '-'}`, requestIp ?? '-', generateRandomBase36(10));

    const context = await createContext(
      { data: {} },
      { user: { id: 'system' }, tenant: { id: tenant_id, name: '', config: source_config }, logger },
    );

    const res = await context.execute(generateCloudTerminalShareLink, { accountId: social_account_id });
    return NextResponse.json({ success: true, data: res }, { status: 200 });
  } catch (error: any) {
    console.error('Token 解析错误:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: '无效的 token' }, { status: 401 });
    }
    return NextResponse.json({ error: error?.message || '未知错误' }, { status: 500 });
  }
};

function generateRandomBase36(length: number): string {
  let result = '';
  while (result.length < length) {
    result += Math.random().toString(36).substring(2);
  }
  return result.substring(0, length);
}
