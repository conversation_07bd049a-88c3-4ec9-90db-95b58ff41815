import useAdStore, {
  useAdActions,
  useCreateAdStatus,
  useCurrentAdGroup,
  useGenerateAdTextStatus,
} from '@/store/ads/adStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Sheet,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  Switch,
} from '@/components/ui';
import { usePrompts } from '@/hooks/usePrompts';
import { useRoots } from '@/hooks/useRoots';
import { useUpload } from '@/hooks/useUpload';
import useMaterialStore from '@/store/materialStore';
import { StoreProductItemType } from '@/types/ads';
import { getVideoDuration } from '@/utils/common';
import CloudSheet from './cloudSheet';
import { useWallet } from '@/hooks/useWallet';
import GoodsDialog from '../../Dialog/GoodsDialog';
import tiktokService from '@/services/tiktokService';
import { adFormSchema, type AdFormValues } from './adFormSchema';

import { AdGroupField } from '../fields/AdGroupField';
import { VideoField } from '../fields/VideoField';
import { AdTextField } from '../fields/AdTextField';
import { GoodsField } from '../fields/GoodsField';
import { IdentityField } from '../fields/IdentityField';
import { QuickCreateFields } from '../fields/QuickCreateFields';
import { getDefaultFormValues, calculateExpectVideoCount, validateForm } from '../utils';
import type { AdSheetProps } from '../types';
import { SectionTitle } from '../SectionTitle';
import { ProMask } from '@/components/pro/pro-mask';

export function AdSheet({ onOpenChange, onSubmit }: AdSheetProps) {
  const { setCloudSheetOpen, generateAdText, cancelGenerateAdText, createOrUpdateAd } = useAdActions();
  const { setUploadTargetDir } = useMaterialStore();
  const { cloudSheetOpen, adRightDrawer } = useAdStore();
  const currentAdGroup = useCurrentAdGroup();
  const { loading: isGenerating } = useGenerateAdTextStatus();
  const { loading: isCreating } = useCreateAdStatus();
  const { tree: promptTree, loading: loadingPrompts } = usePrompts();
  const abortControllerRef = useRef<AbortController | null>(null);
  const { show: open, type, title, formValue } = adRightDrawer;
  const uploadRef = useRef<HTMLInputElement>(null);
  const { data: roots } = useRoots();
  const { quota, refresh: refreshWallet } = useWallet();
  const [selectedRowKeys, setSelectedRowKeys] = useState<StoreProductItemType[]>([]); // 选中的商品
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [adsIdentity, setAdsIdentity] = useState<any[]>([]); // 广告身份
  const { uploadList } = useMaterialStore();

  const form = useForm<AdFormValues>({
    resolver: zodResolver(adFormSchema),
    defaultValues: getDefaultFormValues(type, type === 'edit' ? formValue : undefined, currentAdGroup ?? []),
  });

  useEffect(() => {
    if (type === 'edit') {
      if (!formValue) return;
      form.reset(getDefaultFormValues(type, formValue, currentAdGroup ?? []));
    } else {
      form.setValue('sheetType', adRightDrawer?.createType === 'quick' ? 'quick' : 'normal');
    }
  }, [formValue, type, adRightDrawer?.createType]);

  const handleSubmit = async (values: AdFormValues) => {
    try {
      const adData = {
        ...values,
        selectedVideoItem: values?.selectedVideoItem || [],
        adgroupIds: values?.adgroupIds || [],
        ...(type === 'edit' && formValue ? { id: formValue.id } : {}),
      };

      await createOrUpdateAd(adData, type === 'edit' ? formValue : undefined);

      if (onSubmit) {
        await onSubmit(values);
      }

      form.reset();
      handleCancel();
      onOpenChange(false);
    } catch (error) {
      console.error('提交失败:', error);
      toast.error('提交失败');
    }
  };

  const handleCancel = useCallback(() => {
    form.reset();
    setSelectedRowKeys([]);
    setAdsIdentity([]);
    form.setValue('itemGroupIds', []);
    form.setValue('productNames', []);
    onOpenChange(false);
  }, [form, onOpenChange]);

  const handleCancelGeneration = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      cancelGenerateAdText();
      toast.success('已取消生成');
    }
  };

  const handleGenerateAdText = async () => {
    const adName = form.getValues('adName');

    if (!adName) {
      toast.error('请先填写广告名称');
      return;
    }

    try {
      abortControllerRef.current = new AbortController();
      const result = await generateAdText(
        adName,
        (text: string) => {
          form.setValue('adText', text);
        },
        abortControllerRef.current.signal,
      );
      if (result) {
        toast.success('AI 文案生成成功');
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        toast.error('生成文案失败');
      }
    } finally {
      abortControllerRef.current = null;
    }
  };

  const handleVideoSelect = (list: any[]) => {
    const isContainVideoMoreThen60s = list.some((item) => item?.video_duration && item?.video_duration > 60);
    const filteredList = list.filter((item) => item?.video_duration && item?.video_duration <= 60);
    const theList = isQuickCreate ? list : filteredList;
    if (isContainVideoMoreThen60s && !isQuickCreate) {
      toast.error('常规创建下, 视频时长不能超过60秒, 已自动过滤');
    }

    const prevSelectedVideoItem = form?.getValues('selectedVideoItem') ?? [];
    form.setValue('selectedVideoItem', [...prevSelectedVideoItem, ...theList]);
    calculateExpectVideoCount([...prevSelectedVideoItem, ...theList], form, currentAdGroup ?? []);
  };

  const handleLocalUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const videoDuration = await getVideoDuration(file);
    if (videoDuration && videoDuration > 60 && !isQuickCreate) {
      toast.error('常规创建下, 视频时长不能超过60秒, 已自动过滤');
      return;
    }
    handleFileChange(e, { openModal: false });
  };

  const handleClickSubmit = () => {
    form.setValue('sheetType', type === 'create' ? (adRightDrawer?.createType ?? 'normal') : 'edit');

    if (form.getValues('sheetType') === 'quick') {
      calculateExpectVideoCount(form.getValues('selectedVideoItem') ?? [], form, currentAdGroup ?? []);
    }

    if (type === 'edit' && !form.getValues('adgroupIds')) {
      form.setValue('adgroupIds', [formValue?.groupId ?? formValue?.groupId]);
    }

    form.trigger().then(() => {
      const validation = validateForm(form, quota ?? 0, isQuickCreate);
      if (!validation.valid) {
        toast.error(validation?.message ?? '', {
          duration: 3000,
          position: 'top-center',
        });
        return;
      }

      if (isQuickCreate) {
        refreshWallet();
      }

      form.handleSubmit(handleSubmit)();
    });
  };

  const isQuickCreate = adRightDrawer.createType === 'quick' && type === 'create';

  const { handleFileChange } = useUpload((material) => {
    if (uploadRef.current) {
      uploadRef.current.value = '';
    }

    const currentItems = form.getValues('selectedVideoItem') || [];
    const newItems = [...currentItems, material];
    form.setValue('selectedVideoItem', newItems);
    calculateExpectVideoCount(newItems, form, currentAdGroup ?? []);

    const newUploadList = uploadList.filter((item) => item.id !== material.id);
    useMaterialStore.setState({ uploadList: newUploadList });
  });

  const handleStoreChange = async () => {
    console.log('currentAdGroup', currentAdGroup);
    if (!currentAdGroup || currentAdGroup.length === 0) return;
    try {
      const res = await tiktokService.getIdentityList({
        advertiser_id: currentAdGroup[0]?.advertiserId ?? '',
        identity_type: 'BC_AUTH_TT',
        identity_authorized_bc_id: currentAdGroup[0]?.jsonDate?.storeAuthorizedBcId,
      });
      // @ts-ignore
      setAdsIdentity(res?.data?.identityList || []);
    } catch {
      return;
    }
  };

  const handleSelectedRowKeysChange = (newSelectedRows: StoreProductItemType[]) => {
    setSelectedRowKeys(newSelectedRows);
    const itemGroupIds = newSelectedRows.map((item) => item.itemGroupId);
    form.setValue('itemGroupIds', itemGroupIds);
    form.setValue(
      'productNames',
      newSelectedRows.map((item) => item.title),
    );
  };

  useEffect(() => {
    if (open) {
      handleStoreChange();
    }
  }, [open]);

  useEffect(() => {
    setUploadTargetDir(roots?.find((item) => item.name === '原始素材'));
  }, [roots]);

  return (
    <Sheet
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          handleCancel();
        }
      }}
    >
      <SheetContent className="flex min-w-[800px] flex-col bg-[#151c29]">
        <ProMask loading={isCreating} />
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>{title}</SheetTitle>
          </div>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto pl-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
              {type === 'create' && <AdGroupField form={form} currentAdGroup={currentAdGroup ?? []} />}

              <div>
                <SectionTitle title="广告" />

                <div className="pl-10">
                  <GoodsField
                    form={form}
                    type={type}
                    formValue={type === 'edit' ? formValue : undefined}
                    selectedRowKeys={selectedRowKeys}
                    setSelectedRowKeys={handleSelectedRowKeysChange}
                    setIsModalOpen={setIsModalOpen}
                  />

                  {type !== 'edit' && (
                    <>
                      <IdentityField form={form} adsIdentity={adsIdentity} />
                      <VideoField
                        selectedVideoItem={form.getValues('selectedVideoItem')}
                        form={form}
                        isCreating={isCreating}
                        uploadRef={uploadRef}
                        uploadList={uploadList}
                        onLocalUpload={handleLocalUpload}
                      />
                    </>
                  )}

                  {!isQuickCreate && (
                    <AdTextField
                      form={form}
                      isGenerating={isGenerating}
                      type={type}
                      onGenerate={handleGenerateAdText}
                      onCancelGeneration={handleCancelGeneration}
                    />
                  )}

                  {isQuickCreate && (
                    <QuickCreateFields form={form} loadingPrompts={loadingPrompts} promptTree={promptTree} />
                  )}

                  <FormField
                    control={form.control}
                    name="darkPostStatus"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">只作为广告展示</FormLabel>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={type === 'edit'} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="operationStatus"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">广告状态</FormLabel>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={type === 'edit'} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </form>
          </Form>
        </div>
        <SheetFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isCreating}
            className="h-8 w-[90px] flex-shrink-0 text-sm font-normal text-white"
          >
            取消
          </Button>
          <Button
            onClick={handleClickSubmit}
            disabled={isGenerating || isCreating}
            className="h-8 w-[90px] flex-shrink-0 bg-[#00E1FF] text-center text-sm font-normal text-[#050A1C] hover:bg-[#00E1FF]/90"
          >
            {isCreating ? '提交中...' : '确定'}
          </Button>
        </SheetFooter>
        {currentAdGroup && currentAdGroup.length > 0 && (
          <GoodsDialog
            openDialog={isModalOpen}
            setOpenDialog={setIsModalOpen}
            bcId={currentAdGroup[0]?.jsonDate?.storeAuthorizedBcId ?? ''}
            storeId={currentAdGroup[0]?.jsonDate?.storeId ?? ''}
            handleGoodsSelection={handleSelectedRowKeysChange}
          />
        )}
        <CloudSheet
          open={cloudSheetOpen}
          onOpenChange={setCloudSheetOpen}
          onConfirmSelect={handleVideoSelect}
          isQuickCreate={isQuickCreate}
        />
      </SheetContent>
    </Sheet>
  );
}
