import { pageVideoDistributionSubTasks } from '@/services/domains/task/video-distribution-task';
import { ActionContext, webhook } from '@roasmax/serve';
import { calcDailyVideoCountByFansCount, VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';

/**
 * 获取一个可发布的视频
 *
 * 1. 取得当天应发布的视频数量
 * 2. 根据视频数量，平均分配每一个视频应发布的时间。发布时间为早6点到晚22点。比如，有两条视频，则发布时间为10点和18点(取6点到22点等分两份后，每份的中点)
 * 3. 这个时间将作为视频的计划发布时间
 * 4. 如果对应计划发布时间的视频已经在发布中或已发布，则本次不再返回视频
 */
export const POST = webhook(async (ctx: ActionContext<{ filters: { account_identity?: string } }>) => {
  const social_account = await ctx.db.social_accounts.findFirst({
    where: { account_identity: ctx.data.filters.account_identity },
  });
  if (!social_account) {
    throw new Error('抖音账号不存在');
  }

  // 获取当天当前账号未发布的视频, 早于当前时间且离当前时间最近
  const sub_task = await ctx.db.video_distribution_sub_tasks.findFirst({
    where: {
      social_account_id: social_account.id,
      status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已分发,
      publish_plan_at: { lte: new Date() },
    },
    orderBy: { publish_plan_at: 'asc' },
  });

  if (!sub_task) {
    return null;
  }

  const url = await ctx.cos.getObjectUrlAsync({
    Region: process.env.COS_REGION,
    Bucket: process.env.COS_BUCKET!,
    Key: sub_task.distributed_cos_path!,
  });

  return {
    publish_title: sub_task.publish_title,
    publish_topic: sub_task.publish_topic?.join(' '),
    publish_product_link: sub_task.publish_product_link,
    publish_video_link: url,
  };
});
