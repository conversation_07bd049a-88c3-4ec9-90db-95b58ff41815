export interface UploadResult {
  url: string;
  taskName: string;
  taskId: string;
}

export type VideoType =
  | 'tea'
  | 'chop'
  | 'lady'
  | 'vlog'
  | 'lady_minority'
  | 'lady_school'
  | 'lady_outfits'
  | 'vlog_storm';

export interface VideoItem {
  id: string;
  type: VideoType;
  title: string;
  prompt: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  status?: string;
  progress?: number;
  error_message?: string;
  // 新增字段用于视频配置
  aspectRatio?: string;
  engine?: string;
}

export interface CreateTaskRequest {
  prompt: string;
  type: VideoType;
  files: File[] | UploadResult[];
}

// API请求格式 - 根据文档定义
export interface CreateTaskApiRequest {
  task_type: string;
  prompt: string;
  img_url: string;
  duration: string;
}

// API响应格式 - 根据实际API响应
export interface CreateTaskApiResponse {
  status: boolean;
  data: string; // 这是真正的taskId
  msg: {
    task_type: string;
    prompt: string;
    img_url: string;
    duration: string;
  };
}

// 查询任务响应格式 - 支持多种格式
export type QueryTaskApiResponse =
  | string
  | {
      status: 'failed' | 'completed';
      msg?: string;
      data?: {
        video_url?: string;
        image_url?: string;
        error?: string;
      };
      message?: string;
      error?: string;
    };

// 内部使用的响应格式
export interface CreateTaskResponse {
  taskId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'error';
}

export interface QueryTaskResponse {
  taskId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'error';
  result?: {
    // 视频相关字段
    videoUrl?: string;
    imageUrl?: string;
    thumbnailUrl?: string;
    video_url?: string;
    image_url?: string;
    thumbnail_url?: string;

    // 任务详细信息
    task_type?: string;
    original_prompt?: string;
    img_url?: string;
    status?: 'pending' | 'running' | 'completed' | 'failed' | 'error';
    timestamp?: string;
    midjourney_prompt?: string;

    // 错误信息
    error?: string;
    error_details?: {
      status: boolean;
      msg: string;
      data: any;
    };

    // 新增字段
    generation_details?: {
      image_generation?: {
        status: boolean;
        msg: string;
        data: string;
      };
      video_generation?: {
        status: boolean;
        msg: string;
        data: string;
      };
    };
    video_prompt?: string;
    base_prompt?: string;

    completed_at?: string;
  };
  progress?: number;
  message?: string;
}
