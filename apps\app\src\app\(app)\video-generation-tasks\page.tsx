'use client';

import { RadiusClose, SearchList } from '@/components/icon';
import { ArrowFullfil } from '@/components/icon/ArrowFullfil';
import { EmptyTask } from '@/components/icon/EmptyTask';
import TaskItem from '@/components/TaskItem';
import { Divider, InfiniteScroll, Input, Panel, Skeleton } from '@/components/ui';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import useTaskListFetcher from '@/hooks/useTaskListFetcher';
import { cn } from '@/utils/cn';
import React, { useCallback, useEffect, useState } from 'react';
import CustomSelect from './components/CustomSelect';

const TIME_FILTER_OPTIONS = [
  { value: 'all', label: '全部', days: Infinity },
  { value: '3', label: '三天以内', days: 3 },
  { value: '7', label: '七天以内', days: 7 },
  { value: '30', label: '一个月以内', days: 30 },
];

const GENERATION_TYPE = [
  { value: 'all', label: '全部' },
  { value: '大卖推荐', label: 'AI搜索爆款克隆' },
  { value: 'KOL转移', label: '自定义视频裂变' },
  { value: '其他', label: 'AI视频精剪' },
];

const Main = () => {
  const { loading, loadingMore, refresh, loadMore, list, hasMore } = useTaskListFetcher();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDateFilter, setSelectedDateFilter] = useState<string | undefined>(undefined);
  const [open, setOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(TIME_FILTER_OPTIONS[0]!.value);
  const [open1, setOpen1] = useState(false);
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(GENERATION_TYPE[0]!.value);
  const getFilterType = (filterValue: string) => (filterValue === 'all' ? undefined : filterValue);
  useEffect(() => {
    refresh();
    // 设置定时器，每 15 分钟调用一次 refresh
    const intervalId = setInterval(
      () => {
        refresh();
      },
      15 * 60 * 1000,
    );
    // 清理定时器
    return () => clearInterval(intervalId);
  }, [refresh]);
  const handleBottomOffset = useCallback(async () => {
    if (!loading && hasMore) {
      const type = getFilterType(selectedTypeFilter);
      await loadMore(selectedDateFilter, searchTerm, type);
    }
  }, [loading, loadMore, selectedDateFilter, searchTerm, hasMore]);

  const onSearch = async (e: string) => {
    setSearchTerm(e.trim());
    const type = getFilterType(selectedTypeFilter);
    await refresh(selectedDateFilter, e.trim(), type);
  };

  const onSelectChange = useCallback(
    (value: string) => {
      setSelectedFilter(value);
      const selectedOption = TIME_FILTER_OPTIONS.find((option) => option.value === value);
      const currentDate = new Date();
      if (selectedOption) {
        let tmp_created_at;
        if (selectedOption.days === Infinity) {
          tmp_created_at = undefined;
        } else {
          const filterDate = new Date(currentDate);
          filterDate.setDate(currentDate.getDate() - selectedOption.days);
          tmp_created_at = filterDate.toISOString();
        }
        setSelectedDateFilter(tmp_created_at);
        const type = getFilterType(selectedTypeFilter);
        refresh(tmp_created_at, searchTerm, type);
      }
    },
    [refresh, searchTerm, selectedTypeFilter],
  );
  const onSelectTypeChange = useCallback(
    (value: string) => {
      setSelectedTypeFilter(value);
      const type = value === 'all' ? undefined : value;
      refresh(selectedDateFilter, searchTerm, type);
    },
    [refresh, selectedDateFilter, searchTerm],
  );
  return (
    <Panel className="h-full w-full">
      <div className="mb-6 ml-3 mt-3 flex">
        <div className="mr-2">
          <div className="relative w-[442px]">
            {!searchTerm && (
              <div className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2">
                <SearchList />
              </div>
            )}
            <Input
              value={searchTerm}
              type="search"
              className={cn(
                'h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 text-xs font-medium shadow-none ring-offset-0 placeholder:text-[#9FA4B2] dark:ring-offset-0',
                'hover:bg-[#CCDDFF33]',
                'focus:border-[#00E1FF] focus:bg-[#CCDDFF1A]',
                !searchTerm && 'pl-8',
              )}
              placeholder=" 搜索生成的任务"
              onChange={(e) => onSearch(e.target.value)}
            />
            {searchTerm && (
              <div
                onClick={() => onSearch('')}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500"
              >
                <RadiusClose />
              </div>
            )}
          </div>
        </div>
        <CustomSelect
          className="mr-2"
          value={selectedFilter}
          options={TIME_FILTER_OPTIONS}
          placeholder="选择时效"
          open={open}
          onOpenChange={setOpen}
          onValueChange={onSelectChange}
        />
        <CustomSelect
          value={selectedTypeFilter}
          options={GENERATION_TYPE}
          placeholder="选择类型"
          open={open1}
          onOpenChange={setOpen1}
          onValueChange={onSelectTypeChange}
        />
      </div>
      <ContentWrapper loading={loading} empty={list.length === 0} searchTerm={searchTerm}>
        <InfiniteScroll
          className="h-[calc(100vh-110px)] overflow-y-scroll pl-4"
          bottomOffsetTrigger={1000}
          throttleInterval={100}
          onBottomOffset={handleBottomOffset}
        >
          {list.map((task, index) => (
            // @ts-ignore
            <React.Fragment key={`${task.id}-${index}`}>
              <TaskItem data={task} />
              {index + 1 !== list.length && (
                <div className="pl-[68px]">
                  <Divider color="#363D5480" className="my-[32px]" />
                </div>
              )}
            </React.Fragment>
          ))}
          {!loading && loadingMore && <div className="text-muted-foreground text-center">加载中...</div>}
          {!loading && !loadingMore && !hasMore && list.length > 0 && (
            <div className="text-muted-foreground mt-4 text-center">没有更多数据</div>
          )}
        </InfiniteScroll>
      </ContentWrapper>
    </Panel>
  );
};

/**
 * 内容容器 包含加载态和空态
 * @param props
 * @returns
 */
const ContentWrapper: React.FC<{ children: React.ReactNode; loading: boolean; empty: boolean; searchTerm: string }> = (
  props,
) => {
  return props.loading ? <Loading /> : props.empty ? <Empty searchTerm={props.searchTerm} /> : props.children;
};

/**
 * 加载态
 * @returns
 */
const Loading = () => {
  return (
    <div className="mb-8">
      {[1, 2, 3].map((item) => (
        <div key={item}>
          <div className="mb-2 flex gap-2 pl-6">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <div className="mb-2">
                <Skeleton className="mb-2 h-4 w-[400px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
              <div className="flex flex-wrap gap-2">
                {[1, 2, 3, 4, 5].map((item) => (
                  <div key={item}>
                    <Skeleton className="mb-2 h-[160px] w-[160px]" />
                    <Skeleton className="mb-2 h-4 w-[160px]" />
                    <Skeleton className="h-4 w-[160px]" />
                  </div>
                ))}
              </div>
            </div>
          </div>
          <Skeleton className="my-8 h-1 w-full" />
        </div>
      ))}
    </div>
  );
};

/**
 * 空态
 * @returns
 */
const Empty: React.FC<{ searchTerm: string }> = ({ searchTerm }) => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <div className="flex h-fit flex-col items-center justify-center">
        <div className="mb-8 h-16 w-16">
          <EmptyTask />
        </div>
        {searchTerm ? (
          <>
            <div className="text-muted-foreground text-center">未找到“智能剪辑”相关任务，请检查您的关键词</div>
          </>
        ) : (
          <>
            <div className="text-muted-foreground text-center">隐藏在左侧面板添加视频，开始您的创作之旅吧～</div>
          </>
        )}
      </div>
    </div>
  );
};

export default Main;
