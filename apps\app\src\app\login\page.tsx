'use client';

import { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, Input, Button } from '@/components/ui';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { LoginPanelIcon } from './loginPanelIcon';
import { useUserActions } from '@/store/userStore';

export default function Login() {
  const [showPassword, setShowPassword] = useState(false);
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { setUserId } = useUserActions();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // 添加表单验证
    if (!account || !password) {
      setError('请填写账号和密码');
      setLoading(false);
      return;
    }

    // 验证密码长度
    if (password.length < 6) {
      setError('密码长度至少为6位');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ account, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // 优先使用接口返回的错误信息
        throw new Error(data.message || '登录失败');
      }

      const { data: loginData } = data;
      const { sub: userId } = loginData;
      setUserId(userId);

      const token = Cookies.get('token'); // 服务端带过来的
      if (token) {
        localStorage.setItem('token', token);
      }

      // 跳转到首页
      router.push('/');
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative w-full">
      <div className="flex min-h-screen items-center justify-center bg-slate-950">
        <Card className="container1 animate-border-flow h-[553px] w-[480px] flex-shrink-0 border border-white/10 bg-white/5 py-[54px] backdrop-blur-[100px]">
          <span className="line"></span>
          <span className="line"></span>
          <span className="line"></span>
          <span className="line"></span>
          <div className="mb-10 flex justify-center">
            <LoginPanelIcon />
          </div>
          <CardHeader className="mb-11 px-[60px] py-0">
            <CardTitle className="mb-[6px] bg-gradient-to-r from-[#C5FFF5] via-[#CCBEFF] to-[#A5F4FF] bg-clip-text text-center font-['PingFang_SC'] text-[28px] font-medium leading-normal text-transparent">
              欢迎来到BOWONG AI
            </CardTitle>
            <p className="text-center font-['PingFang_SC'] text-base font-normal leading-normal text-[#9FA4B2]">
              AI驱动新增长模型，快速开启您的AI视频营销之旅
            </p>
          </CardHeader>
          <CardContent className="px-[60px] py-0">
            <form onSubmit={handleSubmit}>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <div className="mb-5 space-y-2">
                <Input
                  placeholder="账号"
                  value={account}
                  onChange={(e) => setAccount(e.target.value)}
                  className="border-slate-700 bg-slate-800 text-slate-100 placeholder:text-slate-400 focus:border-[#00E1FF] focus:bg-[#0C101C] focus:ring-0 focus:backdrop-blur-[25px]"
                />
              </div>
              <div className="relative mb-10 space-y-2">
                <Input
                  type={showPassword ? 'text' : 'password'}
                  placeholder="密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-slate-700 bg-slate-800 text-slate-100 placeholder:text-slate-400 focus:border-[#00E1FF] focus:bg-[#0C101C] focus:ring-0 focus:backdrop-blur-[25px]"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 !m-0 flex -translate-y-1/2 items-center justify-center p-0 text-slate-400 hover:bg-transparent hover:text-slate-100"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span className="sr-only">{showPassword ? '隐藏密码' : '显示密码'}</span>
                </Button>
              </div>
              <Button
                type="submit"
                className="h-12 w-full rounded-lg bg-[#00E1FF] text-slate-900 hover:bg-[#00E1FF]/90"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <svg
                      className="h-4 w-4 animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    登录中...
                  </div>
                ) : (
                  '进入 BOWONG AI'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2">
        <div className="mb-1 mt-3 flex items-center justify-center text-xs text-[#9FA4B2]">
          <div>
            ICP备案号：
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" className="hover:text-black">
              浙ICP备2024116950号-2
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
