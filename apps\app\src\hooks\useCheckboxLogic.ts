import { useCallback } from 'react';

interface UseCheckboxLogicProps<T> {
  selectedRows: string[];
  sortedData: T[];
  currentItems: T[] | undefined;
  setCurrentItems: (items: T[]) => void;
  itemKey: keyof T;
  handleSelect: (id: string, checked: boolean) => void;
  handleSelectAll: (checked: boolean, ids: string[]) => void;
}

export function useCheckboxLogic<T>({
  selectedRows,
  sortedData,
  currentItems,
  setCurrentItems,
  itemKey,
  handleSelect,
  handleSelectAll,
}: UseCheckboxLogicProps<T>) {
  // 处理单个checkbox的变化
  const handleCheckboxChange = useCallback(
    (record: T, checked: boolean) => {
      const id = String(record[itemKey]);
      handleSelect(id, checked);
      if (checked) {
        if (!currentItems?.some((item) => String(item[itemKey]) === id)) {
          setCurrentItems([...(currentItems || []), record]);
        }
      } else {
        setCurrentItems((currentItems || []).filter((item) => String(item[itemKey]) !== id));
      }
    },
    [currentItems, handleSelect, itemKey, setCurrentItems],
  );

  // 处理全选checkbox的变化
  const handleAllCheckboxChange = useCallback(
    (checked: boolean) => {
      const currentPageIds = sortedData.map((item) => String(item[itemKey]));
      handleSelectAll(checked, currentPageIds);
      if (checked) {
        const newSelectedItems = sortedData.filter(
          (item) => !currentItems?.some((existing) => String(existing[itemKey]) === String(item[itemKey])),
        );
        setCurrentItems([...(currentItems || []), ...newSelectedItems]);
      } else {
        setCurrentItems((currentItems || []).filter((item) => !currentPageIds.includes(String(item[itemKey]))));
      }
    },
    [sortedData, currentItems, handleSelectAll, itemKey, setCurrentItems],
  );

  return {
    handleCheckboxChange,
    handleAllCheckboxChange,
  };
}
