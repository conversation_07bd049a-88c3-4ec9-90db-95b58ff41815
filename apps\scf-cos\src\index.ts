'use strict';
import { NodeScfWebServer } from '@roasmax/scf-web-server';
import { RemoveForDistributedHandler } from './distribution/remove-for-distributed';
import { RemoveForWaitDistributeHandler } from './distribution/remove-for-wait-distribute';
import { UploadFromCosForDistributedHandler } from './distribution/upload-from-cos-for-distributed';
import { UploadFromLocalForNeedDistributeHandler } from './distribution/upload-from-local-for-need-distribute';
import { UploadFromVodForWaitDistributeHandler } from './distribution/upload-from-vod-for-wait-distribute';
import { distributionDispatcher } from './distribution';
import { cutDispatcher } from './recording';

/**
 * 根据文件路径解析相关信息
 */
interface ParsedFileInfo {
  appName: string;
  tenantId: string;
  workflowName: string;
  stageName: string;
  remainingPath: string[];
}

const parseFilePath = (record: EventPayload['Records'][0]): Partial<ParsedFileInfo> | null => {
  // 获取文件相对于bucket下的key
  const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

  const keyArr = key.split('/');
  if (keyArr.length < 5) return null;

  const [appName, tenantId, workflowName, stageName, ...remainingPath] = keyArr;

  return {
    appName,
    tenantId,
    workflowName,
    stageName,
    remainingPath,
  };
};

const dispatcher = async (event: EventPayload) => {
  const promiseArr = event.Records.map(async (record) => {
    // 基础验证
    if (record.cos.cosBucket.appid !== process.env.COS_APPID) {
      console.log('appid 不匹配，不处理', record.cos.cosBucket.appid);
      return;
    }

    const fileInfo = parseFilePath(record);
    if (!fileInfo || fileInfo.appName !== 'roasmax') {
      console.log('文件路径格式不正确或非目标文件，不处理');
      return;
    }

    if (fileInfo.workflowName === '自动分发') {
      // 自动分发
      return await distributionDispatcher(event);
    } else if (fileInfo.workflowName === '直播录制') {
      // 自动删除
      return await cutDispatcher(event);
    }
  });

  try {
    await Promise.all(promiseArr);
    return 'Success';
  } catch (e) {
    console.log(e);
    return 'Fail';
  }
};

const server = new NodeScfWebServer(dispatcher);

server.start(Number(process.env.SCF_PORT) || 9000);

export interface Handler {
  handle(request: any): Promise<void>;
}

export interface EventPayload {
  Records: {
    cos: {
      cosSchemaVersion: string;
      cosObject: {
        url: string;
        meta: {
          'x-cos-request-id': string;
          'Content-Type': string;
          'x-cos-meta-mykey': string;
        };
        vid: string;
        key: string;
        size: number;
      };
      cosBucket: {
        region: string;
        name: string;
        appid: string;
        s3Region: string;
      };
      cosNotificationId: string;
    };
    event: {
      eventName: string;
      eventVersion: string;
      eventTime: number;
      eventSource: string;
      requestParameters: {
        requestSourceIP: string;
        requestHeaders: {
          Authorization: string;
          // 自定义头，用于标记删除原因
          'x-roasmax-delete-reason': string;
        };
      };
      eventQueue: string;
      reservedInfo: string;
      reqid: number;
    };
  }[];
}
