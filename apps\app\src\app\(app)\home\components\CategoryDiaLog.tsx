import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, Di<PERSON>Footer } from '@/components/ui';
import { modifySubscribedCategories } from '@/services/actions/category';
import { action } from '@/utils/server-action/action';
import useUserId from '@/store/userStore';
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { el } from 'date-fns/locale';

interface CategoryDiaLogPro {
  showDialog: boolean;
  setShowDialog: (value: boolean) => void;
  selectedCategories: string[];
  setSelectedCategories: (value: string[]) => void;
  handleRefresh: () => void;
}

export const CATEGORY_LIST = [
  { id: 700645, category_name: '保健' },
  { id: 602284, category_name: '母婴用品' },
  { id: 802184, category_name: '儿童时尚' },
  { id: 601450, category_name: '美妆个护' },
  { id: 601152, category_name: '女装与女士内衣' },
  { id: 603014, category_name: '运动与户外' },
  { id: 601739, category_name: '手机与数码' },
  { id: 604206, category_name: '玩具和爱好' },
  { id: 604453, category_name: '家具' },
];
const CATEGORY_LIST01 = [
  { id: 951432, category_name: '收藏品' },
  { id: 824328, category_name: '男装与男士内衣' },
  { id: 605248, category_name: '时尚配件' },
  { id: 600942, category_name: '家电' },
  { id: 600024, category_name: '厨房用品' },
  { id: 700437, category_name: '食品饮料' },
  { id: 600001, category_name: '居家日用' },
  { id: 605196, category_name: '汽车与摩托车' },
  { id: 824584, category_name: '箱包' },
  { id: 601352, category_name: '鞋靴' },
  { id: 604968, category_name: '家装建材' },
  { id: 600154, category_name: '家纺布艺' },
  { id: 601755, category_name: '电脑办公' },
  { id: 604579, category_name: '五金工具' },
  { id: 801928, category_name: '图书&杂志&音频' },
  { id: 602118, category_name: '宠物用品' },
  { id: 953224, category_name: '珠宝与衍生品' },
  { id: 601303, category_name: '穆斯林时尚' },
  { id: 834312, category_name: '虚拟商品' },
  { id: 856720, category_name: '二手' },
  { id: 10000, category_name: '其他' },
  { id: 604206, category_name: '玩具和爱好' },
];

export const CategoryDiaLog = ({
  showDialog,
  setShowDialog,
  selectedCategories,
  setSelectedCategories,
  handleRefresh,
}: CategoryDiaLogPro) => {
  const userInfo = useUserId();
  const { userId } = userInfo;
  const [tempSelectedCategories, setTempSelectedCategories] = useState<string[]>([]);

  useEffect(() => {
    if (showDialog) {
      setTempSelectedCategories([...selectedCategories]);
    }
  }, [showDialog, selectedCategories]);

  const handleConfirm = async () => {
    console.log('handleConfirm');

    try {
      console.log('handleConfir2');
      const categoryIds = tempSelectedCategories
        .map((categoryName) => {
          const category = CATEGORY_LIST.find((cat) => cat.category_name === categoryName);
          return category ? category.id.toString() : '';
        })
        .filter((id) => id !== '');
      if (userId && categoryIds.length > 0) {
        await action(modifySubscribedCategories, {
          userId: userId,
          categoryIds: categoryIds,
        });
      }

      setSelectedCategories(tempSelectedCategories);
      setShowDialog(false);
      handleRefresh();
    } catch (error) {
      console.error('更新分类失败:', error);
    }
  };
  const handleCategoryClick = (categoryName: string) => {
    if (tempSelectedCategories.includes(categoryName)) {
      if (tempSelectedCategories.length === 1) {
        toast.error(`至少选择一个标签`);
        return;
      }
      setTempSelectedCategories(tempSelectedCategories.filter((item) => item !== categoryName));
      toast.success(`已取消 ${categoryName} 标签`);
    } else if (tempSelectedCategories.length < 5) {
      setTempSelectedCategories([...tempSelectedCategories, categoryName]);
      toast.success(`已添加 ${categoryName} 标签`);
    } else {
      toast.error('最多只能选择5个分类');
    }
  };

  return (
    <Dialog
      open={showDialog}
      onOpenChange={(open) => {
        if (!open && tempSelectedCategories.length > 0) {
          setShowDialog(false);
          return;
        } else {
          toast.error('至少选择一个标签');
          return;
        }
      }}
    >
      <DialogContent className="h-[506px] max-h-[506px] w-[720px] max-w-[720px] rounded-2xl border-none bg-[#151C29] px-8 pb-5">
        <DialogHeader className="flex items-center justify-center">
          <DialogTitle>
            <span className="text-base font-medium">选择您关注的行业</span>
            <span className="text-base font-normal text-[#9FA4B2]">（最多选择5个）</span>
          </DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <div className="flex flex-wrap gap-4">
            {CATEGORY_LIST.map((item) => {
              const isSelected = tempSelectedCategories.includes(item.category_name);
              return (
                <Button
                  key={item.id}
                  className={`h-8 bg-[#CCDDFF0D] text-sm text-[#9FA4B2] hover:border-[#00E1FF] hover:bg-[#CCDDFF1A] hover:text-white ${
                    isSelected ? 'border-[#00E1FF] bg-[#CCDDFF1A] text-white' : ''
                  }`}
                  variant="outline"
                  onClick={() => handleCategoryClick(item.category_name)}
                >
                  {item.category_name}
                </Button>
              );
            })}
          </div>
          <div className="mt-2 border-t border-[CCDDFF1A] pt-2">
            <div className="ml-2 text-sm text-[#cccccc]">敬请期待:</div>
            <div className="flex flex-wrap gap-4 pt-2">
              {CATEGORY_LIST01.map((item) => {
                return (
                  <Button
                    key={item.id}
                    className={`h-8 bg-[#CCDDFF0D] text-sm text-[#9FA4B2] hover:border-[#CCDDFF1A] hover:bg-[#CCDDFF0D] hover:text-[#9FA4B2]`}
                    variant="outline"
                    disabled
                  >
                    {item.category_name}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            disabled={tempSelectedCategories.length === 0}
            variant="outline"
            className="h-8 w-[90px] bg-[#CCDDFF1A]"
            onClick={() => setShowDialog(false)}
          >
            取消
          </Button>
          <Button
            type="submit"
            className="h-8 w-[90px] text-black"
            onClick={handleConfirm}
            disabled={tempSelectedCategories.length === 0}
          >
            确定
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
