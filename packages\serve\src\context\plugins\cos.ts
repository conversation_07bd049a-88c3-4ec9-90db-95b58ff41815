import { CosClient as OriginCosClient } from '@roasmax/utils/tencentcloud';
import { ActionContextPluginLoader } from '../../types';

class CosClient extends OriginCosClient {
  constructor(...params: ConstructorParameters<typeof OriginCosClient>) {
    super(...params);
  }

  async getObjectUrlAsync(params: OriginCosClient.GetObjectParams) {
    const promise = new Promise<OriginCosClient.GetObjectUrlResult>((resolve, reject) => {
      this.getObjectUrl(params, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    return promise;
  }
}

const cosPlugin: ActionContextPluginLoader = () => {
  const cos = new CosClient({
    SecretId: process.env.COS_SECRET_ID, // 推荐使用环境变量获取；用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
    SecretKey: process.env.COS_SECRET_KEY, // 推荐使用环境变量获取；用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
  });
  return {
    name: 'cos',
    plugin: cos,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    cos: CosClient;
  }
}

export default cosPlugin;
