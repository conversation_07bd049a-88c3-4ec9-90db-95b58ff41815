import { listPropertyDefinitions } from '@/services/actions/config';
import { action, ActionResult } from '@/utils/server-action/action';
import { useCallback, useEffect, useMemo } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

type PropertyDefinitionsStore = {
  definitions: ActionResult<typeof listPropertyDefinitions>;
  loading: boolean;
  fetch: () => Promise<void>;
};

const usePropertyDefinitionStore = create<PropertyDefinitionsStore>()(
  devtools((set) => ({
    definitions: [],
    loading: false,
    fetch: async () => {
      set({ loading: true });
      const res = await action(listPropertyDefinitions, {});
      set({ definitions: res || [], loading: false });
    },
  })),
);

let hasFetched = false;

export const usePropertyDefinitions = (resource?: string) => {
  const { definitions: allDefinitions, loading, fetch } = usePropertyDefinitionStore();

  useEffect(() => {
    if (!hasFetched) {
      fetch();
      hasFetched = true;
    }
  }, [fetch]);

  const getDefinitions = useCallback(
    (resource: string) => {
      return allDefinitions.filter((item) => item.resource_name === resource);
    },
    [allDefinitions],
  );

  const getDefinition = useCallback(
    (name: string) => {
      return allDefinitions.find((item) => item.name === name);
    },
    [allDefinitions],
  );

  const definitions = useMemo(() => {
    if (!resource) return allDefinitions;
    return getDefinitions(resource);
  }, [allDefinitions, getDefinitions, resource]);

  return useMemo(
    () => ({ definitions, allDefinitions, loading, getDefinition, getDefinitions }),
    [allDefinitions, definitions, loading, getDefinition, getDefinitions],
  );
};
