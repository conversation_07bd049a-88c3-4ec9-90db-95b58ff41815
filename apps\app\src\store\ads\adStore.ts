import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { defaultAdStore } from './defaultStores';
import { createAdGroupSlice } from './slices/adGroupSlice';
import { createAdSlice } from './slices/adSlice';
import { createAdvertiserSlice } from './slices/advertiserSlice';
import { createCampaignSlice } from './slices/campaignSlice';
import type { AdStore, CurrentView, DateRangeType } from './storeTypes';

const useAdStore = create<AdStore>()(
  subscribeWithSelector(
    devtools(
      persist(
        (set, get) => ({
          ...defaultAdStore,
          actions: {
            setDateRange: (dateRange: DateRangeType) => {
              set({ dateRange });
            },
            setCurrentView: (view: CurrentView) => {
              set({ currentView: view });
            },
            ...createAdvertiserSlice(set).actions,
            ...createCampaignSlice(set, get).actions,
            ...createAdGroupSlice(set, get).actions,
            ...createAdSlice(set, get).actions,
          },
        }),
        {
          name: 'BWAI_ADS_STORE',
          partialize: (state) => {
            const {
              actions,
              createAdStatus,
              campaignStatus,
              advertiserStatus,
              adGroupStatus,
              adStatus,
              adsIdentityStatus,
              generateAdTextStatus,
              campaignModal,
              cloudSheetOpen,
              adRightDrawer,
              dateRange,
              ...persistedState
            } = state;
            return persistedState;
          },
        },
      ),
    ),
  ),
);

export const useCurrentAd = () => useAdStore((state) => state.currentAd);
export const useCampaignModal = () => useAdStore((state) => state.campaignModal);
export const useCampaignStatus = () => useAdStore((state) => state.campaignStatus);
export const useCurrentAdvertiser = () => useAdStore((state) => state.currentAdvertiser);
export const useCurrentCampaign = () => useAdStore((state) => state.currentCampaign);
export const useCurrentAdGroup = () => useAdStore((state) => state.currentAdGroup);
export const useAdActions = () => useAdStore((state) => state.actions);
export const useAdGroups = () => useAdStore((state) => state.adGroups);
export const useAdGroupStatus = () => useAdStore((state) => state.adGroupStatus);
export const useAdStatus = () => useAdStore((state) => state.adStatus);
export const useAdsIdentityStatus = () => useAdStore((state) => state.adsIdentityStatus);
export const useAdRightDrawer = () => useAdStore((state) => state.adRightDrawer);
export const useCreateAdStatus = () => useAdStore((state) => state.createAdStatus);
export const useCurrentView = () => useAdStore((state) => state.currentView);
export const useGenerateAdTextStatus = () => useAdStore((state) => state.generateAdTextStatus);
export default useAdStore;
