#!/bin/bash

# 本地认证设置验证脚本
# 验证数据库结构和基础数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[${timestamp}] INFO: ${message}${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[${timestamp}] SUCCESS: ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}[${timestamp}] WARNING: ${message}${NC}"
            ;;
        "ERROR")
            echo -e "${RED}[${timestamp}] ERROR: ${message}${NC}"
            ;;
    esac
}

# 数据库配置
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_NAME=${DB_NAME:-"roasmax_dev"}
DB_USER=${DB_USER:-"roasmax_user"}
DB_PASSWORD=${DB_PASSWORD:-"roasmax_password"}

# 执行 SQL 查询
execute_sql() {
    local sql="$1"
    docker exec roasmax-mysql mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "$sql" 2>/dev/null
}

# 测试数据库连接
test_connection() {
    log "INFO" "测试数据库连接..."
    
    if execute_sql "SELECT 1;" > /dev/null; then
        log "SUCCESS" "数据库连接成功"
        return 0
    else
        log "ERROR" "数据库连接失败"
        return 1
    fi
}

# 验证表结构
verify_tables() {
    log "INFO" "验证表结构..."
    
    local required_tables=("members" "roles" "user_roles" "permissions" "user_sessions")
    local missing_tables=()
    
    for table in "${required_tables[@]}"; do
        if execute_sql "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            log "SUCCESS" "表 $table 存在"
        else
            log "ERROR" "表 $table 不存在"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -eq 0 ]; then
        log "SUCCESS" "所有必需的表都存在"
        return 0
    else
        log "ERROR" "缺少表: ${missing_tables[*]}"
        return 1
    fi
}

# 验证 members 表字段
verify_members_fields() {
    log "INFO" "验证 members 表字段..."
    
    local required_fields=("password_hash" "salt" "is_migrated" "password_reset_required" "authing_user_id" "last_login_at" "login_attempts" "locked_until" "email_verified" "phone")
    local missing_fields=()
    
    for field in "${required_fields[@]}"; do
        if execute_sql "SHOW COLUMNS FROM members LIKE '$field';" | grep -q "$field"; then
            log "SUCCESS" "字段 members.$field 存在"
        else
            log "ERROR" "字段 members.$field 不存在"
            missing_fields+=("$field")
        fi
    done
    
    if [ ${#missing_fields[@]} -eq 0 ]; then
        log "SUCCESS" "members 表所有新字段都存在"
        return 0
    else
        log "ERROR" "members 表缺少字段: ${missing_fields[*]}"
        return 1
    fi
}

# 验证默认数据
verify_default_data() {
    log "INFO" "验证默认数据..."
    
    # 检查默认角色
    local role_count=$(execute_sql "SELECT COUNT(*) FROM roles WHERE tenant_id = 'default';" | tail -1)
    if [ "$role_count" -gt 0 ]; then
        log "SUCCESS" "默认角色数据存在 ($role_count 条记录)"
        
        # 显示角色列表
        log "INFO" "默认角色列表:"
        execute_sql "SELECT code, name FROM roles WHERE tenant_id = 'default';" | while read -r line; do
            if [[ "$line" != "code"* ]]; then
                log "INFO" "  - $line"
            fi
        done
    else
        log "WARNING" "默认角色数据不存在"
    fi
    
    # 检查默认权限
    local permission_count=$(execute_sql "SELECT COUNT(*) FROM permissions WHERE tenant_id = 'default';" | tail -1)
    if [ "$permission_count" -gt 0 ]; then
        log "SUCCESS" "默认权限数据存在 ($permission_count 条记录)"
    else
        log "WARNING" "默认权限数据不存在"
    fi
}

# 创建测试用户
create_test_user() {
    log "INFO" "创建测试用户..."
    
    local test_email="<EMAIL>"
    local existing_user=$(execute_sql "SELECT COUNT(*) FROM members WHERE email = '$test_email';" | tail -1)
    
    if [ "$existing_user" -gt 0 ]; then
        log "WARNING" "测试用户已存在，跳过创建"
        return 0
    fi
    
    # 生成 UUID（简化版本）
    local user_id=$(uuidgen 2>/dev/null || echo "test-user-$(date +%s)")
    local member_id=$(uuidgen 2>/dev/null || echo "test-member-$(date +%s)")
    
    local sql="INSERT INTO members (
        id, tenant_id, user_id, user_status, nickname, email, account, admin, password,
        password_hash, salt, is_migrated, password_reset_required, authing_user_id,
        last_login_at, login_attempts, locked_until, email_verified, phone,
        tmp_created_at, tmp_updated_at
    ) VALUES (
        '$member_id', 'default', '$user_id', 'Activated', 'Test User', '$test_email', '$test_email', 0, '',
        NULL, NULL, false, true, NULL,
        NULL, 0, NULL, true, NULL,
        NOW(), NOW()
    );"
    
    if execute_sql "$sql"; then
        log "SUCCESS" "测试用户创建成功"
        
        # 为测试用户分配角色
        local role_id=$(uuidgen 2>/dev/null || echo "test-role-$(date +%s)")
        local role_sql="INSERT INTO user_roles (
            id, tenant_id, user_id, role_code, assigned_by, assigned_at, expires_at, created_at
        ) VALUES (
            '$role_id', 'default', '$user_id', 'user', 'system', NOW(), NULL, NOW()
        );"
        
        if execute_sql "$role_sql"; then
            log "SUCCESS" "为测试用户分配角色成功"
        else
            log "WARNING" "为测试用户分配角色失败"
        fi
    else
        log "ERROR" "测试用户创建失败"
        return 1
    fi
}

# 测试查询性能
test_performance() {
    log "INFO" "测试查询性能..."
    
    local start_time=$(date +%s%3N)
    execute_sql "SELECT m.*, ur.role_code FROM members m LEFT JOIN user_roles ur ON m.user_id = ur.user_id WHERE m.email = '<EMAIL>' AND m.tenant_id = 'default';" > /dev/null
    local end_time=$(date +%s%3N)
    
    local query_time=$((end_time - start_time))
    
    if [ "$query_time" -lt 100 ]; then
        log "SUCCESS" "查询性能良好: ${query_time}ms"
    else
        log "WARNING" "查询性能可能需要优化: ${query_time}ms"
    fi
}

# 验证索引
verify_indexes() {
    log "INFO" "验证索引..."
    
    local index_count=$(execute_sql "SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = '$DB_NAME' AND table_name IN ('members', 'roles', 'user_roles', 'permissions', 'user_sessions');" | tail -1)
    
    if [ "$index_count" -gt 0 ]; then
        log "SUCCESS" "找到 $index_count 个索引"
    else
        log "WARNING" "未找到索引，可能影响性能"
    fi
}

# 检查环境变量
check_env_vars() {
    log "INFO" "检查环境变量..."
    
    local required_vars=("DATABASE_URL" "JWT_SECRET" "APPSECRET")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -n "${!var}" ]; then
            log "SUCCESS" "$var 已配置"
        else
            log "WARNING" "$var 未配置"
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        log "SUCCESS" "所有必需的环境变量都已配置"
    else
        log "WARNING" "部分环境变量未配置: ${missing_vars[*]}"
    fi
}

# 主函数
main() {
    log "INFO" "=== 本地认证设置验证开始 ==="
    
    local tests_passed=0
    local tests_total=7
    
    # 执行测试
    if test_connection; then ((tests_passed++)); fi
    if verify_tables; then ((tests_passed++)); fi
    if verify_members_fields; then ((tests_passed++)); fi
    if verify_default_data; then ((tests_passed++)); fi
    if create_test_user; then ((tests_passed++)); fi
    if test_performance; then ((tests_passed++)); fi
    if verify_indexes; then ((tests_passed++)); fi
    
    # 检查环境变量（不计入通过率）
    check_env_vars
    
    # 输出结果
    echo
    log "INFO" "=== 验证结果 ==="
    log "INFO" "通过测试: $tests_passed/$tests_total"
    
    if [ "$tests_passed" -eq "$tests_total" ]; then
        log "SUCCESS" "✅ 所有测试通过！本地认证系统准备就绪"
        echo
        log "INFO" "🔧 下一步操作:"
        log "INFO" "1. 更新插件配置以使用本地认证适配器"
        log "INFO" "2. 测试登录和用户管理功能"
        log "INFO" "3. 执行用户数据迁移"
        return 0
    else
        log "ERROR" "❌ 部分测试失败，请检查配置"
        return 1
    fi
}

# 执行主函数
main "$@"
