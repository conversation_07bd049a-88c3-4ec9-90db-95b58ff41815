'use client';

import * as React from 'react';
import { addDays, format, subDays } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { cn } from '@/utils/cn';
import { Button } from '@/components/ui/Button';
import { Calendar } from '@/components/ui/Calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import toast from 'react-hot-toast';
interface DateRangePickerProps {
  className?: string;
  onChange?: (dates: { startTime: string; endTime: string } | undefined) => void;
  value?: { startTime: string; endTime: string };
  disabled?: boolean;
  test?: boolean;
  defaultValue?: { startTime: string; endTime: string };
}
export default function DateRangePicker({
  className,
  onChange,
  value,
  test,
  defaultValue,
  disabled,
}: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(() => {
    try {
      if (value) {
        return {
          from: new Date(value.startTime),
          to: new Date(value.endTime),
        };
      }
      if (test) {
        const now = new Date();
        const to = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
        const from = subDays(to, 6);
        return {
          from,
          to,
        };
      }
      if (defaultValue) {
        // 添加日期验证
        const from = new Date(defaultValue.startTime);
        const to = new Date(defaultValue.endTime);

        if (isNaN(from.getTime()) || isNaN(to.getTime())) {
          console.error('Invalid date format:', { defaultValue });
          return undefined;
        }

        return {
          from,
          to,
        };
      }
      return undefined;
    } catch (error) {
      console.error('Error parsing date:', error);
      return undefined;
    }
  });
  React.useEffect(() => {
    if (disabled && defaultValue) {
      setDate({
        from: new Date(defaultValue.startTime),
        to: new Date(defaultValue.endTime),
      });
    }
  }, [defaultValue, disabled]);
  const [startHours, setStartHours] = React.useState('00');
  const [startMinutes, setStartMinutes] = React.useState('00');
  const [startSeconds, setStartSeconds] = React.useState('00');
  const [endHours, setEndHours] = React.useState('23');
  const [endMinutes, setEndMinutes] = React.useState('59');
  const [endSeconds, setEndSeconds] = React.useState('59');

  const updateSelectedDateTime = React.useCallback(() => {
    if (!date?.from || !date?.to) {
      onChange?.(undefined);
      return;
    }

    const startDate = new Date(date.from);
    const endDate = new Date(date.to);

    // 设置时分秒
    startDate.setHours(parseInt(startHours));
    startDate.setMinutes(parseInt(startMinutes));
    startDate.setSeconds(parseInt(startSeconds));

    endDate.setHours(parseInt(endHours));
    endDate.setMinutes(parseInt(endMinutes));
    endDate.setSeconds(parseInt(endSeconds));
    // 使用格式化字符串而不是 ISO 格式
    const formatDateTime = (date: Date) => {
      return format(date, 'yyyy/MM/dd HH:mm:ss');
    };
    onChange?.({
      startTime: formatDateTime(startDate),
      endTime: formatDateTime(endDate),
    });
  }, [date, startHours, startMinutes, startSeconds, endHours, endMinutes, endSeconds, onChange]);
  const handleTimeChange = (value: string, type: 'hours' | 'minutes' | 'seconds', isStart: boolean) => {
    if (!date?.from || !date?.to) {
      toast.error('请先选择日期范围');
      return;
    }

    const targetDate = isStart ? new Date(date.from) : new Date(date.to);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1); // 设置为昨天的同一时间
    // 设置新的时间
    if (type === 'hours') {
      targetDate.setHours(parseInt(value));
    } else if (type === 'minutes') {
      targetDate.setMinutes(parseInt(value));
    } else {
      targetDate.setSeconds(parseInt(value));
    }
    // 验证开始时间是否早于当前时间
    if (!test && targetDate < yesterday) {
      toast.error('开始时间不能早于当前时间');
      return;
    }

    if (type === 'hours') {
      isStart ? setStartHours(value) : setEndHours(value);
    } else if (type === 'minutes') {
      isStart ? setStartMinutes(value) : setEndMinutes(value);
    } else {
      isStart ? setStartSeconds(value) : setEndSeconds(value);
    }

    setDate({
      from: isStart ? targetDate : date.from,
      to: isStart ? date.to : targetDate,
    });
  };
  React.useEffect(() => {
    if (value) {
      setDate({
        from: new Date(value.startTime),
        to: new Date(value.endTime),
      });
    }
  }, [value]);
  const handleClear = React.useCallback(() => {
    setDate(undefined);
    setStartHours('00');
    setStartMinutes('00');
    setStartSeconds('00');
    setEndHours('23');
    setEndMinutes('59');
    setEndSeconds('59');
    onChange?.(undefined);
  }, [onChange]);

  const handleOpenChange = React.useCallback(
    (open: boolean) => {
      if (!open) {
        // 只在有完整的开始和结束日期时才更新
        if (date?.from && date?.to) {
          updateSelectedDateTime();
        }
      }
    },
    [date, updateSelectedDateTime],
  );
  const handleCalendarSelect = React.useCallback(
    (selectedDate: DateRange | undefined) => {
      if (selectedDate?.from) {
        const selectedStartDate = new Date(selectedDate.from);
        selectedStartDate.setHours(parseInt(startHours));
        selectedStartDate.setMinutes(parseInt(startMinutes));
        selectedStartDate.setSeconds(parseInt(startSeconds));

        const now = new Date();
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1); // 设置为昨天的同一时间

        if (!test && selectedStartDate < yesterday) {
          toast.error('开始时间不能早于当前时间');
          return;
        }
      }
      if (selectedDate?.to) {
        const selectedEndDate = new Date(selectedDate.to);
        selectedEndDate.setHours(parseInt(endHours));
        selectedEndDate.setMinutes(parseInt(endMinutes));
        selectedEndDate.setSeconds(parseInt(endSeconds));
        selectedDate.to = selectedEndDate;
      }
      setDate(selectedDate);
    },
    [startHours, startMinutes, startSeconds, endHours, endMinutes, endSeconds],
  );
  return (
    <div className={cn('grid gap-2', className)}>
      <Popover onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'justify-start border border-[#363D54] bg-transparent text-left font-normal placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0',
              !date && 'text-[#9FA4B2]',
              test ? 'h-8 w-[300px] rounded text-xs' : 'h-10 w-full rounded text-sm',
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              <>
                {date.to ? (
                  <>
                    {format(date.from, 'yyyy/MM/dd HH:mm:ss')} - {format(date.to, 'yyyy/MM/dd HH:mm:ss')}
                  </>
                ) : (
                  format(date.from, 'yyyy/MM/dd HH:mm:ss')
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                  className="ml-2 hover:text-gray-500"
                >
                  ×
                </button>
              </>
            ) : (
              <span>请选择开始时间和结束时间</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleCalendarSelect}
            numberOfMonths={2}
            locale={zhCN}
          />
          <div className="flex items-center gap-8 border-t p-3">
            <div className="flex items-center gap-2">
              <Select value={startHours} onValueChange={(value) => handleTimeChange(value, 'hours', true)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="HH" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0')).map((hour) => (
                    <SelectItem key={hour} value={hour}>
                      {hour}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={startMinutes} onValueChange={(value) => handleTimeChange(value, 'minutes', true)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="MM" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((minute) => (
                    <SelectItem key={minute} value={minute}>
                      {minute}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={startSeconds} onValueChange={(value) => handleTimeChange(value, 'seconds', true)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="SS" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((second) => (
                    <SelectItem key={second} value={second}>
                      {second}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>至</div>
            <div className="flex gap-2">
              <Select value={endHours} onValueChange={(value) => handleTimeChange(value, 'hours', false)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="HH" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0')).map((hour) => (
                    <SelectItem key={hour} value={hour}>
                      {hour}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={endMinutes} onValueChange={(value) => handleTimeChange(value, 'minutes', false)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="MM" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((minute) => (
                    <SelectItem key={minute} value={minute}>
                      {minute}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={endSeconds} onValueChange={(value) => handleTimeChange(value, 'seconds', false)}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="SS" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((second) => (
                    <SelectItem key={second} value={second}>
                      {second}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
