export const UploadToCloud = () => {
  return (
    <svg width="13" height="11" viewBox="0 0 13 11" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.79462 10.4209H3.27487C1.46624 10.4209 0.000488281 8.92925 0.000488281 7.08759C0.000488281 5.51092 1.07624 4.18925 2.52168 3.84258C2.69719 2.87934 3.19619 2.0096 3.93198 1.3845C4.66777 0.759403 5.59383 0.418459 6.54925 0.420913C7.49208 0.418402 8.40676 0.750316 9.13828 1.3604C9.86979 1.97049 10.3731 2.82122 10.563 3.76842C9.89119 3.70676 9.21572 3.83493 8.60987 4.13901C8.00401 4.4431 7.49089 4.9115 7.12613 5.49342C6.99207 5.72592 7.01807 5.90925 7.20332 6.04342C7.46222 6.22999 7.73234 5.95915 7.76939 5.91975C7.87734 5.74869 8.00326 5.58845 8.14591 5.44215C8.67921 4.89517 9.40253 4.58787 10.1567 4.58787C10.911 4.58787 11.6343 4.89517 12.1676 5.44215C12.7009 5.98913 13.0005 6.73099 13.0005 7.50454C13.0005 8.27809 12.7009 9.01996 12.1676 9.56694C11.6554 10.0923 10.9679 10.3965 10.2461 10.4198L10.2461 10.4209H10.1969L10.1567 10.4212L10.1166 10.4209H7.90613H4.69359V7.41249L5.50784 8.24762C5.55002 8.29078 5.60717 8.31501 5.66675 8.31501C5.72633 8.31501 5.78348 8.29078 5.82566 8.24762L6.14403 7.9222C6.16494 7.90077 6.18154 7.87532 6.19286 7.84731C6.20418 7.81929 6.21 7.78927 6.21 7.75894C6.21 7.72862 6.20418 7.69859 6.19286 7.67058C6.18154 7.64256 6.16494 7.61711 6.14403 7.59568L4.45779 5.86621C4.4156 5.82305 4.359 5.79881 4.29941 5.79881C4.23983 5.79881 4.18269 5.82305 4.1405 5.86621L2.45319 7.59457C2.43227 7.616 2.41568 7.64146 2.40436 7.66947C2.39303 7.69748 2.38721 7.72751 2.38721 7.75783C2.38721 7.78816 2.39303 7.81819 2.40436 7.8462C2.41568 7.87421 2.43227 7.89966 2.45319 7.9211L2.77155 8.24707C2.81373 8.29023 2.87088 8.31446 2.93046 8.31446C2.99004 8.31446 3.04719 8.29023 3.08937 8.24707L3.79462 7.52429V10.4209Z"
        fill="#81889D"
      />
    </svg>
  );
};
