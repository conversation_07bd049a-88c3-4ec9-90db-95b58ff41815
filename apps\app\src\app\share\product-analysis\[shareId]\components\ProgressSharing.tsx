'use client';

import { useState, useEffect, useRef, createRef, useCallback } from 'react';
import Image from 'next/image';
import { RefreshCw } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { cn } from '@/utils/cn';
import FilterCover from '@/app/(app)/viral/components/FilterCover';
import { Logo, StepIcon, Progress } from '@/components/icon';
import { AnalysisPanel } from '@/app/(app)/viral/components/AnalysisPanel';
import StepsList from './StepsList';
import { RightLoading } from '@/app/(app)/viral/components/RightLoading';
import OfficialWebsiteIcon from '@/components/icon/OfficialWebsiteIcon';
import { Step, SubStep } from '@/types/product-analysis';

interface LoadingProgressProps {
  isLoading: boolean;
  completedSteps: number;
  playbackSteps: Step[];
  isReturnFromResult?: boolean;
  fiilterCoverage?: string | null; // 添加filterCoverage属性
  link?: string;
  selectedTemplates?: any;
  setSelectedTemplates?: any;
  analysisResult?: any;
  videoIds: string[];
  picturesUrl: string[];
}

export const ProgressSharing = ({
  isLoading,
  completedSteps,
  playbackSteps,
  isReturnFromResult = false,
  fiilterCoverage = null, // 添加默认值
  link = '',
  selectedTemplates,
  setSelectedTemplates,
  analysisResult,
  videoIds,
  picturesUrl,
}: LoadingProgressProps) => {
  const [currentMainStep, setCurrentMainStep] = useState(0);
  const [currentSubStep, setCurrentSubStep] = useState(0);
  const [flattenedSteps, setFlattenedSteps] = useState<
    {
      mainIndex: number;
      subIndex: number | null;
      step: Step | SubStep;
      isSubStep: boolean;
    }[]
  >([]);
  const [currentFlatIndex, setCurrentFlatIndex] = useState(0);
  const [replayCompleted, setReplayCompleted] = useState(false);
  const [showTitles, setShowTitles] = useState<{ [key: string]: boolean }>({}); // 添加标题显示状态
  const [clickedStep, setClickedStep] = useState<{ mainIndex: number; subIndex: number | null } | null>(null);
  const [progress, setProgress] = useState(0); // 添加进度状态
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [typewriterCompleted, setTypewriterCompleted] = useState(true);
  const [activeStep, setActiveStep] = useState<string | null>(null); // 新增：跟踪当前活动的步骤
  const [isWaitingForTypewriter, setIsWaitingForTypewriter] = useState(false); // 新增：明确表示等待打字机的状态

  // 创建refs用于滚动到视图
  const stepRefs = useRef<{ [key: string]: React.RefObject<HTMLDivElement> }>({});
  const playNextStepRef = useRef<(() => void) | null>(null); // 新增：存储下一步的执行函数
  const replayIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // 添加replayCompletedRef引用来解决闭包问题
  const replayCompletedRef = useRef(replayCompleted);

  // 实时更新replayCompletedRef引用的值
  useEffect(() => {
    replayCompletedRef.current = replayCompleted;
  }, [replayCompleted]);

  // 添加组件卸载时的清理函数，重置到第一步
  useEffect(() => {
    return () => {
      setCurrentMainStep(0);
      setCurrentSubStep(0);
      setCurrentFlatIndex(0);
      setReplayCompleted(false);
      setShowAnalysisPanel(false);
      if (replayIntervalRef.current) {
        clearInterval(replayIntervalRef.current);
        replayIntervalRef.current = null;
      }
    };
  }, []);

  // 初始化refs - 直接使用 playbackSteps
  useEffect(() => {
    // 为每个步骤创建ref
    const refs: { [key: string]: React.RefObject<HTMLDivElement> } = {};

    playbackSteps.forEach((step, mainIndex) => {
      // 主步骤的ref
      refs[`main-${mainIndex}`] = createRef<HTMLDivElement>();

      // 子步骤的ref
      if (step && step.sub_steps && step.sub_steps.length > 0) {
        step.sub_steps.forEach((_, subIndex) => {
          refs[`sub-${mainIndex}-${subIndex}`] = createRef<HTMLDivElement>();
        });
      }
    });

    stepRefs.current = refs;
  }, [playbackSteps]);

  // 在分享模式下，自动开始回放
  useEffect(() => {
    if (!replayCompleted && flattenedSteps.length > 0) {
      // 确保在开始回放前，先将索引设置为0
      if (isReturnFromResult) {
        setCurrentFlatIndex(flattenedSteps.length - 1);
        setReplayCompleted(true);
      } else {
        setCurrentFlatIndex(0);
        // 确保从第一个子步骤开始
        setCurrentMainStep(0);
        setCurrentSubStep(0);
        startReplay();
      }
    }
  }, [replayCompleted, flattenedSteps]);

  // 监听平铺索引变化
  useEffect(() => {
    if (flattenedSteps.length > 0 && currentFlatIndex < flattenedSteps.length) {
      const progressPercentage = (currentFlatIndex / (flattenedSteps.length - 1)) * 100;

      setProgress(progressPercentage);
      const currentStep = flattenedSteps[currentFlatIndex];
      if (!currentStep) {
        return;
      }
      const { mainIndex, subIndex } = currentStep;
      const stepKey = `${mainIndex}-${subIndex}`;
      setShowTitles((prev) => ({ ...prev, [stepKey]: false }));
      const timer = setTimeout(() => {
        setShowTitles((prev) => ({
          ...prev,
          [stepKey]: true,
        }));
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [currentFlatIndex, flattenedSteps]);

  // 将步骤扁平化为一维数组 - 补充回来的逻辑
  useEffect(() => {
    const flattened: {
      mainIndex: number;
      subIndex: number | null;
      step: Step | SubStep;
      isSubStep: boolean;
    }[] = [];

    playbackSteps.forEach((step, mainIndex) => {
      // 先添加主步骤
      flattened.push({
        mainIndex,
        subIndex: null,
        step: step,
        isSubStep: false,
      });
      // 再添加子步骤
      if (step.sub_steps && step.sub_steps.length > 0) {
        step.sub_steps.forEach((subStep, subIndex) => {
          flattened.push({
            mainIndex,
            subIndex,
            step: subStep,
            isSubStep: true,
          });
        });
      }
    });

    setFlattenedSteps(flattened);
  }, [playbackSteps]);

  // 更新 currentMainStep 和 currentSubStep 基于 currentFlatIndex
  useEffect(() => {
    if (flattenedSteps.length > 0 && currentFlatIndex < flattenedSteps.length) {
      const currentStep = flattenedSteps[currentFlatIndex];
      if (currentStep) {
        setCurrentMainStep(currentStep.mainIndex);
        setCurrentSubStep(currentStep.subIndex ?? 0);
      }
    }
  }, [currentFlatIndex, flattenedSteps]);
  // 根据 completedSteps 更新当前步骤
  useEffect(() => {
    if (!isLoading) {
      setCurrentMainStep(0);
      return;
    }
    // 如果回放尚未开始，设置为第一步
    if (!replayCompleted && currentFlatIndex === 0) {
      return;
    }
  }, [isLoading, completedSteps, flattenedSteps, playbackSteps, replayCompleted, currentFlatIndex]);

  // 修改 startReplay 函数，添加详细日志并完全重写打字机等待逻辑
  const startReplay = useCallback(() => {
    // 清除可能存在的之前的定时器
    if (replayIntervalRef.current) {
      clearInterval(replayIntervalRef.current);
      replayIntervalRef.current = null;
    }

    // 判断是否是从结果页面返回的
    if (isReturnFromResult) {
      setCurrentFlatIndex(flattenedSteps.length - 1);
      setReplayCompleted(true);
      setShowAnalysisPanel(true);
      return;
    }

    // 重置状态
    setShowAnalysisPanel(false);
    setCurrentFlatIndex(0);
    setReplayCompleted(false);
    setClickedStep(null);
    setTypewriterCompleted(true); // 初始状态设为完成
    setActiveStep(null); // 重置当前活动步骤
    setIsWaitingForTypewriter(false); // 重置等待状态

    const playNextStep = async () => {
      // 检查回放是否已完成，如果已完成则立即停止
      if (replayCompletedRef.current) {
        return;
      }

      // 如果当前正在等待打字机，不要继续
      if (isWaitingForTypewriter) {
        return;
      }

      if (step >= flattenedSteps.length) {
        // 完成逻辑
        const currentStep = flattenedSteps[step - 1]?.step;
        if (currentStep && currentStep.content_type !== 'animation') {
          setReplayCompleted(true);
          setTimeout(() => {
            setShowAnalysisPanel(true);
          }, 2000);
        }
        return;
      }

      // 保存当前步骤编号，因为在异步操作中 step 会变化
      const currentStepIndex = step;

      // 更新当前步骤
      setCurrentFlatIndex(currentStepIndex);
      const currentStep = flattenedSteps[currentStepIndex];

      if (currentStep) {
        const stepKey = `${currentStep.mainIndex}-${currentStep.subIndex}`;

        // 记录当前活动步骤
        setActiveStep(stepKey);

        // 设置标题为可见
        setShowTitles((prev) => ({
          ...prev,
          [stepKey]: true,
        }));

        // 如果有打字机效果，等待它完成
        if (currentStep.isSubStep && currentStep.step.contentLodding) {
          // 明确设置打字机状态
          setTypewriterCompleted(false);
          setIsWaitingForTypewriter(true);
          const contentLength = currentStep.step.contentLodding.length;
          const estimatedTime = Math.min(Math.max(contentLength * 30), 5000);
          await new Promise((resolve) => setTimeout(resolve, estimatedTime));
          setTypewriterCompleted(true);
          setIsWaitingForTypewriter(false);
        } else {
          // 如果没有打字机效果，等待固定时间
          // await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      // 确保我们是在处理正确的步骤后再递增
      step++;

      // 在设置下一个超时前再次检查回放是否已完成
      if (replayCompletedRef.current) {
        return;
      }

      // 递归调用自身播放下一步
      setTimeout(playNextStep, 200);
    };

    // 保存playNextStep引用以便可以从外部触发下一步
    playNextStepRef.current = playNextStep;

    // 开始播放
    let step = 0;
    playNextStep();

    return () => {
      if (replayIntervalRef.current) {
        clearInterval(replayIntervalRef.current);
        replayIntervalRef.current = null;
      }
      playNextStepRef.current = null;
    };
  }, [flattenedSteps, isReturnFromResult, typewriterCompleted, isWaitingForTypewriter, replayCompleted]);

  // 新增：用于通知打字机完成的函数
  const handleTypewriterComplete = useCallback(() => {
    setTypewriterCompleted(true);
  }, []);

  const getMainStepMarkdownContent = (mainStep: Step, mainIndex: number) => {
    // 如果当前步骤的内容类型已经是markdown，直接返回
    if (mainStep.content_type === 'markdown') {
      return mainStep.content || '暂无数据';
    }
    // 如果当前步骤的编号大于已完成的步骤数，显示加载中
    if (mainStep.step_number > completedSteps) {
      return '文档准备中...';
    }

    // 根据步骤ID生成不同的markdown内容
    switch (mainStep.step_id) {
      case 'store_page_information':
        return (
          mainStep.content ||
          `## ①格式化参数：
          - **python分栋链接信息...**
          - **Host请求服务器...**
          - **product_url分析产品链接...**
          - **网站排序分析...**`
        );
      case 'product_analysis':
        return (
          mainStep.content ||
          `## ①大模型分析：
          - **文本解析...**
          - **意图识别...**
          - **模型推理计算...**
          - **多轮优化...**`
        );
      case 'template_information':
        return (
          mainStep.content ||
          `## ①API请求：
          - **API请求中...**
          - **HTTP请求...**`
        );
      case 'cloning_recommendations':
        const templateCount = Array.isArray(mainStep.processed_data?.template_info)
          ? mainStep.processed_data.template_info.length
          : 0;
        return `## 模板信息摘要
              系统已找到 ${templateCount} 个相关模板。
              *这些模板基于商品特点和市场定位进行筛选，可以作为您创建内容的参考。*`;
      default:
        return '正在处理数据...';
    }
  };

  // 修改renderContent函数，为主步骤强制使用markdown
  const renderContent = (step: Step | SubStep, isMainStep: boolean = false) => {
    // 如果是主步骤，强制使用markdown格式
    if (isMainStep) {
      return (
        <div className="relative">
          <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
            {getMainStepMarkdownContent(step as Step, step.step_number - 1)}
          </ReactMarkdown>
        </div>
      );
    }
    // 处理特定步骤显示对应的图片
    if ((step.step_number === 4.2 && picturesUrl[0]) || (step.step_number === 4.1 && picturesUrl[0])) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[0]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.3 && picturesUrl[1]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[1]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.4 && picturesUrl[2]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[2]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.5 && picturesUrl[3]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[3]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }
    // 子步骤使用原来的渲染逻辑
    switch (step.content_type) {
      case 'image':
        // 检查 content 是否是有效的 URL
        const imageUrl =
          step.content && (step.content.startsWith('/') || step.content.startsWith('http'))
            ? step.content
            : '/placeholder.svg';

        return (
          <div className="flex justify-center">
            <Image
              src={imageUrl}
              alt={step.step_title}
              width={600}
              height={400}
              className="w-full rounded-md object-contain"
            />
          </div>
        );
      case 'markdown':
        return (
          <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
            {step.content || '暂无数据'}
          </ReactMarkdown>
        );
      case 'animation':
        return (
          <div className="flex justify-center">
            <FilterCover
              cover={fiilterCoverage}
              videoIds={videoIds}
              isReplaying={true}
              onAnimationComplete={() => {
                setReplayCompleted(true);
                setTimeout(() => {
                  setShowAnalysisPanel(true);
                }, 2000);
              }}
            />
          </div>
        );
      default:
        return <div>Unsupported content type</div>;
    }
  };
  const getCurrentStep = () => {
    if (flattenedSteps.length === 0) return playbackSteps[0];
    return flattenedSteps[currentFlatIndex]?.step;
  };
  // 检查当前步骤是否是主步骤
  const isCurrentStepMain = () => {
    if (flattenedSteps.length === 0) return true;
    return !flattenedSteps[currentFlatIndex]?.isSubStep;
  };

  // 判断是否是第一步或最后一步
  const isFirstStep = currentFlatIndex === 0;
  const isLastStep = currentFlatIndex === flattenedSteps.length - 1;

  // 添加处理上一步和下一步的函数
  const handlePrevStep = () => {
    if (!replayCompleted) return;
    if (flattenedSteps.length > 0) {
      const newIndex = Math.max(0, currentFlatIndex - 1);
      setCurrentFlatIndex(newIndex);
      setReplayCompleted(true);
    }
  };

  const handleNextStep = () => {
    if (!replayCompleted) return;

    if (flattenedSteps.length > 0) {
      const newIndex = Math.min(flattenedSteps.length - 1, currentFlatIndex + 1);
      setCurrentFlatIndex(newIndex);
      setReplayCompleted(true);
    }
  };

  if (!isLoading) return null;
  // 完全重写 completeAllSteps 函数，不依赖于新增状态
  const completeAllSteps = () => {
    // 1. 首先设置为已完成状态，这将立即触发StepsList组件中的replayCompleted监听器
    setReplayCompleted(true);
    // 确保ref值也立即更新，避免闭包问题
    replayCompletedRef.current = true;

    // 2. 清除所有定时器和引用
    if (replayIntervalRef.current) {
      clearInterval(replayIntervalRef.current);
      replayIntervalRef.current = null;
    }
    playNextStepRef.current = null;

    // 3. 重置打字机相关状态
    setTypewriterCompleted(true);
    setIsWaitingForTypewriter(false);
    setActiveStep(null);

    // 4. 跳转到最后一步
    setCurrentFlatIndex(flattenedSteps.length - 1);

    // 5. 设置所有步骤的标题为可见
    const allTitlesVisible: { [key: string]: boolean } = {};
    flattenedSteps.forEach((step) => {
      const stepKey = `${step.mainIndex}-${step.subIndex}`;
      allTitlesVisible[stepKey] = true;
    });
    setShowTitles(allTitlesVisible);

    // 6. 延迟显示分析面板，确保所有状态更新完成
    setTimeout(() => {
      setShowAnalysisPanel(true);
    }, 100); // 增加延迟时间以确保状态完全更新
  };

  const handleProgressChange = (newProgress: number) => {
    if (flattenedSteps.length > 0) {
      // 计算新的索引位置
      const newIndex = Math.round((newProgress / 100) * (flattenedSteps.length - 1));
      // 确保索引在有效范围内
      const validIndex = Math.max(0, Math.min(newIndex, flattenedSteps.length - 1));
      // 更新当前索引
      setCurrentFlatIndex(validIndex);
      setReplayCompleted(true); // 标记为已完成，防止自动播放继续
    }
  };
  return (
    <div className={cn('flex w-full flex-col items-center gap-3 lg:flex-row', 'h-full bg-[#070F1F]')}>
      <div className={cn('flex w-3/5 flex-col', 'h-[calc(100vh-2px)] pb-5')}>
        <div className="mb-10 mt-2 flex items-center justify-between px-6 text-base font-normal">
          <div className="h-[34px] w-[140px]">
            <Logo />
          </div>
          <div className="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-lg font-semibold">{link}</div>
          <a className="" href="https://bowongai.com/" target="_blank">
            <OfficialWebsiteIcon />
          </a>
        </div>
        <StepsList
          playbackSteps={playbackSteps}
          currentMainStep={currentMainStep}
          currentSubStep={currentSubStep}
          flattenedSteps={flattenedSteps}
          currentFlatIndex={currentFlatIndex}
          clickedStep={clickedStep}
          completedSteps={completedSteps}
          replayCompleted={replayCompleted}
          showTitles={showTitles}
          stepRefs={stepRefs}
          setClickedStep={setClickedStep}
          setCurrentFlatIndex={setCurrentFlatIndex}
          setReplayCompleted={setReplayCompleted}
          setShowAnalysisPanel={setShowAnalysisPanel}
          typewriterCompleted={typewriterCompleted}
          setTypewriterCompleted={handleTypewriterComplete}
          activeStep={activeStep}
        />
        <div className="ml-[64px] mr-6 flex h-12 items-center justify-between gap-4 rounded-xl border border-[#EFEDFD1A] bg-[#1B2435] pl-3 pr-2 text-sm">
          <div className="flex items-center">
            <Progress />
            <div className="ml-2 text-sm font-medium text-white">
              {!replayCompleted ? ' ROASMAX 分析中' : ' ROASMAX 分析完成'}
            </div>
          </div>
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={startReplay}
              className="h-8 w-[90px]"
              disabled={!replayCompleted} // 在回放未完成时禁用按钮
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              重播
            </Button>
            <Button
              disabled={showAnalysisPanel}
              onClick={completeAllSteps}
              className="h-8 w-[90px] bg-[#00E1FF] text-black"
            >
              查看结果
            </Button>
          </div>
        </div>
      </div>
      {showAnalysisPanel ? (
        <div className="mb-5 mr-4 mt-4 h-[calc(100vh-40px)] w-2/5 overflow-y-auto rounded-3xl bg-[#E2E9F91A] p-4">
          <AnalysisPanel
            videoIds={videoIds}
            selectedTemplates={selectedTemplates}
            setSelectedTemplates={setSelectedTemplates}
            isLoading={isLoading}
            analysisResult={analysisResult}
          />
        </div>
      ) : (
        <Card
          className={cn('flex w-2/5 flex-col rounded-3xl bg-[#E2E9F91A] p-4', 'mb-5 mr-4 mt-4 h-[calc(100vh-40px)]')}
        >
          <CardHeader className="mb-4 flex-shrink-0 p-0">
            <CardTitle className="text-left text-lg font-medium">【ROASMAX 分析展示】</CardTitle>
          </CardHeader>
          <RightLoading flattenedSteps={flattenedSteps} getCurrentStep={getCurrentStep} />
          <CardContent className="flex h-full flex-col justify-between rounded-lg border border-[#363D54] p-0">
            <div className={cn('grow overflow-auto p-4', 'h-[calc(100vh-252px)] border border-y-[#363D54]')}>
              {flattenedSteps.length > 0 && getCurrentStep() && renderContent(getCurrentStep()!, isCurrentStepMain())}
            </div>
            <div className="flex h-[48px] items-center justify-center px-4">
              <div className="w-full px-4">
                <div className="flex items-center gap-4">
                  <div
                    onClick={handlePrevStep}
                    className={cn('cursor-pointer', isFirstStep ? 'text-[#363D54]' : 'hover:text-primary text-white')}
                  >
                    <StepIcon />
                  </div>
                  <div
                    onClick={handleNextStep}
                    className={cn(
                      'scale-x-[-1] transform cursor-pointer',
                      isLastStep ? 'text-[#363D54]' : 'hover:text-primary text-white',
                    )}
                  >
                    <StepIcon />
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progress}
                    onChange={(e) => handleProgressChange(Number(e.target.value))}
                    className="custom-range-slider h-1 w-full appearance-none rounded-full bg-gray-200 outline-none"
                    style={{
                      background: `linear-gradient(to right, #00E1FF 0%,#00E1FF ${progress}%, #e5e7eb ${progress}%, #e5e7eb 100%)`,
                      WebkitAppearance: 'none',
                    }}
                    disabled={!replayCompleted}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
