import { useUpload } from '@/hooks/useUpload';
import { UploadingItem } from './UploadingItem';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui';
import useMaterialStore from '@/store/materialStore';
import { UploadStatus, uploadStatusText } from '@/types/upload';
import { cn } from '@/utils/cn';
import { dispatchResize, sleep } from '@/utils/common';
import { useLayoutEffect } from 'react';

const UploadingList = () => {
  const { handleRetry } = useUpload();
  const { uploadList, setUploadList } = useMaterialStore();

  const handleDelete = (id: string) => {
    const newUploadList = uploadList.filter((item) => item.id !== id);
    setUploadList(newUploadList);
  };

  const handleCancel = (id: string) => {
    const newUploadList = uploadList.map((item) => {
      if (item.id === id) {
        return { ...item, status: UploadStatus.CANCELED };
      }

      return item;
    });
    setUploadList(newUploadList);
  };

  useLayoutEffect(() => {
    sleep(100).then(() => {
      dispatchResize();
    });
  }, [uploadList]);

  if (!uploadList?.length) return null;
  return (
    <Table className="max-w-[calc(100%-32px)]">
      <TableHeader>
        <TableRow className="hover:bg-[#0D1320] hover:bg-opacity-10">
          <TableHead className="h-[18px] whitespace-nowrap pb-4 pl-0 text-xs text-[#81889D]">文件名称</TableHead>
          <TableHead className="h-[18px] w-1/5 whitespace-nowrap pb-4 text-center text-xs text-[#81889D]">
            上传状态
          </TableHead>
          <TableHead className="h-[18px] whitespace-nowrap pb-4 pl-0 pr-20 text-right text-xs text-[#81889D]">
            操作
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {uploadList?.map((item, idx: number) => (
          <TableRow key={item.id} className={cn('text-xs')}>
            <TableCell className={cn('flex items-center justify-start pl-0 text-white', idx === 0 ? 'mt-2' : 'mt-0')}>
              <UploadingItem {...item} />
            </TableCell>
            <TableCell
              className={cn(
                item.status === UploadStatus.UPLOADING && 'text-white',
                item.status === UploadStatus.SUCCESS && 'text-[#60D2A7]',
                item.status === UploadStatus.FAILED && 'text-[#ff6161]',
                item.status === UploadStatus.CANCELED && 'text-[#ff6161]',
                'px-0 pb-0 text-center',
                idx === 0 ? 'mt-2' : 'mt-0',
              )}
            >
              {uploadStatusText[item.status]}
            </TableCell>
            <TableCell
              className={cn(
                'cursor-pointer whitespace-nowrap px-0 pb-0 pr-20 text-right text-white',
                idx === 0 ? 'mt-2' : 'mt-0',
              )}
            >
              {item.status === UploadStatus.UPLOADING && (
                <span className="text-[#9FA4B2] hover:text-white" onClick={() => handleCancel(item.id)}>
                  取消上传
                </span>
              )}
              {item.status === UploadStatus.SUCCESS && (
                <span className="text-[#9FA4B2] hover:text-white" onClick={() => handleDelete(item.id)}>
                  删除
                </span>
              )}
              {item.status === UploadStatus.FAILED && <span onClick={() => handleRetry(item.id)}>重新上传</span>}
              {item.status === UploadStatus.CANCELED && (
                <span className="text-[#9FA4B2] hover:text-white" onClick={() => handleDelete(item.id)}>
                  删除
                </span>
              )}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export { UploadingList };
