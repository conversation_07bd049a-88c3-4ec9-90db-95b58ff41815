export enum BasicStatus {
  DISABLE = 0,
  ENABLE = 1,
}

export enum ResultEnum {
  SUCCESS = 200,
  ERROR = -1,
  TIMEOUT = 401,
}

export enum StorageEnum {
  UserInfo = 'userInfo',
  UserToken = 'userToken',
  Settings = 'settings',
  Ad = 'ad',
  I18N = 'i18nextLng',
  Material = 'material',
}

export enum ThemeMode {
  Light = 'light',
  Dark = 'dark',
}
export type Action = {
  action_scene: string;
  action_period: number;
  video_user_actions: string[];
  action_category_ids: string[];
};
export interface TargetingType {
  type: string;
  responseKey: string;
  errorMessage: string;
  setState: (data: TreeNode[]) => void;
  searchKeywords?: string;
}
export interface TreeNode {
  label: string;
  value: string;
  key: string;
  children?: TreeNode[];
}
export interface InteractionItem {
  id: string;
  subTargetingType: string;
  name: string;
  childrenIds: string[];
  level: number;
  supportedSpecialIndustries: string[];
  hashtagType: string | null;
}
export interface list {
  store_id: string;
  store_name: string;
  store_authorized_bc_id?: string;
  targeting_region_codes: string[];
}
export interface Topic {
  id: string;
  name: string;
  sub_targeting_type: string;
  children_ids: null;
  level: null;
  supported_special_industries: string[];
  hashtag_type: string;
}
export interface CreateRightDrawerProps {
  open: boolean;
  DEFAULT_DAYPARTING: string;
  setOpen: (open: boolean) => void;
  type: string;
  loadingDrawer: boolean;
  onConfirm?: (values: any) => void;
  confirmLoading?: boolean;
  setConfirmLoading?: (loading: boolean) => void;
  editingGroup?: any;
}
export enum ThemeLayout {
  Vertical = 'vertical',
  Horizontal = 'horizontal',
  Mini = 'mini',
}

export enum ThemeColorPresets {
  Default = 'default',
  Cyan = 'cyan',
  Purple = 'purple',
  Blue = 'blue',
  Orange = 'orange',
  Red = 'red',
}

export enum LocalEnum {
  // en_US = "en_US",
  zh_CN = 'zh_CN',
}

export enum MultiTabOperation {
  FULLSCREEN = 'fullscreen',
  REFRESH = 'refresh',
  CLOSE = 'close',
  CLOSEOTHERS = 'closeOthers',
  CLOSEALL = 'closeAll',
  CLOSELEFT = 'closeLeft',
  CLOSERIGHT = 'closeRight',
}

export enum PermissionType {
  CATALOGUE = 0,
  MENU = 1,
  BUTTON = 2,
}

export enum AdStatus {
  // 一级状态
  DELETE = 'STATUS_DELETE',
  DELIVERY_OK = 'STATUS_DELIVERY_OK',
  DISABLE = 'STATUS_DISABLE',
  NOT_DELIVERY = 'STATUS_NOT_DELIVERY',
  TIME_DONE = 'STATUS_TIME_DONE',
  RF_CLOSED = 'STATUS_RF_CLOSED',
  FROZEN = 'STATUS_FROZEN',
  ALL = 'STATUS_ALL',
  NOT_DELETE = 'STATUS_NOT_DELETE',
}
export const getStatusColor = (status: string) => {
  switch (status) {
    case AdStatus.DELIVERY_OK:
      return 'success';
    case AdStatus.DISABLE:
      return 'warning';
    case AdStatus.DELETE:
      return 'error';
    case AdStatus.NOT_DELIVERY:
      return 'default';
    case AdStatus.FROZEN:
      return 'processing';
    default:
      return 'default';
  }
};

export const getStatusText = (status: string) => {
  switch (status) {
    case AdStatus.DELIVERY_OK:
      return '投放中';
    case AdStatus.DISABLE:
      return '已暂停';
    case AdStatus.DELETE:
      return '已删除';
    case AdStatus.NOT_DELIVERY:
      return '未投放';
    case AdStatus.TIME_DONE:
      return '已完成';
    case AdStatus.RF_CLOSED:
      return '已关闭';
    case AdStatus.FROZEN:
      return '冻结中';
    default:
      return status;
  }
};
// 定义年龄选项
export const ageOptions = [
  // { label: "13-17", value: "AGE_13_17" },
  { label: '18-24', value: 'AGE_18_24' },
  { label: '25-34', value: 'AGE_25_34' },
  { label: '35-44', value: 'AGE_35_44' },
  { label: '45-54', value: 'AGE_45_54' },
  { label: '55+', value: 'AGE_55_100' },
];

//定义语言选项
export const languageOptions = [
  { label: '简体中文', value: 'zh' },
  { label: '英语', value: 'en' },
  { label: '阿拉伯语', value: 'ar' },
  { label: '阿萨姆语', value: 'as' },
  { label: '哈里亚纳语', value: 'bgc' },
  { label: '比哈尔语', value: 'bh' },
  { label: '孟加拉语', value: 'bn' },
  { label: '捷克语', value: 'cs' },
  { label: '德语', value: 'de' },
  { label: '希腊语', value: 'el' },
  { label: '西班牙语', value: 'es' },
  { label: '芬兰语', value: 'fi' },
  { label: '法语', value: 'fr' },
  { label: '古吉拉特语', value: 'gu' },
  { label: '希伯来语', value: 'he' },
  { label: '印地语', value: 'hi' },
  { label: '匈牙利语', value: 'hu' },
  { label: '印尼语', value: 'id' },
  { label: '意大利语', value: 'it' },
  { label: '日语', value: 'ja' },
  { label: '卡纳达语', value: 'kn' },
  { label: '韩语', value: 'ko' },
  { label: '马拉雅拉姆语', value: 'ml' },
  { label: '马拉地语', value: 'mr' },
  { label: '马来语', value: 'ms' },
  { label: '荷兰语', value: 'nl' },
  { label: '奥里亚语', value: 'or' },
  { label: '旁遮普语', value: 'pa' },
  { label: '波兰语', value: 'pl' },
  { label: '葡萄牙语', value: 'pt' },
  { label: '拉贾斯坦语', value: 'raj' },
  { label: '罗马尼亚语', value: 'ro' },
  { label: '俄语', value: 'ru' },
  { label: '瑞典语', value: 'sv' },
  { label: '泰米尔语', value: 'ta' },
  { label: '泰卢固语', value: 'te' },
  { label: '泰语', value: 'th' },
  { label: '土耳其语', value: 'tr' },
  { label: '乌克兰语', value: 'uk' },
  { label: '越南语', value: 'vi' },
  { label: '繁体中文', value: 'zh-Hant' },
];
export interface RegionNode {
  locationId: string;
  name: string;
  parentId: string;
  regionCode: string;
  nextLevelIds: string[];
  areaType: string;
  level: string;
  supportBelow18: boolean;
  children?: RegionNode[];
}

// interface LocationNode {
// 	title: string;
// 	value: string;
// 	key: string;
// 	children?: LocationNode[];
// }

// 网络
export const networktypes = [
  { label: 'WIFI', value: 'WIFI' },
  { label: '4G', value: '4G' },
  { label: '5G', value: '5G' },
];
// 广告主状态
export enum AdvertiserStatus {
  STATUS_DISABLE = 'STATUS_DISABLE',
  STATUS_PENDING_CONFIRM = 'STATUS_PENDING_CONFIRM',
  STATUS_PENDING_VERIFIED = 'STATUS_PENDING_VERIFIED',
  STATUS_CONFIRM_FAIL = 'STATUS_CONFIRM_FAIL',
  STATUS_ENABLE = 'STATUS_ENABLE',
  STATUS_CONFIRM_FAIL_END = 'STATUS_CONFIRM_FAIL_END',
  STATUS_PENDING_CONFIRM_MODIFY = 'STATUS_PENDING_CONFIRM_MODIFY',
  STATUS_CONFIRM_MODIFY_FAIL = 'STATUS_CONFIRM_MODIFY_FAIL',
  STATUS_LIMIT = 'STATUS_LIMIT',
  STATUS_WAIT_FOR_BPM_AUDIT = 'STATUS_WAIT_FOR_BPM_AUDIT',
  STATUS_WAIT_FOR_PUBLIC_AUTH = 'STATUS_WAIT_FOR_PUBLIC_AUTH',
  STATUS_SELF_SERVICE_UNAUDITED = 'STATUS_SELF_SERVICE_UNAUDITED',
  STATUS_CONTRACT_PENDING = 'STATUS_CONTRACT_PENDING',
}
export const getAdvertiserStatusText = (status: AdvertiserStatus): string => {
  const statusMap: Record<AdvertiserStatus, string> = {
    [AdvertiserStatus.STATUS_DISABLE]: '该广告账户已关户',
    [AdvertiserStatus.STATUS_PENDING_CONFIRM]: '申请待审核',
    [AdvertiserStatus.STATUS_PENDING_VERIFIED]: '待验证',
    [AdvertiserStatus.STATUS_CONFIRM_FAIL]: '审核未通过',
    [AdvertiserStatus.STATUS_ENABLE]: '审核通过',
    [AdvertiserStatus.STATUS_CONFIRM_FAIL_END]: 'CRM审核不通过',
    [AdvertiserStatus.STATUS_PENDING_CONFIRM_MODIFY]: '修改待审核',
    [AdvertiserStatus.STATUS_CONFIRM_MODIFY_FAIL]: '修改审核不通过',
    [AdvertiserStatus.STATUS_LIMIT]: '用户被惩罚',
    [AdvertiserStatus.STATUS_WAIT_FOR_BPM_AUDIT]: '等待CRM审核',
    [AdvertiserStatus.STATUS_WAIT_FOR_PUBLIC_AUTH]: '待对公验证',
    [AdvertiserStatus.STATUS_SELF_SERVICE_UNAUDITED]: '自助开户待验证资质',
    [AdvertiserStatus.STATUS_CONTRACT_PENDING]: '合同未生效',
  };
  return statusMap[status] || status;
};
export const getAdvertiserStatusColor = (status: AdvertiserStatus): string => {
  return status === AdvertiserStatus.STATUS_ENABLE ? 'bg-green-500' : 'bg-red-500';
};
// 广告组的二级状态枚举
export enum AdGroupSecondaryStatus {
  // 通用状态
  ADGROUP_STATUS_ALL = 'ADGROUP_STATUS_ALL',
  ADGROUP_STATUS_NOT_DELETE = 'ADGROUP_STATUS_NOT_DELETE',
  ADGROUP_STATUS_DELETE = 'ADGROUP_STATUS_DELETE',
  ADGROUP_STATUS_CAMPAIGN_DELETE = 'ADGROUP_STATUS_CAMPAIGN_DELETE',
  // 审核相关
  ADGROUP_STATUS_ADVERTISER_AUDIT_DENY = 'ADGROUP_STATUS_ADVERTISER_AUDIT_DENY',
  ADGROUP_STATUS_ADVERTISER_AUDIT = 'ADGROUP_STATUS_ADVERTISER_AUDIT',
  ADGROUP_STATUS_AUDIT_DENY = 'ADGROUP_STATUS_AUDIT_DENY',
  ADGROUP_STATUS_REAUDIT = 'ADGROUP_STATUS_REAUDIT',
  ADGROUP_STATUS_AUDIT = 'ADGROUP_STATUS_AUDIT',
  ADGROUP_STATUS_SHADOW_ADGROUP_REAUDIT = 'ADGROUP_STATUS_SHADOW_ADGROUP_REAUDIT',
  ADGROUP_STATUS_RF_ADGROUP_INVALID = 'ADGROUP_STATUS_RF_ADGROUP_INVALID',
  ADGROUP_STATUS_RF_WITHDRAW_ORDER = 'ADGROUP_STATUS_RF_WITHDRAW_ORDER',
  ADGROUP_STATUS_RF_TIME_DONE = 'ADGROUP_STATUS_RF_TIME_DONE',
  // 账户相关
  ADVERTISER_CONTRACT_PENDING = 'ADVERTISER_CONTRACT_PENDING',
  ADVERTISER_ACCOUNT_PUNISH = 'ADVERTISER_ACCOUNT_PUNISH',
  ADVERTISER_ACCOUNT_INVALID = 'ADVERTISER_ACCOUNT_INVALID',

  // 预算相关
  ADGROUP_STATUS_CAMPAIGN_EXCEED = 'ADGROUP_STATUS_CAMPAIGN_EXCEED',
  ADGROUP_STATUS_BUDGET_EXCEED = 'ADGROUP_STATUS_BUDGET_EXCEED',
  ADGROUP_STATUS_BALANCE_EXCEED = 'ADGROUP_STATUS_BALANCE_EXCEED',

  // 投放状态
  ADGROUP_STATUS_ADGROUP_PRE_ONLINE = 'ADGROUP_STATUS_ADGROUP_PRE_ONLINE',
  ADGROUP_STATUS_CREATE = 'ADGROUP_STATUS_CREATE',
  ADGROUP_STATUS_FROZEN = 'ADGROUP_STATUS_FROZEN',
  ADGROUP_STATUS_NOT_START = 'ADGROUP_STATUS_NOT_START',
  ADGROUP_STATUS_LIVE_NOT_START = 'ADGROUP_STATUS_LIVE_NOT_START',
  ADGROUP_STATUS_CAMPAIGN_DISABLE = 'ADGROUP_STATUS_CAMPAIGN_DISABLE',
  ADGROUP_STATUS_DISABLE = 'ADGROUP_STATUS_DISABLE',
  ADGROUP_STATUS_DELIVERY_OK = 'ADGROUP_STATUS_DELIVERY_OK',

  // 其他状态
  ADGROUP_STATUS_PIXEL_UNBIND = 'ADGROUP_STATUS_PIXEL_UNBIND',
  ADGROUP_STATUS_PARTIAL_AUDIT_NO_DELIVERY = 'ADGROUP_STATUS_PARTIAL_AUDIT_NO_DELIVERY',
  ADGROUP_STATUS_PARTIAL_AUDIT_DELIVERY_OK = 'ADGROUP_STATUS_PARTIAL_AUDIT_DELIVERY_OK',

  // 资质相关
  ADGROUP_STATUS_INDUSTRY_QUALIFICATION_MISSING = 'ADGROUP_STATUS_INDUSTRY_QUALIFICATION_MISSING',
  ADGROUP_STATUS_INDUSTRY_QUALIFICATION_EXPIRED = 'ADGROUP_STATUS_INDUSTRY_QUALIFICATION_EXPIRED',
  ADGROUP_STATUS_INDUSTRY_QUALIFICATION_DENY = 'ADGROUP_STATUS_INDUSTRY_QUALIFICATION_DENY',

  ADGROUP_STATUS_ASSET_AUTHORIZATION_LOST = 'ADGROUP_STATUS_ASSET_AUTHORIZATION_LOST',
  ADGROUP_STATUS_TRANSCODING_FAILED = 'ADGROUP_STATUS_TRANSCODING_FAILED',
  ADGROUP_STATUS_ADGROUP_QUOTA_LIMIT = 'ADGROUP_STATUS_ADGROUP_QUOTA_LIMIT',
  ADGROUP_STATUS_IDENTITY_USED_BY_GMV_MAX_AD = 'ADGROUP_STATUS_IDENTITY_USED_BY_GMV_MAX_AD',
  ADGROUP_STATUS_SEARCH_KEYWORDS_IN_REVIEW = 'ADGROUP_STATUS_SEARCH_KEYWORDS_IN_REVIEW',
  ADGROUP_STATUS_SEARCH_KEYWORDS_NOT_AVAILABLE = 'ADGROUP_STATUS_SEARCH_KEYWORDS_NOT_AVAILABLE',
  ADGROUP_STATUS_SEARCH_KEYWORDS_PARTIAL_APPROVED = 'ADGROUP_STATUS_SEARCH_KEYWORDS_PARTIAL_APPROVED',
  ADGROUP_STATUS_TIME_DONE = 'ADGROUP_STATUS_TIME_DONE',
  // 覆盖和频次广告特有状态
  ADGROUP_STATUS_RF_DEDUCTION_FAILED = 'ADGROUP_STATUS_RF_DEDUCTION_FAILED',
  ADGROUP_STATUS_RF_NO_VALID_CREATIVE = 'ADGROUP_STATUS_RF_NO_VALID_CREATIVE',
  ADGROUP_STATUS_RF_CLOSED_OTHERS = 'ADGROUP_STATUS_RF_CLOSED_OTHERS',
  ADGROUP_STATUS_RF_SHORT_BALANCE = 'ADGROUP_STATUS_RF_SHORT_BALANCE',
  ADGROUP_STATUS_RF_BOOKING = 'ADGROUP_STATUS_RF_BOOKING',
  ADGROUP_STATUS_RF_NO_DELIVERY_CREATIVE = 'ADGROUP_STATUS_RF_NO_DELIVERY_CREATIVE',
  ADGROUP_STATUS_RF_SCHEDULE = 'ADGROUP_STATUS_RF_SCHEDULE',
  ADGROUP_STATUS_RF_TERMINATE = 'ADGROUP_STATUS_RF_TERMINATE',
  ADGROUP_STATUS_RF_AD_AUDIT_DENY = 'ADGROUP_STATUS_RF_AD_AUDIT_DENY',
  // 内容加热广告特有状态
  ADGROUP_STATUS_PROMOTE_AD_NOT_APPROVED = 'ADGROUP_STATUS_PROMOTE_AD_NOT_APPROVED',
  ADGROUP_STATUS_PROMOTE_WITHDRAW_ORDER = 'ADGROUP_STATUS_PROMOTE_WITHDRAW_ORDER',
}

export const getSecondaryStatusText = (status?: AdGroupSecondaryStatus): string => {
  if (!status) return '';
  const statusMap: Record<AdGroupSecondaryStatus, string> = {
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ALL]: '全部',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_NOT_DELETE]: '未删除',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DELETE]: '已删除',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADVERTISER_AUDIT_DENY]: '广告主审核未通过',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_CAMPAIGN_DELETE]: '推广系列已删除',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADVERTISER_AUDIT]: '广告主审核中',
    [AdGroupSecondaryStatus.ADVERTISER_CONTRACT_PENDING]: '广告主合同未生效',
    [AdGroupSecondaryStatus.ADVERTISER_ACCOUNT_PUNISH]: '广告主账号被惩罚',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_CAMPAIGN_EXCEED]: '推广系列超出预算',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_BUDGET_EXCEED]: '广告组超出预算',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_BALANCE_EXCEED]: '广告主余额不足',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADGROUP_PRE_ONLINE]: '预上线状态',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_AUDIT_DENY]: '广告组审核拒绝',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_REAUDIT]: '修改审核中',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_AUDIT]: '新建审核中',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_CREATE]: '计划新建',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_FROZEN]: '广告组被冻结',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_NOT_START]: '未到投放时间',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_LIVE_NOT_START]: '直播未开始',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_CAMPAIGN_DISABLE]: '推广系列已暂停',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DISABLE]: '已暂停',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DELIVERY_OK]: '投放中',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_SHADOW_ADGROUP_REAUDIT]: '重新审核中',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_PIXEL_UNBIND]: '广告组的Pixel已解绑',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_PARTIAL_AUDIT_NO_DELIVERY]: '广告组部分审核中(未投放)',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_PARTIAL_AUDIT_DELIVERY_OK]: '广告组部分审核中(投放中)',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_INDUSTRY_QUALIFICATION_MISSING]: '待补充行业资质',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_INDUSTRY_QUALIFICATION_EXPIRED]: '行业资质已过期',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_INDUSTRY_QUALIFICATION_DENY]: '行业资质不通过',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ASSET_AUTHORIZATION_LOST]: '广告组资产无授权',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_TRANSCODING_FAILED]: '广告组视频转码失败',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADGROUP_QUOTA_LIMIT]: '广告账户下无空余广告组投放配额',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_IDENTITY_USED_BY_GMV_MAX_AD]: '广告账户下GMV最大广告组已达上限',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_SEARCH_KEYWORDS_IN_REVIEW]: '搜索的关键词均在审核中',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_SEARCH_KEYWORDS_NOT_AVAILABLE]: '搜索的关键词均未通过审核，或未设置关键词',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_SEARCH_KEYWORDS_PARTIAL_APPROVED]: '搜索的关键词部分通过审核',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_TIME_DONE]: '投放时间已结束',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_DEDUCTION_FAILED]: '预扣费失败',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_NO_VALID_CREATIVE]: '无有效创意',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_CLOSED_OTHERS]: '广告组已关闭',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_SHORT_BALANCE]: '账户余额不足',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_BOOKING]: '预算/库存已预定',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_NO_DELIVERY_CREATIVE]: '无创意可投',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_SCHEDULE]: '已排期',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_TERMINATE]: '广告组已终止',
    [AdGroupSecondaryStatus.ADVERTISER_ACCOUNT_INVALID]: '广告账户无效',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_ADGROUP_INVALID]: '广告组不存在或异常状态',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_WITHDRAW_ORDER]: '推广已撤单',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_TIME_DONE]: '广告组已完成',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_RF_AD_AUDIT_DENY]: '广告创意审核未通过',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_PROMOTE_AD_NOT_APPROVED]: '创意未通过审核',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_PROMOTE_WITHDRAW_ORDER]: '订单已撤销',
  };

  return statusMap[status] || status;
};

export const getSecondaryStatusColor = (status?: AdGroupSecondaryStatus): string => {
  if (!status) return 'default';
  const colorMap: Partial<Record<AdGroupSecondaryStatus, string>> = {
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DELIVERY_OK]: 'success',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_AUDIT]: 'processing',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_REAUDIT]: 'processing',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADVERTISER_AUDIT]: 'processing',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_AUDIT_DENY]: 'error',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_ADVERTISER_AUDIT_DENY]: 'error',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DISABLE]: 'warning',
    [AdGroupSecondaryStatus.ADGROUP_STATUS_DELETE]: 'default',
  };

  return colorMap[status] || 'default';
};
export enum AdCreateStatus {
  CREATING = 'CREATING',
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
}

export const getAdStatusText = (status: AdCreateStatus): string => {
  const statusMap: Record<AdCreateStatus, string> = {
    [AdCreateStatus.CREATING]: '创建中',
    [AdCreateStatus.SUCCESS]: '成功',
    [AdCreateStatus.FAIL]: '失败',
  };
  return statusMap[status] || status;
};

export const getAdStatusColor = (status: AdCreateStatus): string => {
  return status === AdCreateStatus.SUCCESS ? 'green' : status === AdCreateStatus.FAIL ? 'red' : 'gray';
};
//广告二级状态枚举
export enum AdSecondaryStatus {
  // 通用状态
  AD_STATUS_ALL = 'AD_STATUS_ALL',
  AD_STATUS_NOT_DELETE = 'AD_STATUS_NOT_DELETE',
  AD_STATUS_CAMPAIGN_DELETE = 'AD_STATUS_CAMPAIGN_DELETE',
  AD_STATUS_ADGROUP_DELETE = 'AD_STATUS_ADGROUP_DELETE',
  AD_STATUS_DELETE = 'AD_STATUS_DELETE',

  // 审核相关
  AD_STATUS_ADVERTISER_AUDIT_DENY = 'AD_STATUS_ADVERTISER_AUDIT_DENY',
  AD_STATUS_ADVERTISER_AUDIT = 'AD_STATUS_ADVERTISER_AUDIT',
  AD_STATUS_AUDIT = 'AD_STATUS_AUDIT',
  AD_STATUS_REAUDIT = 'AD_STATUS_REAUDIT',
  AD_STATUS_AUDIT_DENY = 'AD_STATUS_AUDIT_DENY',
  AD_STATUS_ADGROUP_AUDIT_DENY = 'AD_STATUS_ADGROUP_AUDIT_DENY',

  // 账户相关
  ADVERTISER_CONTRACT_PENDING = 'ADVERTISER_CONTRACT_PENDING',
  ADVERTISER_ACCOUNT_PUNISH = 'ADVERTISER_ACCOUNT_PUNISH',

  // 预算相关
  AD_STATUS_BALANCE_EXCEED = 'AD_STATUS_BALANCE_EXCEED',
  AD_STATUS_CAMPAIGN_EXCEED = 'AD_STATUS_CAMPAIGN_EXCEED',
  AD_STATUS_BUDGET_EXCEED = 'AD_STATUS_BUDGET_EXCEED',

  // 投放状态
  AD_STATUS_AD_PRE_ONLINE = 'AD_STATUS_AD_PRE_ONLINE',
  AD_STATUS_LIVE_OFFLINE = 'AD_STATUS_LIVE_OFFLINE',
  AD_STATUS_NOT_START = 'AD_STATUS_NOT_START',
  AD_STATUS_DONE = 'AD_STATUS_DONE',
  AD_STATUS_CAMPAIGN_DISABLE = 'AD_STATUS_CAMPAIGN_DISABLE',
  AD_STATUS_ADGROUP_DISABLE = 'AD_STATUS_ADGROUP_DISABLE',
  ADGROUP_STATUS_FROZEN = 'ADGROUP_STATUS_FROZEN',
  AD_STATUS_DISABLE = 'AD_STATUS_DISABLE',
  AD_STATUS_DELIVERY_OK = 'AD_STATUS_DELIVERY_OK',

  // 审核状态
  AD_STATUS_PARTIAL_AUDIT_DELIVERY_OK = 'AD_STATUS_PARTIAL_AUDIT_DELIVERY_OK',
  AD_STATUS_PARTIAL_AUDIT_NO_DELIVERY = 'AD_STATUS_PARTIAL_AUDIT_NO_DELIVERY',
  AD_STATUS_ADGROUP_PARTIAL_AUDIT_NO_DELIVERY = 'AD_STATUS_ADGROUP_PARTIAL_AUDIT_NO_DELIVERY',

  // 资质相关
  AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_MISSING = 'AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_MISSING',
  AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_EXPIRED = 'AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_EXPIRED',
  AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_DENY = 'AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_DENY',
  AD_STATUS_MUSIC_AUTHORIZATION_MISSING = 'AD_STATUS_MUSIC_AUTHORIZATION_MISSING',
  AD_STATUS_ASSET_AUTHORIZATION_LOST = 'AD_STATUS_ASSET_AUTHORIZATION_LOST',
  AD_STATUS_ADGROUP_ASSET_AUTHORIZATION_LOST = 'AD_STATUS_ADGROUP_ASSET_AUTHORIZATION_LOST',

  // 技术相关
  AD_STAUS_PIXEL_UNBIND = 'AD_STAUS_PIXEL_UNBIND',
  AD_STATUS_PROCESS_AUDIO = 'AD_STATUS_PROCESS_AUDIO',
  AD_STATUS_TRANSCODING_FAIL = 'AD_STATUS_TRANSCODING_FAIL',
  AD_STATUS_AD_QUOTA_LIMIT = 'AD_STATUS_AD_QUOTA_LIMIT',

  // 商品相关
  AD_STATUS_PRODUCT_UNAVAILABLE = 'AD_STATUS_PRODUCT_UNAVAILABLE',
  AD_STATUS_ANCHOR_UNAVAILABLE = 'AD_STATUS_ANCHOR_UNAVAILABLE',
  AD_STATUS_NO_AUTHORIZATION_OF_SHOWCASE = 'AD_STATUS_NO_AUTHORIZATION_OF_SHOWCASE',

  // 特殊广告类型
  AD_STATUS_RF_ADGROUP_CLOSED = 'AD_STATUS_RF_ADGROUP_CLOSED',
  AD_STATUS_PROMOTE_AD_OFFLINE_AUDIT = 'AD_STATUS_PROMOTE_AD_OFFLINE_AUDIT',
  AD_STATUS_PROMOTE_ADGROUP_CLOSED = 'AD_STATUS_PROMOTE_ADGROUP_CLOSED',
}
export const getAdSecondaryStatusText = (status?: AdSecondaryStatus): string => {
  if (!status) return '';

  const statusMap: Record<AdSecondaryStatus, string> = {
    [AdSecondaryStatus.AD_STATUS_ALL]: '全部',
    [AdSecondaryStatus.AD_STATUS_NOT_DELETE]: '未删除',
    [AdSecondaryStatus.AD_STATUS_CAMPAIGN_DELETE]: '推广系列已删除',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_DELETE]: '广告组已删除',
    [AdSecondaryStatus.AD_STATUS_DELETE]: '已删除',
    [AdSecondaryStatus.AD_STATUS_ADVERTISER_AUDIT_DENY]: '账号审核拒绝',
    [AdSecondaryStatus.AD_STATUS_ADVERTISER_AUDIT]: '账号审核中',
    [AdSecondaryStatus.ADVERTISER_CONTRACT_PENDING]: '广告主合同未生效',
    [AdSecondaryStatus.ADVERTISER_ACCOUNT_PUNISH]: '广告主账号被惩罚',
    [AdSecondaryStatus.AD_STATUS_BALANCE_EXCEED]: '广告账号余额不足',
    [AdSecondaryStatus.AD_STATUS_CAMPAIGN_EXCEED]: '推广系列超出预算',
    [AdSecondaryStatus.AD_STATUS_BUDGET_EXCEED]: '广告组超出预算',
    [AdSecondaryStatus.AD_STATUS_AD_PRE_ONLINE]: '预上线状态',
    [AdSecondaryStatus.AD_STATUS_AUDIT]: '审核中',
    [AdSecondaryStatus.AD_STATUS_REAUDIT]: '修改审核中',
    [AdSecondaryStatus.AD_STATUS_AUDIT_DENY]: '审核拒绝',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_AUDIT_DENY]: '广告组审核拒绝',
    [AdSecondaryStatus.AD_STATUS_PARTIAL_AUDIT_DELIVERY_OK]: '部分审核中(投放中)',
    [AdSecondaryStatus.AD_STATUS_PARTIAL_AUDIT_NO_DELIVERY]: '部分审核中(未投放)',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_PARTIAL_AUDIT_NO_DELIVERY]: '广告组部分审核中(未投放)',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_MISSING]: '待补充行业资质',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_EXPIRED]: '行业资质已过期',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_INDUSTRY_QUALIFICATION_DENY]: '行业资质不通过',
    [AdSecondaryStatus.AD_STATUS_MUSIC_AUTHORIZATION_MISSING]: '待补充音乐资质',
    [AdSecondaryStatus.AD_STAUS_PIXEL_UNBIND]: 'Pixel已解绑',
    [AdSecondaryStatus.AD_STATUS_LIVE_OFFLINE]: '直播未开始',
    [AdSecondaryStatus.AD_STATUS_NOT_START]: '未到投放时间',
    [AdSecondaryStatus.AD_STATUS_DONE]: '已完成',
    [AdSecondaryStatus.AD_STATUS_CAMPAIGN_DISABLE]: '推广系列暂停',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_DISABLE]: '广告组暂停',
    [AdSecondaryStatus.ADGROUP_STATUS_FROZEN]: '广告组被冻结',
    [AdSecondaryStatus.AD_STATUS_DISABLE]: '广告暂停',
    [AdSecondaryStatus.AD_STATUS_DELIVERY_OK]: '投放中',
    [AdSecondaryStatus.AD_STATUS_PROCESS_AUDIO]: '音频处理中',
    [AdSecondaryStatus.AD_STATUS_TRANSCODING_FAIL]: '视频转码失败',
    [AdSecondaryStatus.AD_STATUS_ASSET_AUTHORIZATION_LOST]: '广告资产无授权',
    [AdSecondaryStatus.AD_STATUS_ADGROUP_ASSET_AUTHORIZATION_LOST]: '广告组资产无授权',
    [AdSecondaryStatus.AD_STATUS_AD_QUOTA_LIMIT]: '无空余广告组投放配额',
    [AdSecondaryStatus.AD_STATUS_PRODUCT_UNAVAILABLE]: '商品不可用',
    [AdSecondaryStatus.AD_STATUS_ANCHOR_UNAVAILABLE]: '商品链接不可用',
    [AdSecondaryStatus.AD_STATUS_NO_AUTHORIZATION_OF_SHOWCASE]: '橱窗认证身份不可用',
    [AdSecondaryStatus.AD_STATUS_RF_ADGROUP_CLOSED]: '覆盖和频次广告组已关闭',
    [AdSecondaryStatus.AD_STATUS_PROMOTE_AD_OFFLINE_AUDIT]: '创意未通过审核',
    [AdSecondaryStatus.AD_STATUS_PROMOTE_ADGROUP_CLOSED]: '内容加热广告组已关闭',
  };

  return statusMap[status] || status;
};

export const getAdSecondaryStatusColor = (status?: AdSecondaryStatus): string => {
  if (!status) return 'default';

  const colorMap: Partial<Record<AdSecondaryStatus, string>> = {
    [AdSecondaryStatus.AD_STATUS_DELIVERY_OK]: 'success',
    [AdSecondaryStatus.AD_STATUS_AUDIT]: 'processing',
    [AdSecondaryStatus.AD_STATUS_REAUDIT]: 'processing',
    [AdSecondaryStatus.AD_STATUS_ADVERTISER_AUDIT]: 'processing',
    [AdSecondaryStatus.AD_STATUS_AUDIT_DENY]: 'error',
    [AdSecondaryStatus.AD_STATUS_ADVERTISER_AUDIT_DENY]: 'error',
    [AdSecondaryStatus.AD_STATUS_DISABLE]: 'warning',
    [AdSecondaryStatus.AD_STATUS_DELETE]: 'default',
  };

  return colorMap[status] || 'default';
};

//系列的二级状态
export enum promotionSecondaryStatus {
  // 通用状态
  CAMPAIGN_STATUS_DELETE = 'CAMPAIGN_STATUS_DELETE',
  CAMPAIGN_STATUS_ADVERTISER_AUDIT_DENY = 'CAMPAIGN_STATUS_ADVERTISER_AUDIT_DENY',
  CAMPAIGN_STATUS_ADVERTISER_AUDIT = 'CAMPAIGN_STATUS_ADVERTISER_AUDIT',
  ADVERTISER_CONTRACT_PENDING = 'ADVERTISER_CONTRACT_PENDING',
  ADVERTISER_ACCOUNT_PUNISH = 'ADVERTISER_ACCOUNT_PUNISH',
  CAMPAIGN_STATUS_BUDGET_EXCEED = 'CAMPAIGN_STATUS_BUDGET_EXCEED',
  CAMPAIGN_STATUS_DISABLE = 'CAMPAIGN_STATUS_DISABLE',
  CAMPAIGN_STATUS_AWAITING_RELEASE = 'CAMPAIGN_STATUS_AWAITING_RELEASE',
  CAMPAIGN_STATUS_IDENTITY_USED_BY_GMV_MAX_AD = 'CAMPAIGN_STATUS_IDENTITY_USED_BY_GMV_MAX_AD',
  CAMPAIGN_STATUS_ENABLE = 'CAMPAIGN_STATUS_ENABLE',
  CAMPAIGN_STATUS_ALL = 'CAMPAIGN_STATUS_ALL',
  CAMPAIGN_STATUS_NOT_DELETE = 'CAMPAIGN_STATUS_NOT_DELETE',
}
export const getPromotionSecondaryStatusText = (status?: promotionSecondaryStatus): string => {
  if (!status) return '';

  const statusMap: Record<promotionSecondaryStatus, string> = {
    [promotionSecondaryStatus.CAMPAIGN_STATUS_DELETE]: '推广系列已删除',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ADVERTISER_AUDIT_DENY]: '账号审核未通过',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ADVERTISER_AUDIT]: '账号审核中',
    [promotionSecondaryStatus.ADVERTISER_CONTRACT_PENDING]: '广告主合同未生效',
    [promotionSecondaryStatus.ADVERTISER_ACCOUNT_PUNISH]: '广告主账号被惩罚',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_BUDGET_EXCEED]: '推广系列超出预算',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_DISABLE]: '推广系列已暂停',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_AWAITING_RELEASE]: '推广系列待投放',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_IDENTITY_USED_BY_GMV_MAX_AD]: '推广系列已使用GMV最大广告',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ENABLE]: '推广系列已开启',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ALL]: '全部',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_NOT_DELETE]: '未删除',
  };

  return statusMap[status] || status;
};

export const getPromotionSecondaryStatusColor = (status?: promotionSecondaryStatus): string => {
  if (!status) return 'default';

  const colorMap: Partial<Record<promotionSecondaryStatus, string>> = {
    [promotionSecondaryStatus.CAMPAIGN_STATUS_DELETE]: 'error',
    [promotionSecondaryStatus.ADVERTISER_ACCOUNT_PUNISH]: 'error',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_BUDGET_EXCEED]: 'error',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_DISABLE]: 'error',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ENABLE]: 'warning',
    [promotionSecondaryStatus.CAMPAIGN_STATUS_ALL]: 'default',
  };

  return colorMap[status] || 'default';
};
