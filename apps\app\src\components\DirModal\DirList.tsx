import { Skeleton } from '@/components/ui';
import DirItem from './DirItem';
import { DirItemType } from '@/types/material';

interface DirListProps {
  onSelectDir: (item: DirItemType) => void;
  dirs: DirItemType[];
  loading: boolean;
  cancelCreating: () => void;
  submitCreating: (name: string) => void;
  isSubmitting: boolean;
}

const DirList = ({ onSelectDir, dirs, loading, cancelCreating, submitCreating, isSubmitting }: DirListProps) => {
  if (loading) {
    return (
      <div className="flex flex-col gap-6 w-full">
        {[...Array(3)].map((_, index) => (
          <Skeleton key={index} className="h-10 w-full bg-slate-700" />
        ))}
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      {dirs.map((dir) => (
        <DirItem
          key={dir.id}
          each={dir}
          handleClick={onSelectDir}
          cancelCreating={cancelCreating}
          submitCreating={submitCreating}
          isSubmitting={isSubmitting}
        />
      ))}
    </div>
  );
};

export default DirList;
