export interface RetryConfig<T> {
  // 最大重试次数
  maxRetries?: number;
  // 重试间隔时间(ms)
  retryDelay?: number;
  // 重试前的回调函数
  onRetry?: (result: T | Error, attempt: number) => void;
  // 自定义重试触发条件，返回true则重试，false则返回结果
  // result 可能是正常返回值或者异常
  shouldRetry?: (result: T | Error) => boolean;
}

export async function withRetry<T>(fn: () => Promise<T>, config: RetryConfig<T> = {}): Promise<T> {
  const { maxRetries = 2, retryDelay = 0, onRetry, shouldRetry = (result) => result instanceof Error } = config;

  let lastResult: T | Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await fn();

      // 检查返回值是否需要重试
      if (!shouldRetry(result)) {
        return result;
      }

      lastResult = result;
    } catch (error) {
      lastResult = error as Error;

      // 检查错误是否需要重试
      if (!shouldRetry(lastResult)) {
        throw error;
      }
    }

    // 如果是最后一次尝试
    if (attempt === maxRetries) {
      if (lastResult instanceof Error) {
        throw lastResult;
      }
      return lastResult;
    }

    // 执行重试回调
    if (onRetry) {
      onRetry(lastResult, attempt + 1);
    }

    // 等待指定时间后重试
    await new Promise((resolve) => setTimeout(resolve, retryDelay));
  }

  // 这行代码理论上永远不会执行，但为了 TypeScript 类型检查需要
  throw new Error('Unexpected retry loop exit');
}

export function withRetryFn<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  config: RetryConfig<TReturn> = {},
): (...args: TArgs) => Promise<TReturn> {
  return (...args: TArgs) => withRetry(() => fn(...args), config);
}
