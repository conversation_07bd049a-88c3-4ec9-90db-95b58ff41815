'use client';
import React, { useState, useEffect } from 'react';
import { Badge } from './Badge';
import { Input } from './Input';
import { cn } from '@/utils/cn';

interface InputTagsProps {
  defaultValue?: string[];
  value?: string[];
  onValueChange?: (tags: string[]) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

const InputTags: React.FC<InputTagsProps> = ({
  defaultValue = [],
  value,
  onValueChange,
  className,
  placeholder,
  disabled,
}) => {
  const [tags, setTags] = useState<string[]>(defaultValue || []);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (value !== undefined) {
      setTags(value);
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim() !== '') {
      const newTags = [...tags, inputValue.trim()];
      setInputValue('');
      if (!value) {
        setTags(newTags);
      }
      onValueChange && onValueChange(newTags);
    }
  };

  const removeTag = (index: number) => {
    const newTags = tags.filter((_, i) => i !== index);
    if (!value) {
      setTags(newTags);
    }
    onValueChange && onValueChange(newTags);
  };

  return (
    <div
      className={cn('flex flex-wrap gap-1 overflow-auto p-2', className)}
      style={{ border: '1px solid #d9d9d9', borderRadius: '4px' }}
    >
      {tags?.map((tag, index) => (
        <Badge variant="secondary" className="mr-1 font-normal" key={index}>
          {tag}
          <span style={{ marginLeft: '5px', cursor: 'pointer' }} onClick={() => removeTag(index)}>
            &times;
          </span>
        </Badge>
      ))}
      <Input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        className="border-none bg-transparent outline-none"
        placeholder={placeholder}
        disabled={disabled}
      />
    </div>
  );
};

export { InputTags };
