'use client';

import React from 'react';

import { MultiSelect } from '@/components/ui/MultiSelect';

export default function SelectAccount({
  currentAdvertiser,
  advertisers,
  placeholderTitle,
  onValueChange,
}: {
  currentAdvertiser: string[];
  advertisers: any[];
  placeholderTitle: string;
  onValueChange: (value: string[]) => void;
}) {
  return (
    <MultiSelect
      options={advertisers}
      onValueChange={onValueChange}
      defaultValue={currentAdvertiser}
      placeholder={`选择${placeholderTitle}`}
      placeholderTitle={placeholderTitle}
      test={true}
      variant="inverted"
      animation={2}
      maxCount={1}
    />
  );
}
