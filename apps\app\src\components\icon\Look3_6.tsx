export const Look3_6 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <path
        d="M20.5401 23C20.4804 23 20.4192 22.9985 20.355 22.994C19.6579 22.9597 18.8548 22.7432 17.9682 22.3492C16.2322 21.5789 14.3126 20.1893 12.5617 18.4369C10.8123 16.6845 9.42115 14.7649 8.64943 13.0304C8.25537 12.1452 8.03893 11.3422 8.0046 10.6451C7.95385 9.60024 8.33299 8.98973 8.66137 8.66284C8.98827 8.33445 9.59728 7.95531 10.6436 8.00606C11.3407 8.04039 12.1438 8.25683 13.0289 8.6509C14.7634 9.42261 16.683 10.8138 18.4369 12.5647C20.1878 14.3156 21.579 16.2352 22.3507 17.9712C22.7448 18.8563 22.9612 19.6594 22.9955 20.3579C23.0463 21.4028 22.6671 22.0133 22.3387 22.3402C22.0298 22.6462 21.473 23 20.5401 23ZM10.4541 9.4241C10.1033 9.4241 9.83462 9.5062 9.66893 9.66741C9.25994 10.0764 9.3689 11.1422 9.95104 12.4512C10.6541 14.0305 11.9393 15.7978 13.5708 17.4308C15.2023 19.0623 16.9711 20.349 18.5488 21.049C19.331 21.3983 20.0281 21.5745 20.5475 21.5745C20.8983 21.5745 21.167 21.4924 21.3327 21.3312C21.7417 20.9222 21.6327 19.8564 21.0506 18.5473C20.346 16.9696 19.0623 15.2022 17.4293 13.5693C15.7964 11.9378 14.029 10.6526 12.4513 9.95102C11.6691 9.60173 10.972 9.4241 10.4541 9.4241Z"
        fill="#9FA4B2"
        stroke="#1B2435"
        strokeWidth="0.5"
      />
      <path
        d="M10.4603 23C9.52733 23 8.97056 22.6462 8.66307 22.3387C8.33618 22.0118 7.95704 21.4013 8.0063 20.3565C8.04063 19.6594 8.25707 18.8563 8.65113 17.9697C9.42135 16.2367 10.811 14.3171 12.5619 12.5632C14.3143 10.8108 16.2354 9.42115 17.9699 8.64943C18.8551 8.25537 19.6581 8.03893 20.3567 8.0046C21.4016 7.95385 22.0121 8.33299 22.339 8.66137C22.6658 8.98827 23.0465 9.59877 22.9957 10.6436C22.9614 11.3407 22.745 12.1438 22.3509 13.0289C21.5792 14.7649 20.1895 16.6845 18.4371 18.4369C16.6847 20.1908 14.7651 21.579 13.0291 22.3507C12.144 22.7448 11.3409 22.9612 10.6439 22.9955C10.5812 22.9985 10.5185 23 10.4603 23ZM20.5463 9.42413C20.0253 9.42413 19.3297 9.60325 18.5476 9.94955C16.9698 10.6526 15.2025 11.9378 13.5695 13.5678C11.938 15.2023 10.6528 16.9696 9.95125 18.5474C9.36911 19.8564 9.26014 20.9222 9.66914 21.3312C10.0781 21.7402 11.1439 21.6312 12.453 21.0491C14.0307 20.3475 15.7995 19.0608 17.431 17.4308C19.064 15.7978 20.3492 14.0305 21.0508 12.4528C21.6329 11.1437 21.7419 10.0779 21.3329 9.66893C21.1672 9.50473 20.897 9.42413 20.5463 9.42413Z"
        fill="#9FA4B2"
        stroke="#1B2435"
        strokeWidth="0.5"
      />
    </svg>
  );
};
