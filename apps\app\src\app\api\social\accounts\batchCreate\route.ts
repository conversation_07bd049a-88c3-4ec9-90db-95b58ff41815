import { social_accounts } from '@roasmax/database';
import { ActionContext, webhook } from '@roasmax/serve';
import { isEmpty } from 'lodash';

type BatchCreateContext = ActionContext<{
  data: social_accounts[];
}>;

// todo: 内部的方法应该抽离出来
export const POST = webhook(async (ctx: BatchCreateContext) => {
  return await ctx.trx(async (ctx) => {
    if (ctx.data.data.some((item) => !item.out_key)) {
      throw new Error('缺少out_key');
    }

    // 查找已存在的账号 由于存在老数据，所以需要同时匹配 out_key 和 account_identity
    const originList = await ctx.db.social_accounts.findMany({
      where: {
        OR: [
          { out_key: { in: ctx.data.data.map((item) => item.out_key!) } },
          { account_identity: { in: ctx.data.data.map((item) => item.account_identity!).filter(<PERSON><PERSON><PERSON>) } },
        ],
      },
    });

    // 将数据分为需要更新和需要创建的两部分
    const toCreate = ctx.data.data.filter(
      (item) =>
        !originList.some(
          (origin) => origin.out_key === item.out_key || origin.account_identity === item.account_identity,
        ),
    );
    const toUpdate = ctx.data.data.filter((item) =>
      originList.some((origin) => origin.out_key === item.out_key || origin.account_identity === item.account_identity),
    );

    // 批量创建账号
    if (toCreate.length > 0) {
      await ctx.db.social_accounts.createMany({
        data: toCreate.map((item) => {
          return {
            tenant_id: ctx.tenant.id,
            nickname: item.nickname,
            account_identity: item.account_identity,
            out_key: item.out_key,
            platform: item.platform,
            status: item.status || '',
            master_id: item.master_id,
            fans_count: item.fans_count,
            ip: item.ip,
            remark: item.remark,
            share_expired_at: item.share_expired_at,
            tmp_deleted_at: null,
            level: item.level,
            share_ratio: item.share_ratio,
            last_live_at: item.last_live_at,
            live_count_30d: item.live_count_30d,
            live_duration_30d: item.live_duration_30d,
            video_count_30d: item.video_count_30d,
            mcn_bind_at: item.mcn_bind_at,
            mcn_unbind_at: item.mcn_unbind_at,
            properties: item.properties || {},
            tags: item.tags || [],
          };
        }),
      });
    }

    for (const item of toUpdate) {
      const origin = originList.find(
        (origin) => origin.out_key === item.out_key || origin.account_identity === item.account_identity,
      );
      if (!origin) {
        continue;
      }

      // 如果数据一致，则不更新
      const diff = checkDiff(origin, item);
      if (diff.length === 0) {
        ctx.logger.log('数据一致，不更新');
        continue;
      }

      await ctx.db.social_accounts.update({
        where: { id: origin.id },
        data: {
          account_identity: item.account_identity,
          nickname: item.nickname,
          out_key: item.out_key,
          platform: item.platform,
          status: item.status || origin.status || '',
          master_id: item.master_id,
          fans_count: item.fans_count,
          ip: item.ip,
          remark: item.remark,
          share_expired_at: item.share_expired_at,
          level: item.level,
          share_ratio: item.share_ratio,
          last_live_at: item.last_live_at,
          live_count_30d: item.live_count_30d,
          live_duration_30d: item.live_duration_30d,
          video_count_30d: item.video_count_30d,
          mcn_bind_at: item.mcn_bind_at,
          mcn_unbind_at: item.mcn_unbind_at,
          tags: item.tags || [],
          properties: {},
        },
      });
    }

    return {
      createdCount: toCreate.length,
      updatedCount: toUpdate.length,
    };
  });
});

function checkDiff(origin: social_accounts, commit: social_accounts) {
  const fields: (keyof social_accounts)[] = [
    'account_identity',
    'nickname',
    'out_key',
    'platform',
    'status',
    'master_id',
    'fans_count',
    'ip',
    'remark',
    'share_expired_at',
    'level',
    'share_ratio',
    'last_live_at',
    'live_count_30d',
    'live_duration_30d',
    'video_count_30d',
    'mcn_bind_at',
    'mcn_unbind_at',
    'tags',
  ];
  const diff = fields.filter((field) => {
    // 当commit中该字段为空时，判定相等
    if (typeof commit[field] === 'object' && isEmpty(commit[field])) {
      return false;
    }
    return JSON.stringify(origin[field]) !== JSON.stringify(commit[field]);
  });
  return diff;
}
