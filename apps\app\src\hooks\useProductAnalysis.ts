import { useState, useRef, useEffect } from 'react';
import { useEvent } from '@/hooks/useEvent';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';
import { handleSSEResponse } from '@/utils/sseHandler';
import { action } from '@/utils/server-action/action';
import { to } from '@roasmax/utils';
import { saveProductAnalysisRecord } from '@/services/actions/tiktok-product';
import { AnalysisResponse, SSEResponse, Step, SubStep } from '@/types/product-analysis';
import {
  createDefaultSteps,
  getStepContentType,
  getStepDescription,
  getStepTitle,
  processAnalysisData,
  processCloneSuggestion,
  processProductInfo,
  processTemplateInfo,
} from '@/utils/product-analysis';

export const useProductAnalysis = (): {
  error: boolean;
  isLoading: boolean;
  isVideoLoading: boolean;
  isFloating: boolean;
  setIsFloating: (value: boolean) => void;
  analysisResult: AnalysisResponse | null;
  handleAnalyze: (link: string) => Promise<void>;
  handleCancelAnalyze: () => void;
  completedSteps: number;
  errorNavigator: boolean;
  errorAi: boolean;
  playbackSteps: Step[];
  shareId: string | null;
  fiilterCoverage: string | null;
  setFilterCoverage: (value: string | null) => void;
  setIsLoading: (value: boolean) => void;
  videoIds: string[];
  picturesUrl: string[];
} => {
  const [isLoading, setIsLoading] = useState(false);
  const [isVideoLoading, setIsVideoLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResponse | null>(null);
  const [completedSteps, setCompletedSteps] = useState(1);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isFloating, setIsFloating] = useState(false);
  const [error, setError] = useState(false);
  const [errorNavigator, setErrorNavigator] = useState(false);
  const [errorAi, setErrorAi] = useState(false);
  const [playbackSteps, setPlaybackSteps] = useState<Step[]>([]);
  const [shareId, setShareId] = useState<string | null>(null);
  const [fiilterCoverage, setFilterCoverage] = useState<string | null>(null);
  const [videoIds, setVideoIds] = useState<string[]>([]);
  const [picturesUrl, setPicturesUrl] = useState<string[]>([]);

  // 添加网络状态监听
  useEffect(() => {
    const handleOnline = () => {
      toast.success('网络已恢复');
      return;
    };

    const handleOffline = () => {
      setErrorNavigator(true);
      toast.error('网络连接已断开，请检查网络设置');
      if (isLoading) {
        handleCancelAnalyze();
      }
      setIsLoading(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isLoading]);

  const createSubSteps = (stepId: string, data?: any): SubStep[] => {
    switch (stepId) {
      case 'store_page_information':
        return [
          {
            step_id: `${stepId}_blueprint`,
            step_title: '参数蓝图构建',
            step_number: 1.1,
            description: '自动配置爬取数，包括目标页面类型、数据采集范围等，确保数据采集的精准性和高效性',
            completed_at: new Date(Date.now() - 5000).toISOString(),
            content_type: 'markdown',
            content: '阅读页面',
            lodding: '参数 配置中',
            contentLodding:
              '## ① 格式化参数：\n- **阅读页面python分掠链接信息...**\n- **Host请求服务器...**\n- **product_url分析产品链接...**\n- **网站排序分析...**',
            data: { config: data?.config || {} },
            processed_data: { config: data?.config || {} },
          },
          {
            step_id: `${stepId}_link_analysis`,
            step_title: '多维度链接解析',
            step_number: 1.2,
            description: '解析链接地址，快速定位目标页面，确保数据来源的可靠性',
            completed_at: new Date(Date.now() - 4000).toISOString(),
            content_type: 'image',
            content: '/playback/1_1.png',
            lodding: '商品链接分析中',
            contentLodding: '## ② 云函数选取：\n- **random.choice随机选择算法...**',

            data: { link: data?.link || '' },
            processed_data: { link: data?.link || '' },
          },
          {
            step_id: `${stepId}_page_analysis`,
            step_title: '深度页面解析',
            step_number: 1.3,
            description: '解析链接地址，快速定位目标页面，确保数据来源的可靠性',
            completed_at: new Date(Date.now() - 3000).toISOString(),
            content_type: 'image',
            content: '/playback/1_2.png',
            lodding: 'TikTok页面解析中',
            contentLodding:
              '## ① 解析TK商品页面：\n- **HTTP请求...**\n- **requests库快速获取链接数据...**\n- **JSON解析...**\n- **生成ison格式数据...**\n- **headers请求多维度内容...**',

            data: { page_data: data?.page_data || {} },
            processed_data: { page_data: data?.page_data || {} },
          },
          {
            step_id: `${stepId}_title_extraction`,
            step_title: '商品标题关键商业要素智能提取',
            step_number: 1.4,
            completed_at: new Date(Date.now() - 2000).toISOString(),
            content_type: 'image',
            content: '/playback/1.3.png',
            lodding: '商品标题获取中',
            contentLodding:
              '## ① 解析TK商品页面：\n- **HTTP请求...**\n- **requests库快速获取链接数据...**\n- **JSON解析...**\n- **生成ison格式数据...**\n- **headers请求多维度内容...**',

            data: { title: data?.title || '' },
            processed_data: { title: data?.title || '' },
          },
          {
            step_id: `${stepId}_description_extraction`,
            step_title: '商品描述关键商业要素智能提取',
            step_number: 1.5,
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/playback/1.5.png',
            lodding: '商品描述信息获取中',
            contentLodding: '## ② 格式化参数：\n- **接收data数据流信息...**\n- **python提取商品信息...**',

            data: { description: data?.description || '' },
            processed_data: { description: data?.description || '' },
          },
        ];
      case 'product_analysis':
        // 提取产品定位
        const productPositioningStart = data?.indexOf('**产品定位：**') + '**产品定位：**'.length;
        const productPositioningEnd = data?.indexOf('**目标人群：**');
        const productPositioning = data?.slice(productPositioningStart, productPositioningEnd).trim();

        // 提取目标人群
        const targetAudienceStart = data?.indexOf('**目标人群：**') + '**目标人群：**'.length;
        const targetAudienceEnd = data?.indexOf('### 2. 核心卖点');
        const targetAudience = data?.slice(targetAudienceStart, targetAudienceEnd).trim();

        // 提取核心卖点
        const coreSellingPointsStart = data?.indexOf('### 2. 核心卖点') + '### 2. 核心卖点'.length;
        const coreSellingPointsEnd = data?.indexOf('### 3. 产品优势分析');
        const coreSellingPoints = data?.slice(coreSellingPointsStart, coreSellingPointsEnd).trim();

        // 提取产品优势分析
        const productAdvantagesStart = data?.indexOf('### 3. 产品优势分析') + '### 3. 产品优势分析'.length;
        const productAdvantagesEnd = data?.indexOf('### 总结');
        const productAdvantages = data?.slice(productAdvantagesStart, productAdvantagesEnd).trim();

        return [
          {
            step_id: `${stepId}_dynamic_config`,
            step_title: '动态参数配置',
            step_number: 2.1,
            description: '自动调整信息整理参数，确保数据格式的统一性和可读性',
            completed_at: new Date(Date.now() - 5000).toISOString(),
            content_type: 'image',
            content: '/playback/2_1.png',
            lodding: '参数调整中',
            contentLodding:
              '## ① 大模型分析：\n- **文本解析...**\n- **意图识别...**\n- **模型推理计算...**\n- **多轮优化...**',

            data: { config: data?.config || {} },
            processed_data: { config: data?.config || {} },
          },
          {
            step_id: `${stepId}_data_formatting`,
            step_title: '格式化数据，信息结构建模',
            step_number: 2.2,
            description: '原始数据转化为结构化格式，便于后续分析与匹配',
            completed_at: new Date(Date.now() - 4000).toISOString(),
            content_type: 'image',
            content: '/playback/2_1.png',
            lodding: '数据格式化中',
            contentLodding:
              '## ① 大模型分析：\n- **文本解析...**\n- **意图识别...**\n- **模型推理计算...**\n- **多轮优化...**',

            data: { formatted_data: data?.formatted_data || {} },
            processed_data: { formatted_data: data?.formatted_data || {} },
          },
          {
            step_id: `${stepId}_ai_enhanced_analysis`,
            step_title: 'AI增强分析商品数据',
            step_number: 2.3,
            description: '多维度深入分析，AI强化分析商品关键信息',
            completed_at: new Date(Date.now() - 3000).toISOString(),
            content_type: 'markdown',
            content: '整理信息',
            lodding: '商品分析中',
            contentLodding:
              '## ① AI生成：\n- **产品定位分析...**\n- **目标人群画像分析...**\n- **核心卖点分析...**\n- **产品优势分析...**\n- **思考中...**',

            data: { analysis: data?.analysis || '' },
            processed_data: { analysis: data?.analysis || '' },
          },
          {
            step_id: `${stepId}_product_positioning`,
            step_title: '智能分析产品定位',
            step_number: 2.4,
            completed_at: new Date(Date.now() - 2000).toISOString(),
            content_type: 'markdown',
            content: `${productPositioning || '加载中'}`,
            lodding: '商品分析中',
            contentLodding:
              '## ① AI生成：\n- **产品定位分析...**\n- **目标人群画像分析...**\n- **核心卖点分析...**\n- **产品优势分析...**\n- **思考中...**',

            data: { positioning: data?.positioning || '' },
            processed_data: { positioning: data?.positioning || '' },
          },
          {
            step_id: `${stepId}_target_audience`,
            step_title: '智能分析目标人群画像',
            step_number: 2.5,
            completed_at: new Date(Date.now() - 1000).toISOString(),
            content_type: 'markdown',
            content: `${targetAudience || '加载中'}`,
            lodding: '商品分析中',
            contentLodding:
              '## ① AI生成：\n- **产品定位分析...**\n- **目标人群画像分析...**\n- **核心卖点分析...**\n- **产品优势分析...**\n- **思考中...**',

            data: { audience: data?.audience || '' },
            processed_data: { audience: data?.audience || '' },
          },
          {
            step_id: `${stepId}_core_selling_points`,
            step_title: '智能分析产品核心卖点',
            step_number: 2.6,
            completed_at: new Date().toISOString(),
            content_type: 'markdown',
            content: `${coreSellingPoints || '加载中'}`,
            lodding: '商品分析中',
            contentLodding:
              '## ① AI生成：\n- **产品定位分析...**\n- **目标人群画像分析...**\n- **核心卖点分析...**\n- **产品优势分析...**\n- **思考中...**',

            data: { selling_points: data?.selling_points || '' },
            processed_data: { selling_points: data?.selling_points || '' },
          },
          {
            step_id: `${stepId}_advantage`,
            step_title: '智能分析产品优势',
            step_number: 2.7,
            completed_at: new Date().toISOString(),
            content_type: 'markdown',
            content: `${productAdvantages || '加载中'}`,
            lodding: '商品分析中',
            contentLodding:
              '## ① AI生成：\n- **产品定位分析...**\n- **目标人群画像分析...**\n- **核心卖点分析...**\n- **产品优势分析...**\n- **思考中...**',

            data: { selling_points: data?.selling_points || '' },
            processed_data: { selling_points: data?.selling_points || '' },
          },
        ];
      case 'template_information':
        setFilterCoverage(data);
        return [
          {
            step_id: `${stepId}_search`,
            step_title: '取商品数据关键信息',
            step_number: 3.1,
            description: '整理后的数据中提取关键信息，用于模板匹配和视频内容的生成',
            completed_at: new Date(Date.now() - 5000).toISOString(),
            content_type: 'image',
            content: '/playback/3_2.png',
            lodding: '商品信息提取中',
            contentLodding:
              '## ② 处理数据：\n- **解析HTML内容...**\n- **处理分页数据...**\n- **存储数据...**\n- **知识库分析...**',

            data: { keywords: ['关键词1', '关键词2'] },
            processed_data: { search_keywords: ['关键词1', '关键词2'] },
          },
          {
            step_id: `${stepId}_filter`,
            step_title: '视频模板知识库检索',
            step_number: 3.2,
            description: '热门数据爬取：监控TikTok每周热卖视频榜(播放量、互动率、GMV等数据高的视频优先入库)',
            description1:
              '多模态特征提取：通过CV/NLP模型解析视频的视觉风格（如动态演示/场景化叙事）、叙事节奏、文案结构、情感元素',
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/playback/3_2.png',
            lodding: '数据爬取中',
            contentLodding: '## ① 智能知识库分析：\n- **Python分栋内容...**\n- **AI语义分析...**\n- **格式化数据...**',

            data: { filtered_templates: data || [] },
            processed_data: { filtered_templates: data || [] },
          },
          {
            step_id: `${stepId}_collocation_degree`,
            step_title: '智能分析行业适配度',
            step_number: 3.3,
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/playback/zsh_1.png',
            lodding: '类别内容匹配中',
            contentLodding: '##  ① AI智能检索知识库：\n- **AI语义分析...**\n- **思考中...**\n- **格式化数据...**',

            data: { filtered_templates: data || [] },
            processed_data: { filtered_templates: data || [] },
          },
          {
            step_id: `${stepId}_similarity`,
            step_title: '智能分析内容相似度加载内容',
            step_number: 3.4,
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/crawl-tiktok.png',
            lodding: '商品内容匹配中',
            contentLodding: '##  ① AI智能检索知识库：\n- **AI语义分析...**\n- **思考中...**\n- **格式化数据...**',

            data: { filtered_templates: data || [] },
            processed_data: { filtered_templates: data || [] },
          },
          {
            step_id: `${stepId}_content_adaptability`,
            step_title: '智能分析视频内容适配度',
            step_number: 3.5,
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/crawl-tiktok.png',
            lodding: '视频内容匹配中',
            contentLodding:
              '## ② 加权排序：\n- **Python加权重排...**\n- **random随机生成算法...**\n- **random库随机生成算法...**',

            data: { filtered_templates: data || [] },
            processed_data: { filtered_templates: data || [] },
          },
          {
            step_id: `${stepId}_algorithm_model`,
            step_title: '建立动态权重算法模型',
            step_number: 3.6,
            description: '算法分析各匹配维度的权重，确保最终生成的内容具有最佳的表现力',
            completed_at: new Date().toISOString(),
            content_type: 'image',
            content: '/playback/3_6.png',
            lodding: '智能算法分析中',
            contentLodding:
              '## ② 加权排序：\n- **Python加权重排...**\n- **random随机生成算法...**\n- **random库随机生成算法...**',

            data: { filtered_templates: data || [] },
            processed_data: { filtered_templates: data || [] },
          },
        ];
      case 'cloning_recommendations':
        return [
          {
            step_id: `${stepId}_suggestion`,
            step_title: '模板视频关键数据解析',
            step_number: 4.1,
            description: '模板视频进行深度解析，包括结构、节奏、内容等。提取关键信息',
            completed_at: new Date(Date.now() - 3000).toISOString(),
            content_type: 'markdown',
            content: '模板视频详情解析',
            lodding: '模板视频详情解析中',
            contentLodding: '## ① 解析视频信息：\n- **API请求...**\n- **数据处理模块处理...**\n- **JSON解析...**',

            data: { suggestion: data?.suggestion || '' },
            processed_data: { suggestion: data?.suggestion || '' },
          },
          {
            step_id: `${stepId}_structured_block`,
            step_title: '结构化分块处理',
            step_number: 4.2,
            description: '商品属性与视频元素的匹配强度分析',
            completed_at: new Date().toISOString(),
            content_type: 'markdown',
            content: '视频智能分块深度分析',
            lodding: '视频智能分块深度分析中',
            contentLodding: '##  ① 视频结构分析：\n- **API请求...**\n- **数据处理模块处理...**\n- **JSON解析...**',

            data: { video_ids: data?.video_ids || [] },
            processed_data: { video_ids: data?.video_ids || [] },
          },
          {
            step_id: `${stepId}_generation_strategy`,
            step_title: '内容智能生成策略执行',
            step_number: 4.3,
            description: '商品信息与模板信息匹配，确保准确传达商品价值，脚本优化分析、视频风格分析',
            completed_at: new Date().toISOString(),
            content_type: 'markdown',
            content: '模板信息与商品信息融合',
            lodding: '模板信息与商品信息融合分析中',
            contentLodding:
              '## ② AI分析匹配度报告：\n- **视频内容匹配度...**\n- **数据表现分析...**\n- **视频结构优势分析...**',

            data: { video_ids: data?.video_ids || [] },
            processed_data: { video_ids: data?.video_ids || [] },
          },
          {
            step_id: `${stepId}_content_matching_degree`,
            step_title: '内容匹配度分析-智能生成内容匹配度报告内容',
            step_number: 4.4,
            completed_at: new Date().toISOString(),
            content_type: 'markdown',
            content: '内容匹配度分析',
            lodding: '内容匹配度分析中',
            contentLodding:
              '## ② AI分析匹配度报告：\n- **视频内容匹配度...**\n- **数据表现分析...**\n- **视频结构优势分析...**',

            data: { video_ids: data?.video_ids || [] },
            processed_data: { video_ids: data?.video_ids || [] },
          },
          {
            step_id: `${stepId}_report_content`,
            step_title: '短视频数据表现分析-生成短视频数据表现报告内容',
            step_number: 4.5,
            completed_at: new Date().toISOString(),
            // content_type: 'markdown',
            // content: '短视频数据表现',
            content_type: 'markdown',
            content: '短视频数据表现',
            lodding: '短视频数据表现提取中',
            contentLodding:
              '## ② AI分析匹配度报告：\n- **视频内容匹配度...**\n- **数据表现分析...**\n- **视频结构优势分析...**',

            data: { video_ids: data?.video_ids || [] },
            processed_data: { video_ids: data?.video_ids || [] },
          },
          {
            step_id: `${stepId}_videos`,
            step_title: '视频结构优势分析-智能生成视频结构优势报告内容',
            step_number: 4.6,
            completed_at: new Date().toISOString(),
            content_type: 'animation',
            content: '视频结构优势',
            lodding: '视频结构优势分析中',
            contentLodding:
              '## ② AI分析匹配度报告：\n- **视频内容匹配度...**\n- **数据表现分析...**\n- **视频结构优势分析...**',

            data: { video_ids: data?.video_ids || [] },
            processed_data: { video_ids: data?.video_ids || [] },
          },
        ];
      default:
        return [];
    }
  };
  const processSSEData = (line: SSEResponse<any>) => {
    try {
      // setCompletedSteps((prev) => Math.min(prev + 1, 4));
      if (!line?.data?.outputs?.data) {
        return null;
      }

      let parsedData;
      try {
        parsedData =
          typeof line.data.outputs.data === 'string' ? JSON.parse(line.data.outputs.data) : line.data.outputs.data;
      } catch {
        return null;
      }

      let processedData = null;
      let stepNumber = 0;

      // 根据节点类型处理不同的数据
      switch (line.data.title) {
        case 'store_page_information':
          processedData = processProductInfo(parsedData.data);
          stepNumber = 1;
          // 创建回放步骤
          const storeInfoStep: Step = {
            step_id: line.data.title,
            step_title: getStepTitle(line.data.title),
            step_number: stepNumber,
            description: getStepDescription(line.data.title),
            completed_at: new Date().toISOString(),
            content_type: getStepContentType(line.data.title),
            content: '阅读页面',
            lodding: '阅读页面分析 工作中',
            contentLodding:
              '## ① 格式化参数：\n- **阅读页面python分掠链接信息...**\n- **Host请求服务器...**\n- **product_url分析产品链接...**\n- **网站排序分析...**',
            data: parsedData.data || {},
            processed_data: parsedData.data,
            sub_steps: createSubSteps(line.data.title, parsedData.data),
          };

          setPlaybackSteps((prev) => {
            const existingIndex = prev.findIndex((step) => step.step_id === line.data?.title);
            if (existingIndex >= 0) {
              const updated = [...prev];
              updated[existingIndex] = storeInfoStep;
              return updated;
            } else {
              return [...prev, storeInfoStep];
            }
          });
          setCompletedSteps((prev) => Math.min(1, 4));
          break;

        case 'product_analysis':
          processedData = processAnalysisData(parsedData.data);
          stepNumber = 2;
          const analysisStep: Step = {
            step_id: line.data.title,
            step_title: getStepTitle(line.data.title),
            step_number: stepNumber,
            description: getStepDescription(line.data.title),
            completed_at: new Date().toISOString(),
            content_type: getStepContentType(line.data.title),
            content: '整理信息',
            lodding: '整理信息 工作中',
            contentLodding:
              '## ① 大模型分析：\n- **文本解析...**\n- **意图识别...**\n- **模型推理计算...**\n- **多轮优化...**',
            data: parsedData.data || '',
            processed_data: processedData,
            sub_steps: createSubSteps(line.data.title, parsedData.data),
          };

          setPlaybackSteps((prev) => {
            const existingIndex = prev.findIndex((step) => step.step_id === line.data?.title);
            if (existingIndex >= 0) {
              const updated = [...prev];
              updated[existingIndex] = analysisStep;
              return updated;
            } else {
              return [...prev, analysisStep];
            }
          });

          setCompletedSteps((prev) => Math.min(2, 4));
          break;

        case 'template_information':
          processedData = processTemplateInfo(parsedData);
          stepNumber = 3;
          const templateStep: Step = {
            step_id: line.data.title,
            step_title: getStepTitle(line.data.title),
            step_number: stepNumber,
            description: getStepDescription(line.data.title),
            completed_at: new Date().toISOString(),
            content_type: getStepContentType(line.data.title),
            content: '匹配方案',
            lodding: '匹配方案 工作中',
            contentLodding: '## ① API请求：\n- **API请求中...**\n- **HTTP请求中...**',
            data: parsedData || [],
            processed_data: processedData,
            sub_steps: createSubSteps(line.data.title, parsedData),
          };

          setPlaybackSteps((prev) => {
            const existingIndex = prev.findIndex((step) => step.step_id === line.data?.title);
            if (existingIndex >= 0) {
              const updated = [...prev];
              updated[existingIndex] = templateStep;
              return updated;
            } else {
              return [...prev, templateStep];
            }
          });
          setCompletedSteps((prev) => Math.min(3, 4));
          break;

        case 'cloning_recommendations':
          processedData = processCloneSuggestion(parsedData);
          stepNumber = 4;
          const cloningStep: Step = {
            step_id: line.data.title,
            step_title: getStepTitle(line.data.title),
            step_number: stepNumber,
            description: getStepDescription(line.data.title),
            completed_at: new Date().toISOString(),
            content_type: getStepContentType(line.data.title),
            content: parsedData?.suggestion || '',
            lodding: '生成报告 工作中',
            contentLodding: '## ① 解析视频信息：\n- **API请求...**\n- **数据处理模块处理...**\n- **JSON解析...**',
            data: parsedData || {},
            processed_data: processedData,
            sub_steps: createSubSteps(line.data.title, parsedData),
          };

          setPlaybackSteps((prev) => {
            const existingIndex = prev.findIndex((step) => step.step_id === line.data?.title);
            if (existingIndex >= 0) {
              const updated = [...prev];
              updated[existingIndex] = cloningStep;
              return updated;
            } else {
              return [...prev, cloningStep];
            }
          });

          setCompletedSteps(4);
          break;

        default:
          return null;
      }

      return processedData;
    } catch (error) {
      return null;
    }
  };

  const onSseFinish = async (link: string) => {
    try {
      setTimeout(async () => {
        await saveAnalysisWithPlayback(link);
      }, 2000);
    } catch (error) {
      toast.error('保存分析记录失败，请稍后再试');
    }
  };

  const saveAnalysisWithPlayback = useEvent(async (productUrl: string) => {
    try {
      console.log('analysisResult?.suggestion2', analysisResult?.suggestion);

      const serializedPlaybackSteps = JSON.parse(JSON.stringify(playbackSteps));

      const [err, result] = await to(
        action(
          saveProductAnalysisRecord,
          {
            productUrl,
            analysisResult: {
              product_title: analysisResult?.title,
              product_analysis: analysisResult?.product_analysis,
              video_ids: analysisResult?.video_ids,
              template_info: analysisResult?.template_info,
              price: analysisResult?.price,
              description: analysisResult?.description,
              suggestion: analysisResult?.suggestion,
              picturesUrl: picturesUrl,
            },
            completedSteps,
            status: error ? 'failed' : 'completed',
            ...(error && { errorMessage: '分析失败' }),
            playbackSteps: serializedPlaybackSteps,
          },
          { errorType: 'return' },
        ),
      );

      if (err || !result.success) {
        return null;
      }
      setShareId(result.data.shareId);
      return result.data.shareId;
    } catch (error) {
      return null;
    }
  });
  const simulate = () => {
    const defaultSteps = createDefaultSteps();
    // 初始化所有步骤的子步骤
    defaultSteps.forEach((step, index) => {
      switch (step.step_id) {
        case 'store_page_information':
          step.sub_steps = createSubSteps('store_page_information');
          break;
        case 'product_analysis':
          step.sub_steps = createSubSteps('product_analysis');
          break;
        case 'template_information':
          step.sub_steps = createSubSteps('template_information', fiilterCoverage);
          break;
        case 'cloning_recommendations':
          step.sub_steps = createSubSteps('cloning_recommendations');
          break;
      }
    });
    setCompletedSteps(1);
    setPlaybackSteps(defaultSteps);
  };
  const getPicturesUrl = async (link: string) => {
    try {
      const response = await fetch('/api/screenshots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: link,
        }),
      });

      if (!response.ok) {
        throw new Error('获取截图失败');
      }

      const data = await response.json();
      // 根据返回的数据进行处理
      if (data.status) {
        setPicturesUrl(data.data);
      }
      return data;
    } catch (error) {
      return null;
    }
  };
  const handleAnalyze = async (link: string) => {
    setAnalysisResult(null);
    setIsVideoLoading(true);
    setIsLoading(true);
    setErrorNavigator(false); // 重置网络错误状态
    setError(false);
    setErrorAi(false);
    setPicturesUrl([]);

    if (!navigator.onLine) {
      toast.error('网络连接已断开，请检查网络设置');
      setErrorNavigator(true);
      setIsLoading(false);
      return;
    }

    if (!link.includes('tiktok.com')) {
      toast.error('链接解析失败');
      setError(true);
      setIsLoading(false);
      return;
    }

    // 立即初始化步骤，不等待API响应
    setPlaybackSteps(createDefaultSteps());
    // 设置初始完成步骤为1
    setCompletedSteps(1);
    abortControllerRef.current = new AbortController();
    simulate();
    try {
      //link
      getPicturesUrl(link).catch((error) => {
        console.error('获取截图失败:', error);
        // 这里不设置任何错误状态，让主流程继续执行
      });
      const response = await fetch('/api/llm/chat/analyze-product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          AccessToken: Cookies.get('Authorization') || '',
        },
        body: JSON.stringify({
          productUrl: link,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error('网络请求失败，请检查网络连接');
      }

      await handleSSEResponse<SSEResponse<any>>(response, {
        onFinish: () => onSseFinish(link), // 保存分析记录
        onLine: (line) => {
          try {
            if (line) {
              console.log('line', line);
              // @ts-ignore
              if (line.data?.inputs?.data === '页面解析失败') {
                toast.error('页面解析失败');
                setIsLoading(false);
                setError(true);
                return;
                // @ts-ignore
              } else if (line.data?.inputs?.data === '网络出错了，请检查您的网络') {
                toast.error('网络连接已断开，请检查网络设置');
                setErrorNavigator(true);
                setIsLoading(false);
                return;
              } else {
                const processedData = processSSEData(line);
                if (processedData) {
                  if (line?.data?.title === 'template_information') {
                    // @ts-ignore
                    setVideoIds(line?.data?.outputs?.video_ids);
                  }
                  setAnalysisResult((prev) => ({
                    ...prev,
                    ...processedData,
                  }));
                }
              }
            }
          } catch (e) {
            setIsLoading(false);
            if (!navigator.onLine) {
              toast.error('网络连接已断开，请检查网络设置');
              setErrorNavigator(true);
            }
          }
        },
        onError: (error) => {
          if (!navigator.onLine) {
            toast.error('网络连接已断开，请检查网络设置');
            setErrorNavigator(true);
          } else {
            toast.error(error.message);
          }
          setIsLoading(false);
          if (error.message === '网络出错了，请检查您的网络') {
            setErrorNavigator(true);
            return;
          }
          if (error.message === '链接解析失败') {
            setError(true);
            return;
          }
          setErrorAi(true);
        },
      });
    } catch (error) {
      setIsLoading(false);
      if (error instanceof Error && error.name === 'AbortError') {
        toast.error('请求已取消');
        setError(true);
      }
      toast.error('分析商品失败');
      // 即使出错也尝试保存分析记录
      try {
        await saveAnalysisWithPlayback(link);
      } catch (saveError) {
        console.error('保存分析记录失败:', saveError);
      }
    } finally {
      abortControllerRef.current = null;
    }
  };

  // 取消分析
  const handleCancelAnalyze = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsLoading(false);
    setIsVideoLoading(false);
    setAnalysisResult(null);
    // 重置时也设置为 1
    setCompletedSteps(1);
    setIsFloating(false);
    setError(false);
    setErrorAi(false);
    setErrorNavigator(false);
    setVideoIds([]);
    setPicturesUrl([]);
  };

  return {
    error,
    isLoading,
    isVideoLoading,
    isFloating,
    setIsFloating,
    analysisResult,
    completedSteps,
    handleAnalyze,
    handleCancelAnalyze,
    errorNavigator,
    errorAi,
    playbackSteps,
    shareId,
    fiilterCoverage,
    setFilterCoverage,
    setIsLoading,
    videoIds,
    picturesUrl,
  };
};
