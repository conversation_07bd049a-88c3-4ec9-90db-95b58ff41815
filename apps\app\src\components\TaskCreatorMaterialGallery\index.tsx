import { CrossOutline } from '@/components/icon/CrossOutline';
import { Mp4Fullfil } from '@/components/icon/Mp4Fullfil';
import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { PlusOutline } from '@/components/icon/PlusOutline';
import { ProImage } from '@/components/pro/pro-image';
import { Button, RainbowBorder } from '@/components/ui';
import { MaterialType } from '@/types/material';
import { cn } from '@/utils/cn';
import React from 'react';

const MaterialGalleryEmptyContent: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ className, ...props }) => {
  return (
    <div {...props} className={cn('flex h-full flex-col items-center justify-center', className)}>
      <div className="mb-2 flex h-16 w-16 items-center text-center sm:mb-3 md:mb-5">
        <Mp4Fullfil />
      </div>
      <div className="mb-1 text-xs text-[#9FA4B2]">单个文件不能超过10GB</div>
      <div className="golden-text mb-2 text-xs sm:mb-5 md:mb-8">请上传MP4格式文件，单次最多可上传10个文件</div>
      <Button
        type="button"
        variant="ghost"
        className="w-40 rounded-[26px] border border-[#EFEDFD1A] hover:border-white hover:bg-transparent hover:text-white"
      >
        导入素材
      </Button>
    </div>
  );
};

/**
 * 素材展框 用于展示已选中的素材
 * @returns
 */
export const TaskCreatorMaterialGallery: React.FC<
  React.HTMLAttributes<HTMLDivElement> & {
    materials: MaterialType[];
    add?: () => void;
    remove?: (material: MaterialType) => void;
  }
> = ({ materials: selectedMaterials, add, remove, className, ...props }) => {
  return (
    <div {...props} className={className}>
      <RainbowBorder className="h-72 w-full rounded-[16px]">
        {selectedMaterials.length ? (
          <div className="flex flex-wrap gap-x-[10px] gap-y-2 px-3 py-[10px]">
            {selectedMaterials.map((material) => {
              return (
                <div
                  key={material.id}
                  className="relative h-[84px] w-[84px] overflow-hidden rounded-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  {material ? (
                    <div className="h-full w-full">
                      <ProImage
                        className="aspect-square rounded-lg object-cover"
                        height={84}
                        width={84}
                        style={{ height: '84px', width: '84px', objectFit: 'cover', objectPosition: 'center' }}
                        src={material?.vod_cover_url || ''}
                        alt={material?.name || ''}
                        fallback={() => (
                          <div className="flex h-full w-full flex-col items-center justify-center gap-[16px] bg-[#272D3E] text-[#9FA4B2]">
                            <NotFoundCover className="h-[35px] w-[48px]" />
                          </div>
                        )}
                      />
                    </div>
                  ) : (
                    <div className="h-full w-full rounded-lg border"></div>
                  )}
                  <div className="absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full bg-[#00000080]">
                    <CrossOutline
                      className="h-2 w-2 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        remove?.(material);
                      }}
                    />
                  </div>
                </div>
              );
            })}
            {selectedMaterials.length < 10 && (
              <div
                onClick={() => add?.()}
                className="flex h-[84px] w-[84px] cursor-pointer items-center justify-center rounded-lg bg-[#FFFFFF0D]"
              >
                <PlusOutline className="h-6 w-8" />
              </div>
            )}
          </div>
        ) : (
          <MaterialGalleryEmptyContent onClick={() => add?.()} />
        )}
      </RainbowBorder>
    </div>
  );
};
