import { onFileUploaded } from '@/utils/file-recorder';
import { prisma } from '@/utils/prisma';
import { materials, source_configs, video_generation_sub_tasks, video_generation_tasks } from '@prisma/client';
import { calcCostQuota, calcGenerateCount, VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';
import { request } from '@roasmax/utils/net';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import Langfuse from 'langfuse';
import { fetchDir, refundQuota, TaskStatus } from '../utils';
import { mq } from '@/utils/mq';
import { feishuRobot } from '@/utils/feishu';

dayjs.extend(utc);
dayjs.extend(timezone);

type LangfuseObservationOutput = {
  /** 视频标题 */
  title: string;
  /** 视频话题 */
  topics: string[];
  /** 视频vod地址 */
  edit_media_vod_url: string;
  /** 视频fileId */
  edit_media_file_id: string;
  /** metadata */
  metadata: {
    kol_style: string;
    template_video_tiktok_ids: string[];
    template_file_id: string;
  };
};

type LangfuseTrace = Awaited<ReturnType<Langfuse['fetchTrace']>>['data'];

type VideoMergeLangfuseObservation = Omit<LangfuseTrace['observations'][number], 'output'> & {
  output: LangfuseObservationOutput;
};

type VideoMergeLangfuseTrace = Omit<LangfuseTrace, 'observations'> & {
  observations?: VideoMergeLangfuseObservation[];
};

/**
 * 创建视频处理子任务
 *
 * 一个任务中有多个子任务
 * 每一个子任务对应一个视频片段
 * 每一个视频片段对应一个trace
 *
 * 视频处理子任务需要自行处理错误
 * 由于此处不存在业务造成的错误，且无法满足退点需求（数据库或数据错误导致的错误），所以此处的错误仅为兜底处理
 * 不进行退点，但需要进行报警
 *
 * @param params
 * @returns
 */
export const createGenerationSubTask = async (params: {
  taskId: string;
  originMaterialId: string;
  splitMaterialVideoId: string;
  splitMaterialAudioId: string;
}) => {
  const { taskId, originMaterialId, splitMaterialVideoId, splitMaterialAudioId } = params;
  try {
    const task = await prisma.video_generation_tasks.findUnique({ where: { id: taskId } });
    if (!task) {
      throw new Error('任务不存在');
    }

    const sourceConfig = await prisma.source_configs.findUnique({ where: { tenant_id: task.tenant_id } });
    if (!sourceConfig) {
      throw new Error('源配置不存在');
    }

    // 创建生成子任务
    const subTask = await prisma.video_generation_sub_tasks.create({
      data: {
        tenant_id: sourceConfig.tenant_id,
        user_id: task.user_id,
        task_id: task.id,
        sub_task_type: 'generation',
        origin_material_id: originMaterialId,
        slice_vod_file_id: splitMaterialVideoId,
        slice_vod_audio_file_id: splitMaterialAudioId,
        generated_material_ids: [],
        status: TaskStatus.PENDING,
        status_desc: JSON.stringify({
          completed: 0,
          expected: task.generate_round * (task.method === 'gc_imitate' ? 1 : task.prompts.length),
          stage: '',
        }),
      },
    });

    // 创建好后，尝试触发
    await handlePendingGenerationSubGenerationTask({ subTaskId: subTask.id });
  } catch (e: any) {
    const m = `创建视频处理子任务失败 ${taskId}, ${originMaterialId}, ${splitMaterialVideoId}, ${splitMaterialAudioId}`;
    await feishuRobot.error('创建视频处理子任务失败', [m, e.message || e.toString()]);
    console.error(e.message || e.toString(), m);
    return null;
  }
};

/**
 * 触发视频处理子任务（generation）
 *
 * 一个任务中有多个子任务
 * 每一个子任务对应一个视频片段
 * 每一个视频片段对应一个trace
 *
 * 视频出发子任务需要自行处理错误
 * 这里有2层兜底，以及一个任务水池
 * 1. 如果是请求langfuse或推送到队列时失败，则需要进行兜底处理，退点+报警
 * 2. 最外层兜底，如果触发视频处理子任务失败，则需要进行兜底处理，由于可能不满足退点报警条件，所以这里只进行报警
 * 3. 任务水池，来限制任务的并发，如果任务水池满了，则需要等待，直接抛出异常即可，有专门的队列来处理等待的任务
 *
 * @param ctx
 */
export const handlePendingGenerationSubGenerationTask = async (params: { subTaskId: string }) => {
  const { subTaskId } = params;
  try {
    const subTask = await prisma.video_generation_sub_tasks.findUnique({ where: { id: subTaskId } });
    if (!subTask) {
      throw new Error('子任务不存在');
    }
    if (subTask.status !== TaskStatus.PENDING) {
      throw new Error('子任务状态不正确');
    }

    // 这里需要做一个任务水池，来限制任务的并发
    // 如果任务水池满了，则需要等待，直接抛出异常即可，有专门的队列来处理等待的任务
    const processingSubTasks = await prisma.video_generation_sub_tasks.findMany({
      where: { status: TaskStatus.PROCESSING },
      select: { task: { select: { generate_round: true } } },
      take: Number(process.env.GENERATION_TASK_POOL_SIZE || 10),
    });

    // 计算当前任务水池中的预期生成视频数量
    const processingExpectedCount = processingSubTasks.reduce((acc, cur) => {
      return acc + cur.task.generate_round;
    }, 0);

    if (processingExpectedCount >= Number(process.env.GENERATION_TASK_POOL_SIZE || 10)) {
      throw new Error('任务并发达到上限');
    }

    const task = await prisma.video_generation_tasks.findUnique({ where: { id: subTask.task_id } });
    if (!task) {
      throw new Error('任务不存在');
    }

    const sourceConfig = await prisma.source_configs.findUnique({ where: { tenant_id: subTask.tenant_id } });
    if (!sourceConfig) {
      throw new Error('源配置不存在');
    }

    // 更新任务
    await prisma.video_generation_sub_tasks.update({
      where: { id: subTask.id },
      data: { trace_id: subTask.id, status: TaskStatus.PROCESSING },
    });

    try {
      const langfuse = new Langfuse({
        baseUrl: process.env.LANGFUSE_HOST,
        publicKey: sourceConfig.langfuse_public_key,
        secretKey: sourceConfig.langfuse_secret_key,
      });

      const trace = langfuse.trace({
        id: subTask.id,
        name: task?.name,
        userId: task.user_id,
        sessionId: task.id,
        input: {
          file_id: subTask.slice_vod_file_id,
          file_name: `${subTask.slice_vod_file_id}`,
          audio_file_id: subTask.slice_vod_audio_file_id,
        },
        metadata: {},
        tags: ['video_generation'],
      });
      await langfuse.flushAsync();

      // 准备任务的参数
      const region = sourceConfig.slice_task_pool?.name === 'Bowong' ? 'HangZhou' : 'ShenZhen';
      const input = {
        file_id: subTask.slice_vod_file_id,
        file_name: `${subTask.slice_vod_file_id}`,
        audio_file_id: subTask.slice_vod_audio_file_id,
        metadata: {
          // 统一的基础配置参数 工作流处理需要 无关任务类型
          tenant_id: sourceConfig.tenant_id,
          trace_id: trace.id,
          x_region: region,
          x_langfuse_pk: sourceConfig.langfuse_public_key,
          x_langfuse_sk: sourceConfig.langfuse_secret_key,
          vod_sub_id: Number(sourceConfig.vod_sub_app_id),
          vod_slice_class_id: Number(sourceConfig.vod_c_screen_record_split),
          vod_merge_class_id: Number(sourceConfig.vod_c_screen_record_merge),
          vod_fin_class_id: Number(sourceConfig.vod_c_screen_record_final),
          // 通用生成配置
          video_duration: 300, // 视频时长 临时写死，这个字段本身并不会被后端使用，后端会自己从vod中获取
          video_speed: Number(task.video_speed.slice(0, -1)), // 视频播放速度
          prompt_keys: task.prompts, // GPT提示词
          language: task.video_language, // 原视频语言
          batch_count: task.generate_round, // 生成轮询次数
          generate_subtitles: !!task.subtitle, // 是否生成字幕
          transition_mode: task.transition_mode || 'null', // 转场模式
          dify_workflow_key: task.dify_workflow_key, // 工作流key
          // method分为 AI视频精剪 和 AI智能生成
          method: task.method === 'gc_imitate' ? 'generation' : 'clip',
          // AI视频精剪 时有效
          industry: task.method === 'normal' ? task.industry : null,
          // AI智能生成 时有效
          product_url: task.method === 'gc_imitate' ? task.product_url : null,
          product_title: task.method === 'gc_imitate' ? task.product_title : null,
          product_min_price: task.method === 'gc_imitate' ? task.product_min_price : null,
          product_max_price: task.method === 'gc_imitate' ? task.product_max_price : null,
          product_currency: task.method === 'gc_imitate' ? task.product_currency : null,
          scene_implantation: task.method === 'gc_imitate' ? task.scene_implantation : null,
          festive_atmosphere: task.method === 'gc_imitate' ? task.festive_atmosphere : null,
          // AI智能生成 时有效 generation_type有值 分为 大V严选 和 KOL转移
          generation_type: task.method === 'gc_imitate' ? task.generation_type : null,
          // AI智能生成/大V严选 时有效
          industry_id: task.method === 'gc_imitate' ? task.industry_id : null,
          kol: task.method === 'gc_imitate' ? task.kol_style : null,
          target_language: task.method === 'gc_imitate' ? task.target_language : null,
          // AI智能生成/KOL转移 时有效
          template_file_id: task.method === 'gc_imitate' ? task.template_video_vod_id : null,
          product_analysis: task.method === 'gc_imitate' ? task.product_analysis : null,
          template_video_tiktok_ids: task.method === 'gc_imitate' ? task.template_video_tiktok_ids : null,
          template_video_language: task.method === 'gc_imitate' ? task.template_video_language : null,
          custom_prompt: task.method === 'gc_imitate' ? task.custom_prompt : null,
          target_voice_clone_type: task.method === 'gc_imitate' ? task.target_voice_clone_type : null,
          target_voice: task.method === 'gc_imitate' ? task.target_voice : null,
          target_video_duration: task.method === 'gc_imitate' ? task.target_video_duration : null,
        },
      };
      // 推送到队列
      await mq.push({
        host: process.env.BULLMQ_ENDPOINT,
        topic: `/queues/${task.method === 'gc_imitate' ? 'gpt' : 'asr'}/jobs/${region}`,
        name: 'upload-video',
        data: input,
        options: { priority: 5, attempts: 3, backoff: { type: 'exponential', delay: 1000 } },
      });
    } catch (e: any) {
      // 如果在请求langfuse或推送到队列时失败，则需要进行兜底处理，退点+报警
      const quota = calcCostQuota({
        method: task.method as 'gc_imitate' | 'normal',
        sliceDuration: 300,
        // 由于这是单个视频片段，所以这里传入一个300的数组
        materialDurations: [300],
        generateRound: task.generate_round,
        generationType: task.generation_type || undefined,
        prompts: task.prompts,
        templateCount: task.template_video_vod_id ? 1 : undefined,
      });
      await refundQuota({ tenantId: task.tenant_id, quota, reason: `触发视频处理子任务失败 ${subTask.id}` });
      await feishuRobot.error('触发视频处理子任务失败', ['触发视频处理子任务失败', e.message || e.toString()]);
      console.error(e.message || e.toString(), '触发视频处理子任务失败');
    }
  } catch (e: any) {
    const m = `触发视频处理子任务失败 ${subTaskId}`;
    await feishuRobot.error('触发视频处理子任务失败', [m, e.message || e.toString()]);
    console.error(e.message || e.toString(), m);
  }
};

/**
 * 处理进行中的生成子任务（generation）
 * @param taskId
 */
export const handleProcessingGenerationSubGenerationTask = async (params: {
  subTask: video_generation_sub_tasks;
  task: video_generation_tasks;
  originMaterial: materials;
  sourceConfig: source_configs;
}) => {
  const { subTask, task, originMaterial, sourceConfig } = params;
  if (subTask.status !== TaskStatus.PROCESSING) {
    console.log('任务状态不是处理中，子任务id：' + subTask.id);
    return;
  }

  const langfuse = new Langfuse({
    baseUrl: process.env.LANGFUSE_HOST,
    publicKey: sourceConfig.langfuse_public_key,
    secretKey: sourceConfig.langfuse_secret_key,
  });

  // 获取Langfuse任务的最新观察结果
  const trace = (await langfuse.fetchTrace(subTask.trace_id!)) as { data: VideoMergeLangfuseTrace };
  const latestObservation = trace.data.observations?.[trace.data.observations.length - 1];
  const latestObservationName = latestObservation?.name;

  // 存在字幕生成任务提交报错的情况，这种情况下直接失败
  const subtitleGenerationFailedObservation = trace.data.observations?.find(
    (item) => item.name === '字幕生成任务提交' && item.level === 'ERROR',
  );
  if (subtitleGenerationFailedObservation) {
    console.log('任务失败', subTask.id, latestObservationName);
    await prisma.video_generation_sub_tasks.update({
      where: { id: subTask.id },
      data: {
        status: TaskStatus.FAILED,
        status_desc: '字幕生成任务提交失败',
      },
    });
    // 退点计算 每个预切分任务，退 轮询次数 * prompts数 * 10
    const quota = task.generate_round * (task.method === 'gc_imitate' ? 1 : task.prompts.length) * 10;
    await refundQuota({ tenantId: task.tenant_id, quota: quota, reason: '生成任务失败返点' });
    return;
  }

  // 计算已经完成的视频合成结果数量
  // 获取已经完成的视频合成结果 要么是有输出的，要么是有错误的，要么是超时的
  const spans = groupObservationsByStatus(trace.data.observations?.filter((item) => item.name === '视频合成') || []);
  const completedCount = spans.success.length + spans.failed.length + spans.timeout.length;

  // 计算预期生成的视频合成结果数量
  const calcParamsForExpectedCount = {
    method: task.method as 'gc_imitate' | 'normal',
    sliceDuration: Number(task.slice_duration),
    materialDurations: [Number(task.slice_duration)],
    generateRound: task.generate_round,
    prompts: task.prompts,
    generationType: task.generation_type || '',
    templateCount: task.template_video_tiktok_ids?.length,
  };
  const expectedCount = calcGenerateCount(calcParamsForExpectedCount);

  // 所有的预期结果都已经成功/失败，或者已经超出最大的等待时间，都会进入结算
  // 任务超时4h未完成，算作超时失败，这个超时从任务提交开始计算
  const taskExpired = new Date().getTime() - new Date(task.tmp_created_at).getTime() > 4 * 60 * 60 * 1000;
  if (completedCount < expectedCount && !taskExpired) {
    // 任务未完成
    console.log('任务进行中', subTask.id, latestObservationName);
    await prisma.video_generation_sub_tasks.update({
      where: { id: subTask.id },
      data: {
        status: TaskStatus.PROCESSING,
        status_desc: JSON.stringify({
          completed: completedCount,
          expected: expectedCount,
          stage: latestObservationName,
        }),
      },
    });
    return;
  }

  // 如果所有的视频合成都已完成，或任务超时
  console.log(
    taskExpired ? '任务已超时' : '任务已完成',
    subTask.id,
    `结果统计: 成功 ${spans.success.length} / 失败 ${spans.failed.length} / 超时 ${spans.timeout.length} / 期望 ${expectedCount}`,
  );

  // 获取生成的视频合成结果素材的vod_file_id
  const successObservationOutputs = spans.success.map((o) => o.output).filter((v) => v.edit_media_file_id);

  // 创建生成素材
  const generatedMaterials = await createGeneratedMaterials({
    taskName: task.name,
    taskId: task.id,
    tenantId: subTask.tenant_id,
    originName: originMaterial.name,
    materials: successObservationOutputs.map((o) => ({
      vodFileId: o.edit_media_file_id,
      vodUrl: o.edit_media_vod_url,
    })),
  });

  // 更新素材的参考风格
  for (const material of generatedMaterials) {
    const metadata = successObservationOutputs.find((o) => o.edit_media_file_id === material.vod_file_id)?.metadata;
    await prisma.materials.update({
      where: { id: material.id },
      data: {
        generation_params: {
          kol_style: metadata?.kol_style,
          template_video_tiktok_ids: metadata?.template_video_tiktok_ids,
          template_file_id: metadata?.template_file_id,
        },
      },
    });
  }

  // 更新子任务状态
  await prisma.video_generation_sub_tasks.update({
    where: { id: subTask.id },
    data: {
      status: TaskStatus.SUCCESS,
      status_desc: latestObservationName,
      generated_material_ids: generatedMaterials.map((m) => m.id),
    },
  });

  // 剩余失败的产出，需要退点
  const quota = (expectedCount - generatedMaterials.length) * 10;
  await refundQuota({ tenantId: subTask.tenant_id, quota: quota, reason: '生成任务失败返点' });

  // 触发生成任务完成回调
  await onGenerationSubGenerationTaskSuccess({ subTask, task, originMaterial, generatedMaterials });
};

/**
 * 触发生成任务子任务（generation）完成回调
 * @param params
 */
const onGenerationSubGenerationTaskSuccess = async (params: {
  subTask: video_generation_sub_tasks;
  task: video_generation_tasks;
  originMaterial: materials;
  generatedMaterials: materials[];
}) => {
  const { subTask, task, originMaterial, generatedMaterials } = params;
  // 判断task 是否携带generation_source字段并且为ADS
  if (task?.generation_source && task?.generation_source === 'ADS') {
    console.log('任务为ADS生成任务', task.id);
    // 检查所有子任务是否都已完成
    const allSubTasks = await prisma.video_generation_sub_tasks.findMany({
      where: { task_id: task.id },
    });

    const allSubTasksWithoutStatusDesc = allSubTasks.map((task) => {
      const { status_desc = '', ...restTask } = task;
      return restTask;
    });

    const allTasksCompleted = allSubTasks
      .filter((subTask) => subTask.sub_task_type === 'generation')
      .every((task) => task.status === TaskStatus.SUCCESS || task.status === TaskStatus.FAILED);

    if (allTasksCompleted) {
      console.log('所有子任务已完成');
      try {
        await request.post('https://video-dev.bowongai.com/api/webhooks/generate-ads-callback', {
          taskId: task.id,
          status: 'completed',
          subTasks: allSubTasksWithoutStatusDesc,
        });
        console.log('已发送生成完成回调通知', task.id);
      } catch (error) {
        console.error('发送生成完成回调通知失败', error);
      }
    }
  }

  // 检查原始素材是否含有distribute字段, 若值为真则需要创建自动分发子任务
  if (originMaterial.properties?.distribute === 'auto') {
    const { ipName, productName, liveRoom, liveSession } = originMaterial.properties;
    // 前缀 roasmax/租户id/自动分发/生成素材/IP/商品/直播间/日期/
    const cosPushPath = `roasmax/${originMaterial.tenant_id}/自动分发/生成素材/${ipName}/${productName}/${liveRoom}/${liveSession}/`;
    console.log('自动分发，创建分发子任务');
    const autoDistributionTask = await prisma.video_distribution_sub_tasks.createMany({
      data: generatedMaterials.map((m) => {
        return {
          tenant_id: subTask.tenant_id,
          material_id: m.id,
          status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.出片中,
          wait_cos_path: `${cosPushPath}${m.name}`,
          ip: ipName,
          goods_name: productName,
          live_room: liveRoom,
          live_session: liveSession,
          status_desc: '等待投送到COS',
          publish_product_name: '',
          publish_product_link: '',
        };
      }),
    });
    console.log('自动分发子任务创建完成', autoDistributionTask);
    // IP/商品/直播间/日期
    console.log('开始转推到COS', cosPushPath);
    await request
      .post(
        `${process.env.COS_PUSH_HOST}/api/transferVideo`,
        generatedMaterials.map((m) => ({
          vodUrl: m.vod_media_url,
          fileName: m.name,
          cosDest: cosPushPath,
          fileId: m.vod_file_id,
        })),
      )
      .then(async (res) => {
        console.log('转推结果', JSON.stringify(res.data));
        await prisma.video_distribution_sub_tasks.updateMany({
          where: { material_id: { in: generatedMaterials.map((t) => t.id) } },
          data: {
            status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.出片中,
            status_desc: '已成功提交COS推送，等待推送成功',
          },
        });
      })
      .catch(async (err) => {
        console.error('转推失败', err?.message);
        await prisma.video_distribution_sub_tasks.updateMany({
          where: { material_id: { in: generatedMaterials.map((t) => t.id) } },
          data: {
            status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已取消,
            status_desc: '投送COS失败',
          },
        });
      });
  }
};

/**
 * 检查观察结果的状态 这里只对[视频合成]进行检查
 * @param observation
 * @returns
 */
const checkObservationStatus = (
  observation: Awaited<ReturnType<Langfuse['fetchTrace']>>['data']['observations'][number],
) => {
  if (observation.output) {
    return 'success';
  }
  if (observation.level === 'ERROR') {
    return 'failed';
  }
  if (new Date().getTime() - new Date(observation.startTime).getTime() > 45 * 60 * 1000) {
    // 视频合成span超过40min未完成，算作超时失败，这个超时从视频合成任务开始计算
    return 'timeout';
  }
  return 'processing';
};

/**
 * 根据观察结果的状态进行分组
 * @param observations
 * @returns
 */
const groupObservationsByStatus = (observations: VideoMergeLangfuseObservation[]) => {
  return observations.reduce<{
    success: typeof observations;
    failed: typeof observations;
    timeout: typeof observations;
  }>(
    (pre, cur) => {
      if (checkObservationStatus(cur) === 'success') {
        pre.success.push(cur);
      } else if (checkObservationStatus(cur) === 'failed') {
        pre.failed.push(cur);
      } else if (checkObservationStatus(cur) === 'timeout') {
        pre.timeout.push(cur);
      }
      return pre;
    },
    { success: [], failed: [], timeout: [] },
  );
};

/**
 * 创建生成素材
 * @param params
 * @returns
 */
const createGeneratedMaterials = async (params: {
  taskName: string;
  taskId: string;
  tenantId: string;
  originName: string;
  materials: { vodFileId: string; vodUrl: string }[];
}) => {
  if (params.materials.length === 0) {
    return [];
  }
  // 获取生成素材需要存放的目录
  const subTaskDir = await fetchDir({ tenantId: params.tenantId, taskDirName: `${params.taskName}_${params.taskId}` });
  // 登记生成的素材
  return await onFileUploaded({
    forceUpdate: true,
    tenantId: params.tenantId,
    info: {
      directoryId: subTaskDir!.id,
      list: params.materials.map((o) => {
        return {
          vodFileId: o.vodFileId,
          name: `[出片]${params.originName.replace('.', '-')}-${o.vodUrl.split('/').pop()}`,
        };
      }),
    },
  });
};
