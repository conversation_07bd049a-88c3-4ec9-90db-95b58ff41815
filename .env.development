# Roasmax 开发环境配置

# 数据库配置 - 本地 Docker MySQL
DATABASE_URL="mysql://roasmax_user:roasmax_password@localhost:3306/roasmax_dev?connection_limit=5&pool_timeout=300"

# 数据库连接参数（用于迁移脚本）
DB_HOST=localhost
DB_PORT=3306
DB_NAME=roasmax_dev
DB_USER=roasmax_user
DB_PASSWORD=roasmax_password

# JWT 配置
JWT_SECRET=your-super-secure-jwt-secret-key-for-development
APPSECRET=your-super-secure-jwt-secret-key-for-development

# 密码加密配置
PASSWORD_SALT_ROUNDS=12

# 会话配置
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_DURATION=3600

# 应用配置
NODE_ENV=development
PORT=3000

# 其他现有配置（保持不变，用于兼容）
BULLMQ_ENDPOINT=your-bullmq-endpoint
LANGFUSE_BASEURL=your-langfuse-url
VOD_SECRETKEY=your-vod-secret
VOD_SECRETID=your-vod-id

# Authing 配置（迁移期间保留，后续移除）
# APPHOST=
# APPID=
# APPSECRET=
# PRIVATE_ACCESSKEYID_ID=
# PRIVATE_ACCESSKEYSECRET_ID=
