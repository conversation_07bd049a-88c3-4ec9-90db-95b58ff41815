{"extends": "../../packages/typescript-config/base.json", "compilerOptions": {"target": "ESNext", "lib": ["esnext"], "module": "ESNext", "moduleResolution": "Node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "paths": {"@/*": ["./src/*"], "@roasmax/database": ["../../packages/database/src"], "@roasmax/utils": ["../../packages/utils/src/"], "@roasmax/utils/*": ["../../packages/utils/src/*"], "@roasmax/scf-web-server": ["../../packages/scf-web-server/src/"]}}, "include": ["src/**/*", "typings.d.ts"], "exclude": ["node_modules"]}