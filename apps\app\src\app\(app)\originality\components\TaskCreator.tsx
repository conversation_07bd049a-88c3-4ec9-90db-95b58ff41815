'use client';
import { TEMPLATE_VIDEO_LANGUAGES, UPLOAD_VIDEO_LANGUAGES } from '@/common/statics/languages';
import { asyncConfirm, confirm } from '@/components/ConfirmDialog';
import { FilePicker } from '@/components/FilePicker';
import { RadiusClose } from '@/components/icon';
import { StarsFullfil } from '@/components/icon/StartsFullfil';
import { UploadIcon } from '@/components/icon/upload';
import { WarningFullfil } from '@/components/icon/WarningFullfil';
import { ProFormField } from '@/components/pro/pro-form';
import { ProSelect } from '@/components/pro/pro-select';
import { ProToggleGroup } from '@/components/pro/pro-toggle-group';
import { TaskCreatorMaterialGallery } from '@/components/TaskCreatorMaterialGallery';
import { useTaskCreatorStore } from '@/hooks/useTaskCreator';

import {
  Button,
  Form,
  Input,
  Panel,
  Progress,
  Skeleton,
  Switch,
  Tooltip,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui';
import {
  formSchema,
  GENERATE_ROUND_OPTIONS,
  GENERATE_SPEED_OPTIONS,
  TaskCreatorFormValues,
  useTaskCreator,
} from '@/hooks/useTaskCreator';
import useTaskListFetcher from '@/hooks/useTaskListFetcher';
import { useWallet } from '@/hooks/useWallet';
import { getTenantEnabledSubSystems } from '@/services/actions/tenant';
import { startVideoGenerationTask } from '@/services/actions/video-generation-task';
import { guide } from '@/store/guide';
import { cn } from '@/utils/cn';
import { checkHasAudioTrack, checkIsMP4, getVideoDuration } from '@/utils/common';
import { action, useAction } from '@/utils/server-action/action';
import { uploadToVod } from '@/utils/upload';
import { calcCostQuota, calcGenerateCount } from '@roasmax/utils';
import dayjs from 'dayjs';
import { uniqBy } from 'lodash';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';

const TaskCreator: React.FC<{
  onClickMaterialImport?: () => void;
}> = (props) => {
  const router = useRouter();

  const {
    form,
    formValues,
    promptTree,
    loadingPrompts,
    kolStyles,
    loadingKolStyles,
    selectedMaterials,
    removeSelectedMaterials,
    setSelectedMaterials,
    resetForm,
  } = useTaskCreator();
  const { refresh: refreshTaskList } = useTaskListFetcher();
  const { refresh: refreshWallet } = useWallet();
  const { currentStep, handleHighlight, steps } = guide();
  const { formData } = useTaskCreatorStore();

  const { data: subSystems } = useAction(getTenantEnabledSubSystems, {});

  useEffect(() => {
    if (Object.keys(formData).length > 0) {
      form.reset(formData as TaskCreatorFormValues);
    }
  }, [form, formData]);

  useEffect(() => {
    setSelectedMaterials(() => []);
    form.setValue('name', `视频精剪-${dayjs().format('YYYYMMDDHHmmss')}`);
    form.setValue('method', 'normal' as const);
  }, [form]);

  /**
   * 计算生成视频数量和消耗点数
   */
  const { expectVideoCount, expectCostQuota } = useMemo(() => {
    const p = {
      method: formValues.method || 'normal',
      sliceDuration: formValues.sliceType === '300' ? 300 : 1800,
      materialDurations: selectedMaterials.map((m) => m.video_duration),
      generateRound: Number(formValues.generateRound),
      prompts: (formValues as TaskCreatorFormValues<'normal'>).prompts,
    };
    const expectVideoCount = calcGenerateCount(p);
    const expectCostQuota = calcCostQuota(p);
    return { expectVideoCount, expectCostQuota };
  }, [
    formValues.generateRound,
    formValues.sliceType,
    (formValues as TaskCreatorFormValues<'normal'>).prompts,
    selectedMaterials,
  ]);

  const [templateVideoUploading, setTemplateVideoUploading] = useState<boolean>(false);
  const [templateVideoUploadProgress, setTemplateVideoUploadProgress] = useState<number>(0);

  /**
   * 确认云盘已满的提示
   */
  const confirmCloudFull = useCallback(() => {
    return confirm({
      content: (
        <div className="mb-[12px] flex gap-[8px]">
          <div className="mt-[3px]">
            <WarningFullfil className="h-[18px] w-[18px]" />
          </div>
          <div className="w-[200px]">
            <div className="mb-[4px]">当前云盘存储空间已满</div>
            <div className="text-xs font-normal text-[#95A0AA]">无法生成相关任务，请合理利用资源 </div>
          </div>
        </div>
      ),
      operations: ({ setOpen }) => [
        <Button
          key="cancel"
          className="h-[32px] w-[92px] bg-[#CCDDFF1A] text-[#FFF]"
          variant="secondary"
          onClick={() => setOpen(false)}
        >
          我知道了
        </Button>,
        <Button
          key="go-to-cloud"
          className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]"
          onClick={() => {
            setOpen(false);
            router.push('/cloud');
          }}
        >
          管理云盘
        </Button>,
      ],
    });
  }, [router]);

  /**
   * 提交确认
   */
  const onSubmitConfirm = useCallback(
    async (values: z.infer<typeof formSchema>) => {
      const res = await action(
        startVideoGenerationTask,
        {
          name: values.name,
          sliceType: values.sliceType,
          batchName: values.name,
          method: values.method,
          language: (values as TaskCreatorFormValues<'normal'>).language,
          industry: (values as TaskCreatorFormValues<'normal'>).industry,
          industryId: (values as TaskCreatorFormValues<'gc_imitate'>).industryId,
          generateCount: Number(values.generateRound),
          accelerate: values.speed,
          subtitles: values.subtitle,
          transition_mode: values.transition_mode,
          materialIds: selectedMaterials.map((material) => material.id),
          kolStyle: (values as TaskCreatorFormValues<'gc_imitate'>).kolStyle,
          prompts: (values as TaskCreatorFormValues<'normal'>).prompts,
          productUrl: (values as TaskCreatorFormValues<'gc_imitate'>).productUrl,
          dify: (values as TaskCreatorFormValues<'normal'>).dify || false,
          generationType: (values as TaskCreatorFormValues<'gc_imitate'>).generationType,
          templateVideoVodId: (values as TaskCreatorFormValues<'gc_imitate'>).templateVideoVodId,
          templateVideoLanguage: (values as TaskCreatorFormValues<'gc_imitate'>).templateVideoLanguage,
          targetLanguage: (values as TaskCreatorFormValues<'gc_imitate'>).targetLanguage,
          targetVoice: (values as TaskCreatorFormValues<'gc_imitate'>).targetVoice,
          sceneImplantation: (values as TaskCreatorFormValues<'gc_imitate'>).sceneImplantation,
          festiveAtmosphere: (values as TaskCreatorFormValues<'gc_imitate'>).festiveAtmosphere,
        },
        { errorType: 'return' },
      );
      if (!res.success) {
        if (res.message === '当前云盘存储空间已满') {
          setTimeout(() => confirmCloudFull(), 0);
          return;
        }
        toast.error(res.message);
        return;
      }
      resetForm();
      setSelectedMaterials(() => []);
      await refreshTaskList();
      refreshWallet();
      router.push('/video-generation-tasks');
    },
    [selectedMaterials, resetForm, form, setSelectedMaterials, refreshTaskList, refreshWallet, confirmCloudFull],
  );

  /**
   * 提交
   */
  const onSubmit = useCallback<SubmitHandler<z.infer<typeof formSchema>>>(
    async (values) => {
      if (values.method === 'gc_imitate') {
        await asyncConfirm({
          content: (
            <div className="mb-2 flex gap-[8px]">
              <div className="mt-[1px]">
                <WarningFullfil className="h-[18px] w-[18px]" />
              </div>
              <div className="w-[300px]">
                <div className="mb-2 text-sm">生成视频确认</div>
                <div className="text-[13px] font-normal leading-[22px] text-[#95A0AA]">
                  <div>
                    预计扣除：<span className="text-[#00E1FF]">{expectCostQuota} 点数</span>；预计生成{' '}
                    <span className="text-[#00E1FF]">{expectVideoCount} 条视频</span>
                  </div>
                </div>
              </div>
            </div>
          ),
          buttonText: { cancel: '返回修改', confirm: '确定生成' },
          onConfirm: () => onSubmitConfirm(values),
        });
        return;
      }

      const lang = UPLOAD_VIDEO_LANGUAGES.find(
        (lang) => lang.value === (values as TaskCreatorFormValues<'normal'>).language,
      )?.label;
      await asyncConfirm({
        content: (
          <div className="mb-2 flex gap-[8px]">
            <div className="mt-[1px]">
              <WarningFullfil className="h-[18px] w-[18px]" />
            </div>
            <div className="w-[300px]">
              <div className="mb-2 text-sm">生成视频确认</div>
              <div className="text-[13px] font-normal leading-[22px] text-[#95A0AA]">
                <div>
                  视频语言：
                  <span className="text-[#00E1FF]">{lang || (values as TaskCreatorFormValues<'normal'>).language}</span>
                </div>
                <div>
                  预计扣除：<span className="text-[#00E1FF]">{expectCostQuota} 点数</span>；预计生成{' '}
                  <span className="text-[#00E1FF]">{expectVideoCount} 条视频</span>
                </div>
              </div>
            </div>
          </div>
        ),
        buttonText: { cancel: '返回修改', confirm: '确定生成' },
        onConfirm: () => onSubmitConfirm(values),
      });
    },
    [expectCostQuota, expectVideoCount, onSubmitConfirm],
  );

  useEffect(() => {
    const hasSeenTour = steps.slice(0, 5).every((step) => step.isNextStep === true);
    if (hasSeenTour) {
      const el = document.getElementById('next2');
      if (el) {
        el.style.zIndex = '1';
      }
    } else {
      handleHighlight('next1', 0, currentStep);
      handleHighlight('next2', 4, currentStep);
    }
  }, [currentStep, handleHighlight, steps]);
  return (
    <Panel className="relative h-full w-full pb-16 pt-0" id="BWAI_TASK_CREATOR_SIDEBAR">
      <div className="h-full overflow-y-auto rounded-t-xl" style={{ scrollbarWidth: 'none' }}>
        {/* <RadioGroup
          value={formValues.method}
          onValueChange={(v) => {
            form.setValue('method', v as 'normal' | 'gc_imitate');
            form.setValue('industry', '');
            form.setValue('industryId', '');
            form.setValue('referenceDuration', '');
            form.setValue('prompts', []);
            form.setValue('kolStyle', '');
            form.setValue('productUrl', '');
            if (v === 'gc_imitate') {
              form.setValue('generationType', '大卖推荐');
              form.setValue('sceneImplantation', true);
              const defaultKol = kolStyles[0];
              form.setValue('industryId', defaultKol?.industry_id ? String(defaultKol?.industry_id) : '');
              form.setValue('kolStyle', defaultKol?.kol || '');
            }
          }}
          className={cn(
            'sticky top-0 grid h-12 grid-cols-2 gap-0 rounded-t-2xl backdrop-blur-lg [&>*]:flex',
            '[&>*]:h-12 [&>*]:cursor-pointer [&>*]:items-center [&>*]:justify-center',
          )}
        >
          <Label
            htmlFor="option1"
            className={cn(
              'cursor-pointer',
              formValues.method === 'normal'
                ? 'border-b-2 border-[#00E1FF] text-[#00E1FF]'
                : 'border-b border-[#363D54] pb-[1px]',
            )}
          >
            <RadioGroupItem value="normal" id="option1" className="peer sr-only" />
            <div>AI精剪</div>
          </Label>
          <Label
            htmlFor="option2"
            className={cn(
              disabledGeneration ? 'cursor-not-allowed' : 'cursor-pointer',
              formValues.method === 'gc_imitate'
                ? 'border-b-2 border-[#00E1FF] text-[#00E1FF]'
                : 'border-b border-[#363D54] pb-[1px]',
            )}
          >
            <RadioGroupItem value="gc_imitate" id="option2" className="peer sr-only" disabled={disabledGeneration} />
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <div>AI生成</div>
                    <NewBadge className="absolute right-[-38px] top-[-2px]" />
                  </div>
                </TooltipTrigger>
                {disabledGeneration && (
                  <TooltipContent className="flex h-[32px] items-center bg-[white] text-[13px] text-[#282E43]">
                    {'视频 <60s 无法使用AI智能生成'}
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </Label>
        </RadioGroup> */}
        <div className="px-4 pt-4">
          <Form {...form}>
            <form autoComplete="off">
              <TaskCreatorMaterialGallery
                // id="next1"
                className="mb-10"
                materials={selectedMaterials}
                add={props.onClickMaterialImport}
                remove={(m) => removeSelectedMaterials(m.id)}
              />
              <ProFormField
                name="name"
                label="任务名称"
                form={form}
                renderFormControl={(field) => (
                  <div className="relative">
                    <Input
                      value={field.value}
                      onChange={(e) => {
                        const v = e.target.value.slice(0, 20);
                        field.onChange(v);
                      }}
                      className={cn(
                        'rounded-[8px] bg-[#CCDDFF1A] font-normal file:border-0 placeholder:font-normal placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A]',
                      )}
                      placeholder="请输入批次名称"
                    />
                    {formValues.name?.length !== 0 && (
                      <div
                        onClick={() => form.setValue('name', '')}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500"
                      >
                        <RadiusClose />
                      </div>
                    )}
                  </div>
                )}
                extra={
                  <div
                    className={cn(
                      'text-xs',
                      (formValues.name?.length || 0) >= 20 ? 'text-[#FF4D4D]' : 'text-[#BEC5D6]',
                    )}
                  >
                    {formValues.name?.length || 0} / 20
                  </div>
                }
              />
              {formValues.method === 'normal' && (
                <div id="next2">
                  <ProFormField
                    name="language"
                    label={
                      <div>
                        原视频语言
                        <span className="text-normal text-[13px] text-[#F3A93C]">{' (请确保语言与视频一致)'}</span>
                      </div>
                    }
                    form={form}
                    renderFormControl={(field) => (
                      <ProSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder={<span className="font-normal text-[#9FA4B2]">请选择语言</span>}
                        options={UPLOAD_VIDEO_LANGUAGES.map(({ value, label }) => ({ value, label }))}
                        className="bg-[#CCDDFF1A]"
                      />
                    )}
                  />
                </div>
              )}
              {formValues.method === 'gc_imitate' && (
                <ProFormField
                  name="generationType"
                  label="生成方式"
                  form={form}
                  renderFormControl={(field) => (
                    <ProToggleGroup
                      type="single"
                      className="flex-wrap justify-start"
                      value={field.value}
                      onValueChange={(v) => {
                        field.onChange(v as '大V严选' | 'KOL转移' | '大卖推荐');
                        form.setValue('targetLanguage', '');
                        form.setValue('kolStyle', '');
                        form.setValue('templateVideoVodId', '');
                        form.setValue('templateVideoLanguage', '');
                        form.setValue('targetVoice', '');
                        form.setValue('industry', '');
                        form.setValue('industryId', '');
                        if (v === '大V严选') {
                          const defaultKol = kolStyles[0];
                          form.setValue('industryId', defaultKol?.industry_id ? String(defaultKol?.industry_id) : '');
                          form.setValue('kolStyle', defaultKol?.kol || '');
                        }
                      }}
                      eclipse={{ lines: 3 }}
                      toggles={[
                        { value: '大卖推荐', label: '大卖推荐' },
                        { value: '大V严选', label: '大V严选' },
                        { value: 'KOL转移', label: 'KOL转移' },
                      ]}
                      toggleItemProps={{
                        className: cn(
                          'border border-transparent bg-[#CCDDFF1A] text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-[#FFF]',
                          'text-xs data-[state=on]:text-[#FFF] data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#CCDDFF0F]',
                        ),
                      }}
                    />
                  )}
                />
              )}
              {formValues.method === 'gc_imitate' &&
                (formValues.generationType === '大V严选' || formValues.generationType === 'KOL转移') && (
                  <ProFormField
                    name="productUrl"
                    label={
                      <div>
                        商品链接
                        <span className="text-normal text-[13px] text-[#F3A93C]">
                          {' (仅支持 TikTok 美区商品详情链接)'}
                        </span>
                      </div>
                    }
                    form={form}
                    renderFormControl={(field) => (
                      <Input
                        value={field.value as string}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                        className={cn(
                          'rounded-[8px] bg-[#CCDDFF1A] font-normal file:border-0 placeholder:font-normal placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A]',
                        )}
                        placeholder="请输入商品链接"
                      />
                    )}
                  />
                )}
              {(!('generationType' in formValues) || formValues.generationType !== '大卖推荐') && (
                <ProFormField label="视频风格" tooltip="根据视频风格类型，匹配提供的对应模板(可多选)">
                  {formValues.method === 'normal' && (
                    <div className="pt-[8px]">
                      {loadingPrompts ? (
                        <div className="text-primary-foreground flex flex-col space-y-3">
                          <div className="space-y-2">
                            <Skeleton className="h-5 w-[120px]" />
                            <Skeleton className="h-10 w-full" />
                          </div>
                        </div>
                      ) : (
                        <div>
                          <ProFormField
                            name="industry"
                            label="行业选择"
                            form={form}
                            subField
                            renderFormControl={(field) => (
                              <ProSelect
                                value={field.value}
                                onValueChange={(v) => {
                                  field.onChange(v);
                                  form.setValue('referenceDuration', ''); // 清空参考时长
                                  form.setValue('prompts', []); // 清空模式选择
                                  // 自动选择参考时长的第一个值
                                  const selectedIndustry = promptTree.find((item) => item.industry === v);
                                  if (selectedIndustry && selectedIndustry.durations.length > 0) {
                                    const firstDuration = selectedIndustry.durations[0]?.duration;
                                    if (firstDuration) {
                                      form.setValue('referenceDuration', firstDuration);
                                    }
                                    // 自动选择模式选择的第一个值
                                    const firstModel = selectedIndustry.durations[0]?.model[0]?.value;
                                    if (firstModel) {
                                      form.setValue('prompts', [firstModel]); // 自动选择第一个模式
                                    }
                                  }
                                }}
                                placeholder={<span className="font-normal text-[#9FA4B2]">请选择行业类目</span>}
                                options={promptTree?.map((v) => ({ value: v.industry, label: v.industry }))}
                                className="bg-[#CCDDFF1A]"
                              />
                            )}
                          />
                          {!!formValues.industry && (
                            <ProFormField
                              name="referenceDuration"
                              label="参考时长"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProToggleGroup
                                  type="single"
                                  className="flex-wrap justify-start"
                                  value={field.value}
                                  onValueChange={(v) => {
                                    field.onChange(v);
                                    form.setValue('prompts', []); // 清空模式选择
                                    // 自动选择模式选择的第一个值
                                    const selectedIndustry = promptTree.find(
                                      (item) => item.industry === formValues.industry,
                                    );
                                    const selectedDuration = selectedIndustry?.durations.find((d) => d.duration === v);
                                    if (
                                      selectedDuration &&
                                      selectedDuration.model.length > 0 &&
                                      selectedDuration.model[0]?.value
                                    ) {
                                      form.setValue('prompts', [selectedDuration.model[0].value]); // 自动选择第一个模式
                                    }
                                  }}
                                  toggles={promptTree
                                    .find((v) => v.industry === formValues.industry)
                                    ?.durations?.map((v) => ({ value: v.duration, label: v.duration }))}
                                  toggleItemProps={{
                                    className: cn(
                                      'border border-transparent bg-[#CCDDFF1A] text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-[#FFF]',
                                      'text-xs data-[state=on]:text-[#FFF] data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#CCDDFF0F]',
                                    ),
                                  }}
                                />
                              )}
                            />
                          )}
                          {!!(formValues as TaskCreatorFormValues<'normal'>).referenceDuration && (
                            <ProFormField
                              name="prompts"
                              label="模式选择（可多选）"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProToggleGroup
                                  type="multiple"
                                  className="flex-wrap justify-start"
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  eclipse={{ lines: 3 }}
                                  toggles={promptTree
                                    .find((v) => v.industry === formValues.industry)
                                    ?.durations?.find(
                                      (v) =>
                                        v.duration ===
                                        (formValues as TaskCreatorFormValues<'normal'>).referenceDuration,
                                    )
                                    ?.model.map((v) => ({ value: v.value, label: v.label }))}
                                  toggleItemProps={{
                                    className: cn(
                                      'border border-transparent bg-[#CCDDFF1A] text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-[#FFF]',
                                      'text-xs data-[state=on]:text-[#FFF] data-[state=on]:border-[#00E1FF] data-[state=on]:bg-[#CCDDFF0F]',
                                    ),
                                  }}
                                />
                              )}
                            />
                          )}
                          {subSystems?.includes('dify') && (
                            <ProFormField
                              name="dify"
                              label="使用Dify"
                              form={form}
                              subField
                              layout="horizontal"
                              renderFormControl={(field) => (
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  thumbClassName="bg-[#FFF]"
                                />
                              )}
                            />
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  {formValues.method === 'gc_imitate' && (
                    <div className="pt-[8px]">
                      {loadingKolStyles ? (
                        <div className="text-primary-foreground flex flex-col space-y-3">
                          <div className="space-y-2">
                            <Skeleton className="h-5 w-[120px]" />
                            <Skeleton className="h-10 w-full" />
                          </div>
                        </div>
                      ) : (
                        <div>
                          {formValues.generationType === '大V严选' && (
                            <ProFormField
                              name="industryId"
                              label="行业选择"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProSelect
                                  value={field.value}
                                  onValueChange={(v) => {
                                    field.onChange(v);
                                    form.setValue('kolStyle', ''); // 清空模式选择
                                    // 自动选择参考时长的第一个值
                                    const kol = kolStyles.find((item) => item.industry === v);
                                    if (kol) {
                                      form.setValue('kolStyle', kol.kol); // 自动选择第一个大v
                                    }
                                  }}
                                  placeholder={<span className="font-normal text-[#9FA4B2]">请选择行业</span>}
                                  options={uniqBy(
                                    kolStyles?.map((v) => ({ value: String(v.industry_id), label: v.industry })),
                                    (s) => s.value,
                                  )}
                                  className="bg-[#CCDDFF1A]"
                                />
                              )}
                            />
                          )}
                          {formValues.generationType === '大V严选' && !!formValues.industryId && (
                            <ProFormField
                              name="kolStyle"
                              label="大V"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProSelect
                                  value={field.value as string}
                                  onValueChange={(v) => field.onChange(v)}
                                  options={kolStyles
                                    .filter((s) => String(s.industry_id) === formValues.industryId)
                                    .map((s) => ({ value: s.kol, label: s.kol }))}
                                  className="bg-[#CCDDFF1A]"
                                />
                              )}
                            />
                          )}
                          {formValues.generationType === '大V严选' && (
                            <ProFormField
                              name="targetLanguage"
                              label="生成视频语言"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProSelect
                                  value={field.value as string}
                                  onValueChange={(v) => field.onChange(v)}
                                  options={TEMPLATE_VIDEO_LANGUAGES.map(({ value, label }) => ({ value, label }))}
                                  className="bg-[#CCDDFF1A]"
                                />
                              )}
                            />
                          )}
                          {formValues.generationType === 'KOL转移' && (
                            <ProFormField
                              name="templateVideoVodId"
                              label="模板视频"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <FilePicker
                                  disabled={templateVideoUploading}
                                  onChange={async (e) => {
                                    const file = e.target.files?.[0];
                                    if (!file) return;
                                    if (file.size > 500 * 1024 * 1024) {
                                      toast.error('文件大小不能超过500MB');
                                      return;
                                    }
                                    if (!checkIsMP4(file)) {
                                      toast.error('文件不是有效的视频文件');
                                      return;
                                    }
                                    if (!checkHasAudioTrack(file)) {
                                      toast.error('文件不包含音频轨道');
                                      return;
                                    }
                                    const duration = await getVideoDuration(file);
                                    if (duration > 5 * 60) {
                                      toast.error('视频时长不能超过5分钟');
                                      return;
                                    }
                                    setTemplateVideoUploading(true);
                                    const res = await uploadToVod({
                                      params: {
                                        mediaFile: file,
                                        enableRaceRegion: true,
                                        enableResume: true,
                                        chunkParallelLimit: 3,
                                        fileParallelLimit: 1,
                                        chunkSize: 5 * 1024 * 1024,
                                        chunkRetryTimes: 8,
                                        commitRequestTimeout: 60 * 1000,
                                        applyRequestTimeout: 60 * 1000,
                                        retryDelay: 3000,
                                      },
                                      onMediaProgress: (info) => {
                                        setTemplateVideoUploadProgress(info.loaded / info.total);
                                      },
                                      onMediaUpload: () => {
                                        setTemplateVideoUploadProgress(0);
                                      },
                                    });
                                    setTemplateVideoUploading(false);
                                    field.onChange(res.fileId);
                                  }}
                                >
                                  <div
                                    className={cn(
                                      'flex w-full cursor-pointer items-center justify-center gap-2 rounded-[8px] bg-[#CCDDFF1A] p-2 font-normal text-[#9FA4B2] file:border-0 placeholder:font-normal placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A]',
                                      templateVideoUploading ? 'cursor-not-allowed' : '',
                                    )}
                                  >
                                    <UploadIcon className="h-5 w-5" />
                                    {templateVideoUploading ? (
                                      <div className="flex items-center justify-center">
                                        <Progress
                                          value={templateVideoUploadProgress * 100}
                                          className={cn('h-1.5 w-[120px] bg-[#1f2434]')}
                                          color="#60D2A7"
                                        />
                                        <span className="ml-2 text-xs font-normal">
                                          {(templateVideoUploadProgress * 100).toFixed()}%
                                        </span>
                                      </div>
                                    ) : field.value ? (
                                      field.value
                                    ) : (
                                      '点击上传模板视频'
                                    )}
                                  </div>
                                </FilePicker>
                              )}
                            />
                          )}
                          {formValues.generationType === 'KOL转移' && (
                            <ProFormField
                              name="templateVideoLanguage"
                              label={
                                <div>
                                  模板视频语言
                                  <span className="text-normal text-[13px] text-[#F3A93C]">
                                    {' (请确保语言与模板视频一致)'}
                                  </span>
                                </div>
                              }
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProSelect
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  placeholder={<span className="font-normal text-[#9FA4B2]">请选择语言</span>}
                                  options={TEMPLATE_VIDEO_LANGUAGES.map(({ value, label }) => ({ value, label }))}
                                  className="bg-[#CCDDFF1A]"
                                />
                              )}
                            />
                          )}
                          {formValues.generationType === 'KOL转移' && (
                            <ProFormField
                              name="targetVoice"
                              label="声音选取"
                              form={form}
                              subField
                              renderFormControl={(field) => (
                                <ProSelect
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  placeholder={<span className="font-normal text-[#9FA4B2]">请选择语言</span>}
                                  options={[
                                    { value: 'en_female', label: '英语女声' },
                                    { value: 'en_male', label: '英语男声' },
                                    { value: 'es_female', label: '西班牙语女声' },
                                    { value: 'es_male', label: '西班牙语男声' },
                                  ]}
                                  className="bg-[#CCDDFF1A]"
                                />
                              )}
                            />
                          )}
                          <ProFormField
                            name="sceneImplantation"
                            label="场景植入"
                            form={form}
                            subField
                            layout="horizontal"
                            renderFormControl={(field) => (
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                thumbClassName="bg-[#FFF]"
                              />
                            )}
                          />
                          <ProFormField
                            name="festiveAtmosphere"
                            label="节日氛围"
                            form={form}
                            subField
                            layout="horizontal"
                            renderFormControl={(field) => (
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                thumbClassName="bg-[#FFF]"
                              />
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </ProFormField>
              )}
              <div className="flex justify-between gap-2">
                <ProFormField
                  name="generateRound"
                  label="生成轮次"
                  tooltip="生成视频数量翻倍值"
                  form={form}
                  className="flex-1"
                  renderFormControl={(field) => (
                    <>
                      <ProSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder={<span className="font-normal text-[#9FA4B2]">请选择生成轮次</span>}
                        options={GENERATE_ROUND_OPTIONS.map((v) => ({ value: v, label: `${v} 轮` }))}
                        className="bg-[#CCDDFF1A]"
                      />
                      {!!expectVideoCount && (
                        <div className="mt-[8px] text-xs font-normal text-[#9FA4B2]">
                          预计生成视频：<span className="text-[#FFF]">{expectVideoCount} 条</span>
                        </div>
                      )}
                    </>
                  )}
                />
                <ProFormField
                  name="speed"
                  label="视频加速"
                  tooltip="生成视频加速倍率"
                  form={form}
                  className="flex-1"
                  renderFormControl={(field) => (
                    <ProSelect
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder={<span className="font-normal text-[#9FA4B2]">请选择视频加速</span>}
                      options={GENERATE_SPEED_OPTIONS.map((v) => ({ value: v, label: `${v}` }))}
                      className="bg-[#CCDDFF1A]"
                    />
                  )}
                />
              </div>
              <ProFormField
                name="subtitle"
                label="生成字幕"
                form={form}
                layout="horizontal"
                renderFormControl={(field) => (
                  <Switch checked={field.value} onCheckedChange={field.onChange} thumbClassName="bg-[#FFF]" />
                )}
              />
              <ProFormField
                name="transition_mode"
                label="叠化效果"
                form={form}
                layout="horizontal"
                renderFormControl={(field) => (
                  <Switch
                    checked={field.value === 'fade'}
                    onCheckedChange={(checked) => field.onChange(checked ? 'fade' : 'null')}
                    thumbClassName="bg-[#FFF]"
                  />
                )}
              />
            </form>
          </Form>
        </div>
      </div>
      <div className="h-full w-full px-4">
        <SubmitButton
          type="submit"
          expectCostQuota={expectCostQuota}
          disabled={!selectedMaterials.length || !formSchema.safeParse(formValues).success}
          onClick={async () => {
            await form.handleSubmit(onSubmit)();
          }}
        />
      </div>
    </Panel>
  );
};

/**
 * 提交按钮
 * @returns
 */
const SubmitButton: React.FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> & { expectCostQuota: number; onClick: () => void }
> = ({ disabled, expectCostQuota, className, onClick }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="xl"
            disabled={disabled}
            onClick={onClick}
            className={cn(
              'w-full rounded-xl',
              !disabled
                ? 'bg-[linear-gradient(90.59deg,_#54FFE0_4.36%,_#00E1FF_22.69%,_#9D81FF_96%)]'
                : 'disabled:opacity-1 disabled:pointer-events-auto disabled:cursor-not-allowed disabled:bg-[#202736]',
              className,
            )}
          >
            <div>
              <div className={cn('mb-[2px] flex h-[20px] gap-2', !disabled ? 'text-[#050A1C]' : 'text-[#81889D99]')}>
                <StarsFullfil />
                <div>立即生成</div>
              </div>
              <div className={cn('text-xs', !disabled ? 'text-[#050A1CCC]' : 'text-[#81889D99]')}>
                消耗 {expectCostQuota || 0} 点数
              </div>
            </div>
          </Button>
        </TooltipTrigger>
        {disabled ? (
          <TooltipContent className="bg-white text-[13px] text-black">
            <p>信息未填写全</p>
          </TooltipContent>
        ) : null}
      </Tooltip>
    </TooltipProvider>
  );
};

export default TaskCreator;
