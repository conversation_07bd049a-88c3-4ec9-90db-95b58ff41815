'use client';

import * as React from 'react';
import { Check, ChevronDown, ChevronUp, XIcon } from 'lucide-react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/ui/Button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/Command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Badge } from '@/components/ui/Badge';
import { Separator } from '@/components/ui/Separator';
import { useDebounce } from '@/hooks/usedebounce';
import tiktokService from '@/services/tiktokService';

interface Topic {
  id: string;
  name: string;
}

interface ApiResponse {
  hashtagInteraction?: {
    searchResult: Record<string, any[]>;
  };
}

interface SearchableSelectProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  advertiser_id: string;
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
  maxCount?: number;
  test?: boolean;
  variant?: 'default' | 'secondary';
  className?: string;
}

export function SearchableSelect({
  value = [],
  onChange,
  advertiser_id,
  placeholder = '请搜索话题标签',
  multiple = false,
  disabled,
  maxCount = 1,
  test = false,
  className,
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');
  const [topics, setTopics] = React.useState<Topic[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [animation] = React.useState(0.5);
  const [idToNameMap, setIdToNameMap] = React.useState<Record<string, string>>({});
  const debouncedSearch = useDebounce(searchValue, 300);
  const handleTogglePopover = () => {
    if (!disabled) {
      setOpen(!open);
    }
  };

  const handleClear = () => {
    onChange?.([]);
  };

  const toggleOption = (topicId: string) => {
    const newValue = value.filter((id) => id !== topicId);
    onChange?.(newValue);
  };

  React.useEffect(() => {
    const fetchTopics = async () => {
      if (!debouncedSearch) {
        setTopics([]);
        return;
      }

      setLoading(true);
      try {
        const res = (await tiktokService.getInterestList({
          advertiser_id,
          sub_targeting_types: 'HASHTAG_INTERACTION',
          search_keywords: debouncedSearch,
        })) as ApiResponse;

        if (!res?.hashtagInteraction) return;

        const searchResult = res.hashtagInteraction.searchResult || {};
        const newTopics = Object.values(searchResult)
          .flat()
          .map((item: any) => ({
            id: item.id,
            name: item.name,
          }))
          .filter((item): item is Topic => Boolean(item.id && item.name));

        const newIdToNameMap = newTopics.reduce(
          (acc, topic) => {
            acc[topic.id] = topic.name;
            return acc;
          },
          {} as Record<string, string>,
        );

        setIdToNameMap((prev) => ({ ...prev, ...newIdToNameMap }));
        setTopics(newTopics);
      } catch (error) {
        console.error('Failed to fetch topics:', error);
        setTopics([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, [debouncedSearch, advertiser_id]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          onClick={handleTogglePopover}
          className={cn(
            'flex h-9 w-full items-center justify-between border border-[rgb(28,42,63)] bg-inherit p-1 hover:bg-inherit [&_svg]:pointer-events-auto',
            className,
          )}
          disabled={disabled}
        >
          {value.length > 0 ? (
            <div className="flex h-9 w-full items-center justify-between">
              <div className="flex flex-nowrap items-center">
                {value.slice(0, maxCount).map((topicId) => (
                  <Badge
                    key={topicId}
                    className={cn(
                      'rounded bg-[#CCDDFF0D] text-[#9FA4B2] hover:bg-transparent',
                      'max-w-[130px] truncate',
                      test ? 'text-xs' : 'text-sm',
                    )}
                    style={{ animationDuration: `${animation}s` }}
                  >
                    <span className="truncate">{idToNameMap[topicId] || topicId}</span>
                    <XIcon
                      className="mx-2 h-4 cursor-pointer text-[#9FA4B2]"
                      onClick={(event) => {
                        event.stopPropagation();
                        toggleOption(topicId);
                      }}
                    />
                  </Badge>
                ))}
                {value.length > maxCount && (
                  <Badge
                    className={cn(
                      'text-foreground border-foreground/1 rounded bg-[#CCDDFF0D] text-[#9FA4B2] hover:bg-transparent',
                      'max-w-[120px] truncate',
                      test ? 'text-xs' : 'text-sm',
                    )}
                    style={{ animationDuration: `${animation}s` }}
                  >
                    {`已选 ${value.length}`}
                  </Badge>
                )}
              </div>
              <div className="flex items-center justify-between">
                {!disabled && (
                  <>
                    <XIcon
                      className="mx-2 h-4 cursor-pointer text-[#9FA4B2]"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleClear();
                      }}
                    />
                    <Separator orientation="vertical" className="flex h-full min-h-6" />
                    {open ? (
                      <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                    ) : (
                      <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                    )}
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="mx-auto flex w-full items-center justify-between">
              <span className={cn('mx-3 text-[#9FA4B2]', test ? 'text-xs' : 'text-sm')}>{placeholder}</span>
              {!disabled &&
                (open ? (
                  <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                ) : (
                  <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                ))}
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput placeholder={placeholder} value={searchValue} onValueChange={setSearchValue} />
          <CommandList>
            {loading && <div className="py-6 text-center text-sm">Loading...</div>}
            {!loading && topics.length === 0 && <CommandEmpty>未找到标签。</CommandEmpty>}
            <CommandGroup className="max-h-[200px] overflow-y-auto">
              {topics.map((topic) => (
                <CommandItem
                  key={topic.id}
                  value={topic.name}
                  onSelect={() => {
                    if (multiple) {
                      const newValue = value.includes(topic.id)
                        ? value.filter((v) => v !== topic.id)
                        : [...value, topic.id];
                      onChange?.(newValue);
                    } else {
                      onChange?.([topic.id]);
                      setOpen(false);
                    }
                  }}
                >
                  <Check className={cn('mr-2 h-4 w-4', value.includes(topic.id) ? 'opacity-100' : 'opacity-0')} />
                  {topic.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
