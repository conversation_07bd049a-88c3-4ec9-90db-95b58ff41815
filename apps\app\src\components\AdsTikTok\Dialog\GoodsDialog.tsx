import React, { useEffect } from 'react';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import { useTableLogic } from '@/hooks/useTableLogic';
import toast from 'react-hot-toast';
import Pagination from '@/components/ui/PaginationAcc';
import { usePagination } from '@/hooks/usePagination';
import { useTKProductList } from '@/hooks/useTKProductList';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui';
import { StoreProductItemType } from '@/types/ads';
export default function GoodsDialog({
  openDialog,
  setOpenDialog,
  bcId,
  storeId,
  handleGoodsSelection,
}: {
  openDialog: boolean;
  setOpenDialog: (open: boolean) => void;
  bcId: string;
  storeId: string;
  handleGoodsSelection: (selectedGoods: StoreProductItemType[]) => void;
}) {
  const { products, isLoading, mutate } = useTKProductList(bcId, storeId);

  const { selectedRows, sortState, sortedData, handleSort, setSelectedRows } = useTableLogic<StoreProductItemType>(
    products?.storeProducts || [],
    'itemGroupId',
  );

  useEffect(() => {
    setSelectedRows([]);
  }, [storeId]);

  const { currentPage, pageSize, handlePaginationChange } = usePagination({
    totalItems: products?.pageInfo?.totalNumber || 0,
    initialPageSize: 10,
    initialPage: 1,
    onPaginationChange: (page, pageSize) => {
      mutate(page, pageSize);
    },
  });
  // 修改单个选择逻辑
  const handleSelect = (record: StoreProductItemType, checked: boolean) => {
    if (checked) {
      if (selectedRows.length >= 20) {
        toast.error('最多只能选择20个商品');
        return;
      }
      setSelectedRows([...selectedRows, record]);
    } else {
      setSelectedRows(selectedRows.filter((row: StoreProductItemType) => row.itemGroupId !== record.itemGroupId));
    }
  };

  const handleSelectAll = (checked: boolean, currentPageData: any[]) => {
    if (checked) {
      // 计算选择后的总数
      const newSelectedRows = [...selectedRows];
      currentPageData.forEach((item: StoreProductItemType) => {
        if (!selectedRows.some((row) => row.itemGroupId === item.itemGroupId)) {
          newSelectedRows.push(item);
        }
      });

      if (newSelectedRows.length > 20) {
        toast.error('最多只能选择20个商品');
        return;
      }
      setSelectedRows(newSelectedRows);
    } else {
      // 仅取消当前页的选择
      const currentPageIds = currentPageData.map((item: StoreProductItemType) => item.itemGroupId);
      setSelectedRows(selectedRows.filter((row: StoreProductItemType) => !currentPageIds.includes(row.itemGroupId)));
    }
  };
  const columns = [
    {
      key: 'checkbox',
      title: (
        <Checkbox
          className="border-[#9FA4B2]"
          checked={
            selectedRows.length === products?.storeProducts?.filter((item) => item.status === 'AVAILABLE').length
          }
          onCheckedChange={(checked: boolean) =>
            handleSelectAll(checked, products?.storeProducts?.filter((item) => item.status === 'AVAILABLE') || [])
          }
        />
      ),
      width: 48,
    },
    {
      key: 'title',
      title: '商品名称',
      width: 500,
    },
    {
      key: 'price',
      title: '价格',
      width: 150,
    },
    {
      key: 'category',
      title: '分类',
      width: 150,
    },
    {
      key: 'status',
      title: '状态',
      width: 100,
    },
  ];

  const renderCell = (key: string, record: StoreProductItemType) => {
    switch (key) {
      case 'checkbox':
        return (
          <Checkbox
            className="border-[#9FA4B2]"
            checked={selectedRows.some((row) => row.itemGroupId === record.itemGroupId)}
            disabled={record.status !== 'AVAILABLE'}
            onCheckedChange={(checked: boolean) => record.status === 'AVAILABLE' && handleSelect(record, checked)}
          />
        );
      case 'title':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  <img src={record.productImageUrl} alt="" className="h-16 w-16 rounded-md" />
                  <div className="ml-2 truncate">
                    <div className="cursor-pointer text-sm">{record.title}</div>
                    <div className="flex text-xs text-[#9FA4B2]">
                      <span className="w-16">SPU 编号：</span>
                      <span>{record.itemGroupId}</span>
                    </div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{record.title}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'price':
        return (
          <div>
            {record.minPrice} ~ {record.maxPrice}
          </div>
        );
      case 'category':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center truncate">{record.category}</div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{record.category}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'status':
        return (
          <div>
            <span>{record.status === 'AVAILABLE' ? '可用' : '不可用'}</span>
          </div>
        );
      default:
        return null;
    }
  };

  const handleConfirm = () => {
    if (selectedRows.length === 0) {
      toast.error('请至少选择一个商品');
      return;
    }
    handleGoodsSelection(selectedRows);
    setOpenDialog(false);
  };

  return (
    <Dialog open={openDialog} onOpenChange={setOpenDialog}>
      <DialogContent className="max-w-[1000px] bg-[#151C29]">
        <DialogHeader className="flex items-center justify-center">
          <DialogTitle className="text-base font-medium">商品列表</DialogTitle>
        </DialogHeader>
        <CommonTable
          columns={columns}
          dataSource={sortedData}
          sortState={sortState}
          rowKey="itemGroupId"
          onSelectAll={handleSelectAll}
          onSort={handleSort}
          renderCell={renderCell}
          loading={isLoading}
          selectedRows={selectedRows.map((row) => row.itemGroupId)}
          onRowSelect={(record: StoreProductItemType, checked: boolean) => {
            if (record.status === 'AVAILABLE') {
              handleSelect(record, checked);
            }
          }}
        />
        <div className="mt-4 flex justify-between">
          <div>{selectedRows.length > 0 ? `已选择 ${selectedRows.length}/20` : null}</div>
          <Pagination
            totalItems={products?.pageInfo?.totalNumber ?? 0}
            currentPage={currentPage}
            pageSize={pageSize}
            handlePaginationChange={handlePaginationChange}
          />
        </div>
        <DialogFooter>
          <Button className="h-8 w-[90px] rounded" variant="outline" onClick={() => setOpenDialog(false)}>
            取消
          </Button>
          <Button className="h-8 w-[90px] rounded" disabled={selectedRows.length === 0} onClick={handleConfirm}>
            添加
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
