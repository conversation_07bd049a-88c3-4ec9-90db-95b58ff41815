import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import * as z from 'zod';

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  Sheet,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  Switch,
} from '@/components/ui';

import { ProMask } from '@/components/pro/pro-mask';
import { FormMultiSelect } from '@/components/ui/FormMultiSelect';
import { Label } from '@/components/ui/Label';
import { useAdvertisers } from '@/hooks/useAdvertisers';
import useAdStore, { useAdActions, useCampaignStatus, useCurrentAdvertiser } from '@/store/ads/adStore';
import { CampaignItem } from '@/types/ads';
import { cn } from '@/utils/cn';
import { useEffect } from 'react';
import { SectionTitle } from './SectionTitle';

const PROMOTION_OBJECTIVES = [
  {
    value: 'APP_PROMOTION',
    title: '应用推广',
    description: '推广您的应用',
    disabled: true,
  },
  {
    value: 'WEBSITE_CONVERSIONS',
    title: '网站转化量',
    description: '增加您的网站转化',
    disabled: true,
  },
  {
    value: 'PRODUCT_SALES',
    title: '商品销量',
    description: '提升您的商品销售',
    disabled: false,
  },
];

const formSchema = z
  .object({
    objectiveType: z.string({
      required_error: '请选择推广目标',
    }),
    campaignProductSource: z.string({
      required_error: '请选择商品数据源',
    }),
    campaignName: z.string().min(1, '请输入系列名称').max(200, '最多输入200个字符'),
    operationStatus: z.string(),
    budgetMode: z.string(),
    budget: z.number(),
    adType: z.string(),
    campaignId: z.string().optional().nullable(),
    id: z.string().optional().nullable(),
    advertiserId: z.string().optional().nullable(),
  })
  .refine((data) => !(data.budgetMode === 'BUDGET_MODE_TOTAL' && data.budget < 150), {
    message: '总预算不得低于150 USD',
    path: ['budget'],
  })
  .refine((data) => data.budget >= 11, {
    message: '日预算不得低于11 USD',
    path: ['budget'],
  });

export type CampaignFormValues = z.infer<typeof formSchema>;

interface CampaignSheetProps {
  onSubmit: (values: CampaignFormValues) => Promise<void>;
}

export function CampaignSheet({ onSubmit }: CampaignSheetProps) {
  const { campaignModal } = useAdStore();
  const { loading: isLoading } = useCampaignStatus();
  const currentAdvertiser = useCurrentAdvertiser();
  const { advertisers } = useAdvertisers(1, 999);
  const { setCampaignModal } = useAdActions();
  const { show: open, type, title, formValue } = campaignModal;
  const isEditMode = type === 'edit';
  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues:
      isEditMode && (formValue as CampaignItem)
        ? {
            objectiveType: (formValue as CampaignItem).jsonDate?.objectiveType || 'PRODUCT_SALES',
            campaignProductSource: (formValue as CampaignItem).jsonDate?.campaignProductSource || 'STORE',
            campaignName: (formValue as CampaignItem).campaignName || '',
            operationStatus: (formValue as CampaignItem)?.operationStatus || 'DISABLE',
            budgetMode: (formValue as CampaignItem).jsonDate?.budgetMode || 'BUDGET_MODE_DAY',
            budget: (formValue as CampaignItem).jsonDate?.budget || 11,
            adType: 'VIDEO',
            campaignId: (formValue as CampaignItem).campaignId,
          }
        : {
            objectiveType: 'PRODUCT_SALES',
            campaignProductSource: 'STORE',
            campaignName: '',
            operationStatus: 'DISABLE',
            budgetMode: 'BUDGET_MODE_DAY',
            budget: 11,
            adType: 'VIDEO',
          },
  });

  useEffect(() => {
    if (open && formValue) {
      form.reset({
        objectiveType: (formValue as CampaignItem).jsonDate?.objectiveType || 'PRODUCT_SALES',
        campaignProductSource: (formValue as CampaignItem).jsonDate?.campaignProductSource || 'STORE',
        campaignName: (formValue as CampaignItem).campaignName || '',
        operationStatus: (formValue as CampaignItem)?.operationStatus || 'DISABLE',
        budgetMode: (formValue as CampaignItem).jsonDate?.budgetMode || 'BUDGET_MODE_DAY',
        budget: (formValue as CampaignItem).jsonDate?.budget || 11,
        adType: 'VIDEO',
        campaignId: (formValue as CampaignItem).campaignId,
      });
    } else {
      form.reset();
    }
  }, [open]);

  const handleSubmit = async (values: CampaignFormValues) => {
    try {
      const newValues = {
        ...(formValue?.campaignId && { campaignId: formValue.campaignId }),
        ...values,
        id: formValue?.id,
        ...(isEditMode && { advertiserId: (formValue as CampaignItem).advertiserId }),
        budget: Number(values.budget),
      };
      await onSubmit(newValues);
      form.reset();
      setCampaignModal({ show: false });
    } catch (error) {
      toast.error('表单提交失败');
    }
  };

  const handleCancel = () => {
    form.reset();
    setCampaignModal({ show: false, formValue: undefined, type: 'create', title: '新建广告系列' });
  };

  return (
    <Sheet
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          handleCancel();
        }
      }}
    >
      <SheetContent className="flex min-w-[800px] flex-col bg-[#151c29]">
        <ProMask loading={isLoading} />

        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto pl-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
              <div>
                <SectionTitle title="已选账户" />

                <div className="pl-10">
                  <FormField
                    control={form.control}
                    name="advertiserId"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center gap-[62px]">
                        <FormLabel className="text-sm text-white">
                          广告账户 <span className="ml-2 text-red-500">*</span>
                        </FormLabel>
                        <FormControl className="w-[360px]">
                          <FormMultiSelect
                            className="mt-0"
                            disabled={true}
                            name="advertiserId"
                            control={form.control}
                            options={
                              advertisers?.list?.map((item) => ({
                                label: item.advertiserName,
                                value: item.advertiserId,
                              })) ?? []
                            }
                            defaultValue={currentAdvertiser?.map((item) => item.advertiserId) || []}
                            placeholder="选择广告账户"
                            placeholderTitle="广告账户"
                            rules={{ required: '请选择广告账户' }}
                            required={true}
                            onValueChange={(value) => {
                              if (!value || value.length === 0) {
                                toast.error('请选择至少一个广告账户');
                                return;
                              } else {
                                form.clearErrors('advertiserId');
                              }
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div>
                <SectionTitle title="推广目标" />
                <div className="pl-10">
                  <FormField
                    control={form.control}
                    name="objectiveType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormControl>
                          <RadioGroup
                            disabled={isEditMode}
                            onValueChange={field.onChange}
                            value={field.value}
                            className="flex flex-col space-y-3"
                          >
                            {PROMOTION_OBJECTIVES.map((obj) => (
                              <div
                                key={obj.value}
                                className={cn(
                                  'flex h-[63px] w-[480px] flex-col items-start gap-2.5 rounded-lg border p-4',
                                  field.value === obj.value
                                    ? 'border-[#00E1FF] bg-[rgba(0,225,255,0.10)]'
                                    : 'border-[#363D54]',
                                  obj.disabled && 'cursor-not-allowed opacity-50',
                                )}
                              >
                                <div className="flex items-center space-x-3">
                                  <RadioGroupItem value={obj.value} disabled={obj.disabled} id={obj.value} />
                                  <Label htmlFor={obj.value} className="font-normal">
                                    <div className="font-medium">{obj.title}</div>
                                    <div className="text-[#9FA4B2]text-sm">{obj.description}</div>
                                  </Label>
                                </div>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <SectionTitle title="推广类型" />

                <div className="pl-10">
                  <FormField
                    control={form.control}
                    name="campaignProductSource"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">
                          商品数据源 <span className="ml-2 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            disabled={isEditMode}
                            onValueChange={field.onChange}
                            value={field.value}
                            className="flex space-x-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="STORE" id="store" />
                              <Label htmlFor="store">TikTok Shop</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="CATALOG" id="catalog" disabled />
                              <Label htmlFor="catalog">Showcase</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <SectionTitle title="广告系列" />

                <div className="pl-10">
                  <FormField
                    control={form.control}
                    name="campaignName"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">
                          广告系列名称 <span className="ml-2 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="请输入广告系列名称"
                            {...field}
                            className="flex h-[40px] w-[360px] flex-shrink-0 items-center gap-3 rounded border border-[#363D54] bg-transparent px-3 py-[7px] text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="operationStatus"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">
                          广告系列状态 <span className="ml-2 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Switch
                            disabled={isEditMode}
                            checked={field.value === 'ENABLE'}
                            onCheckedChange={(checked) => field.onChange(checked ? 'ENABLE' : 'DISABLE')}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="budgetMode"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center">
                        <FormLabel className="w-1/5 text-sm text-white">
                          广告系列预算 <span className="ml-2 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            disabled={isEditMode}
                            onValueChange={field.onChange}
                            value={field.value}
                            className="flex space-x-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="BUDGET_MODE_DAY" id="day" />
                              <Label htmlFor="day">日预算</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem
                                value="BUDGET_MODE_DYNAMIC_DAILY_BUDGET"
                                id="BUDGET_MODE_DYNAMIC_DAILY_BUDGET"
                              />
                              <Label htmlFor="BUDGET_MODE_DYNAMIC_DAILY_BUDGET" className="text-sm">
                                动态日预算
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="BUDGET_MODE_TOTAL" id="total" />
                              <Label htmlFor="total">总预算</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem className="mt-6 flex h-8 items-center pl-[20%]">
                        <FormControl>
                          <div className="relative w-[360px]">
                            <Input
                              disabled={isEditMode}
                              type="text"
                              {...field}
                              onChange={(e) => {
                                // 只允许输入正整数
                                const value = e.target.value.replace(/[^\d]/g, '');
                                if (value) {
                                  field.onChange(Number(value));
                                } else {
                                  field.onChange(0);
                                }
                              }}
                              className="flex h-[40px] flex-shrink-0 items-center gap-3 rounded border border-[#363D54] bg-transparent px-3 py-[7px] text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                            />
                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                              <span className="text-gray-500">USD</span>
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </form>
          </Form>
        </div>

        <SheetFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="h-8 w-[90px] flex-shrink-0 text-sm font-normal text-white"
          >
            取消
          </Button>
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isLoading}
            className="h-8 w-[90px] flex-shrink-0 bg-[#00E1FF] text-center text-sm font-normal text-[#050A1C] hover:bg-[#00E1FF]/90"
          >
            {isLoading ? '提交中...' : '确认'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
