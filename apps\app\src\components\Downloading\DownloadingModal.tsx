'use client';

import React from 'react';
import { Dialog, DialogContent } from '@/components/ui';
import { Progress } from '@/components/ui/Progress';
import { useBatchDownload } from '@/hooks/useBatchDownload';
import { cn } from '@/utils/cn';
import { Download } from '@/components/icon';

const convertBytes = (kb: number) => {
  const GB = 1024 * 1024 * 1024;
  const MB = 1024 * 1024;
  if (kb >= GB) {
    const gbValue = (kb / GB).toFixed(1); // 保留两位小数
    return `${gbValue} GB`;
  } else {
    const mbValue = (kb / MB).toFixed(1); // 保留两位小数
    return `${mbValue} MB`;
  }
};

export function DownloadingModal() {
  const { downloadingList, isDownloading } = useBatchDownload();
  const totalNum = downloadingList.reduce((acc, item) => acc + item.total, 0);
  const totalProgress = downloadingList.reduce((acc, item) => acc + item.progress, 0);
  const averageProgress = downloadingList.length ? totalProgress / downloadingList.length : 0;
  if (!downloadingList?.length) return null;

  return (
    <Dialog open={isDownloading}>
      <DialogContent className="flex w-[400px] flex-col justify-start rounded-2xl border-none bg-[#151c29] [&_.bwai-dialog-close-icon]:hidden">
        <div className="mb-6 mt-2 flex items-center justify-center text-base font-medium">文件下载中...</div>
        <div className="mb-4 ml-4 flex items-center">
          <div className="relative">
            <Download />
            <div className="absolute right-[-6px] top-[-6px] flex h-5 w-5 items-center justify-center rounded-full bg-[#00E1FF] text-xs font-medium text-black">
              {downloadingList.length}
            </div>
          </div>
          <div className="ml-4 mr-4 flex-1">
            <div className="mb-3 text-sm font-medium">{convertBytes(totalNum)}</div>
            <div className="flex items-center">
              <Progress value={averageProgress} className={cn('h-1.5 bg-[#1f2434]')} color="#60D2A7" />
              <span className="ml-2 text-xs font-normal">{averageProgress.toFixed()}%</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
{
  /* <DownloadingList /> */
}
