export const Mp4Fullfil = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="60" height="61" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.45996 8.82521C3.45996 4.40693 7.04168 0.825214 11.46 0.825214H30.496L40.5097 0.8252C42.5528 0.825197 44.5185 1.60692 46.0037 3.01005L48.7201 5.57651L53.9282 10.3211C55.5922 11.837 56.5406 13.9839 56.5406 16.2349V30.3295V52.8252C56.5406 57.2435 52.9589 60.8252 48.5406 60.8252H11.46C7.04167 60.8252 3.45996 57.2435 3.45996 52.8252V8.82521Z"
        fill="url(#paint0_linear_738_829)"
      />
      <path
        d="M40.0854 32.6696L33.6348 30.5546V23.5048L40.0854 21.3546C40.2516 21.2998 40.4283 21.2844 40.6015 21.3097C40.7747 21.3349 40.9396 21.4002 41.0832 21.5002C41.2268 21.6002 41.3452 21.7323 41.429 21.886C41.5127 22.0396 41.5596 22.2107 41.5658 22.3856V31.6033C41.567 31.7819 41.5253 31.9581 41.4444 32.1173C41.3635 32.2766 41.2457 32.4141 41.1008 32.5185C40.9558 32.6228 40.7881 32.691 40.6114 32.7173C40.4348 32.7436 40.2544 32.7272 40.0854 32.6696Z"
        fill="#00E1FF"
      />
      <path
        d="M32.5553 19.0986H22.1171C21.1402 19.0986 20.2033 19.4867 19.5125 20.1775C18.8217 20.8683 18.4336 21.8052 18.4336 22.7822V31.3477C18.4359 32.3052 18.8173 33.2229 19.4944 33.9C20.1715 34.577 21.0891 34.9584 22.0466 34.9608H32.5553C33.5128 34.9584 34.4305 34.577 35.1075 33.9C35.7846 33.2229 36.166 32.3052 36.1683 31.3477V22.7117C36.166 21.7542 35.7846 20.8365 35.1075 20.1594C34.4305 19.4824 33.5128 19.101 32.5553 19.0986ZM30.317 27.3822L25.5231 30.1052C25.4437 30.1522 25.3535 30.1781 25.2613 30.1802C25.169 30.1823 25.0778 30.1606 24.9963 30.1172C24.9149 30.0738 24.846 30.0102 24.7962 29.9325C24.7465 29.8547 24.7176 29.7655 24.7124 29.6734V24.1833C24.7125 24.0895 24.7373 23.9973 24.7841 23.916C24.831 23.8347 24.8983 23.767 24.9794 23.7198C25.0605 23.6726 25.1525 23.6474 25.2463 23.6468C25.3402 23.6462 25.4326 23.6701 25.5143 23.7163L30.3082 26.4393C30.3909 26.4867 30.4598 26.5549 30.5079 26.6372C30.5561 26.7194 30.5819 26.8129 30.5828 26.9082C30.5837 27.0035 30.5597 27.0974 30.513 27.1806C30.4664 27.2638 30.3988 27.3332 30.317 27.3822Z"
        fill="#00E1FF"
      />
      <path
        d="M18.1702 52.4075C18.3902 52.4075 18.5882 52.4845 18.7532 52.6385C18.9072 52.7925 18.9952 52.9905 18.9952 53.2325C18.9952 53.4525 18.9072 53.6505 18.7532 53.8155C18.5882 53.9585 18.3902 54.0355 18.1702 54.0355C17.9282 54.0355 17.7412 53.9585 17.5982 53.8155C17.4332 53.6505 17.3562 53.4525 17.3562 53.2325C17.3562 52.9905 17.4332 52.7925 17.5982 52.6385C17.7412 52.4845 17.9282 52.4075 18.1702 52.4075ZM20.4413 46.1265H21.8603L24.5993 52.4185H24.6323L27.3603 46.1265H28.7793V53.9805H27.5803V48.3705H27.5363L25.1273 53.9805H24.0933L21.6843 48.3705H21.6403V53.9805H20.4413V46.1265ZM30.3241 46.1265H33.5801C35.4501 46.1265 36.3851 46.9185 36.3851 48.5135C36.3851 50.1195 35.4391 50.9225 33.5581 50.9225H31.5231V53.9805H30.3241V46.1265ZM31.5231 47.1495V49.8995H33.4811C34.0751 49.8995 34.5041 49.7895 34.7791 49.5695C35.0431 49.3495 35.1861 48.9975 35.1861 48.5135C35.1861 48.0295 35.0431 47.6775 34.7681 47.4795C34.4931 47.2595 34.0641 47.1495 33.4811 47.1495H31.5231ZM40.8219 46.1265H41.9219V51.3185H43.1099V52.2755H41.9219V53.9805H40.7779V52.2755H36.9499V51.1425L40.8219 46.1265ZM40.7449 47.6445L37.9289 51.3185H40.7779V47.6445H40.7449Z"
        fill="#81889D"
      />
      <path
        d="M54.5629 11.6814L43.7716 1.60237C43.679 1.51588 43.557 1.46777 43.4303 1.46777H42.2656C38.9519 1.46777 36.2656 4.15407 36.2656 7.46778V12.8421C36.2656 16.1559 38.9519 18.8421 42.2656 18.8421H52.4711C54.4741 18.8421 56.0979 17.2184 56.0979 15.2154C56.0979 13.8756 55.542 12.5959 54.5629 11.6814Z"
        fill="#4B5A77"
      />
      <defs>
        <linearGradient
          id="paint0_linear_738_829"
          x1="6.495"
          y1="8.70487"
          x2="49.4097"
          y2="59.8338"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#1F273D" />
          <stop offset="0.575" stopColor="#273048" />
          <stop offset="1" stopColor="#36435E" />
        </linearGradient>
      </defs>
    </svg>
  );
};
