import React from 'react';

export const AudioError = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="56" height="57" viewBox="0 0 56 57" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.6543 21.3917L44.3451 53.1071" stroke="#00E1FF" strokeWidth="5" stroke-linecap="round" />
      <g opacity="0.4" filter="url(#filter0_b_1807_3980)">
        <path
          d="M38.89 41.3745C39.4482 40.071 39.7349 38.6674 39.7326 37.2495C39.7385 34.5063 38.658 31.8723 36.7274 29.9235C36.4517 29.6447 36.2971 29.2684 36.2971 28.8763C36.2971 28.4842 36.4517 28.1079 36.7274 27.8291C36.8628 27.6919 37.0242 27.583 37.2021 27.5087C37.3799 27.4344 37.5708 27.3961 37.7636 27.3961C37.9564 27.3961 38.1472 27.4344 38.3251 27.5087C38.503 27.583 38.6643 27.6919 38.7998 27.8291C41.2837 30.3344 42.6743 33.7215 42.6674 37.2495C42.6704 39.4434 42.1332 41.6043 41.1032 43.5415L38.89 41.3745ZM44.3548 46.7271C46.1331 43.8862 47.0733 40.601 47.0674 37.2495C47.0769 32.545 45.2227 28.0283 41.9106 24.6875C41.6367 24.4086 41.4833 24.0333 41.4833 23.6425C41.4833 23.2516 41.6367 22.8764 41.9106 22.5975C42.0463 22.4599 42.2079 22.3507 42.3862 22.2762C42.5645 22.2016 42.7558 22.1632 42.949 22.1632C43.1422 22.1632 43.3335 22.2016 43.5117 22.2762C43.69 22.3507 43.8517 22.4599 43.9874 22.5975C47.8493 26.4949 50.011 31.7628 50 37.2495C50 41.5395 48.7086 45.5215 46.502 48.8258L44.3548 46.7271ZM21.7322 24.5841L27.3928 18.8707C29.2408 17.0095 32.4 18.3295 32.4 20.9651V35.0275L21.73 24.5841H21.7322ZM32.4 47.9656V53.5316C32.4 56.1694 29.2386 57.4916 27.3906 55.626L20.74 48.9138C20.3333 48.5019 19.849 48.1746 19.315 47.9511C18.7811 47.7275 18.2081 47.6121 17.6292 47.6114H8.93262C8.15115 47.6074 7.40326 47.2932 6.85336 46.7379C6.30345 46.1826 5.99652 45.4317 6.00003 44.6503V29.8487C5.99652 29.0672 6.30345 28.3163 6.85336 27.761C7.40326 27.2058 8.15115 26.8916 8.93262 26.8875H10.6596L32.4 47.9678V47.9656Z"
          fill="#6F7793"
        />
      </g>
      <defs>
        <filter
          id="filter0_b_1807_3980"
          x="2"
          y="14"
          width="52"
          height="46.4991"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1807_3980" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1807_3980" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};
