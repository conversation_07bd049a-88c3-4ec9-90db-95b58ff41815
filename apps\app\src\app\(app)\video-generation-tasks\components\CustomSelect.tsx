import { ArrowFullfil } from '@/components/icon/ArrowFullfil';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { cn } from '@/utils/cn';
import { ReactNode } from 'react';

interface Option {
  value: string;
  label: string;
  [key: string]: any;
}

interface CustomSelectProps {
  value: string;
  options: Option[];
  placeholder?: string;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onValueChange: (value: string) => void;
}

const CustomSelect = ({
  value,
  options,
  placeholder,
  className,
  open,
  onOpenChange,
  onValueChange,
}: CustomSelectProps) => {
  return (
    <div className={className}>
      <Select value={value} onOpenChange={onOpenChange} onValueChange={onValueChange}>
        <SelectTrigger
          className="h-9 w-[180px] rounded-md bg-[#CCDDFF] bg-opacity-10 font-normal outline-none hover:bg-[#CCDDFF33] focus:ring-offset-0"
          arrow={<ArrowFullfil className={cn('text-[#7E8495]', open ? 'rotate-180' : '')} />}
        >
          <SelectValue placeholder={placeholder} className="font-normal" />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default CustomSelect;
