import { ChangeEvent, useRef, useState } from 'react';
import { Button } from './ui/Button';
import { ExcelManager } from '@/utils/excelManager';
import { toast } from 'react-hot-toast';

interface ExcelButtonProps {
  className?: string;
  title?: string;
  mapper?: Record<string, string>;
  type?: 'generate' | 'resolve';
  generateData?: Record<string, any>[] | (() => Promise<Record<string, any>[]>);
  generateTitle?: string;
  onDataParsed?: (data: any[]) => Promise<void>;
  onGenerate?: (data: any[]) => Promise<void>;
  valueTransformer?: Record<string, (value: any) => any>;
}

export const ExcelButton = ({
  className,
  title,
  mapper,
  generateData,
  generateTitle,
  onDataParsed,
  onGenerate,
  type = 'resolve',
  valueTransformer = {},
}: ExcelButtonProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const generateExcelFromMapper = async (excelTitle: string) => {
    const excelManager = ExcelManager.getInstance();

    if (mapper) {
      excelManager.setHeaderMapper(mapper);
    }

    try {
      setIsLoading(true);
      const finalData = typeof generateData === 'function' ? await generateData() : generateData;

      if (finalData && finalData.length > 0) {
        const transformedData = finalData.map((item) => {
          const transformed = { ...item };
          Object.entries(valueTransformer).forEach(([key, transformer]) => {
            if (key in transformed) {
              transformed[key] = transformer(transformed[key]);
            }
          });
          return transformed;
        });

        excelManager.generateAndDownloadExcel(transformedData, excelTitle);
        await onGenerate?.(transformedData);
      } else {
        toast.error('没有数据');
      }
    } catch (error) {
      toast.error('获取数据失败');
      console.error('获取数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClick = () => {
    if (type === 'resolve') {
      fileInputRef.current?.click();
    } else {
      generateExcelFromMapper(generateTitle ?? 'test');
    }
  };

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const buffer = await file.arrayBuffer();
    const excelManager = ExcelManager.getInstance();
    if (mapper) {
      excelManager.setHeaderMapper(mapper);
    }

    try {
      const data = excelManager.parseExcel(Buffer.from(buffer));
      const transformedData = data.map((item) => {
        const transformed = { ...item };
        Object.entries(valueTransformer).forEach(([key, transformer]) => {
          if (key in transformed) {
            transformed[key] = transformer(transformed[key]);
          }
        });
        return transformed;
      });

      await onDataParsed?.(transformedData);
    } catch (error) {
      console.error('Excel解析错误:', error);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <Button className={className} onClick={handleClick} disabled={isLoading}>
        {isLoading ? '加载中...' : title || '选择Excel文件'}
      </Button>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".xlsx,.xls"
        style={{ display: 'none' }}
      />
    </>
  );
};
