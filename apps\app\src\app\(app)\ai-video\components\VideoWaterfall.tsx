import { cn } from '@/utils/cn';
import Image from 'next/image';
import type React from 'react';
import { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import type { VideoItem } from '../types';
import { useHoverDisplay } from '@/hooks/useHoverDisplay';
import { getVideoTypeDescription } from '../data';
import { Waterfall, getBoxSizeByMedia } from '@/components/Waterfall';
import { TemplateVideoCard } from './TemplateVideoCard';

interface VideoWaterfallProps {
  videos: VideoItem[];
  onVideoClick: (video: VideoItem) => void;
  isLoading?: boolean;
  onApply?: (video: VideoItem) => void;
}

// 定义16:9视频类型
const LANDSCAPE_VIDEO_TYPES = ['vlog', 'vlog_storm', 'chop'];

// 获取视频宽高比
const getVideoAspectRatio = (videoType: string): number => {
  return LANDSCAPE_VIDEO_TYPES.includes(videoType) ? 9 / 16 : 16 / 9; // 高/宽
};

// 瀑布流卡片包装器组件，用于适配 TemplateVideoCard
const VideoCardWrapper: React.FC<{
  data: VideoItem;
  width: number;
  height: number;
  onClick: (video: VideoItem) => void;
  onApply?: (video: VideoItem) => void;
}> = ({ data: video, width, height, onClick, onApply }) => {
  const handleClick = () => {
    onClick(video);
  };

  const handleApply = onApply
    ? (video: VideoItem) => {
        onApply(video);
      }
    : undefined;

  // 计算当前视频的宽高比
  const aspectRatio = getVideoAspectRatio(video.type);

  return (
    <div style={{ width, height }}>
      <TemplateVideoCard video={video} onClick={handleClick} onApply={handleApply} aspectRatio={aspectRatio} />
    </div>
  );
};

export const VideoWaterfall: React.FC<VideoWaterfallProps> = ({ videos, onVideoClick, isLoading = false, onApply }) => {
  const [containerWidth, setContainerWidth] = useState(760); // 默认宽度

  // 监听容器宽度变化
  useEffect(() => {
    const updateWidth = () => {
      const container = document.getElementById('scroll-container');
      if (container) {
        setContainerWidth(container.clientWidth);
      }
    };

    // 初始化时获取宽度
    updateWidth();

    // 监听窗口大小变化
    window.addEventListener('resize', updateWidth);

    // 使用 MutationObserver 监听容器大小变化（处理布局变化）
    const container = document.getElementById('scroll-container');
    if (container) {
      const resizeObserver = new ResizeObserver(() => {
        updateWidth();
      });
      resizeObserver.observe(container);

      return () => {
        window.removeEventListener('resize', updateWidth);
        resizeObserver.disconnect();
      };
    }

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  // 计算动态尺寸和列数
  const { boxWidth, boxHeight, column } = useMemo(() => {
    const boxSizes = getBoxSizeByMedia(
      'VideoBox',
      containerWidth,
      16, // itemPadding - 列之间的间距
      0, // containerPadding - 已经在 containerWidth 计算中处理了
      (width) => {
        // 自定义宽高比计算函数
        // 这里返回一个基础比例，具体每个视频的高度会在 waterfallData 中根据视频类型单独计算
        return 9 / 16; // 使用一个基础比例用于列数计算
      },
    );

    return {
      boxWidth: boxSizes.width,
      boxHeight: boxSizes.height,
      column: boxSizes.column,
    };
  }, [containerWidth]);

  // 准备瀑布流数据
  const waterfallData = useMemo(() => {
    return videos.map((video) => {
      // 根据视频类型获取宽高比
      const aspectRatio = getVideoAspectRatio(video.type);
      // 计算视频区域高度
      const videoHeight = boxWidth * aspectRatio;
      // 总高度 = 视频高度 + 底部信息区域高度 (约112px)
      const cardHeight = videoHeight + 112;

      return {
        w: boxWidth,
        h: cardHeight,
        data: video,
      };
    });
  }, [videos, boxWidth]);

  // 处理数据获取（这里可以用于加载更多数据）
  const handleGetData = useCallback(() => {
    // 在实际项目中，这里可以触发加载更多数据的逻辑
    // console.log('Load more data...');
  }, []);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-gray-900 dark:border-gray-100"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <svg
            className="mx-auto mb-4 h-16 w-16 text-gray-300 dark:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
          <p className="text-lg">暂无视频内容</p>
          <p className="mt-1 text-sm">生成新的视频开始探索</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <Waterfall
        containerId="scroll-container"
        list={waterfallData}
        getData={handleGetData}
        component={VideoCardWrapper}
        componentProps={{ onClick: onVideoClick, onApply }}
        itemPadding={16}
        itemPaddingY={16}
        maxH={800}
        topHeight={16}
        column={column}
        className="h-full w-full"
      />
    </div>
  );
};
