import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { cn } from '@/utils/cn';

interface FormMultiSelectProps {
  name: string;
  control: any;
  label?: string;
  required?: boolean;
  options: { label: string; value: string }[];
  defaultValue?: string[];
  placeholder?: string;
  placeholderTitle?: string;
  className?: string;
  disabled?: boolean;
  maxCount?: number;
  onValueChange?: (value: string[]) => void;
  rules?: Record<string, any>;
}

export function FormMultiSelect({
  name,
  control,
  label,
  required,
  options,
  defaultValue,
  placeholder,
  placeholderTitle,
  className,
  disabled = false,
  maxCount = 1,
  onValueChange,
  rules,
}: FormMultiSelectProps) {
  return (
    <FormField
      control={control}
      name={name}
      rules={rules}
      render={({ field }) => {
        const currentValue = field.value || defaultValue || [];
        return (
          <FormItem className={cn('mt-6 flex items-center', className)}>
            {label && (
              <FormLabel className="w-1/5 text-sm text-white">
                {label}
                {(rules?.required || required) && <span className="ml-2 text-red-500">*</span>}
              </FormLabel>
            )}
            <FormControl className="w-4/5">
              <MultiSelect
                options={options}
                onValueChange={(value) => {
                  console.log(value);
                  field.onChange(value);
                  onValueChange?.(value);
                }}
                value={currentValue}
                defaultValue={defaultValue}
                placeholder={placeholder}
                placeholderTitle={placeholderTitle}
                variant="inverted"
                animation={2}
                test={false}
                maxCount={maxCount}
                className="h-10 w-full"
                disabled={disabled}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
