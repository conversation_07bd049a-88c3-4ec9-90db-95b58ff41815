import { type Action } from '@/types/enum';
import moment from 'moment';
import { UseFormReturn } from 'react-hook-form';
// 构建互动行为
export const buildActions = (values: any): Action[] => {
  const actions = [];

  // 添加视频互动
  if (values.action_category_ids1?.length) {
    actions.push({
      action_scene: 'VIDEO_RELATED',
      action_period: values.A_few_days_of_behavior || 15,
      video_user_actions: values.video_user_actions || [],
      action_category_ids: values.action_category_ids1,
    });
  }

  // 添加创作者互动
  if (values.action_category_ids2?.length) {
    actions.push({
      action_scene: 'CREATOR_RELATED',
      action_period: 0,
      video_user_actions: values.creator_user_actions || [],
      action_category_ids: values.action_category_ids2,
    });
  }

  // 添加话题互动
  if (values.action_category_ids3?.length) {
    actions.push({
      action_scene: 'HASHTAG_RELATED',
      action_period: 0,
      video_user_actions: values.hashtag_user_actions || [],
      action_category_ids: values.action_category_ids3,
    });
  }

  // 确保话题互动是唯一的
  return actions.reduce<Array<Action>>((acc, curr) => {
    const filteredAcc =
      curr.action_scene === 'HASHTAG_RELATED' ? acc.filter((a) => a.action_scene !== 'HASHTAG_RELATED') : acc;
    return [...filteredAcc, curr];
  }, []);
};

// 验证表单数据
export const validateFormData = (values: any, form: any, displayOptimizationGoal: string): boolean => {
  if (!values.campaign_id?.length) {
    form.setError('campaign_id', {
      type: 'manual',
      message: '请选择至少一个广告系列',
    });
    return false;
  }
  const budget = values.budget || 0;
  if (displayOptimizationGoal !== 'VALUE') {
    if (values.billing_event === 'CPC' && (!values.bid_price || values.bid_price <= 0)) {
      form.setError('bid_price', {
        type: 'manual',
        message: '请输入有效的出价金额',
      });
      return false;
    }
    if (values.billing_event === 'OCPM' && (!values.conversion_bid_price || values.conversion_bid_price <= 0)) {
      form.setError('conversion_bid_price', {
        type: 'manual',
        message: '请输入有效的转化出价',
      });
      return false;
    }
    if (values.billing_event === 'CPC' && values.bid_price >= budget) {
      form.setError('bid_price', {
        type: 'manual',
        message: '出价不能大于等于预算金额',
      });
      return false;
    }
    if (values.billing_event === 'OCPM' && values.conversion_bid_price >= budget) {
      form.setError('conversion_bid_price', {
        type: 'manual',
        message: '转化出价不能大于等于预算金额',
      });
      return false;
    }
  }

  return true;
};

// 准备创建广告组的数据
export const prepareCreateData = (
  values: any,
  displayOptimizationGoal: string,
  adgroupNameTags: string[],
  replacePlaceholders: (name: string, campaignId: string) => string,
): any => {
  const campaignId = values.campaign_id[0];
  return {
    ...values,
    is_template: false,
    adgroup_name: replacePlaceholders(values.adgroup_name || '', campaignId),
    schedule_start_time: values.schedule_start_time
      ? moment(values.schedule_start_time).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    schedule_end_time: values.schedule_end_time
      ? moment(values.schedule_end_time).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    bid_type: displayOptimizationGoal === 'VALUE' ? 'BID_TYPE_NO_BID' : 'BID_TYPE_CUSTOM',
    deep_bid_type: displayOptimizationGoal === 'VALUE' ? 'VO_HIGHEST_VALUE' : undefined,
    tags: adgroupNameTags,
    frequency: 1,
    frequency_schedule: 2,
    promotion_type: 'VIDEO_SHOPPING',
    product_source: 'STORE',
    placement_type: 'PLACEMENT_TYPE_NORMAL',
  };
};

// 清理不需要的字段
export const cleanupFormData = (values: any): void => {
  const fieldsToDelete = [
    'action_category_ids1',
    'action_category_ids2',
    'action_category_ids3',
    'video_user_actions',
    'creator_user_actions',
    'hashtag_user_actions',
    'A_few_days_of_behavior',
  ];

  fieldsToDelete.forEach((field) => delete values[field]);
};

// 新增处理表单初始值的函数
export const getInitialFormValues = (editingGroup: any) => {
  if (!editingGroup?.jsonDate?.action) return null;

  const { action } = editingGroup.jsonDate;

  return {
    adgroup_name: action.adgroupName,
    operation_status: action.operationStatus,
    store_id: action.storeId,
    shopping_ads_type: action.shoppingAdsType || 'VIDEO',
    placements: action.placements,
    comment_disabled: action.commentDisabled,
    video_download_disabled: action.videoDownloadDisabled,
    budget_mode: action.budgetMode,
    budget: action.budget,
    schedule_type: action.scheduleType,
    dayparting: action.dayparting,
    optimization_goal: action.optimizationGoal,
    bid_type: action.bidType,
    bid_price: action.bidPrice,
    conversion_bid_price: action.conversionBidPrice,
    billing_event: action.billingEvent,
    pacing: action.pacing,
    location_ids: action.locationIds,
    languages: action.languages,
    gender: action.gender,
    age_groups: action.ageGroups,
    audience_ids: action.audienceIds,
    interest_category_ids: action.interestCategoryIds,
    network_types: action.networkTypes,
  };
};

// 处理标签相关的函数

export const handleTagOperations = (
  type: string,
  form: UseFormReturn<any>,
  adgroupNameTags: string[],
  setAdgroupNameTags: (tags: string[]) => void,
) => {
  let newTag = '';
  switch (type) {
    case 'date':
      newTag = moment().format('YYYY-MM-DD');
      break;
    case 'campaign':
      newTag = '系列名称';
      break;
    case 'store':
      newTag = '商品数据源';
      break;
    case 'optimization':
      newTag = '优化目标';
      break;
    case 'bid':
      newTag = '是否出价';
      break;
  }

  if (newTag) {
    const currentTags = [...adgroupNameTags];

    if (currentTags.includes(newTag)) {
      // 移除已存在的标签
      const tagIndex = currentTags.indexOf(newTag);
      currentTags.splice(tagIndex, 1);

      // 从广告组名称中移除标签
      const currentName = form.getValues('adgroup_name') || '';
      const nameWithoutTag = currentName.replace(`_${newTag}`, '').replace(newTag, '');
      form.setValue('adgroup_name', nameWithoutTag.replace(/^_+|_+$/g, ''));
    } else {
      // 添加新标签
      currentTags.push(newTag);

      // 更新广告组名称
      const nameBase = form.getValues('adgroup_name') || '';
      const newName = nameBase ? `${nameBase}_${newTag}` : newTag;
      form.setValue('adgroup_name', newName);
    }

    setAdgroupNameTags(currentTags);
  }
};

export const handleCustomTag = (
  customTagInput: string,
  form: UseFormReturn<any>,
  adgroupNameTags: string[],
  setAdgroupNameTags: (tags: string[]) => void,
) => {
  if (!customTagInput.trim()) {
    return;
  }

  const currentTags = [...adgroupNameTags];
  if (!currentTags.includes(customTagInput)) {
    currentTags.push(customTagInput);
    setAdgroupNameTags(currentTags);

    // 更新广告组名称
    const currentName = form.getValues('adgroup_name') || '';
    const newName = currentName ? `${currentName}_${customTagInput}` : customTagInput;
    form.setValue('adgroup_name', newName);
  }
};
