import axios, { AxiosInstance } from 'axios';
import chalk from 'chalk';
import { ActionContextPluginLoader } from '../../types';

const requestPlugin: ActionContextPluginLoader = (context) => {
  const request = axios.create();

  request.interceptors.request.use(async (config) => {
    const hash = Math.random().toString(36).substring(2);
    (config as any).__hash = hash;
    (config as any).__requestTime = Date.now();
    context.logger.log(chalk.yellow('[Request]'), hash, config.url, JSON.stringify(config.data));
    return config;
  });

  request.interceptors.response.use(async (response) => {
    const hash = (response.config as any).__hash;
    const requestTime = (response.config as any).__requestTime;
    const cost = Date.now() - requestTime;
    context.logger.log(chalk.yellow('[Response]'), hash, response.status, cc(cost), JSON.stringify(response.data));
    return response;
  });

  request.interceptors.response.use(undefined, async (error) => {
    const hash = (error.config as any).__hash;
    const requestTime = (error.config as any).__requestTime;
    const cost = Date.now() - requestTime;
    context.logger.error(
      chalk.yellow('[Response]'),
      hash,
      error.response?.status,
      cc(cost),
      error.config.url,
      error.message,
    );
    return Promise.reject(new Error());
  });

  const cc = (cost: number) => (cost < 500 ? chalk.green(`${cost}ms`) : chalk.red(`${cost}ms`));

  return {
    name: 'request',
    plugin: request,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * Web请求操作API
     */
    request: AxiosInstance;
  }
}

export default requestPlugin;
