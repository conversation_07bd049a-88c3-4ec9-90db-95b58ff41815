export const Look3 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <g clipPath="url(#clip0_5119_686)">
        <path
          d="M20.5625 15.5C21.3084 15.5 22.0238 15.7963 22.5512 16.3238C23.0787 16.8512 23.375 17.5666 23.375 18.3125V18.9375C23.376 19.6376 23.1149 20.3127 22.6431 20.83C22.724 20.8616 22.7969 20.9104 22.8569 20.9731L23.5263 21.6425C23.6374 21.7608 23.6981 21.9177 23.6956 22.08C23.6931 22.2424 23.6275 22.3973 23.5127 22.5121C23.398 22.6269 23.2431 22.6926 23.0808 22.6953C22.9185 22.6979 22.7615 22.6373 22.6431 22.5263L21.9731 21.8569C21.8668 21.7541 21.8004 21.6171 21.7856 21.47C21.4045 21.655 20.9862 21.7507 20.5625 21.75H19.9375C19.1916 21.75 18.4762 21.4537 17.9488 20.9262C17.4213 20.3988 17.125 19.6834 17.125 18.9375V18.3125C17.125 17.5666 17.4213 16.8512 17.9488 16.3238C18.4762 15.7963 19.1916 15.5 19.9375 15.5H20.5625ZM20.875 8C21.3533 7.99997 21.8134 8.1827 22.1614 8.5108C22.5094 8.8389 22.7188 9.28757 22.7469 9.765L22.75 9.875V13.625C22.7498 13.7843 22.6888 13.9375 22.5795 14.0534C22.4701 14.1692 22.3207 14.2389 22.1616 14.2482C22.0026 14.2576 21.846 14.2058 21.7239 14.1036C21.6017 14.0013 21.5232 13.8563 21.5044 13.6981L21.5 13.625V9.875C21.5 9.72192 21.4438 9.57416 21.3421 9.45977C21.2403 9.34537 21.1002 9.27229 20.9481 9.25438L20.875 9.25H10.875C10.7219 9.25002 10.5742 9.30622 10.4598 9.40795C10.3454 9.50967 10.2723 9.64984 10.2544 9.80187L10.25 9.875V20.5C10.25 20.6531 10.3062 20.8008 10.4079 20.9152C10.5097 21.0296 10.6498 21.1027 10.8019 21.1206L10.875 21.125H15.25C15.4093 21.1252 15.5625 21.1862 15.6784 21.2955C15.7942 21.4049 15.8639 21.5543 15.8732 21.7134C15.8826 21.8724 15.8308 22.029 15.7286 22.1511C15.6263 22.2733 15.4813 22.3518 15.3231 22.3706L15.25 22.375H10.875C10.3967 22.375 9.93655 22.1923 9.58859 21.8642C9.24062 21.5361 9.03118 21.0874 9.00312 20.61L9 20.5V9.875C8.99997 9.39674 9.1827 8.93655 9.5108 8.58859C9.8389 8.24062 10.2876 8.03118 10.765 8.00312L10.875 8H20.875ZM20.5625 16.75H19.9375C19.5231 16.75 19.1257 16.9146 18.8326 17.2076C18.5396 17.5007 18.375 17.8981 18.375 18.3125V18.9375C18.375 19.3519 18.5396 19.7493 18.8326 20.0424C19.1257 20.3354 19.5231 20.5 19.9375 20.5H20.5625C20.9769 20.5 21.3743 20.3354 21.6674 20.0424C21.9604 19.7493 22.125 19.3519 22.125 18.9375V18.3125C22.125 17.8981 21.9604 17.5007 21.6674 17.2076C21.3743 16.9146 20.9769 16.75 20.5625 16.75ZM15.875 15.5C16.0407 15.5 16.1997 15.5659 16.3169 15.6831C16.4341 15.8003 16.4999 15.9593 16.4999 16.125C16.4999 16.2907 16.4341 16.4497 16.3169 16.5669C16.1997 16.6841 16.0407 16.75 15.875 16.75H12.75C12.5843 16.75 12.4253 16.6841 12.3081 16.5669C12.1909 16.4497 12.1251 16.2907 12.1251 16.125C12.1251 15.9593 12.1909 15.8003 12.3081 15.6831C12.4253 15.5659 12.5843 15.5 12.75 15.5H15.875ZM19 12.375C19.1657 12.375 19.3247 12.4409 19.4419 12.5581C19.5591 12.6753 19.6249 12.8343 19.6249 13C19.6249 13.1657 19.5591 13.3247 19.4419 13.4419C19.3247 13.5591 19.1657 13.625 19 13.625H12.75C12.6679 13.625 12.5866 13.6089 12.5108 13.5775C12.435 13.546 12.366 13.5 12.308 13.442C12.2499 13.3839 12.2039 13.315 12.1725 13.2392C12.1411 13.1634 12.1249 13.0821 12.1249 13C12.1249 12.9179 12.1411 12.8366 12.1725 12.7608C12.2039 12.685 12.2499 12.6161 12.308 12.558C12.366 12.5 12.435 12.454 12.5108 12.4225C12.5866 12.3911 12.6679 12.375 12.75 12.375H19Z"
          fill="#9FA4B2"
        />
      </g>
      <defs>
        <clipPath id="clip0_5119_686">
          <rect width="15" height="15" fill="white" transform="translate(9 8)" />
        </clipPath>
      </defs>
    </svg>
  );
};
