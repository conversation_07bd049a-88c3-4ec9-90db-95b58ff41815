'use client';

import Avatar from '@/components/ButtonAvatar';
import { Logo, MenuItemIcon } from '@/components/icon/index';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui';
import { cn } from '@/utils/cn';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Highlighter, { HighlighterItem } from '@/components/ui/Highlighter';
import { CreativeCenter } from '@/components/icon/CreativeCenter';
import { VideoGeneration } from '@/components/icon/VideoGeneration';
import { CloneViral, KocClone, AIClip, House } from '@/components/icon';

import { getTenantEnabledSubSystems } from '@/services/actions/tenant';
import { useAction } from '@/utils/server-action/action';
import { useMemo } from 'react';

// 定义菜单项类型
type MenuItem = {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  permission?: string;
};

// 折叠状态的菜单项组件
const CollapsedMenuItem = ({ item, isActive }: { item: MenuItem; isActive: boolean }) => (
  <div className="relative">
    <SidebarMenuItem className="hover:text-white">
      <SidebarMenuButton
        variant="glass"
        isActive={isActive}
        tooltip={item.title}
        className="!mx-auto h-10 w-full !p-0 hover:text-white data-[active=true]:bg-[rgba(204,221,255,0.05)]"
      >
        <item.icon className={cn('h-8 w-8 shrink-0', isActive && 'text-white')} />
      </SidebarMenuButton>
    </SidebarMenuItem>
    {isActive && (
      <div className="border-image-clip-path absolute left-[10px] top-0 h-10 w-[34px] rounded-md border"></div>
    )}
  </div>
);

// 展开状态的菜单项组件
const ExpandedMenuItem = ({ item, isActive }: { item: MenuItem; isActive: boolean }) => {
  return (
    <div className="relative">
      <HighlighterItem
        key={item.href}
        className="group/highlighter-item relative w-full items-center rounded-lg hover:bg-[#CCDDFF1A]"
      >
        <SidebarMenuItem className="w-full rounded-lg hover:bg-[#CCDDFF1A] hover:text-white">
          <SidebarMenuButton
            variant="glass"
            isActive={isActive}
            className={cn(
              '!mx-auto h-10 w-full !p-0 group-hover/highlighter-item:text-white data-[active=true]:bg-[#CCDDFF1A]',
            )}
          >
            <item.icon
              className={cn(
                'ml-3 mr-1 h-8 w-8 shrink-0 group-hover/highlighter-item:text-white',
                isActive && 'text-white',
              )}
            />
            <span className="text-sm group-hover/highlighter-item:text-white">{item.title}</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </HighlighterItem>
      {isActive && <div className="border-image-clip-path absolute left-0 top-0 h-10 w-full rounded-lg border"></div>}
      {isActive && (
        <span className="absolute -left-2 -top-2">
          <MenuItemIcon />
        </span>
      )}
    </div>
  );
};

const configMenuItems: MenuItem[] = [
  {
    title: '爆款视频',
    icon: VideoGeneration,
    href: '/ai-video',
  },
  // {
  //   title: '首页',
  //   icon: CreativeCenter,
  //   href: '/home',
  // },
  {
    title: 'AI搜索爆款克隆',
    icon: CloneViral,
    href: '/viral',
  },
  {
    title: '自定义视频裂变',
    icon: KocClone,
    href: '/tools/koc',
  },
 
  {
    title: 'AI视频精剪',
    icon: AIClip,
    href: '/originality',
  },
  // {
  //   title: '智能创作中心',
  //   icon: CreativeCenter,
  //   href: '/dashboard',
  // },
  // {
  //   title: '广告投放管理',
  //   icon: ADS,
  //   href: '/ads',
  //   permission: 'ADS',
  // },
  {
    title: '历史精剪',
    icon: VideoGeneration,
    href: '/video-generation-tasks',
  },
];

const configToolsItems: any[] = [
  // {
  //   title: '大V视频克隆',
  //   icon: CloneViral,
  //   href: '/tools/bigv',
  //   permission: 'bigv',
  // },
];

const MenuSideBar = () => {
  const pathname = usePathname();
  const { state } = useSidebar();
  const collapsed = state === 'collapsed';

  const { data: subSystems } = useAction(getTenantEnabledSubSystems, {});

  const menuItems = useMemo(() => {
    return configMenuItems.filter((item) => !item.permission || subSystems?.includes(item.permission));
  }, [subSystems]);

  const toolsItems = useMemo(() => {
    return configToolsItems.filter((item) => !item.permission || subSystems?.includes(item.permission));
  }, [subSystems]);

  const renderMenuItem = (item: MenuItem) => {
    const isActive = pathname === item.href;
    return (
      <Link href={item.href} prefetch key={item.href}>
        {collapsed ? (
          <CollapsedMenuItem item={item} isActive={isActive} />
        ) : (
          <ExpandedMenuItem item={item} isActive={isActive} />
        )}
      </Link>
    );
  };

  return (
    <Sidebar collapsible="icon" className={cn('px-4 py-6', collapsed && 'px-0')}>
      <SidebarHeader className={cn('mb-4 p-0')}>
        <div className={cn('flex items-center', !collapsed ? 'justify-between' : 'justify-center')}>
          <Link href="/ai-video" className={cn(collapsed && 'hidden')} prefetch>
            <Logo />
          </Link>
          <SidebarTrigger className="h-8 w-8" />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className={cn(collapsed && 'gap-2')}>
          <SidebarGroupContent>
            <SidebarMenu>
              <Highlighter className={cn(collapsed ? 'space-y-5' : 'space-y-4', 'flex flex-col')}>
                {menuItems.map(renderMenuItem)}
              </Highlighter>
            </SidebarMenu>
          </SidebarGroupContent>
          {/* <SidebarGroupLabel className={cn(collapsed && 'hidden', 'mb-1 mt-8 text-[12px] text-[#9FA4B2]')}>
            工具
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <Highlighter className="flex flex-col space-y-3">{toolsItems.map(renderMenuItem)}</Highlighter>
            </SidebarMenu>
          </SidebarGroupContent> */}
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <Avatar />
      </SidebarFooter>
    </Sidebar>
  );
};

export default MenuSideBar;
