import chalk from 'chalk';
import { ActionContext, ActionContextPluginLoader } from '../../types';

type Transaction<T> = <R = any>(handler: (ctx: ActionContext<T>) => Promise<R>) => Promise<R>;

const transactionPlugin: ActionContextPluginLoader<'trx', Transaction<any>> = (context) => {
  let transaction = false;

  return {
    name: 'trx',
    plugin: async (handler) => {
      if (transaction) {
        // 已经在事务中，直接执行，避免嵌套事务
        return await handler(context);
      }
      // 开启事务前，先保存原始的 db 对象
      const db = context.db;
      transaction = true;
      // 进入事务
      const now = Date.now();
      context.logger.debug(chalk.yellow('[Transaction]'));
      const res = await context.db.$transaction(async (trx) => {
        context.db = trx as any;
        return await handler(context);
      });
      context.logger.debug(chalk.yellow('[Transaction]'), 'done', `${Date.now() - now}ms`);
      // 恢复原始的 db 对象
      context.db = db;
      transaction = false;
      return res;
    },
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T> {
    /**
     * 事务操作API 它是 ctx.db 的一个代理，会自动开启事务
     * @param handler
     * @returns
     */
    trx: Transaction<T>;
  }
}

export default transactionPlugin;
