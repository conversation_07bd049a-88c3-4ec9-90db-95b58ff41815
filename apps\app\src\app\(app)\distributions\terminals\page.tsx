'use client';

import { ProFilter } from '@/components/pro/pro-filter';
import ProTable from '@/components/pro/pro-table';
import { Button, Panel } from '@/components/ui';
import { pageCloudTerminals, updateCloudTerminal } from '@/services/actions/cloud-terminal';
import { action, ActionParams, useAction } from '@/utils/server-action/action';
import { cloud_terminals } from '@roasmax/database';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { createInbuiltColumns, filterColumns } from './config';

export default function Terminals() {
  const router = useRouter();

  const [filters, setFilters] = useState<ActionParams<typeof pageCloudTerminals>['filters']>({});

  const { data, loading, mutate } = useAction(pageCloudTerminals, { pagination: { page: 1, pageSize: 5000 }, filters });

  const columns = useMemo(() => {
    return createInbuiltColumns();
  }, []);

  const handleRecordEditSubmit = async (v: string, record: cloud_terminals) => {
    if (record.remark) {
      const res = await action(updateCloudTerminal, { id: record.id, data: { remark: v } }, { errorType: 'return' });
      if (!res?.success) {
        toast.error(res?.message || '更新失败');
        return;
      }
      toast.success('更新成功');
      mutate((prev) => {
        if (!prev) return prev;
        const index = prev.list!.findIndex((item) => item.id === record.id);
        if (typeof index === 'number' && index !== -1 && prev.list?.[index]) {
          prev.list[index].remark = v;
        }
        return prev;
      });
    }
  };

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter value={filters} onSubmit={(v) => setFilters(v)} columns={filterColumns} className="mb-2" />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <div className="text-base font-bold">云终端</div>
          </div>
          <div className="flex items-center justify-end gap-2">
            <Button variant="link" className="h-[32px]" onClick={() => router.push('/distributions/social-accounts')}>
              账号资产
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable onEdit={handleRecordEditSubmit} columns={columns} loading={loading} dataSource={data?.list || []} />
        </div>
      </Panel>
    </div>
  );
}
