import { cn } from '@/utils/cn';
import Image from 'next/image';
import type React from 'react';
import { useState, useRef } from 'react';
import type { VideoItem } from '../types';
import { useHoverDisplay } from '@/hooks/useHoverDisplay';
import { getVideoTypeDescription } from '../data';

interface HistoryVideoCardProps {
  video: VideoItem;
  onClick: () => void;
}

export const HistoryVideoCard: React.FC<HistoryVideoCardProps> = ({ video, onClick }) => {
  const [imageError, setImageError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 判断任务状态
  const isCompleted = video.status === 'completed' && video.videoUrl;
  const isFailed = video.status === 'failed';
  const isProcessing = video.status === 'processing' || video.status === 'pending' || video.status === 'running';

  // 使用 useHoverDisplay hook 来处理视频播放
  const { onMouseOver, onMouseLeave, loading: videoLoading } = useHoverDisplay(isCompleted ? video.videoUrl || '' : '');

  // 获取状态显示信息
  const getStatusInfo = () => {
    if (isCompleted) {
      return { icon: '✓', color: 'text-green-500', bgColor: 'bg-green-500/10', label: '已完成' };
    } else if (isFailed) {
      return { icon: '✗', color: 'text-red-500', bgColor: 'bg-red-500/10', label: '生成失败' };
    } else if (isProcessing) {
      return { icon: '⏳', color: 'text-yellow-500', bgColor: 'bg-yellow-500/10', label: '生成中' };
    }
    return { icon: '?', color: 'text-gray-500', bgColor: 'bg-gray-500/10', label: '未知状态' };
  };

  const statusInfo = getStatusInfo();

  // 在外层容器处理鼠标事件，避免内部状态变化导致的事件异常
  const handleContainerMouseEnter = () => {
    if (isCompleted && videoRef.current && video.videoUrl) {
      onMouseOver(videoRef.current);
    }
  };

  const handleContainerMouseLeave = () => {
    if (isCompleted && videoRef.current) {
      onMouseLeave(videoRef.current);
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'group cursor-pointer overflow-hidden rounded-lg bg-[#1F2434] transition-all duration-200',
        'border border-[#00E1FF]/20 hover:border-[#00E1FF]/50 hover:shadow-lg hover:shadow-[#00E1FF]/10',
        isCompleted
          ? 'hover:border-green-400/50 hover:shadow-green-400/10'
          : isFailed
            ? 'border-red-500/30 hover:border-red-400/50 hover:shadow-red-400/10'
            : isProcessing
              ? 'border-yellow-500/30 hover:border-yellow-400/50 hover:shadow-yellow-400/10'
              : ''
      )}
      onClick={onClick}
      onMouseEnter={handleContainerMouseEnter}
      onMouseLeave={handleContainerMouseLeave}
      tabIndex={0}
      role="button"
      aria-label={`查看${video.title} - ${statusInfo.label}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <div className="relative">
        <div className="relative aspect-square w-full bg-[#272D3E]">
          {/* 视频元素 - 只有在完成状态时显示 */}
          {isCompleted && (
            <video
              ref={videoRef}
              className="absolute inset-0 h-full w-full object-cover"
              muted
              loop
              playsInline
              style={{ display: videoLoading ? 'none' : 'block' }}
            />
          )}

          {/* 缩略图 - 默认显示，当视频播放时隐藏 */}
          {!imageError && video.thumbnailUrl ? (
            <Image
              src={video.thumbnailUrl}
              alt={video.title}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              onError={() => setImageError(true)}
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
              style={{ display: isCompleted && !videoLoading && videoRef.current?.src ? 'none' : 'block' }}
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-[#272D3E]">
              <svg className="h-12 w-12 text-[#7E8495]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </div>
          )}

          {/* 状态标识 */}
          <div
            className={cn(
              'absolute right-2 top-2 flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium',
              statusInfo.bgColor,
            )}
          >
            <span className={statusInfo.color}>{statusInfo.icon}</span>
          </div>

          {/* 播放按钮 - 只在成功完成时显示，且当前没有播放视频时显示 */}
          {isCompleted && (videoLoading || !videoRef.current?.src) && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/90">
                <svg className="ml-1 h-6 w-6 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 5v10l7-5z" />
                </svg>
              </div>
            </div>
          )}

          {/* 视频加载指示器 */}
          {videoLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/40">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-[#00E1FF] border-t-transparent"></div>
            </div>
          )}

          {/* 进度条 - 在处理中时显示 */}
          {isProcessing && video.progress !== undefined && (
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-600">
              <div
                className="h-full bg-yellow-500 transition-all duration-300"
                style={{ width: `${video.progress}%` }}
              />
            </div>
          )}

          {/* Hover 遮罩和预览按钮 */}
          <div className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
            <button
              onClick={onClick}
              className="flex h-10 w-20 items-center justify-center rounded-md bg-white/90 text-sm font-medium text-gray-800 transition-all hover:bg-white"
            >
              {isCompleted ? '预览' : '查看'}
            </button>
          </div>
        </div>
      </div>

      <div className="p-3">
        <div className="mb-1 flex items-center justify-between">
          <h3 className="line-clamp-1 flex-1 text-sm font-medium text-white">{video.title}</h3>
          <span className={cn('ml-2 text-xs', statusInfo.color)}>{statusInfo.label}</span>
        </div>
        <div className="mb-1">
          <span className="inline-flex rounded-full bg-[#00E1FF]/10 px-2 py-1 text-xs font-medium text-[#00E1FF]">
            {getVideoTypeDescription(video.type)}
          </span>
        </div>
        <p className="line-clamp-1 text-xs leading-4 text-[#7E8495]">
          {isFailed && video.error_message ? video.error_message : video.prompt}
        </p>
        <div className="mt-2 text-xs text-[#7E8495]">
          {new Date(video.createdAt).toLocaleString('zh-CN', { 
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>
    </div>
  );
}; 