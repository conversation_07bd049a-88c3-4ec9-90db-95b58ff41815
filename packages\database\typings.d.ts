declare namespace PrismaJson {
  /**
   * 货币解析
   * format 为正则表达式，用于匹配货币格式，若不指定则直接使用原值转数字
   * unit 为货币单位，用于将匹配到的值转换为数字，数据库存储使用分做单位，乘以unit得到具体金额。若原数据单位为元，则unit为100
   */
  type ColumnTypeConfigCurrency = { format: string; unit: number };
  /**
   * 百分比解析
   * format 为正则表达式，用于匹配百分比格式，若不指定则直接使用原值转数字
   * unit 为百分比单位，用于将匹配到的值转换为数字，数据库存储使用百分比保留4位小数，如12.34%，存储为123400，乘以unit得到具体百分比。若原数据单位为百分比，则unit为10000
   */
  type ColumnTypeConfigPercent = { format: string; unit: number };
  /** 日期解析
   * format 为日期格式，用于匹配日期格式，若不指定则直接使用原值。如 YYYY/MM/DD，则匹配2024/01/01，若为YYYY-MM-DD，则匹配2024-01-01
   */
  type ColumnTypeConfigDate = { format: string };

  /** 字符串数组 */
  type StringList = string[];
  /** 数字数组 */
  type NumberList = number[];
  /** Id数组 */
  type IdList = string[];
  /** 租户任务池配置 */
  type TenantTaskPoolType = { name: string; workers: string | number[] };
  /** 视频素材切分数据 */
  type SliceMaterialListType = { video: string; audio: string }[];
  /** 自定义属性对象 */
  type Properties = Record<string, any>;
  /** 自定义属性对象数组 */
  type PropertiesList = Properties[];
  /** 数据导入模板内容 */
  type DataImportTemplateContent = {
    /** 唯一键 */
    uniqueKeys: string[];
    /** 列 */
    columns: ({
      key: string;
      title: string;
      defaultValue?: any;
    } & (
      | { type: 'text' | 'boolean' | 'images' | 'tags' }
      | { type: 'number'; config?: { format: string } }
      | { type: 'currency'; config?: ColumnTypeConfigCurrency }
      | { type: 'percent'; config?: ColumnTypeConfigPercent }
      | { type: 'date'; config?: ColumnTypeConfigDate }
    ))[];
    /** 需要更新的键 */
    shouldUpdateKeys?: string[];
    /** 省略起始行(默认0，一些表格会在头几行有标题或备注) */
    skipStartLines?: number;
    /** 指定特定列的默认值 */
    defaultValues?: Record<string, any>;
    /** 视频生成参数 */
    generationParams?: {
      /** 视频风格 */
      kol_style?: string;
      /** 模板视频tiktok id列表 */
      template_video_tiktok_ids: string[];
      /** 模板视频fileId */
      template_file_id: string;
    };
  };
}
