/**
 * 本地认证插件
 * 替换 Authing AuthenticationClient 插件
 */

import { LocalAuthenticationClient } from '../../adapters/LocalAuthenticationClient';
import { ActionContextPluginLoader } from '../../types';

type LocalAuthPlugin = LocalAuthenticationClient;

const localAuthPlugin: ActionContextPluginLoader = (context) => {
  // 使用环境变量配置，保持与原 Authing 插件的兼容性
  const authClient = new LocalAuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  
  // 设置访问令牌
  authClient.setAccessToken(context.token);

  return {
    name: 'authing', // 保持原名称，确保业务代码兼容性
    plugin: authClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 本地认证客户端（兼容 Authing AuthenticationClient）
     */
    authing: LocalAuthPlugin;
  }
}

export default localAuthPlugin;
