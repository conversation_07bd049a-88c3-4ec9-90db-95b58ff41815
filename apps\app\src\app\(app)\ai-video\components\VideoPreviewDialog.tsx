import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import type React from 'react';
import type { VideoItem } from '../types';
import { VideoPlayer } from '@/components/VideoPlayer';
import dayjs from 'dayjs';
import { getVideoTypeDescription, getVideoTypeDetailDescription } from '../data';

interface VideoPreviewDialogProps {
  video: VideoItem | null;
  isOpen: boolean;
  onClose: () => void;
  onApply: (video: VideoItem) => void;
}

export const VideoPreviewDialog: React.FC<VideoPreviewDialogProps> = ({ video, isOpen, onClose, onApply }) => {
  if (!isOpen || !video) return null;

  const handleApply = () => {
    onApply(video);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-[900px] overflow-hidden border-0 bg-[#1A1D29] p-0 shadow-xl sm:w-[90vw] md:w-[800px] lg:w-[900px]">
        {/* Header */}
        <DialogHeader className="border-b border-[#2A2F3A] p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <DialogTitle className="text-lg font-medium text-white">{video.title}</DialogTitle>
              <div className="mt-2 flex items-center gap-2">
                <span className="rounded bg-[#00E1FF]/10 px-2 py-1 text-xs text-[#00E1FF]">
                  {getVideoTypeDescription(video.type)}
                </span>
                <span className="text-xs text-[#7E8495]">{dayjs(video.createdAt).format('MM/DD HH:mm')}</span>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex">
          {/* Left side - Video */}
          <div className="flex w-[65%] items-center justify-center bg-[#0F1117] p-8">
            <VideoPlayer
              src={video.videoUrl}
              controls
              width="100%"
              height={380}
              light={video.thumbnailUrl}
              style={{
                borderRadius: '8px',
              }}
            />
          </div>

          {/* Right side - Info */}
          <div className="flex w-[35%] flex-col bg-[#1F2434] p-6">
            <div className="flex-1 space-y-6">
              {/* 描述 */}
              <div>
                <h4 className="mb-2 text-sm font-medium text-white">类型描述</h4>
                <p className="text-sm leading-relaxed text-[#B8BCC8]">{getVideoTypeDetailDescription(video.type)}</p>
              </div>

              {/* 视频配置 */}
              {(video.aspectRatio || video.engine) && (
                <div>
                  <h4 className="mb-2 text-sm font-medium text-white">视频配置</h4>
                  <div className="space-y-2">
                    {video.aspectRatio && (
                      <div className="flex items-center justify-between rounded bg-[#252A38] px-3 py-2">
                        <span className="text-xs text-[#7E8495]">视频比例</span>
                        <span className="text-xs font-medium text-[#00E1FF]">{video.aspectRatio}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 提示词 */}
              <div>
                <h4 className="mb-2 text-sm font-medium text-white">提示词</h4>
                <div className="max-h-32 overflow-y-auto rounded bg-[#252A38] p-3">
                  <p className="text-sm leading-relaxed text-[#B8BCC8]">{video.prompt}</p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="mt-8 flex gap-3">
              <Button
                onClick={handleApply}
                className="flex-1 bg-[#00E1FF] text-sm font-medium text-black hover:bg-[#00E1FF]/90"
              >
                应用
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                className="border-[#2A2F3A] bg-transparent text-sm text-[#B8BCC8] hover:border-[#00E1FF]/30 hover:text-white"
              >
                取消
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
