'use server';

import { server, ActionContext } from '@roasmax/serve';
export const getAuthingAppId = async () => {
  return process.env.APPID;
};

export const listPropertyDefinitions = server('获取自定义字段配置', async (ctx) => {
  return await ctx.db.property_definitions.findMany({
    select: { id: true, name: true, display_name: true, resource_name: true, type: true },
  });
});

export const listDataImportTemplates = server(
  '获取数据导入模板',
  async (ctx: ActionContext<{ resource_name: string }>) => {
    return await ctx.db.data_import_templates.findMany({
      where: { resource_name: ctx.data.resource_name },
    });
  },
);

export const createDataImportTemplate = server(
  '创建数据导入模板',
  async (
    ctx: ActionContext<{ resource_name: string; name: string; content: PrismaJson.DataImportTemplateContent }>,
  ) => {
    return await ctx.db.data_import_templates.create({
      data: {
        ...ctx.data,
        tenant_id: ctx.tenant.id,
      },
    });
  },
);

export const updateDataImportTemplate = server(
  '更新数据导入模板',
  async (ctx: ActionContext<{ id: string; name?: string; content?: PrismaJson.DataImportTemplateContent }>) => {
    return await ctx.db.data_import_templates.update({
      where: { id: ctx.data.id },
      data: ctx.data,
    });
  },
);
