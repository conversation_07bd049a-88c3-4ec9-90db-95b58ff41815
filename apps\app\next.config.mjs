import bundleAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true', //当环境变量ANALYZE为true时开启
});

/** @type {import("next").NextConfig} */
const nextConfig = {
  output: 'standalone', // 输出模式
  transpilePackages: ['@roasmax/*'],
  typescript: {
    // 是否检查 TypeScript 构建时的错误
    ignoreBuildErrors: false,
  },
  reactStrictMode: false, // 开启严格模式
  async redirects() {
    return [
      {
        source: '/',
        destination: '/ai-video',
        permanent: true,
      },
      {
        source: '/main',
        destination: '/ai-video',
        permanent: true,
      },
      {
        source: '/ads',
        destination: '/ai-video',
        permanent: true,
      },
      {
        source: '/dashboard',
        destination: '/ai-video',
        permanent: true,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    contentDispositionType: 'inline', // 解决图片在浏览器中显示为下载的问题
  },
  async headers() {
    return [
      {
        source: '/api/webhooks/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Max-Age', value: '86400' },
          { key: 'Access-Control-Expose-Headers', value: '*' },
          {
            key: 'Access-Control-Allow-Headers',
            value:
              'X-CSRF-Token, X-Requested-With, Accept, auth-token, AccessToken, Access-Token, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version',
          },
        ],
      },
    ];
  },
  webpack: (config) => {
    // 修改 splitChunks 配置
    config.optimization.splitChunks = {
      chunks: 'all', // 对所有类型的代码进行分割
      minSize: 20000, // 生成的包的最小大小
      minChunks: 1, // 至少被引用的次数
      maxAsyncRequests: 30, // 最大异步请求数
      maxInitialRequests: 30, // 最大初始请求数
      automaticNameDelimiter: '~', // 自动命名的分隔符
      cacheGroups: {
        default: {
          minChunks: 2, // 至少被引用的次数
          priority: -20,
          reuseExistingChunk: true, // 重用已存在的块
        },
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
        },
      },
    };

    return config; // 返回修改后的配置
  },
  swcMinify: true, // 开启swcMinify 使用 SWC 进行编译优化：
  poweredByHeader: false, // 禁用Powered by Next.js
  eslint: {
    ignoreDuringBuilds: true, // 忽略eslint报错
  },
  experimental: {
    esmExternals: true, // 减少缓存大小
  },
};

export default withBundleAnalyzer(nextConfig);
