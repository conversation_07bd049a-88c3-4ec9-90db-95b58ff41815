'use server';

import { Prisma } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import { addVideoCutTask, updateVideoCutTask } from '../domains/task/video-cut-task';

export const pageVideoCutTasks = server(
  '分页查询视频切分任务列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters: {
        name?: string;
      };
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      const where: Prisma.video_cut_tasksWhereInput = {};
      if (ctx.data.filters.name) {
        where.name = { contains: ctx.data.filters.name };
      }

      const [total, list] = await Promise.all([
        ctx.db.video_cut_tasks.count({ where }),
        ctx.db.video_cut_tasks.findMany({
          where,
          skip: (ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
          take: ctx.data.pagination.pageSize,
          orderBy: { tmp_created_at: 'desc' },
          select: {
            id: true,
            name: true,
            tmp_created_at: true,
            status: true,
            status_desc: true,
            ip: true,
            live_room: true,
            live_session: true,
          },
        }),
      ]);

      return { list, pagination: { total, page: ctx.data.pagination.page, pageSize: ctx.data.pagination.pageSize } };
    });
  },
);

export const getVideoCutTaskById = server('获取视频切分任务详情', async (ctx: ActionContext<{ id: string }>) => {
  return await ctx.db.video_cut_tasks.findUnique({ where: { id: ctx.data.id } });
});

export const createVideoCutTask = server(
  '创建自动切分视频任务',
  async (
    ctx: ActionContext<{
      data: Parameters<typeof addVideoCutTask>[0]['data'];
      run: boolean;
    }>,
  ) => {
    const res = await ctx.execute(addVideoCutTask, ctx.data.data);
    if (ctx.data.run && res.status === 'DRAFT') {
      // 使用scf执行任务
      await ctx.db.video_cut_tasks.update({
        where: { id: res.id },
        data: { status: 'PROCESSING', status_desc: '执行中' },
      });
      await ctx.request.post(process.env.SCF_CUT_URL, { taskId: res.id });
    }
    return res;
  },
);

export const modifyVideoCutTask = server(
  '更新视频切分任务',
  async (
    ctx: ActionContext<{
      where: { id: string };
      data: Pick<
        Prisma.video_cut_tasksUpdateInput,
        'ip' | 'cut_mode' | 'live_room' | 'live_session' | 'origin_url' | 'name' | 'goods_list' | 'language'
      >;
      run: boolean;
    }>,
  ) => {
    const res = await ctx.execute(updateVideoCutTask, { where: ctx.data.where, data: ctx.data.data });
    if (ctx.data.run && res.status === 'DRAFT') {
      // 使用scf执行任务
      await ctx.db.video_cut_tasks.update({
        where: { id: res.id },
        data: { status: 'PROCESSING', status_desc: '执行中' },
      });
      await ctx.request.post(process.env.SCF_CUT_URL, { taskId: res.id });
    }
    return res;
  },
);
