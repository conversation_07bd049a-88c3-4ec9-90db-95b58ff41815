import axios from 'axios';
import { OpenAI } from 'openai';

// 定义响应类型接口
interface ModelResponse {
  content: string;
}

interface ImageInput {
  url: string;
}

// 模型类型定义
type ModelType = 'gpt4o' | 'ds-r1' | 'claude1022' | 'ds-r1-hs' | 'ds-v3-hs' | 'doubao-v';

// 统一的模型调用接口
const invokeModel = async (prompt: string, modelType: ModelType = 'gpt4o', imageUrl?: string): Promise<string> => {
  try {
    switch (modelType) {
      case 'doubao-v':
        if (!imageUrl) {
          throw new Error('调用豆包视觉模型需要提供图片URL');
        }
        return await invokeDoubaoV(prompt, imageUrl);
      case 'gpt4o':
        return await invokeGpt4o(prompt);
      case 'ds-r1':
        return await invokeDsR1(prompt);
      case 'claude1022':
        return await invokeClaude1022(prompt);
      case 'ds-r1-hs':
        return await invokeDsR1Hs(prompt);
      case 'ds-v3-hs':
        return await invokeDsV3Hs(prompt);
      default:
        throw new Error(`不支持的模型类型: ${modelType}`);
    }
  } catch (error: any) {
    throw new Error(`调用模型时出错: ${error.message}`);
  }
};

const llm = {
  invokeModel,
};

export default llm;

// GPT-4o 模型调用
const invokeGpt4o = async (prompt: string): Promise<string> => {
  try {
    const client = new OpenAI({
      apiKey: '********************************',
      baseURL: 'https://scriptgeneration.openai.azure.com/',
      defaultHeaders: {
        'api-key': '********************************',
      },
      defaultQuery: {
        'api-version': '2023-12-01-preview',
      },
    });

    const response = await client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
      max_tokens: 4096,
    });

    return response.choices[0]!.message.content!;
  } catch (error: any) {
    throw new Error(`调用 GPT-4o 时出错: ${error.message}`);
  }
};

// DeepSeek-R1 模型调用
const invokeDsR1 = async (prompt: string): Promise<string> => {
  try {
    const endpoint = 'https://DeepSeek-R1-hyhvw.eastus.models.ai.azure.com';
    const apiKey = 'stMtfb6qZt7bRyP60oNI7kML6FhUhlJo';
    const deploymentName = 'deepseek-r1';
    const apiVersion = '2024-02-15-preview';

    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        const response = await axios.post(
          `${endpoint}/openai/deployments/${deploymentName}/chat/completions?api-version=${apiVersion}`,
          {
            messages: [
              {
                role: 'user',
                content: prompt,
              },
            ],
            max_tokens: 4096,
            temperature: 0.7,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'api-key': apiKey,
            },
            timeout: 30000,
          },
        );

        if (response.status === 200 && response.data.choices && response.data.choices.length > 0) {
          return response.data.choices[0].message.content;
        } else {
          throw new Error('API响应格式不正确');
        }
      } catch (error: any) {
        retryCount++;
        if (retryCount === maxRetries) {
          throw new Error(`重试${maxRetries}次后仍然失败: ${error.message}`);
        }
        // 等待1秒后重试
        await new Promise((resolve) => setTimeout(resolve, 1000));
        continue;
      }
    }

    throw new Error('未能成功完成请求');
  } catch (error: any) {
    throw new Error(`调用 ds-r1 时出错: ${error.message}`);
  }
};

// Claude 1022 模型调用
const invokeClaude1022 = async (prompt: string): Promise<string> => {
  try {
    const apiKey = 'sk-LbmRW2MRgrCOPDg7C9F4235b5a5046A880DbD0B9C9D6D8B3';
    const apiUrl = 'https://api.road2all.com/v1/chat/completions';

    const response = await axios.post(
      apiUrl,
      {
        model: 'claude-3-5-sonnet-20241022',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: false,
      },
      {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 120000,
      },
    );

    if (response.status === 200) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API调用失败: ${response.status}`);
    }
  } catch (error: any) {
    throw new Error(`调用 Claude 1022 时出错: ${error.message}`);
  }
};

// DeepSeek-R1-HS 模型调用
const invokeDsR1Hs = async (prompt: string): Promise<string> => {
  try {
    const client = new OpenAI({
      apiKey: 'd02857ef-5014-4406-ac7c-17700d053946',
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });

    const completion = await client.chat.completions.create({
      model: 'ep-20250220130832-zpx26',
      messages: [{ role: 'user', content: prompt }],
    });

    return completion.choices[0]!.message.content!;
  } catch (error: any) {
    throw new Error(`调用 ds-r1-hs 时出错: ${error.message}`);
  }
};

// DeepSeek-V3-HS 模型调用
const invokeDsV3Hs = async (prompt: string): Promise<string> => {
  try {
    const client = new OpenAI({
      apiKey: 'd02857ef-5014-4406-ac7c-17700d053946',
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });

    const completion = await client.chat.completions.create({
      model: 'ep-20250220132209-zb9q8',
      messages: [{ role: 'user', content: prompt }],
    });

    return completion.choices[0]!.message.content!;
  } catch (error: any) {
    throw new Error(`调用 ds-v3-hs 时出错: ${error.message}`);
  }
};

// 图片转Base64
const encodeImageToBase64 = async (imagePath: string): Promise<string> => {
  try {
    const fs = require('fs').promises;
    const path = require('path');

    const fileExt = path.extname(imagePath).toLowerCase().replace('.', '');
    const supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];

    if (!supportedFormats.includes(fileExt)) {
      throw new Error('不支持的图片格式，请使用 jpg、jpeg、png、gif 或 bmp 格式');
    }

    const imageBuffer = await fs.readFile(imagePath);
    const base64Data = imageBuffer.toString('base64');
    const mimeType = fileExt === 'jpg' || fileExt === 'jpeg' ? 'jpeg' : fileExt;

    return `data:image/${mimeType};base64,${base64Data}`;
  } catch (error: any) {
    throw new Error(`图片转换失败: ${error.message}`);
  }
};

// 豆包视觉模型调用
const invokeDoubaoV = async (prompt: string, imageInput: string): Promise<string> => {
  try {
    const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
    const headers = {
      Authorization: `Bearer d02857ef-5014-4406-ac7c-17700d053946`,
      'Content-Type': 'application/json',
    };

    let imageUrl = imageInput;
    if (!imageInput.startsWith('http') && !imageInput.startsWith('data:image')) {
      imageUrl = await encodeImageToBase64(imageInput);
    }

    const response = await axios.post(
      apiUrl,
      {
        model: 'doubao-1-5-vision-pro-32k-250115',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt,
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
        ],
        stream: false,
        max_tokens: 4096,
        temperature: 0.1,
      },
      {
        headers,
        timeout: 120000,
      },
    );

    if (response.status === 200) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API调用失败: ${response.status}, ${response.data}`);
    }
  } catch (error: any) {
    throw new Error(`调用豆包视觉模型时出错: ${error.message}`);
  }
};
