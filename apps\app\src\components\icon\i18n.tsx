import React from 'react';

export const I18n = () => {
  return (
    <svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.0009 3C18.001 3 20.4011 4.30008 20.0011 7.50026" stroke="#050A1C" strokeLinecap="round" />
      <path d="M2.4469 15.5137C2.4469 17.5138 3.74697 19.9139 6.94716 19.5139" stroke="#050A1C" strokeLinecap="round" />
      <rect width="14.0008" height="14.0008" rx="3" fill="white" />
      <path
        d="M4.58182 10.7004L5.16631 9.0772H8.20268L8.78569 10.7004H10.4563L7.74989 3H5.78926L3 10.7004H4.58182ZM5.58211 7.78688L6.70669 4.49895H6.74812L7.79132 7.78688H5.58211Z"
        fill="#2B62FC"
      />
      <path
        d="M4.58182 10.7004L5.16631 9.0772H8.20268L8.78569 10.7004H10.4563L7.74989 3H5.78926L3 10.7004H4.58182ZM5.58211 7.78688L6.70669 4.49895H6.74812L7.79132 7.78688H5.58211Z"
        fill="#2B62FC"
      />
      <g filter="url(#filter0_b_2651_396)">
        <rect x="8" y="9" width="14.0008" height="14.0008" rx="3" fill="url(#paint0_linear_2651_396)" />
      </g>
      <path
        d="M12.0012 17.4634H13.1179V16.8705H14.7317V19.7004H15.9151V16.8705H17.564V17.3663H18.7813V13.6149H15.9163V12.7893C15.9111 12.577 15.9163 12.4063 15.9163 12.1602C15.9163 12.1328 15.9124 12.1081 15.9163 12.073C15.9163 12.0508 15.9163 12 15.8169 12H14.699V13.6138H12V17.4646L12.0012 17.4634ZM13.1179 14.5364H14.7317V15.9829H13.1191L13.1179 14.5364ZM17.5616 15.9829H15.9128V14.5376H17.5616V15.9829Z"
        fill="white"
      />
      <path
        d="M12.0012 17.4634H13.1179V16.8705H14.7317V19.7004H15.9151V16.8705H17.564V17.3663H18.7813V13.6149H15.9163V12.7893C15.9111 12.577 15.9163 12.4063 15.9163 12.1602C15.9163 12.1328 15.9124 12.1081 15.9163 12.073C15.9163 12.0508 15.9163 12 15.8169 12H14.699V13.6138H12V17.4646L12.0012 17.4634ZM13.1179 14.5364H14.7317V15.9829H13.1191L13.1179 14.5364ZM17.5616 15.9829H15.9128V14.5376H17.5616V15.9829Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_b_2651_396"
          x="-92"
          y="-91"
          width="214.001"
          height="214.001"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="50" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2651_396" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2651_396" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_2651_396"
          x1="7.84085"
          y1="8.88769"
          x2="20.9098"
          y2="23.0021"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="0.145" stopColor="#5C92FF" />
          <stop offset="0.247839" stopColor="#3073FD" />
        </linearGradient>
      </defs>
    </svg>
  );
};
