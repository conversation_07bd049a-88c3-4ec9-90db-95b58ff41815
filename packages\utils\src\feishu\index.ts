import { request } from '../net/request';
import { AxiosRequestConfig } from 'axios';

type FeishuRobotHyperTextMessageContentType = Array<
  { tag: 'text'; text: string } | { tag: 'a'; text: string; href: string } | { tag: 'at'; user_id: string }
>;

export class FeishuRobot {
  private host: string;

  constructor(params: { host: string | undefined }) {
    if (!params.host) {
      throw new Error('FeishuRobot host is required');
    }
    this.host = params.host;
  }

  /**
   * 用于请求开放接口
   * @param path
   * @param options
   * @returns
   */
  public async request<T>(path: string, options?: AxiosRequestConfig) {
    try {
      const res = await request<T>({
        ...(options || {}),
        headers: { 'Content-Type': 'application/json', ...(options?.headers || {}) },
        url: `${this.host}${path}`,
      });
      return res;
    } catch (e: any) {
      console.error(`Failed to request: ${e.message}. request path: ${path}; options: ${JSON.stringify(options)}`);
      throw new Error(`Failed to request: ${e.message}`);
    }
  }

  /**
   * 发送富文本消息
   * @param params
   * @returns
   */
  public async sendHyperTextMessage(params: { title: string; content: FeishuRobotHyperTextMessageContentType }) {
    return await this.request('', {
      method: 'POST',
      data: {
        msg_type: 'post',
        content: {
          post: {
            zh_cn: {
              title: params.title,
              content: [params.content],
            },
          },
        },
      },
    });
  }

  public async error(title: string, description: string | (string | { text: string; href?: string })[]) {
    let content: FeishuRobotHyperTextMessageContentType = [];
    if (Array.isArray(description)) {
      content = description.map((text) => {
        if (typeof text === 'object' && text.href) {
          return { tag: 'a', text: text.text, href: text.href! };
        }
        return { tag: 'text', text };
      }) as FeishuRobotHyperTextMessageContentType;
    } else if (typeof content === 'string') {
      content = [{ tag: 'text', text: description }];
    }

    try {
      return await this.sendHyperTextMessage({ title: title, content: content });
    } catch (e: any) {
      console.error(`Failed to send error message: ${e.message}`);
    }
  }
}
