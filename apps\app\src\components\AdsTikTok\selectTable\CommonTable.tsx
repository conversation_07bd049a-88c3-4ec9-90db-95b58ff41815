import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui';
import { Skeleton } from '@/components/ui/Skeleton';
import { cn } from '@/utils/cn';
import React, { useLayoutEffect, useState } from 'react';
import { ProMask } from '@/components/pro/pro-mask';

export type SortOrder = 'asc' | 'desc' | undefined;

export interface SortState {
  key: string;
  order: SortOrder;
}

export interface Column {
  key: string;
  title: React.ReactNode;
  width: number;
  sortable?: boolean;
  fixed?: 'left' | 'right';
  tooltip?: {
    enable?: boolean;
    contentKey?: string;
    placement?: 'top' | 'right' | 'bottom' | 'left';
  };
}

export interface TableProps {
  columns: Column[];
  dataSource: any[];
  selectedRows?: string[];
  sortState?: SortState;
  rowKey: string;
  onSelectAll?: (checked: boolean, currentPageIds: string[]) => void;
  onSelect?: (id: string, checked: boolean) => void;
  onSort?: (key: string) => void;
  renderCell: (key: string, record: any) => React.ReactNode;
  loading?: boolean;
  align?: 'center' | 'left' | 'right';
  onRowSelect?: (record: any, checked: boolean) => void;
  test?: boolean;
}

/**
 * 计算固定列的偏移量
 */
const calcStickyOffset = (index: number, columns: Column[], options: { containsSelection: boolean }) => {
  const col = columns[index];
  if (col?.fixed === 'right') {
    return columns
      .slice(index + 1)
      .filter((c) => c.fixed === 'right')
      .reduce((acc, c) => acc + (c.width || 100), 0);
  }
  if (col?.fixed === 'left') {
    return columns
      .slice(0, index)
      .filter((c) => c.fixed === 'left')
      .reduce((acc, c) => acc + (c.width || 100), options.containsSelection ? 48 : 0);
  }
  return 0;
};

export function CommonTable({
  columns,
  dataSource,
  selectedRows,
  sortState,
  rowKey,
  onSelectAll,
  onSelect,
  onSort,
  renderCell,
  loading = false,
  align = 'left',
  onRowSelect,
  test,
}: TableProps) {
  const [maxTableWidth, setMaxTableWidth] = useState('calc(100vw - 260px)');

  useLayoutEffect(() => {
    const updateMaxWidth = () => {
      // 使用 localStorage 判断侧边栏状态
      const isSidebarCollapsed = localStorage?.getItem('BWAI_SIDEBAR_STATE') === 'false';
      const sidebarWidth = isSidebarCollapsed ? 48 : 255;
      setMaxTableWidth(`calc(100vw - ${sidebarWidth}px)`);
    };

    // 初始化时执行一次
    updateMaxWidth();

    // 监听窗口大小变化
    window.addEventListener('resize', updateMaxWidth);

    // 监听 localStorage 变化
    window.addEventListener('storage', updateMaxWidth);

    return () => {
      window.removeEventListener('resize', updateMaxWidth);
      window.removeEventListener('storage', updateMaxWidth);
    };
  }, []);

  // const renderColumnHeader = (col: Column, index: number) => {
  //   if (!col.sortable) return col.title;
  //   return (
  //     <div className="flex cursor-pointer items-center justify-center" onClick={() => onSort(col.key)}>
  //       {col.title}
  //       <div className="ml-1 flex flex-col">
  //         <span
  //           className={`mb-1 h-1 leading-[7px] ${
  //             sortState.key === col.key && sortState.order === 'asc' ? 'text-red-500' : 'text-blue-500'
  //           }`}
  //         >
  //           ▲
  //         </span>
  //         <span
  //           className={`mt-1 h-1 leading-[7px] ${
  //             sortState.key === col.key && sortState.order === 'desc' ? 'text-red-500' : 'text-blue-500'
  //           }`}
  //         >
  //           ▼
  //         </span>
  //       </div>
  //     </div>
  //   );
  // };

  const renderLoadingSkeleton = () => (
    <TableBody>
      {[...Array(10)].map((_, index) => (
        <TableRow key={`skeleton-${index}`} className={cn('border-gray-800 hover:bg-gray-900')}>
          {columns.map((col: Column) => (
            <TableCell
              key={`skeleton-${index}-${col.key}`}
              className={cn(`text-${align}`)}
              style={{ width: `${col.width}px` }}
            >
              <Skeleton
                className={cn(
                  'h-6 w-4/5',
                  {
                    'mx-auto': align === 'center',
                    'ml-auto': align === 'right',
                    'mr-auto': align === 'left',
                  },
                  'bg-gray-700',
                )}
              />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );

  const renderCellWithTooltip = (key: string, record: any, column: Column) => {
    const content = renderCell(key, record);

    if (column.tooltip?.enable) {
      const tooltipContent = column.tooltip.contentKey ? record[column.tooltip.contentKey] : content;

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="truncate">{content}</div>
            </TooltipTrigger>
            <TooltipContent side={column.tooltip.placement || 'right'}>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return content;
  };

  return (
    <div className={cn('relative mb-6 mt-4')}>
      <div
        className={cn(
          'relative overflow-auto',
          test ? 'max-h-[calc(100vh-550px)]' : 'max-h-[calc(100vh-300px)]',
          'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-600',
        )}
        style={{
          maxWidth: maxTableWidth,
          overscrollBehavior: 'contain',
          WebkitOverflowScrolling: 'touch',
        }}
      >
        <table className={cn('table min-w-full border-none')}>
          <TableHeader className={cn('sticky top-0 z-20 h-[50px] bg-[#262E3F]')}>
            <TableRow className={cn('border-none bg-[#262E3F] hover:bg-[#262E3F]')}>
              {columns.map((col: Column, index) => {
                const offset = calcStickyOffset(index, columns, { containsSelection: false });
                const style: React.CSSProperties = {
                  ...(col.fixed === 'left'
                    ? {
                        position: 'sticky',
                        left: offset,
                        width: `${col.width}px`,
                        maxWidth: `${col.width}px`,
                        backgroundColor: '#262E3F',
                        boxShadow: '2px 0 4px rgba(0,0,0,0.15)',
                      }
                    : col.fixed === 'right'
                      ? {
                          position: 'sticky',
                          right: offset,
                          width: `${col.width}px`,
                          maxWidth: `${col.width}px`,
                          backgroundColor: '#262E3F',
                          boxShadow: '-2px 0 4px rgba(0,0,0,0.15)',
                        }
                      : {
                          width: `${col.width}px`,
                          maxWidth: `${col.width}px`,
                        }),
                };
                return (
                  <TableHead
                    key={col.key}
                    className={cn(
                      'px-3 py-[15px] text-xs font-semibold text-white first:border-l-0 last:border-r-0',
                      'whitespace-nowrap',
                      col.fixed && 'z-10',
                      col.key === 'switch' ||
                        col.key === 'accountName' ||
                        col.key === 'campaignName' ||
                        col.key === 'groupName' ||
                        col.key === 'tagNames' ||
                        col.key === 'actions' ||
                        col.key === 'pubStatus' ||
                        col.key === 'operationStatus' ||
                        col.key === 'adName' ||
                        col.key === 'checkbox' ||
                        col.key === 'user'
                        ? 'text-left'
                        : 'text-center',
                    )}
                    style={style}
                  >
                    {col.title}
                  </TableHead>
                );
              })}
            </TableRow>
          </TableHeader>
          {loading && (!dataSource || dataSource.length === 0) ? (
            renderLoadingSkeleton()
          ) : (
            <TableBody className="max-h-full w-full bg-[#131B2A]">
              {dataSource?.map((record: any, rowIndex) => (
                <TableRow
                  key={record[rowKey]}
                  className={cn('hover:bg-[#1e4da5]! group border-b border-gray-800 hover:opacity-50')}
                  onClick={(e) => {
                    if (
                      e.target instanceof HTMLElement &&
                      (e.target.closest('[data-checkbox-container]') || e.target.tagName === 'INPUT')
                    ) {
                      return;
                    }
                    if (onRowSelect) {
                      const isSelected = selectedRows?.includes(record[rowKey]);
                      onRowSelect(record, !isSelected);
                    }
                  }}
                >
                  {columns?.map((col: Column, colIndex) => {
                    const offset = calcStickyOffset(colIndex, columns, { containsSelection: false });
                    const style: React.CSSProperties =
                      col.fixed === 'left'
                        ? {
                            position: 'sticky',
                            left: offset,
                            width: `${col.width}px`,
                            maxWidth: `${col.width}px`,
                            backgroundColor: '#131B2A',
                            boxShadow: '2px 0 4px rgba(0,0,0,0.15)',
                          }
                        : col.fixed === 'right'
                          ? {
                              position: 'sticky',
                              right: offset,
                              width: `${col.width}px`,
                              maxWidth: `${col.width}px`,
                              backgroundColor: '#131B2A',
                              boxShadow: '-2px 0 4px rgba(0,0,0,0.15)',
                            }
                          : { width: `${col.width}px`, maxWidth: `${col.width}px` };
                    return (
                      <TableCell
                        key={`${record[rowKey]}-${col.key}`}
                        className={cn(
                          `text-sm`,
                          'px-3 py-4',
                          col.fixed && 'z-10',
                          col.key === 'switch' ||
                            col.key === 'accountName' ||
                            col.key === 'campaignName' ||
                            col.key === 'groupName' ||
                            col.key === 'tagNames' ||
                            col.key === 'actions' ||
                            col.key === 'pubStatus' ||
                            col.key === 'operationStatus' ||
                            col.key === 'adName' ||
                            col.key === 'checkbox' ||
                            col.key === 'user'
                            ? 'text-left'
                            : 'text-center',
                          col.key === 'checkbox' ? 'data-checkbox-container' : '',
                        )}
                        style={style}
                      >
                        {renderCellWithTooltip(col.key, record, col)}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          )}
        </table>
      </div>

      {/* 使用 ProMask 组件替换原有的加载遮罩层 */}
      <ProMask loading={loading && dataSource && dataSource.length > 0} />
    </div>
  );
}
