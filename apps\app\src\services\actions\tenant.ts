'use server';
import { server } from '@roasmax/serve';
import { fetchShareLink } from '../domains/cos';

export const getTenantEnabledSubSystems = server('获取租户子系统', async (ctx) => {
  return ctx.tenant.config.enabled_sub_systems;
});

/**
 * 获取租户下的自动分发的文件源的读写分享链接
 */
export const fetchDistributionFileSourceReadWriteShareLink = server('获取自动分发文件源的读写分享链接', async (ctx) => {
  return await ctx.execute(fetchShareLink, {
    resource: '自动分发',
    allow: 'readwrite',
    expire: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
  });
});
