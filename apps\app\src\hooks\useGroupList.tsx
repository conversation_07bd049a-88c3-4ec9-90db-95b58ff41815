import tiktokService from '@/services/tiktokService';
import { useCurrentAdvertiser, useCurrentCampaign } from '@/store/ads/adStore';
import { useEffect, useState } from 'react';
import useSWR from 'swr';

// 抽离 fetcher 函数
export const groupFetcher = async (params: {
  page: number;
  pageSize: number;
  campaignIds?: string[];
  advertiserIds?: string[];
  tagNames?: string;
  startTime?: string;
  endTime?: string;
}) => {
  const { page, pageSize, campaignIds, advertiserIds, tagNames, startTime, endTime } = params;
  const data = await tiktokService.getAdGroupList({
    page,
    page_size: pageSize,
    ...(advertiserIds ? { advertiser_ids: advertiserIds } : {}),
    ...(campaignIds && campaignIds?.length ? { campaign_ids: campaignIds } : {}),
    ...(tagNames ? { tag_names: tagNames } : {}),
    ...(startTime ? { start_time: startTime } : {}),
    ...(endTime ? { end_time: endTime } : {}),
  });

  return data;
};

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

export function useGroupList(initialPage = 1, initialPageSize = 10) {
  const selectedCampaigns = useCurrentCampaign();
  const currentAdvertiser = useCurrentAdvertiser();
  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
    total: 0,
  });

  const [tagNames, setTagNames] = useState<string>('');
  const [dateRange, setDateRange] = useState<{ startTime?: string; endTime?: string }>({});

  const campaignIds = selectedCampaigns?.map((campaign) => String(campaign.campaignId));
  const advertiserIds = currentAdvertiser?.map((advertiser) => advertiser.advertiserId);

  const swrKey = [
    '/api/ads/listAdGroup',
    {
      ...pagination,
      ...(advertiserIds ? { advertiserIds } : {}),
      ...(campaignIds && campaignIds?.length ? { campaignIds } : {}),
      ...(tagNames ? { tagNames } : {}),
      ...dateRange,
    },
  ];

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    swrKey,
    ([_, params]) => groupFetcher(params as any),
    {
      keepPreviousData: true,
      refreshInterval: 1000 * 60 * 5, // 5分钟刷新一次
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnMount: true,
      revalidateOnReconnect: false,
    },
  );

  useEffect(() => {
    const handleRefresh = (
      event: CustomEvent<{
        page?: number;
        pageSize?: number;
        total?: number;
        tagNames?: string;
        startTime?: string;
        endTime?: string;
      }>,
    ) => {
      const { page, pageSize, total = 0, tagNames: newTagNames, startTime, endTime } = event.detail || {};

      if (page !== undefined || pageSize !== undefined) {
        setPagination((prev) => ({
          page: page ?? prev.page,
          pageSize: pageSize ?? prev.pageSize,
          total: total ?? prev.total,
        }));
      }

      if (newTagNames !== undefined) {
        setTagNames(newTagNames);
      }

      if (startTime !== undefined || endTime !== undefined) {
        setDateRange((prev) => ({
          startTime: startTime ?? prev.startTime,
          endTime: endTime ?? prev.endTime,
        }));
      }

      mutate();
    };

    window.addEventListener('REFRESH_GROUP_LIST', handleRefresh as EventListener);
    return () => {
      window.removeEventListener('REFRESH_GROUP_LIST', handleRefresh as EventListener);
    };
  }, [mutate]);

  return {
    groups: data,
    isLoading: isLoading || isValidating,
    isError: error,
    refresh: () => mutate(),
    mutate: (
      pageInfo: { newPage?: number; newPageSize?: number; total: number },
      newTagNames?: string,
      newDateRange?: { startTime?: string; endTime?: string },
    ) => {
      if (pageInfo.newPage !== undefined || pageInfo.newPageSize !== undefined) {
        setPagination((prev) => ({
          page: pageInfo.newPage ?? prev.page,
          pageSize: pageInfo.newPageSize ?? prev.pageSize,
          total: pageInfo.total ?? prev.total,
        }));
      }
      if (newTagNames !== undefined) {
        setTagNames(newTagNames);
      }
      if (newDateRange) {
        setDateRange(newDateRange);
      }
    },
    swrKey,
  };
}
