import React, { useState } from 'react';
import { Edit } from '../icon';
import { Loader } from '../icon/loader';
import { <PERSON>Field, ProEditField, ProFieldProps } from './pro-field';
import { Popover, PopoverContent, PopoverTrigger } from '../ui';
import { cn } from '@/utils/cn';

export type ProColumnBaseDataType = Record<string, any>;

export type ProColumnEditableType<T extends ProColumnBaseDataType> = {
  onEditCommit: (newValue: any, col: ProColumnType<T>) => void | Promise<void>;
};

export interface ProColumnType<T extends ProColumnBaseDataType = any> extends Pick<ProFieldProps, 'type' | 'config'> {
  key?: keyof T | string;
  dataIndex?: keyof T | string | string[] | (keyof T)[];
  title: React.ReactNode;
  width?: number;
  editable?: ProColumnEditableType<T>;
  render?: (value: any, row: T) => React.ReactNode;
}

const getValue = <T extends ProColumnBaseDataType>(row: T, column: ProColumnType) => {
  const { key, dataIndex } = column;
  if (!dataIndex && !key) return undefined;
  if (!dataIndex) return row[key as keyof T];
  if (Array.isArray(dataIndex)) {
    return dataIndex.reduce((acc, key) => acc && acc[key as keyof T], row);
  }
  return row[dataIndex as string];
};

export const ProColumnContent = <T extends ProColumnBaseDataType>(props: { value: T; col: ProColumnType<T> }) => {
  const { value, col } = props;
  const [isEditing, setIsEditing] = useState(false);
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [editValue, setEditValue] = useState(getValue(value, col));

  const handleEditCommit = async (newValue: T) => {
    setLoadingEdit(true);
    await col?.editable?.onEditCommit(newValue, col);
    setLoadingEdit(false);
    setIsEditing(false);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditValue(getValue(value, col));
  };

  return (
    <div className="flex items-center gap-1 overflow-hidden" style={{ width: col.width }}>
      <ProField
        value={getValue(value, col)}
        type={col.type}
        render={col.render ? (v) => col.render!(v, value) : undefined}
        config={col.config}
        className={cn(
          'w-[calc(100%-20px)] overflow-hidden text-ellipsis whitespace-nowrap',
          col.width ? '' : 'min-w-[fit-content]',
        )}
      />
      {col.editable && (
        <Popover open={isEditing} onOpenChange={setIsEditing}>
          <PopoverTrigger>
            <Edit
              className="h-[12px] w-[12px] cursor-pointer"
              onClick={() => {
                setIsEditing(true);
                setEditValue(getValue(value, col));
              }}
            />
          </PopoverTrigger>
          <PopoverContent className="w-[400px]">
            <div>
              <ProEditField
                type={col.type}
                initialValue={editValue}
                value={editValue}
                disabled={loadingEdit}
                onValueChange={setEditValue}
                config={col.config}
                className="w-full"
              />
              {loadingEdit ? (
                <Loader />
              ) : (
                <div className="flex gap-2 pl-1">
                  <button onClick={() => handleEditCommit(editValue)}>保存</button>
                  <button onClick={() => handleEditCancel()}>取消</button>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
};
