import { source_configs } from '@roasmax/database';
import { ActionContextPlugins } from '@roasmax/serve';

export type ActionPayload<T> = {
  authorization?: string;
  data: T;
};

export type ActionContextPluginLoader<
  Name extends keyof ActionContextPlugins = keyof ActionContextPlugins,
  Plugin extends ActionContextPlugins[Name] = ActionContextPlugins[Name],
> = (context: ActionContext<any>) => PromiseLike<{ name: Name; plugin: Plugin }> | { name: Name; plugin: Plugin };

export type ActionContext<T> = ActionContextPlugins<T> & {
  /**
   * 用户 token
   */
  token: string;
  /**
   * 用户信息
   */
  user: {
    /**
     * 用户 id，全局唯一
     */
    id: string;
  };
  /**
   * 租户信息
   */
  tenant: {
    /**
     * 租户 id，全局唯一
     */
    id: string;
    /**
     * 租户名称
     */
    name: string;
    /**
     * 租户配置信息
     * 注意不要直接取用在对应业务处理中会变更的配置信息。
     * 若有必要，重新通过 ctx.db.source_config 获取
     */
    config: source_configs;
  };
  /**
   * 请求数据
   */
  data: T;
  /**
   * 日志操作API
   */
  logger: import('@roasmax/utils').Logger;
  /**
   * 发送 SSE 消息
   */
  send: (data: any) => void;
};

export type ServerAction<R, P> = (data: ActionContext<P>) => Promise<R>;
export type ServerActionWrapper<R, P> = (
  payload: ActionPayload<P>,
  context?: ActionContext<any>,
) => Promise<
  | { success: true; code: undefined; message: undefined; data: R }
  | { success: false; code: number; message: string; data: null }
>;
