import { TaskStatus } from '@/types/task';
import { PrismaClient } from '@prisma/client';
import { materials } from '@roasmax/database';
import { Logger } from '@roasmax/utils';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { DifyBatchResponse } from '../../llm/chat/batch-generate-product-description/route';

const requestSchema = z.object({
  taskId: z.string(),
  status: z.enum(['completed']),
  subTasks: z.array(
    z.object({
      id: z.string(),
      status: z.enum([TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.PROCESSING, TaskStatus.PENDING]),
      generated_material_ids: z.array(z.string()).optional(),
      sub_task_type: z.string().optional().nullable(),
      origin_material_id: z.string().optional().nullable(),
      slice_vod_file_id: z.string().optional().nullable(),
      trace_id: z.string().optional().nullable(),
      tmp_created_at: z.string().optional().nullable(),
      tmp_updated_at: z.string().optional().nullable(),
      tenant_id: z.string().optional().nullable(),
      user_id: z.string().optional().nullable(),
      task_id: z.string().optional().nullable(),
    }),
  ),
});

// 新增类型定义
interface ProductGroup {
  group_id: string;
  ad_ids: string[];
  product_names: string[];
}

interface GroupedResult {
  group_id: string;
  titles: string[];
}

export async function POST(request: Request) {
  const prisma = new PrismaClient();
  const logger = new Logger('generate-ads-callback', 'webhook', request.headers.get('x-request-id') || 'unknown');
  const baseUrl = 'https://ads-sv.bowong.cc';
  const baseNextUrl =
    process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://video-dev.bowongai.com';

  try {
    logger._start('开始处理生成任务回调');
    const body = await request.json();

    logger.info('收到的请求体:', JSON.stringify(body, null, 2));

    const validatedData = requestSchema.parse(body);
    logger.info('数据验证通过:', JSON.stringify(validatedData));

    logger._push('查询生成任务信息');
    const task = await prisma.video_generation_tasks.findUnique({
      where: {
        id: validatedData.taskId,
      },
    });

    if (!task || !task.generation_source_info) {
      throw new Error('未找到任务信息或generation_source_info为空');
    }
    // @ts-ignore
    const sourceInfo = JSON.parse(task?.generation_source_info ?? {});
    logger.info('任务source info:', JSON.stringify(sourceInfo));

    const allGenerationSUbTasks = validatedData.subTasks.filter((each) => each.sub_task_type === 'generation');
    logger.info('all generation sub tasks length', allGenerationSUbTasks.length);

    logger.info('所有生成任务:', JSON.stringify(allGenerationSUbTasks));
    const successfulTasks = validatedData.subTasks.filter(
      (task) => task.status === 'SUCCESS' && task.sub_task_type === 'generation',
    );
    logger.info(`成功任务数量: ${successfulTasks.length}`);

    const successfulGenerationTasks = validatedData.subTasks.filter(
      (task) =>
        task.status === 'SUCCESS' &&
        task.sub_task_type === 'generation' &&
        task?.generated_material_ids &&
        task?.generated_material_ids?.length > 0,
    );
    logger.info(`成功生成任务数量: ${successfulGenerationTasks.length}`);

    // 添加对 successfulGenerationTasks 为空的处理
    if (successfulGenerationTasks.length === 0) {
      logger.warn('没有成功的生成任务');

      const failedFormattedData = {
        task_id: sourceInfo?.adsBatchCreateTaskId,
        success: false,
        message: '视频生成任务失败',
        data:
          sourceInfo?.adIds?.map((adId: string) => ({
            ad_id: Number(adId),
            is_success: false,
            message: '视频生成任务失败',
            trace_id: null,
            creatives: [
              {
                ad_text: '',
                vod_url: '',
                cover_url: '',
              },
            ],
          })) || [],
        subTasks: validatedData.subTasks,
      };

      logger.info('格式化后的失败返回数据:', JSON.stringify(failedFormattedData));

      // 发送失败回调
      logger._push('发送失败回调请求');
      const failedCallbackResponse = await fetch(`${baseUrl}/api/create/callBack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(failedFormattedData),
      });

      if (!failedCallbackResponse.ok) {
        logger.error(
          '外部系统回调失败:',
          JSON.stringify({
            error: `外部系统回调失败 ${failedCallbackResponse.statusText}`,
            trace_id: validatedData.taskId,
            task_id: sourceInfo?.adsBatchCreateTaskId,
            formattedData: failedFormattedData,
          }),
        );
        throw new Error(`外部系统回调失败 ${failedCallbackResponse.statusText}`);
      }

      logger._end('失败回调处理完成');
      return NextResponse.json({
        success: false,
        message: '失败回调处理成功',
        data: failedFormattedData,
      });
    }

    const generatedMaterialIds = successfulGenerationTasks.flatMap((task) => task.generated_material_ids as string[]);
    logger.debug('生成的素材IDs:', generatedMaterialIds);

    // 获取所有生成的素材信息
    const allGeneratedMaterialIds = successfulGenerationTasks
      .flatMap((task) => task.generated_material_ids || [])
      .filter(Boolean);

    logger.info('所有生成的素材ID数量:', allGeneratedMaterialIds.length);

    // 检查素材数量是否与广告ID数量匹配
    const totalAdIds = sourceInfo.productInfo.reduce((sum: number, group: ProductGroup) => {
      return sum + group.ad_ids.length;
    }, 0);
    logger.info('totalAdIds =>>>>>>', totalAdIds);

    let generatedMaterials: materials[] = [];
    if (allGeneratedMaterialIds.length > 0) {
      generatedMaterials = await prisma.materials.findMany({
        where: {
          id: {
            in: allGeneratedMaterialIds,
          },
        },
      });
    }

    logger.info('generatedMaterials =>>>>>>', JSON.stringify(generatedMaterials.length));

    // 获取文案描述
    const batchGenerateResults = await getBatchProductDescriptions(baseNextUrl, sourceInfo, logger);
    logger.info('批量生成文案描述结果:', JSON.stringify(batchGenerateResults));

    // 创建 adId 到 title 的映射
    const adIdToTitleMap = new Map<string, string>();
    sourceInfo.productInfo.forEach((group: ProductGroup, groupIndex: number) => {
      const groupResults = batchGenerateResults.find((r) => r.group_id === group.group_id);
      group.ad_ids.forEach((adId, index) => {
        adIdToTitleMap.set(adId, groupResults?.titles[index] || '');
      });
    });
    logger.info('adIdToTitleMap:', JSON.stringify(Array.from(adIdToTitleMap.entries())));

    const formattedData = {
      task_id: sourceInfo.adsBatchCreateTaskId,
      data: sourceInfo.productInfo.flatMap((group: ProductGroup, groupIndex: number) => {
        const groupResults = batchGenerateResults.find((r) => r.group_id === group.group_id);

        // 计算当前组之前的所有ad数量
        const previousGroupsAdCount = sourceInfo.productInfo
          .slice(0, groupIndex)
          .reduce((sum: number, g: ProductGroup) => sum + g.ad_ids.length, 0);

        logger.info('adIds =>>>>>>', group.ad_ids);
        return group.ad_ids.map((adId, index) => {
          // 获取对应索引的素材
          const materialIndex = previousGroupsAdCount + index;
          // 如果素材索引超出了可用素材数量，则标记为失败
          if (materialIndex >= generatedMaterials.length) {
            return {
              ad_id: Number(adId),
              is_success: false,
              message: '未能生成对应的素材',
              trace_id: null,
              creatives: [
                {
                  ad_text: groupResults?.titles[index] || '',
                  vod_url: '',
                  cover_url: '',
                },
              ],
            };
          }

          const material = generatedMaterials[materialIndex];
          // 找到对应的subTask(用于获取trace_id)
          const subTask = allGenerationSUbTasks.find((task) =>
            task.generated_material_ids?.includes(material?.id as string),
          );

          const title = groupResults?.titles[index] || '';

          const failureReason = getFailureReason(subTask, material, title, adId);
          const isSuccess = failureReason === 'ok';

          return {
            ad_id: Number(adId),
            is_success: isSuccess,
            ...(failureReason && { message: failureReason }),
            trace_id: subTask?.trace_id || null,
            creatives: [
              {
                ad_text: title || '',
                vod_url: material?.vod_media_url || '',
                cover_url: material?.vod_cover_url || '',
              },
            ],
          };
        });
      }),
    };

    logger.info('格式化后的返回数据:', JSON.stringify(formattedData));

    // 发送回调
    logger._push('发送回调请求');
    const callbackResponse = await fetch(`${baseUrl}/api/create/callBack`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formattedData),
    });

    if (!callbackResponse.ok) {
      logger.error(
        '外部系统回调失败:',
        JSON.stringify({
          error: `外部系统回调失败 ${callbackResponse.statusText}`,
          trace_id: sourceInfo?.adsBatchCreateTaskId,
          formattedData: formattedData,
          subTasks: validatedData.subTasks,
        }),
      );
      throw new Error(`外部系统回调失败 ${callbackResponse.statusText}`);
    }
    logger._pop();

    logger._end('回调处理完成');
    return NextResponse.json({
      success: true,
      message: '回调处理成功',
      data: formattedData,
    });
  } catch (error) {
    logger.error('详细错误信息:', {
      message: error instanceof Error ? error.message : '未知错误',
      stack: error instanceof Error ? error.stack : undefined,
    });
    logger.error('处理生成任务回调失败:', error instanceof Error ? error.message : '未知错误');
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '处理回调请求失败',
      },
      { status: 500 },
    );
  }
}

async function getBatchProductDescriptions(
  baseNextUrl: string,
  sourceInfo: { productInfo: ProductGroup[] },
  logger: Logger,
): Promise<GroupedResult[]> {
  try {
    const productGroups: ProductGroup[] = sourceInfo.productInfo || [];
    const results: GroupedResult[] = [];
    const MAX_RETRIES = 2; // 最大重试次数（1次初始请求 + 1次重试）
    const RETRY_DELAY = 1000; // 重试延迟时间（毫秒）

    // 添加延迟函数
    const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

    // 对每个产品组分别调用生成接口
    for (const group of productGroups) {
      let attempt = 0;
      let success = false;
      let lastError: Error | null = null;

      while (attempt < MAX_RETRIES && !success) {
        try {
          if (attempt > 0) {
            logger.info(`正在进行第 ${attempt} 次重试，group_id: ${group.group_id}`);
            await delay(RETRY_DELAY);
          }

          const batchResponse = await fetch(`${baseNextUrl}/api/llm/chat/batch-generate-product-description`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              productName: group?.product_names?.join(',') ?? '',
              count: group.ad_ids.length,
              language: 'en',
            }),
          });

          if (!batchResponse.ok) {
            throw new Error(`批量生成请求失败: ${batchResponse.statusText}`);
          }

          const responseData: DifyBatchResponse = await batchResponse.json();
          let titles: string[] = [];
          logger.info(`批量生成请求成功 ${group.group_id}`, JSON.stringify(responseData));

          if (responseData?.data?.outputs?.data) {
            const parsedData = JSON.parse(responseData.data.outputs.data);
            titles = Array.isArray(parsedData) ? parsedData.map((item) => item.title || '') : [];
          } else {
            throw new Error('响应数据格式不正确');
          }

          results.push({
            group_id: group.group_id,
            titles,
          });

          success = true;
        } catch (error) {
          lastError = error as Error;
          logger.warn(`批量生成请求失败 (尝试 ${attempt + 1}/${MAX_RETRIES})`, {
            error: lastError.message,
            group_id: group.group_id,
          });
          attempt++;
        }
      }

      if (!success) {
        logger.error(`所有重试都失败了，group_id: ${group.group_id}`, {
          error: lastError?.message,
        });
        // 如果所有重试都失败，添加空结果
        results.push({
          group_id: group.group_id,
          titles: Array(group.ad_ids.length).fill(''),
        });
      }
    }

    return results;
  } catch (error) {
    console.error('获取批量产品描述失败:', error);
    // 返回空结果
    return sourceInfo?.productInfo?.map((group: ProductGroup) => ({
      group_id: group.group_id,
      titles: Array(group.ad_ids.length).fill(''),
    }));
  }
}

function getFailureReason(subTask: any, material: any, title: string, adId: any): string {
  if (subTask?.status !== TaskStatus.SUCCESS) {
    return '任务执行失败';
  }

  if (!material?.vod_media_url) {
    return '素材视频地址为空';
  }

  if (!material?.vod_cover_url) {
    return '素材封面地址为空';
  }

  if (!title) {
    return '文案为空';
  }

  if (!adId) {
    return '广告ID为空';
  }

  return 'ok';
}
