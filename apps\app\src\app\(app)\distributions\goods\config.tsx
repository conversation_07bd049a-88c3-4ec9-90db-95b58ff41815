import { GOODS_STATUS } from '@/common/goods';
import { confirm } from '@/components/ConfirmDialog';
import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { Badge, Button, Input } from '@/components/ui';
import { pageGoods, removeGoods } from '@/services/actions/goods';
import { cn } from '@/utils/cn';
import { action, ActionParams, ActionResult } from '@/utils/server-action/action';
import { convertBigintToString } from '@roasmax/utils';
import toast from 'react-hot-toast';

export const createInbuiltColumns = (config: {
  onRefreshNeeded: () => void;
}): ProTableColumnType<ActionResult<typeof pageGoods>['list'][number]>[] => {
  return [
    { key: 'images', title: '商品图片', type: 'images', fixed: 'left' },
    { key: 'hot_product', title: '爆品', type: 'boolean', editable: true, render: (value) => (value ? '爆品' : '') },
    { key: 'ip', title: 'IP', editable: true },
    {
      key: 'name',
      title: '商品名',
      editable: true,
      width: 260,
      render: (value) => <div className="max-w-[260px] text-wrap">{value}</div>,
    },
    {
      key: 'status',
      title: '状态',
      editable: true,
      type: 'enum',
      config: {
        options: GOODS_STATUS.map(({ key, text }) => ({ value: key, label: text })),
      },
      render: (v) => {
        const status = GOODS_STATUS.find((s) => s.key === v) || GOODS_STATUS[GOODS_STATUS.length - 1]!;
        return (
          <Badge variant="outline" style={{ backgroundColor: status.color }} className={cn('text-xs font-normal')}>
            {status.text}
          </Badge>
        );
      },
    },
    { key: 'alias', title: '别名', editable: true, type: 'tags' },
    { key: 'tags', title: '商品标签', editable: true, type: 'tags' },
    { key: 'live_rooms', title: '直播间', type: 'tags', editable: true, width: 200 },
    { key: 'link', title: '推广链接', editable: true, type: 'link' },
    {
      key: 'sub_tasks_count',
      title: '视频数量',
      width: 260,
      render: (_, record) => {
        const generated = record.video_distribution_sub_tasks_generated_count || 0;
        const wait = record.video_distribution_sub_tasks_wait_count || 0;
        const done = record.video_distribution_sub_tasks_done_count || 0;
        return (
          <div className="grid grid-cols-4 gap-1 text-right">
            <div className="grid grid-rows-2 gap-1">
              <div className="text-gray-500">已出片</div>
              <div className="text-sm">{convertBigintToString(generated)}</div>
            </div>
            <div className="grid grid-rows-2 gap-1">
              <div className="text-gray-500">已成片</div>
              <div className="text-sm">{convertBigintToString(wait)}</div>
            </div>
            <div className="grid grid-rows-2 gap-1">
              <div className="text-gray-500">已分发</div>
              <div className="text-sm">{convertBigintToString(done)}</div>
            </div>
          </div>
        );
      },
    },
    { key: 'product_url', title: '商品链接', type: 'link' },
    {
      key: 'platform',
      title: '平台',
      type: 'enum',
      config: {
        options: [
          { value: 'baiyin', label: '百应' },
          { value: 'chanxuan', label: '蝉选' },
          { value: 'chanjian', label: '蝉剪' },
          { value: 'merged', label: '合并' },
        ],
      },
    },
    {
      key: 'change_ip_time',
      title: '变更记录备注',
      dataIndex: ['properties', 'change_ip_time'],
    },
    {
      key: 'yesterday_sales_amount',
      title: '销量',
      dataIndex: ['properties', 'yesterday_sales_amount'],
    },
    {
      key: 'sales_amount_in_one_week',
      title: '7 日销量',
      dataIndex: ['properties', 'sales_amount_in_one_week'],
    },
    { key: 'product_identity', title: '商品ID' },
    { key: 'product_status1', title: '商品状态1', dataIndex: ['properties', 'product_status1'] },
    { key: 'product_status2', title: '商品状态2', dataIndex: ['properties', 'product_status2'] },
    { key: 'shop_name', title: '店铺名', editable: true },
    { key: 'price', title: '价格', type: 'currency' },
    { key: 'stock_amount', title: '库存', editable: true },
    { key: 'commission_rate', title: '佣金率', type: 'percent', editable: true },
    {
      key: 'material_origin',
      title: '素材来源',
      dataIndex: ['properties', 'material_origin'],
    },
    { key: 'remark', title: '备注', editable: true },
    {
      key: 'action',
      title: '操作',
      fixed: 'right',
      render: (_, row) => {
        return (
          <div className="flex gap-2">
            <Button
              variant="link"
              className="text-xs"
              onClick={async () => {
                window.open(`bw-file://L:/${row.tenant_id}/自动分发/生成素材/${row.ip}/${row.name}`);
              }}
            >
              查看素材
            </Button>
            <Button
              variant="link"
              size="sm"
              onClick={() => {
                confirm({
                  content: '确定删除该商品吗？',
                  buttonText: { confirm: '删除' },
                  onConfirm: async () => {
                    const res = await action(removeGoods, { goodsIds: [row.id] }, { errorType: 'return' });
                    if (!res?.success) {
                      toast.error(res?.message || '删除失败');
                      return;
                    }
                    toast.success('删除成功');
                    config.onRefreshNeeded();
                  },
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
};

export const filterColumns: ProFilterColumnType<NonNullable<ActionParams<typeof pageGoods>['filters']>>[] = [
  {
    key: 'ip',
    title: 'IP',
    type: 'text',
  },
  {
    key: 'name',
    title: '商品名称',
    type: 'text',
  },
  {
    key: 'status',
    title: '状态',
    type: 'enum',
    config: {
      options: GOODS_STATUS.map(({ key, text }) => ({ value: key, label: text })),
    },
  },
  {
    key: 'video_distribution_sub_tasks_generated_count',
    title: '出片数量(大于等于)',
    type: 'number',
  },
  {
    key: 'video_distribution_sub_tasks_wait_count',
    title: '成片数量(大于等于)',
    type: 'number',
  },
  {
    key: 'video_distribution_sub_tasks_done_count',
    title: '分发数量(大于等于)',
    type: 'number',
  },
  {
    key: 'tags',
    title: '商品标签',
    type: 'tags',
    render: (value, onChange) => (
      <Input
        className={cn('h-[32px] w-full')}
        value={value as string}
        onChange={(e) => onChange(e.target.value)}
        placeholder="商品标签"
      />
    ),
  },
  {
    key: 'product_identity',
    title: '商品ID',
    type: 'text',
  },
  {
    key: 'product_url',
    title: '商品链接',
    type: 'text',
  },
  {
    key: 'hot_product',
    title: '爆品标签',
    type: 'boolean',
    config: {
      switch: [
        { checked: true, value: 1, label: '爆品' },
        { checked: false, value: 0, label: '其他' },
      ],
    },
  },
  {
    key: 'shop_name',
    title: '店铺名称',
    type: 'text',
  },
  {
    key: 'remark',
    title: '备注',
    type: 'text',
  },
  {
    key: 'platform',
    title: '平台',
    type: 'enum',
    config: {
      options: [
        { value: 'baiyin', label: '百应' },
        { value: 'chanxuan', label: '蝉选' },
        { value: 'chanjian', label: '蝉剪' },
        { value: 'merged', label: '合并' },
      ],
    },
  },
];
