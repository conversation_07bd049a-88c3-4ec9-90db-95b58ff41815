import { getVideoGenerationTaskById } from '@/services/actions/video-generation-task';
import { Logger } from '@roasmax/utils';
import { NextRequest, NextResponse } from 'next/server';

export const POST = async (request: NextRequest) => {
  const logger = new Logger('video-generation', 'api', request.headers.get('x-request-id') || 'unknown');

  try {
    logger._start('开始处理视频生成任务查询请求');
    const { task_id } = await request.json();
    logger.debug('收到请求数据', { task_id });

    if (!task_id) {
      logger.warn('缺少必要参数 task_id');
      return NextResponse.json({ error: 'task_id is required' }, { status: 400 });
    }

    logger.info('开始查询视频生成任务', { task_id });
    const result = await getVideoGenerationTaskById({
      data: { taskId: task_id },
    });

    logger.info('视频生成任务查询成功', { task_id });
    logger._end('视频生成任务查询请求处理完成');
    return NextResponse.json(result);
  } catch (error) {
    logger.error('视频生成任务查询失败', (error as Error).message);
    logger._end('视频生成任务查询请求处理失败');
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
};
