import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui';
import { FormMultiSelect } from '@/components/ui/FormMultiSelect';
import { AdFormValues } from '../AdSheet/adFormSchema';
import { UseFormReturn } from 'react-hook-form';
import { SectionTitle } from '../SectionTitle';

interface AdGroupFieldProps {
  form: UseFormReturn<AdFormValues>;
  currentAdGroup: any[];
}

export const AdGroupField = ({ form, currentAdGroup }: AdGroupFieldProps) => {
  return (
    <div>
      <SectionTitle title="已选广告组" />

      <div className="pl-10">
        <FormField
          control={form.control}
          name="adgroupIds"
          rules={{ required: '请选择广告组' }}
          render={({ field }) => (
            <FormItem className="mb-10 flex h-8 items-center">
              <FormLabel className="w-1/5 text-sm text-white">
                广告组
                <span className="ml-2 text-red-500">*</span>
              </FormLabel>

              <FormControl>
                <FormMultiSelect
                  disabled={true}
                  name="adgroupIds"
                  control={form.control}
                  options={
                    currentAdGroup
                      ?.filter((item) => item.source === 'ADS')
                      ?.map((item) => ({
                        label: item.groupName,
                        value: item.groupId,
                      }))
                      .filter((item) => item.value) ?? []
                  }
                  defaultValue={currentAdGroup?.map((item) => item.groupId) || []}
                  placeholder="选择广告组"
                  placeholderTitle="广告组"
                  rules={{ required: '请选择广告组' }}
                  required={true}
                  onValueChange={(values) => {
                    const selectedGroupIds =
                      currentAdGroup
                        ?.filter((item) => values.includes(item.groupId))
                        .map((item) => item.groupId)
                        .filter(Boolean) || [];
                    form.setValue('adgroupIds', selectedGroupIds);
                  }}
                  maxCount={1}
                  className="w-[360px]"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
