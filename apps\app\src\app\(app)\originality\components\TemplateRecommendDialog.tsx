'use client';
import { ProButton } from '@/components/pro/pro-button';
import { useProductAnalysis } from '@/hooks/useProductAnalysis';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui';
import { fetchMatchedVideoTemplates } from '@/services/actions/video-generation-task';
import { useAction } from '@/utils/server-action/action';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { AnalysisPanel } from '../../viral/components/AnalysisPanel';
// import { LoadingProgress } from '../../viral/components/LoadingProgress';

export type TemplateRecommendDialogRef = {
  show: (params: { productUrl: string }) => void;
};

type TemplateRecommendDialogProps = {
  onConfirm: (params: { templateIds: string[] }) => void;
};

// 修改类型定义，使用英文键名
type RecommendType = {
  contentMatchAnalysis: string;
  keyElementsToImitate: string;
  suggestedOptimizations: string;
};

const TemplateRecommendDialog = React.forwardRef<TemplateRecommendDialogRef, TemplateRecommendDialogProps>(
  (props, ref) => {
    const [open, setOpen] = useState<boolean>(false);
    const { isLoading, isVideoLoading, analysisResult, handleAnalyze, handleCancelAnalyze, completedSteps } =
      useProductAnalysis();

    const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);

    useImperativeHandle(ref, () => {
      return {
        show: (params) => {
          setOpen(true);
          handleAnalyze(params.productUrl);
        },
      };
    });

    const handleOpenChange = (open: boolean) => {
      setOpen(open);
    };

    const keyMapping: Record<keyof RecommendType, string> = {
      contentMatchAnalysis: '内容匹配度分析',
      keyElementsToImitate: '重点模仿元素',
      suggestedOptimizations: '建议优化',
    };

    const getBackendKey = (key: keyof RecommendType): string => {
      return keyMapping[key];
    };

    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="min-w-[1080px] rounded-[16px] bg-[#151C29]">
          <DialogHeader className="flex flex-shrink-0 items-center justify-center">
            <DialogTitle className="font-medium">视频模版</DialogTitle>
          </DialogHeader>
          {isLoading ? (
            <div className="h-[622px] w-full">
              {/* <LoadingProgress isLoading={isLoading} completedSteps={completedSteps} /> */}
            </div>
          ) : (
            <>
              <div className="h-[622px] w-full overflow-y-auto">
                <AnalysisPanel
                  selectedTemplates={selectedTemplates}
                  setSelectedTemplates={setSelectedTemplates}
                  isLoading={isLoading}
                  analysisResult={analysisResult}
                />
              </div>
              <DialogFooter>
                <ProButton
                  variant="outline"
                  onClick={() => {
                    handleCancelAnalyze();
                    setOpen(false);
                  }}
                >
                  取消
                </ProButton>
                <ProButton
                  onClick={() => {
                    props.onConfirm({ templateIds: selectedTemplates });
                    setOpen(false);
                  }}
                >
                  使用 ({selectedTemplates.length} / {analysisResult?.template_info?.length})
                </ProButton>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  },
);

TemplateRecommendDialog.displayName = 'TemplateRecommendDialog';

export default TemplateRecommendDialog;
