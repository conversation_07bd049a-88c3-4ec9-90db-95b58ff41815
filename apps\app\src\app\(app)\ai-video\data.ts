import type { VideoType } from './types';

// API数据缓存，从default接口获取
let apiDataCache: Record<string, any> | null = null;

// 视频类型选项 - 基于API数据动态生成
export const getVideoTypeOptions = () => {
  if (!apiDataCache) return [];

  return Object.entries(apiDataCache).map(([key, value]) => ({
    value: key as VideoType,
    label: value.description || key,
    description: value.detailDescription || value.description || key,
  }));
};

// 设置API数据缓存
export const setApiDataCache = (data: Record<string, any>) => {
  apiDataCache = data;
};

// 获取API数据缓存
export const getApiDataCache = () => apiDataCache;

// 获取视频类型的描述
export const getVideoTypeDescription = (type: VideoType): string => {
  if (!apiDataCache) return type;
  return apiDataCache[type]?.description || type;
};

// 获取视频类型的详细描述
export const getVideoTypeDetailDescription = (type: VideoType): string => {
  if (!apiDataCache) return type;
  return apiDataCache[type]?.detailDescription || apiDataCache[type]?.description || type;
};

// 获取预设提示词
export const getPresetPrompts = (type: VideoType): string[] => {
  if (!apiDataCache) return [];
  return apiDataCache[type]?.presetPrompts || [];
};

// 获取视频标题（优先中文，没有则使用英文标题）
export const getVideoTitle = (type: VideoType): string => {
  if (!apiDataCache) return type;
  const data = apiDataCache[type];
  return data?.title_zh || `${type.charAt(0).toUpperCase() + type.slice(1)} Video`;
};

// 获取视频描述（优先中文，没有则使用英文描述）
export const getVideoDescription = (type: VideoType): string => {
  if (!apiDataCache) return type;
  const data = apiDataCache[type];
  return data?.description_zh || data?.detailDescription || data?.description || type;
};

// 获取视频比例
export const getVideoAspectRatio = (type: VideoType): string => {
  if (!apiDataCache) return '16:9';
  const data = apiDataCache[type];
  return data?.aspect_ratio || '16:9';
};

// 获取视频引擎
export const getVideoEngine = (type: VideoType): string => {
  if (!apiDataCache) return 'unknown';
  const data = apiDataCache[type];
  return data?.engine || 'unknown';
};

// 获取引擎的显示名称
export const getEngineDisplayName = (engine: string): string => {
  const engineMap: Record<string, string> = {
    mj: 'Midjourney',
    veo: 'Google Veo',
    midjourney: 'Midjourney',
    'google-veo': 'Google Veo',
  };
  return engineMap[engine.toLowerCase()] || engine;
};

// 获取所有预设提示词（按类型分组）
export const getAllPresetPrompts = (): Record<VideoType, string[]> => {
  if (!apiDataCache) return {} as Record<VideoType, string[]>;

  const result: Record<string, string[]> = {};
  Object.entries(apiDataCache).forEach(([key, value]) => {
    result[key] = value.presetPrompts || [];
  });
  return result as Record<VideoType, string[]>;
};
