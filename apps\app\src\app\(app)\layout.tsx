'use client';

import { ConfirmDialog } from '@/components/ConfirmDialog';
import { DownloadingModal } from '@/components/Downloading';
// import Header from '@/components/Header';
import MenuSideBar from '@/components/MenuSideBar';
import { useSidebar } from '@/components/ui';
import { UploadListModal } from '@/components/UploadListModal';
import UserGuide from '@/components/UserGuide';
import { TaskCreatorProvider } from '@/hooks/useTaskCreator';
import { cn } from '@/utils/cn';
import { dispatchResize, sleep } from '@/utils/common';
import { useLayoutEffect } from 'react';
import { Toaster } from 'react-hot-toast';

const RootLayout = ({ children }: React.PropsWithChildren) => {
  const { state } = useSidebar();

  useLayoutEffect(() => {
    sleep(200).then(() => {
      dispatchResize();
    });
  }, [state]);

  return (
    <div className="flex h-[100vh] w-[100vw]">
      <TaskCreatorProvider>
        <div className="flex w-full">
          <MenuSideBar />
          <div className={cn('flex flex-1 flex-col', state === 'expanded' ? 'w-full' : 'w-[calc(100vw-16rem)]')}>
            <UserGuide />
            {/* <Header /> */}
            <main className="flex-1">{children}</main>
          </div>
        </div>
        <DownloadingModal />
        <UploadListModal />
        <Toaster
          toastOptions={{
            className: 'custom-toast',
            duration: 3000,
            success: {
              iconTheme: {
                primary: '#60D2A7',
                secondary: 'black',
              },
            },
          }}
        />
        <ConfirmDialog />
      </TaskCreatorProvider>
    </div>
  );
};

export default RootLayout;
