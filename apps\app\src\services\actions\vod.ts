'use server';
import { translateSubtitles, getSubtitles } from '@/services/domains/vod';
import { calcPlayAuthToken, calcUploadSignature, vod } from '@/utils/vod';
import { ActionContext, server } from '@roasmax/serve';
/**
 * 获取多个视频的信息
 * @deprecated 使用 materials/getMaterialInfo
 */
export const getVodMediaInfoList = server(
  '获取多个视频的信息',
  async (ctx: ActionContext<{ appId: number; fileIds: string[] }>) => {
    if (!ctx.data.fileIds?.length || !ctx.data.appId) {
      throw new Error('fileId or appId is null');
    }

    // TODO: 原逻辑中，res.MediaInfoSet.[].BasicInfo， 即类型 MediaBasicInfo，在此处逻辑实现时额外扩展了类型 { IntranetMediaUrl?: string }。但是这个字段又没有用到。这个字段是哪里来的？是确认了vod库的类型缺少了这个字段吗？
    const res = await vod.DescribeMediaInfos({
      SubAppId: ctx.data.appId,
      FileIds: ctx.data.fileIds,
    });
    return res;
  },
);

/**
 * 获取单个视频的信息
 * @deprecated 使用 materials/getMaterialInfo
 */
export const getVodMediaInfo = server(
  '获取单个视频的信息',
  async (ctx: ActionContext<{ appId: number; fileId: string }>) => {
    const res = await vod.DescribeMediaInfos({
      SubAppId: ctx.data.appId,
      FileIds: [ctx.data.fileId],
    });
    if (!res.MediaInfoSet?.length) {
      throw new Error('未找到对应媒体信息');
    }
    return res.MediaInfoSet[0];
  },
);

/**
 * 获取单个视频的播放密钥
 * 这个接口的数据本来是和获取播放路径的接口合并的，应该是为了用来解密被加密的播放链接
 * 但是看目前的逻辑中，播放链接是公开的不需要解密，所以这个接口就以独立的形式存在，以备后续可能的需求
 * @deprecated 没有使用
 */
export const getVodMediaPlayAuthToken = server(
  '获取单个视频的播放密钥',
  async (ctx: ActionContext<{ appId: number; fileId: string }>) => {
    return calcPlayAuthToken({ appId: ctx.data.appId, fileId: ctx.data.fileId });
  },
);

/**
 * 获取上传签名
 */
export const getUploadSignature = server('获取原始素材上传签名', async (ctx: ActionContext<undefined>) => {
  if (!ctx.tenant.config) {
    throw new Error('未找到租户配置信息');
  }
  return calcUploadSignature({
    subAppId: ctx.tenant.config.vod_sub_app_id,
    classId: ctx.tenant.config.vod_c_screen_record_original,
  });
});

/**
 * 获取字幕
 */
export const getVodMediaSubtitle = server('获取字幕', async (ctx: ActionContext<{ fileId: string }>) => {
  const vodFileIds = [ctx.data.fileId];
  const captions = await ctx.execute(getSubtitles, {
    vodFileIds,
  });
  return captions;
});
/**
 * 翻译字幕
 */
export const translateVodMediaSubtitle = server(
  '翻译字幕',
  async (ctx: ActionContext<{ captions: string; languages: string }>) => {
    const subtitles = await ctx.execute(translateSubtitles, {
      captions: ctx.data.captions,
      languages: ctx.data.languages,
    });
    return subtitles;
  },
);
