import React from 'react';

export default function OfficialWebsiteIcon() {
  return (
    <button
      type="submit"
      className="group relative isolation-auto z-10 mx-auto flex h-10 items-center justify-center gap-2 overflow-hidden rounded-lg border bg-[#CCDDFF1A] px-4 text-xs shadow-xl backdrop-blur-md before:absolute before:-left-full before:-z-10 before:aspect-square before:w-full before:rounded-full before:bg-[linear-gradient(90deg,#54FFE0_0%,#00E1FF_20%,#9D81FF_100%)] before:text-black before:transition-all before:duration-700 hover:text-black before:hover:left-0 before:hover:w-full before:hover:scale-150 before:hover:duration-700 lg:font-semibold"
    >
      ROASMAX官网
      <svg
        className="h-7 w-7 rotate-45 justify-end rounded-full border border-gray-700 p-2 text-gray-50 duration-300 ease-linear group-hover:rotate-90 group-hover:border-none group-hover:bg-[#070F1F]"
        viewBox="0 0 16 19"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7 18C7 18.5523 7.44772 19 8 19C8.55228 19 9 18.5523 9 18H7ZM8.70711 0.292893C8.31658 -0.0976311 7.68342 -0.0976311 7.29289 0.292893L0.928932 6.65685C0.538408 7.04738 0.538408 7.68054 0.928932 8.07107C1.31946 8.46159 1.95262 8.46159 2.34315 8.07107L8 2.41421L13.6569 8.07107C14.0474 8.46159 14.6805 8.46159 15.0711 8.07107C15.4616 7.68054 15.4616 7.04738 15.0711 6.65685L8.70711 0.292893ZM9 18L9 1H7L7 18H9Z"
          className="fill-gray-50 group-hover:fill-gray-50"
        ></path>
      </svg>
    </button>
  );
}
