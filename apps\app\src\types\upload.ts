import { MaterialType } from './material';

export enum UploadStatus {
  UPLOADING = 1,
  SUCCESS = 2,
  FAILED = 3,
  VALIDATE_FAILED = 4,
  CANCELED = 5,
}

export const uploadStatusText = {
  [UploadStatus.UPLOADING]: '上传中',
  [UploadStatus.SUCCESS]: '上传成功',
  [UploadStatus.FAILED]: '上传失败',
  [UploadStatus.VALIDATE_FAILED]: '验证失败',
  [UploadStatus.CANCELED]: '已取消',
};

export interface UploadItemType extends MaterialType {
  id: string;
  status: UploadStatus;
  progress: number;
  name: string;
  url: string;
  coverUrl?: string; // 封面
  file: File | null;
  type: 'video'; // 目前只有一种素材类型
  duration?: number;
  message?: string;
  material?: MaterialType | null; // 素材信息
}
