'use client';

import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'react-hot-toast';

import tiktokService from '@/services/tiktokService';

const Auth = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const handleAuth = async () => {
      try {
        const authCode = searchParams?.get('auth_code');

        if (!authCode) {
          router.replace('/ads');
          return;
        }

        const { list = [] } = await tiktokService.getTkAccount({ authCode });

        if (!list || !list.length) {
          toast.error('授权失败，请重试');
          return;
        }
        
        toast.success('授权成功');

        if (window.opener) {
          window.close();
        } else {
          router.replace('/ads');
        }
      } catch (error) {
        console.error('授权失败:', error);
        toast.error('授权失败，请重试');

        if (window.opener) {
          window.close();
        } else {
          router.replace('/ads');
        }
      }
    };

    handleAuth();
  }, [searchParams, router]);

  return (
    <div className="flex h-full items-center justify-center">
      <p>正在处理授权，请稍候...</p>
    </div>
  );
};

export default Auth;
