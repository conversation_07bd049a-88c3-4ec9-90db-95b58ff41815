import { addMaterial } from '@/services/actions/materials';
import { getUploadSignature } from '@/services/actions/vod';
import useMaterialStore from '@/store/materialStore';
import { UploadItemType, UploadStatus } from '@/types/upload';
import {
  checkConnection,
  checkHasAudioTrack,
  checkIsMP4,
  getFileNameAndExtension,
  getVideoMetadata,
  sleep,
} from '@/utils/common';
import { action } from '@/utils/server-action/action';
import { UploadManager } from '@/utils/uploadManager';
import { materials } from '@roasmax/database';
import { to } from '@roasmax/utils';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useCloudStorageSize } from './useWallet';
import { useTaskCreator } from './useTaskCreator';

export type ProgressInfoType = {
  loaded: number;
  total: number;
  speed: number;
  percent: number;
};

const MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024;

export type UploadFinishCallback<Metadata> = (material: materials, metadata?: Metadata) => void;

const checkFileValid = async (
  file: File,
  options?: { checkVideoWidthAndHeight?: boolean; checkVideoBitrate?: boolean; checkVideoDuration?: boolean },
) => {
  if (!checkIsMP4(file)) {
    toast.error(`文件 "${file.name}" 不是有效的视频文件`);
    return false;
  }
  if (file.size > MAX_FILE_SIZE) {
    toast.error('文件大小不能超过10GB');
    return false;
  }

  if (getFileNameAndExtension(file).fileName.length > 36) {
    toast.error('文件名长度不能超过36个字符');
    return false;
  }
  if (getFileNameAndExtension(file).fileName.includes(' ')) {
    toast.error('文件名不能包含空格，无法上传。');
    return false;
  }

  if (!getFileNameAndExtension(file).fileName.match(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/)) {
    toast.error('文件名中包含特殊字符，无法上传。');
    return false;
  }

  if (getFileNameAndExtension(file).fileName.includes(' ')) {
    toast.error('文件名不能包含空格，无法上传。');
    return false;
  }

  if (options?.checkVideoWidthAndHeight || options?.checkVideoBitrate || options?.checkVideoDuration) {
    const { width, height, bitrate, duration } = await getVideoMetadata(file);
    if (options?.checkVideoWidthAndHeight && width > height && width > 1920) {
      toast.error('视频分辨率不能超过1920*1080');
      return false;
    }
    if (options?.checkVideoWidthAndHeight && width < height && height > 1920) {
      toast.error('视频分辨率不能超过1080*1920');
      return false;
    }
    if (options?.checkVideoBitrate && bitrate > 18 * 1024 * 1024) {
      toast.error('视频码率不能超过18Mbps，请将视频压缩后重新上传');
      return false;
    }
    if (options?.checkVideoDuration && duration > 8 * 60 * 60) {
      toast.error('视频时长不能超过8小时');
      return false;
    }
  }
  return true;
};

export const useUpload = <Metadata = any>(onFinish?: UploadFinishCallback<Metadata>) => {
  const { storage, refresh: refreshCloudStorageSize } = useCloudStorageSize();
  const { uploadTargetDir, uploadList, addUploadItem } = useMaterialStore();
  const { form } = useTaskCreator();

  useEffect(() => {
    refreshCloudStorageSize();
  }, []);
  const onProgress = (info: ProgressInfoType, id: string, done?: boolean) => {
    const { percent } = info;

    useMaterialStore.setState((state) => {
      let progress = percent * 100;
      // 前 99% 是progress，最后 1%是留给入库，如果入库失败，即上传失败
      if (!done && progress === 100) {
        progress = 99;
      }
      const { uploadList } = state;

      // 通过 id 查找要更新的项目
      const itemIndex = uploadList.findIndex((item) => item.id === id);
      if (itemIndex !== -1) {
        const updatedItem = {
          ...uploadList[itemIndex],
          progress: done ? 100 : progress,
          status: done ? UploadStatus.SUCCESS : UploadStatus.UPLOADING,
        };

        // 创建新的 uploadList 数组,替换更新的项目
        const newUploadList = [...uploadList.slice(0, itemIndex), updatedItem, ...uploadList.slice(itemIndex + 1)];

        return { uploadList: newUploadList as UploadItemType[] };
      }

      return state; // 如果没有找到匹配的项目,返回原始状态
    });
  };

  const createMaterial = useCallback(
    async (params: {
      doneResult: any;
      uploadId: string;
      uploadTarDirId?: string;
      autoGenByTaskId?: string;
      name?: string;
      isAudios?: boolean;
    }) => {
      const { doneResult, uploadId, uploadTarDirId, name, isAudios } = params;
      const { fileId } = await doneResult;

      if (!uploadTarDirId && !uploadTargetDir?.id) {
        onError('未找到素材目录', uploadId);
        if (onFinish) {
          // @ts-ignore
          onFinish(null);
        }
        return;
      }

      const [err, res] = await to(
        action(
          addMaterial,
          {
            name,
            vodFileId: fileId,
            directoryId: uploadTarDirId ?? uploadTargetDir?.id ?? '',
            autoGenByTaskId: params.autoGenByTaskId,
            isAudio: isAudios,
          },
          {
            errorType: 'return',
          },
        ),
      );

      if (err || !res?.success) {
        toast.error('素材创建失败');
        onError('素材创建失败', uploadId);
        return;
      }

      const { data: material } = res;

      if (!material) {
        toast.error('素材创建失败');
        const currUploadList = useMaterialStore.getState().uploadList;
        const newUploadList = currUploadList.map((item) =>
          item.id === uploadId ? { ...item, status: UploadStatus.FAILED } : item,
        );
        useMaterialStore.setState({
          uploadList: newUploadList,
        });
        return;
      }

      const { vod_cover_url, vod_media_url } = material;
      useMaterialStore.setState((state) => {
        const { uploadList } = state;
        const itemIndex = uploadList.findIndex((item) => item.id === uploadId);
        if (itemIndex !== -1 && uploadList[itemIndex]) {
          const updatedItem: UploadItemType = {
            ...uploadList[itemIndex],
            type: 'video',
            url: vod_media_url,
            coverUrl: vod_cover_url,
            material,
          };
          const newUploadList = [...uploadList.slice(0, itemIndex), updatedItem, ...uploadList.slice(itemIndex + 1)];
          return { uploadList: newUploadList };
        }
        return state;
      });

      return material;
    },
    [uploadTargetDir?.id],
  );

  const getSignature = async () => {
    return action(getUploadSignature, undefined, { errorType: 'throw' });
  };

  const onError = (errMsg: string, id: string) => {
    useMaterialStore.setState((state) => {
      const { uploadList } = state;
      const itemIndex = uploadList.findIndex((item) => item.id === id);
      if (itemIndex !== -1 && uploadList[itemIndex]) {
        const updatedItem = { ...uploadList[itemIndex], status: UploadStatus.FAILED, message: errMsg ?? '' };
        const newUploadList = [...uploadList.slice(0, itemIndex), updatedItem, ...uploadList.slice(itemIndex + 1)];
        return { uploadList: newUploadList };
      }
      return state;
    });
  };

  const resetUploadStatus = (id: string) => {
    useMaterialStore.setState((state) => {
      const { uploadList } = state;
      const itemIndex = uploadList.findIndex((item) => item.id === id);
      if (itemIndex !== -1 && uploadList[itemIndex]) {
        const updatedItem = { ...uploadList[itemIndex], status: UploadStatus.UPLOADING, progress: 0 };
        const newUploadList = [...uploadList.slice(0, itemIndex), updatedItem, ...uploadList.slice(itemIndex + 1)];
        return { uploadList: newUploadList };
      }
      return state;
    });
  };

  const handleRetry = async (id: string, options?: { autoGenByTaskId?: string }) => {
    const item = uploadList.find((item) => item.id === id);
    if (!item) return;

    resetUploadStatus(id);
    const isNetworkAvailable = checkConnection();
    if (!isNetworkAvailable) {
      return;
    }
    const instance = UploadManager.getInstance(getSignature);

    try {
      // 检查文件有效性并设置isAudios状态
      let fileHasAudio = false;
      if (item.file) {
        try {
          fileHasAudio = await checkHasAudioTrack(item.file);
        } catch (err) {
          console.error('检查音轨失败:', err);
        }

        await checkFileValid(item.file, {
          checkVideoWidthAndHeight: form.getValues('method') === 'normal',
          checkVideoBitrate: form.getValues('method') === 'normal',
          checkVideoDuration: true,
        });
      }

      const uploaders = await instance.batchUpload([item], {
        handleValidateFailed: (failedItems) => {
          toast.error(failedItems.map((each) => each.message).join('\n'));
        },
        onError,
        onProgress: (info, uploadId) => {
          const isCanceled = uploadList.find((item) => item.id === uploadId)?.status === UploadStatus.CANCELED;

          if (isCanceled) {
            const uploader = uploaders?.find((u) => u && u?.id === uploadId);
            if (uploader) {
              uploader.cancel();
            }
          } else {
            onProgress(info, uploadId);
          }
        },
        onSuccess: async (doneResult, uploadId) => {
          await sleep(3000);
          const currentItem = uploadList.find((item) => item.id === uploadId);
          if (currentItem?.status === UploadStatus.CANCELED) {
            return;
          }

          console.log('使用音频检测结果:', fileHasAudio, 'for retry file:', currentItem?.name);

          const material = await createMaterial({
            doneResult,
            uploadId: uploadId,
            uploadTarDirId: uploadTargetDir?.id,
            autoGenByTaskId: options?.autoGenByTaskId,
            name: currentItem?.name ?? '',
            isAudios: fileHasAudio, // 使用检测结果而不是状态变量
          });
          if (material) {
            onProgress({ loaded: 100, total: 100, speed: 0, percent: 1 }, uploadId, true);
            refreshCloudStorageSize();
            await onFinish?.(material);
            // 清理文件对象
            useMaterialStore.setState((state) => {
              const { uploadList } = state;
              const itemIndex = uploadList.findIndex((item) => item.id === uploadId);
              if (itemIndex !== -1 && uploadList[itemIndex]) {
                const updatedItem = { ...uploadList[itemIndex], file: null };
                const newUploadList = [
                  ...uploadList.slice(0, itemIndex),
                  updatedItem,
                  ...uploadList.slice(itemIndex + 1),
                ];
                return { uploadList: newUploadList };
              }
              return state;
            });
          }
        },
      });

      if (!uploaders.length) {
        return;
      }
    } catch (error) {
      console.error('Retry upload failed:', error);
      toast.error('重试上传过程中发生错误');
      onError('重试上传失败', id);
    }
  };

  const handleFileChange = useCallback(
    async (
      e: React.ChangeEvent<HTMLInputElement>,
      options?: {
        openModal?: boolean;
        uploadTarDirId?: string;
        autoGenByTaskId?: string;
        shouldUnder40Second?: boolean;
      },
    ) => {
      if (!e.target.files?.length) return;
      const fileList = Array.from(e.target.files);

      const loadingToast = toast.loading('正在校验文件...', { duration: Infinity });
      useMaterialStore.setState({ uploadValidating: true });

      try {
        const validFiles = [];
        const audioStatusMap = new Map<string, boolean>(); // 存储每个文件的音频状态
        if (fileList.length > Math.max(0, 10 - uploadList.length)) {
          e.target.value = '';
          toast.dismiss(loadingToast);
          toast.error('最多同时上传 10 个素材');
          useMaterialStore.setState({ uploadValidating: false });
          return;
        }
        for (const file of fileList) {
          if (storage.used >= storage.total) {
            toast.error('云盘容量已达上限，请联系管理员');
            return false;
          }
          const isSameFileNameExist = uploadList.some((existingItem) =>
            fileList.some((newFile) => newFile.name === existingItem.name),
          );
          if (isSameFileNameExist) {
            toast.error('存在名称重复的文件');
            return false;
          }

          // 检查文件有效性并设置isAudios状态
          let hasAudioTrack = false;
          try {
            hasAudioTrack = await checkHasAudioTrack(file);
            console.log(hasAudioTrack, 'hasAudioTrack for file:', file.name);
            audioStatusMap.set(file.name, hasAudioTrack); // 存储检测结果
          } catch (err) {
            console.error('检查音轨失败:', err);
          }

          const isValid = await checkFileValid(file, {
            checkVideoWidthAndHeight: form.getValues('method') === 'normal',
            checkVideoBitrate: form.getValues('method') === 'normal',
            checkVideoDuration: true,
          });

          if (!isValid) {
            continue;
          }
          validFiles.push(file);
        }

        if (!validFiles.length) {
          e.target.value = '';
          toast.dismiss(loadingToast);
          useMaterialStore.setState({
            uploadValidating: false,
          });
          return;
        }

        if (validFiles.length !== fileList.length) {
          e.target.value = '';
        }

        toast.dismiss(loadingToast);
        useMaterialStore.setState({ uploadValidating: false });

        const instance = UploadManager.getInstance(getSignature);

        // 创建基础上传项,但不包含封面
        const uploadItems = validFiles.map((file) => {
          const id = Math.random().toString(36).substring(2, 15);
          return {
            id,
            name: file.name,
            type: 'video' as const,
            status: UploadStatus.UPLOADING,
            progress: 0,
            file,
            url: '',
            coverUrl: '', // 初始为空
            material: null,
          };
        }) as UploadItemType[];

        e.target.value = '';
        if (options?.openModal) {
          useMaterialStore.setState({
            uploadListModalOpen: true,
          });
        }
        const isNetworkAvailable = checkConnection();
        if (!isNetworkAvailable) {
          return;
        }
        // 添加到上传列表
        await Promise.all(uploadItems.map((item) => addUploadItem(item as UploadItemType)));

        const uploaders = await instance.batchUpload(uploadItems as UploadItemType[], {
          handleValidateFailed: (failedItems) => {
            toast.error(failedItems.map((each) => each.message).join('\n'));
          },
          onError,
          onProgress: (info, id) => {
            const uploadList = useMaterialStore.getState().uploadList;
            const isCanceled = uploadList.find((item) => item.id === id)?.status === UploadStatus.CANCELED;

            if (isCanceled) {
              const uploader = uploaders?.find((u) => u && u?.id === id);
              if (uploader) {
                uploader.cancel();
              }
            } else {
              onProgress(info, id);
            }
          },
          onSuccess: async (doneResult, id) => {
            await sleep(3000);
            const uploadList = useMaterialStore.getState().uploadList;
            const item = uploadList.find((item) => {
              return item.id === id;
            });
            if (item?.status === UploadStatus.CANCELED) {
              return;
            }

            // 使用存储的音频检测结果，而不是状态变量
            const fileHasAudio = audioStatusMap.get(item?.name || '') || false;
            console.log('使用音频检测结果:', fileHasAudio, 'for file:', item?.name);

            const material = await createMaterial({
              doneResult,
              uploadId: id,
              uploadTarDirId: options?.uploadTarDirId ?? uploadTargetDir?.id,
              autoGenByTaskId: options?.autoGenByTaskId,
              name: item?.name ?? '',
              isAudios: fileHasAudio, // 使用存储的检测结果
            });
            if (material) {
              onProgress({ loaded: 100, total: 100, speed: 0, percent: 1 }, id, true);
              refreshCloudStorageSize();
              await onFinish?.(material);
              // 清理文件对象
              useMaterialStore.setState((state) => {
                const { uploadList } = state;
                const itemIndex = uploadList.findIndex((item) => item.id === id);
                if (itemIndex !== -1 && uploadList[itemIndex]) {
                  const updatedItem = { ...uploadList[itemIndex], file: null };
                  const newUploadList = [
                    ...uploadList.slice(0, itemIndex),
                    updatedItem,
                    ...uploadList.slice(itemIndex + 1),
                  ];
                  return { uploadList: newUploadList };
                }
                return state;
              });
            }
          },
        });

        if (!uploaders.length) {
          e.target.value = '';
          return;
        }
      } catch (error) {
        console.error('Batch upload failed:', error);
        toast.error('上传过程中发生错误');
      } finally {
        // 关闭 loading toast
        toast.dismiss(loadingToast);
        useMaterialStore.setState({
          uploadValidating: false,
        });
        e.target.value = '';
      }
    },
    [uploadTargetDir?.id, uploadList, addUploadItem, createMaterial, onFinish, storage, refreshCloudStorageSize, form],
  );

  return {
    handleFileChange,
    handleRetry,
  };
};
