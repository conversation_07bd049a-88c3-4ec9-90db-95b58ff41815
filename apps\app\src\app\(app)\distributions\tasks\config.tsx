import { ProTableColumnType } from '@/components/pro/pro-table';
import { useMemo, useRef } from 'react';
import { DistributionTaskType } from './page';
import { Button } from '@/components/ui';
import { VideoDistributionTaskPreviewModalRef } from '@/components/VideoDistributionTaskPreviewModal';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';

export const useDistributionTaskColumns = () => {
  const taskPreviewModal = useRef<VideoDistributionTaskPreviewModalRef>(null);
  const columns = useMemo(() => {
    const inbuilt: ProTableColumnType<DistributionTaskType>[] = [
      { dataIndex: ['distribution_batch_no'], title: '批次' },
      { dataIndex: ['goods', 'images'], title: '商品图片', type: 'images', fixed: 'left' },
      { dataIndex: ['goods', 'ip'], title: 'IP' },
      { dataIndex: ['goods', 'name'], title: '商品名' },
      { key: 'plan_count', title: '计划分发数量', type: 'number', editable: true },
      { dataIndex: ['goods', 'product_id'], title: '商品ID' },
      { dataIndex: ['goods', 'product_url'], title: '商品链接', type: 'link' },
      { dataIndex: ['goods', 'hot_product'], title: '爆品', render: (v) => (v ? '爆品' : '') },
      { dataIndex: ['goods', 'shop_name'], title: '店铺名' },
      { dataIndex: ['goods', 'commission_rate'], title: '佣金率', type: 'percent' },
      { dataIndex: ['goods', 'tags'], title: '商品标签', type: 'tags' },
      { dataIndex: ['goods', 'remark'], title: '备注' },
      {
        key: 'sub_tasks',
        title: '视频数量',
        width: 200,
        render: (_, record) => {
          const distributed = record.distributed_sub_tasks?.length || 0;
          const should_audit =
            record.wait_sub_tasks?.filter((st) => st.status === VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已出片).length || 0;
          const should_distributed =
            record.wait_sub_tasks?.filter((st) => st.status === VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片).length || 0;

          return (
            <div className="grid grid-cols-4 gap-1 text-right">
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">已出片</div>
                <div className="text-sm">{should_audit}</div>
              </div>
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">已成片</div>
                <div className="text-sm">{should_distributed}</div>
              </div>
              <div className="grid grid-rows-2 gap-1">
                <div className="text-gray-500">已分发</div>
                <div className="text-sm">{distributed}</div>
              </div>
            </div>
          );
        },
      },
      {
        key: 'options',
        title: <div className="ml-4">操作</div>,
        fixed: 'right' as const,
        render: (_, record) => {
          return (
            <div>
              <Button
                variant="link"
                className="text-xs"
                onClick={async () => taskPreviewModal.current?.show(record.id)}
              >
                分发情况
              </Button>
              <Button
                variant="link"
                className="text-xs"
                onClick={async () => {
                  window.open(
                    `bw-file://L:/${record.tenant_id}/自动分发/生成素材/${record.goods.ip}/${record.goods.name}`,
                  );
                }}
              >
                查看素材
              </Button>
              <Button variant="link" className="text-xs" onClick={() => {}}>
                账号列表
              </Button>
            </div>
          );
        },
      },
    ];
    return inbuilt;
  }, []);

  return useMemo(() => {
    return { columns, taskPreviewModalRef: taskPreviewModal };
  }, [columns]);
};
