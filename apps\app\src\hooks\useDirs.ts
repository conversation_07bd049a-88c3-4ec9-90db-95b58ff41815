import { getMaterialDirectories, makeMaterialDirectory, updateDirName } from '@/services/actions/materials';
import useMaterialStore from '@/store/materialStore';
import { DirItemType } from '@/types/material';
import { action } from '@/utils/server-action/action';
import { to } from '@roasmax/utils';
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { useRoots } from './useRoots';

const useDirs = (clearSearch?: () => void) => {
  const { setUploadTargetDir, cloudTab } = useMaterialStore();
  const [dirList, setDirList] = useState<DirItemType[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [currParentId, setCurrParentId] = useState<string | undefined>(undefined);
  const { data: roots } = useRoots();
  const currRoot = roots?.find((item) => item.name === (cloudTab === 1 ? '原始素材' : '生成素材'));

  /**
   * 获取文件夹列表
   * @param options
   * @param options.parentId 父级目录ID
   * @param options.dirType 目录类型，1：素材库，2：生成库
   */
  const getDirs = async (
    options: { parentId?: string; dirType?: number; name?: string } = { dirType: 1, parentId: '', name: '' },
  ) => {
    const { parentId, dirType, name } = options;
    setLoading(true);
    // 如果有parentId，就直接请求parentId下的目录，否则根据dirType请求对应的根目录
    const directories = await action(getMaterialDirectories, { parentId: parentId ?? '' });
    setLoading(false);
    if (!directories) {
      toast.error('获取文件夹失败');
      return;
    }
    const filteredDirectories = name
      ? directories.filter((dir) => dir.name.toLowerCase().includes(name.toLowerCase().trim()))
      : directories;
    if (!parentId) {
      // 没有指定parentId时,获取根目录
      if (dirType === 1) {
        const materialRootDirectory = filteredDirectories.find((item) => item.name === '原始素材');
        setCurrParentId(materialRootDirectory?.id);
        setUploadTargetDir(materialRootDirectory);
        setDirList(
          filteredDirectories?.filter((item) => item.parent_id === materialRootDirectory?.id) as DirItemType[],
        );
      } else {
        const generatedRootDirectory = filteredDirectories.find((item) => item.name === '生成素材');
        setCurrParentId(generatedRootDirectory?.id);
        setUploadTargetDir(generatedRootDirectory);
        setDirList(
          filteredDirectories?.filter((item) => item.parent_id === generatedRootDirectory?.id) as DirItemType[],
        );
      }
    } else {
      // 有指定parentId时,直接设置获取到的目录列表
      setDirList(filteredDirectories as DirItemType[]);
      setCurrParentId(parentId);
      setUploadTargetDir({ id: parentId, name: parentId === currRoot?.id ? currRoot?.name : name });
      return filteredDirectories;
    }
  };

  const addTmpDir = () => {
    setDirList([{ id: 'temp', name: '', status: 'creating' }, ...dirList]);
  };

  const createDir = async (options: { name: string; parentId?: string }) => {
    const toastId = toast.loading('创建中...');
    setIsSubmitting(true);
    try {
      const { name = '文件夹', parentId } = options;
      const validName = name.trim().slice(0, 20);

      let targetParentId = parentId;
      // 且没有指定父级目录，则使用原始根目录或生成根目录
      if (!parentId) {
        targetParentId = currRoot?.id ?? '';
      }

      const [err, res] = await to(
        action(
          makeMaterialDirectory,
          { name: validName, parentId: targetParentId },
          {
            errorType: 'return',
          },
        ),
      );
      if (err || !res?.success) {
        toast.dismiss(toastId);
        toast.error(`文件夹创建失败，${res?.message}`);
        cancelCreating();
        setIsSubmitting(false);
        return;
      }

      if (res) {
        toast.dismiss(toastId);
        toast.success('文件夹创建成功');
        setIsSubmitting(false);
        await getDirs({
          parentId: targetParentId ?? '',
        });
      }
    } catch (error) {
      toast.dismiss(toastId);
      toast.error('文件夹创建失败');
      cancelCreating();
      setIsSubmitting(false);
    }
  };
  const updateDir = async (options: { name: string; id?: string }) => {
    const toastId = toast.loading('修改中...');
    try {
      const { name, id } = options;
      setDirList((prevDirList) =>
        prevDirList.map((dir) => {
          if (dir.id === id) {
            return { ...dir, name: name };
          }
          return dir;
        }),
      );
      const [err, res] = await to(
        action(
          updateDirName,
          { name: name, id },
          {
            errorType: 'return',
          },
        ),
      );
      if (err || !res?.success) {
        toast.dismiss(toastId);
        toast.error(`文件夹名称修改失败，${res?.message}`);
        return;
      }

      if (res) {
        toast.dismiss(toastId);
        toast.success('文件夹名称修改成功');
        clearSearch?.();
      }
    } catch (error) {
      toast.dismiss(toastId);
      toast.error('文件夹名称修改失败');
    }
  };
  const cancelCreating = () => {
    setDirList(dirList.filter((dir) => dir.status !== 'creating'));
    setIsSubmitting(false);
  };

  return {
    dirList,
    loading,
    getDirs,
    createDir,
    updateDir,
    addTmpDir,
    cancelCreating,
    setDirList,
    isSubmitting,
    currParentId,
    setCurrParentId,
  };
};

export { useDirs };
