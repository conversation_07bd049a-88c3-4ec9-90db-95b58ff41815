import { CampaignListResponse, CampaignListResponseCamel } from '@/services/interfaces/ads/res';
import tiktokService from '@/services/tiktokService';
import useAdStore, { useCurrentAdvertiser } from '@/store/ads/adStore';
import { ToCamelCase } from '@/utils/camel';
import { useEffect, useState } from 'react';
import useSWR from 'swr';
import emitter from '@/utils/mitt';

// 抽离 fetcher 函数
export const campaignFetcher = async (params: {
  page: number;
  pageSize: number;
  advertiserIds?: string;
  campaignName?: string;
  operationStatus?: string;
  startTime?: string;
  endTime?: string;
}) => {
  const { page, pageSize, advertiserIds, campaignName = '', operationStatus, startTime, endTime } = params;
  const data: CampaignListResponseCamel = await tiktokService.getCampaignList({
    page,
    page_size: pageSize,
    advertiser_ids: advertiserIds,
    ...(campaignName ? { campaign_name: campaignName } : {}),
    ...(operationStatus ? { operation_status: operationStatus === 'All' ? undefined : operationStatus } : {}),
    ...(startTime ? { start_time: startTime } : {}),
    ...(endTime ? { end_time: endTime } : {}),
  });

  return data;
};

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

export function useCampaignList(
  initialPage = 1,
  initialPageSize = 10,
  {
    withDateRange = false,
  }: {
    withDateRange?: boolean;
  },
) {
  const currentAdvertiser = useCurrentAdvertiser();
  const { dateRange } = useAdStore();

  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
    total: 0,
  });
  const [campaignName, setCampaignName] = useState<string>('');
  const [operationStatus, setOperationStatus] = useState<string>('All');
  const advertiserIds = currentAdvertiser?.map((advertiser) => advertiser.advertiserId).join(',');
  const swrKey = [
    '/api/ads/campaigns',
    {
      ...pagination,
      ...(advertiserIds ? { advertiserIds } : {}),
      ...(campaignName ? { campaignName } : {}),
      ...(operationStatus !== 'All' ? { operationStatus } : {}),
      ...(withDateRange && dateRange?.startTime ? { startTime: dateRange.startTime } : {}),
      ...(withDateRange && dateRange?.endTime ? { endTime: dateRange.endTime } : {}),
    },
  ];

  const { data, error, isLoading, isValidating, mutate } = useSWR<ToCamelCase<CampaignListResponse>>(
    swrKey,
    ([_, params]) => campaignFetcher(params as any),
    {
      keepPreviousData: true,
      refreshInterval: 1000 * 60 * 5, // 5分钟刷新一次
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnMount: true,
      revalidateOnReconnect: false,
    },
  );

  useEffect(() => {
    const handleRefresh = (params: {
      page?: number;
      pageSize?: number;
      total?: number;
      advertiserId?: string;
      campaignName?: string;
      operationStatus?: string;
    }) => {
      const { page, pageSize, total = 0, campaignName: newCampaignName, operationStatus: newOperationStatus } = params;

      if (page !== undefined || pageSize !== undefined) {
        setPagination((prev) => ({
          page: page ?? prev.page,
          pageSize: pageSize ?? prev.pageSize,
          total: total ?? prev.total,
        }));
      }
      if (newCampaignName) {
        setCampaignName(newCampaignName);
      }
      if (newOperationStatus) {
        setOperationStatus(newOperationStatus);
      }
      mutate();
    };

    // 使用 mitt
    emitter.on('REFRESH_CAMPAIGN_LIST', handleRefresh);
    return () => {
      emitter.off('REFRESH_CAMPAIGN_LIST', handleRefresh);
    };
  }, [mutate]);

  return {
    campaigns: data,
    isLoading: isLoading || isValidating,
    isError: error,
    refresh: () => mutate(),
    mutate: (pageInfo: {
      newPage?: number;
      newPageSize?: number;
      total: number;
      newCampaignName?: string;
      operationStatus?: string;
    }) => {
      if (pageInfo.newPage !== undefined || pageInfo.newPageSize !== undefined) {
        setPagination((prev) => ({
          page: pageInfo.newPage ?? prev.page,
          pageSize: pageInfo.newPageSize ?? prev.pageSize,
          total: pageInfo.total ?? prev.total,
          campaignName: pageInfo.newCampaignName,
          operationStatus: pageInfo.operationStatus,
        }));
      }
    },
    swrKey,
  };
}
