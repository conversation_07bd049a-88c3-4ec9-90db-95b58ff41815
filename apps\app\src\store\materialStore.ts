import { DirItemType } from '@/types/material';
import { UploadItemType } from '@/types/upload';
import { MaterialItemType } from '@/hooks/useMaterial';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface MaterialState {
  uploadList: UploadItemType[];
  selectedMaterialList: MaterialItemType[];
  uploadTargetDir: DirItemType | null;
  materialDrawerOpen: boolean;
  dirModalOpen: boolean;
  uploadListModalOpen: boolean;
  cloudTab: number;
  batchMode: boolean;
  uploadValidating: boolean;
  setUploadValidating: (validating: boolean) => void;
  setCloudTab: (tab: number) => void;
  setBatchMode: (mode: boolean) => void;
  setDirModalOpen: (open: boolean) => void;
  setUploadListModalOpen: (open: boolean) => void;
  setSelectedMaterialList: (list: MaterialItemType[]) => void;
  setUploadList: (list: UploadItemType[]) => void;
  addUploadItem: (item: UploadItemType) => void;
  updateUploadItem: (id: string, info: Partial<UploadItemType>) => void;
  removeUploadItem: (id: string) => void;
  clearUploadList: () => void;
  setMaterialDrawerOpen: (open: boolean) => void;
  setUploadTargetDir: (dir: DirItemType | null | undefined) => void;
}
const useMaterialStore = create<MaterialState>()(
  devtools((set) => ({
    uploadList: [],
    uploadTargetDir: null,
    selectedMaterialList: [],
    dirModalOpen: false,
    uploadListModalOpen: false,
    cloudTab: 1,
    batchMode: false,
    uploadValidating: false,
    setUploadValidating: (validating) => set({ uploadValidating: validating }),
    setCloudTab: (tab) => set({ cloudTab: tab }),
    setBatchMode: (mode) => set({ batchMode: mode }),
    setDirModalOpen: (open) => set({ dirModalOpen: open }),
    setUploadListModalOpen: (open) => set({ uploadListModalOpen: open }),
    setUploadList: (list) => set({ uploadList: list }),
    setUploadTargetDir: (dir) => set({ uploadTargetDir: dir }),
    setSelectedMaterialList: (list) => set({ selectedMaterialList: list }),
    addUploadItem: (item) => set((state) => ({ uploadList: [...state.uploadList, item] })),
    updateUploadItem: (id, info) =>
      set((state) => ({
        uploadList: state.uploadList.map((item) => (item.id === id ? { ...item, ...info } : item)),
      })),
    removeUploadItem: (id) => set((state) => ({ uploadList: state.uploadList.filter((item) => item.id !== id) })),
    clearUploadList: () => set({ uploadList: [] }),
    materialDrawerOpen: false,
    setMaterialDrawerOpen: (open) => set({ materialDrawerOpen: open }),
  })),
);

export default useMaterialStore;
