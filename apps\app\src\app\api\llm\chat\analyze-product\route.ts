import { NextRequest } from 'next/server';
import { DifyService } from '@/utils/dify';
import { api } from '@roasmax/serve';

interface RequestBody {
  productUrl: string;
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 12);
}

// 使用 api 包装器并启用 SSE
export const POST = api(
  async (ctx, req: NextRequest) => {
    const requestId = generateRequestId();
    const requestIp = req.headers.get('x-forwarded-for') || 'unknown';
    const difyService = new DifyService(requestId, requestIp);

    try {
      const body = ctx.data as RequestBody;
      const { productUrl } = body;
      console.log('productUrl', productUrl);
      if (!productUrl) {
        throw new Error('Product URL is required');
      }

      const result = await difyService.request({
        apiUrl: process.env.DIFY_API_URL!,
        apiKey: process.env.DIFY_API_KEY_PRODUCT_ANALYZE!,
        inputs: {
          product_url: productUrl,
        },
        query: `<prompt>
              请分析这个商品链接: ${productUrl}
              注意:所有内容必须使用中文返回。
              </prompt>`,
        responseMode: 'streaming',
        user: requestId,
        onMessage: (chunk: string) => {
          // @ts-ignore
          ctx?.send(chunk);
        },
      });
      console.log('result', result);
      return result;
    } catch (error) {
      console.error('Error:', error);
      throw error; // api 包装器会处理错误响应
    }
  },
  { sse: true },
);
