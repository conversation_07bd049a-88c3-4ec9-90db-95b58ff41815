'use server';

import { TaskStatus } from '@/types/task';
import {
  HistoricalDistributionForVideoDistribution,
  OrderForVideoDistribution,
  ProductForVideoDistribution,
  UserForVideoDistribution,
  VideoDistributionModel,
  VideoForVideoDistribution,
  WeightConfigForVideoDistribution,
} from '@/utils/video-distribution-model';
import { Prisma } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import {
  calcDailyVideoCountByFansCount,
  calculateEvenlySpacedTimes,
  snowflake,
  VIDEO_DISTRIBUTION_SUB_TASK_STATUS,
} from '@roasmax/utils';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { uniq } from 'lodash';
import { listVideoDistributionSubTasksByTaskIds } from '../domains/task/video-distribution-task';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 分页查询视频分发任务列表
 */
export const pageVideoDistributionTasks = server(
  '分页查询视频分发任务列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters: { distribution_batch_no: string };
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      const where: Prisma.video_distribution_tasksWhereInput = {};
      if (ctx.data.filters.distribution_batch_no) {
        where.distribution_batch_no = { equals: ctx.data.filters.distribution_batch_no };
      }

      const [list = [], total = 0] = await Promise.all([
        ctx.db.video_distribution_tasks.findMany({
          skip: (ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
          take: ctx.data.pagination.pageSize,
          where,
          orderBy: { tmp_created_at: 'desc' },
          include: { goods: true },
        }),
        ctx.db.video_distribution_tasks.count({ where }),
      ]);

      // 查询对应任务的可发视频
      const wait_sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
        where: {
          status: { in: [VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已出片, VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片] },
          OR: list.map((t) => {
            return { ip: t.goods.ip!, goods_name: t.goods.name };
          }),
        },
      });

      const distributed_sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
        where: { task_id: { in: list.map((t) => t.id) } },
      });

      return {
        list: list.map((t) => {
          return {
            ...t,
            wait_sub_tasks: wait_sub_tasks.filter((st) => st.ip === t.goods.ip && st.goods_name === t.goods.name),
            distributed_sub_tasks: distributed_sub_tasks.filter((st) => st.task_id === t.id),
          };
        }),
        pagination: {
          total,
          page: ctx.data.pagination.page,
          pageSize: ctx.data.pagination.pageSize,
        },
      };
    });
  },
);

export const listVideoDistributionTasks = server(
  '列表视频分发任务',
  async (ctx: ActionContext<{ taskIds: string[] }>) => {
    return await ctx.trx(async (ctx) => {
      const list = await ctx.db.video_distribution_tasks.findMany({
        where: { id: { in: ctx.data.taskIds } },
        include: { goods: true },
      });

      const distributed_sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
        where: { task_id: { in: list.map((t) => t.id) } },
      });

      return list.map((t) => {
        return {
          ...t,
          distributed_sub_tasks: distributed_sub_tasks.filter((st) => st.task_id === t.id),
        };
      });
    });
  },
);

export const updateVideoDistributionTaskPlanCount = server(
  '更新视频分发任务计划分发数量',
  async (ctx: ActionContext<{ taskId: string; planCount: number }>) => {
    return await ctx.db.video_distribution_tasks.update({
      where: { id: ctx.data.taskId },
      data: { plan_count: ctx.data.planCount },
    });
  },
);

/**
 * 创建视频分发任务
 */
export const createVideoDistributionTasksByGoods = server(
  '创建视频分发任务',
  async (ctx: ActionContext<{ goodsIdList: string[]; batchNo: string }>) => {
    await ctx.trx(async (ctx) => {
      // 获取商品的列表
      const goods = await ctx.db.goods.findMany({
        where: { id: { in: ctx.data.goodsIdList } },
      });

      if (goods?.length !== ctx.data.goodsIdList.length) {
        throw new Error('商品不存在');
      }

      // 判定是否存在商品名重复，并给出重复的商品名
      const goodsByName = await ctx.db.goods.groupBy({
        by: ['name'],
        having: { name: { _count: { gt: 1 } } },
        where: { id: { in: ctx.data.goodsIdList } },
      });
      if (goodsByName.length > 0) {
        throw new Error(`商品名重复，请先处理: ${goodsByName.map((g) => g.name).join(', ')}`);
      }

      // 每一个任务的唯一性由 日期+商品决定
      const uniqueKeys = goods.flatMap<{ distribution_batch_no: string; goods_id: string }>((g) => {
        return { distribution_batch_no: ctx.data.batchNo, goods_id: g.id };
      });

      // 查询已经存在的任务
      const existTasks = await ctx.db.video_distribution_tasks.findMany({
        where: {
          OR: uniqueKeys.map((uk) => {
            return { distribution_batch_no: uk.distribution_batch_no, goods_id: uk.goods_id };
          }),
        },
      });

      // 从已经存在的任务中过滤掉待创建的任务
      const uniqueKeysToCreate = uniqueKeys.filter(
        (uk) =>
          !existTasks.some(
            (et) => et.distribution_batch_no === uk.distribution_batch_no && et.goods_id === uk.goods_id,
          ),
      );

      if (uniqueKeysToCreate.length === 0) {
        return;
      }

      for (const uk of uniqueKeysToCreate) {
        // 创建分发任务
        await ctx.db.video_distribution_tasks.create({
          data: {
            tenant_id: ctx.tenant.id,
            distribution_batch_no: uk.distribution_batch_no,
            goods_id: uk.goods_id,
            video_generation_task_id: '',
            cos_push_path: `${ctx.tenant.config.cos_path}/video-distribution/no-assigned/${uk.distribution_batch_no}`,
            status: '',
          },
        });
      }
    });

    return true;
  },
);

/**
 * 将视频和社交帐号进行分配分发，这个处理交给分发服务。这里只调用分发接口并提供对应的数据
 */
export const assignVideoDistributionTask = server(
  '分配视频分发任务到社交帐号',
  async (ctx: ActionContext<{ taskIds: string[] }>) => {
    const requestSn = snowflake.nextId();
    // 分发任务信息
    const video_distribution_tasks = await ctx.db.video_distribution_tasks.findMany({
      select: {
        id: true,
        distribution_batch_no: true,
        plan_count: true,
        goods: { select: { id: true, name: true, ip: true, hot_product: true } },
      },
      where: { id: { in: ctx.data.taskIds }, goods: { ip: { not: null } } },
    });

    // 当前任务分发批次号
    const distributionBatchNo = video_distribution_tasks[0]!.distribution_batch_no;
    // 如果存在任务列表有多个批次号，则抛出错误
    if (uniq(video_distribution_tasks.map((t) => t.distribution_batch_no)).length > 1) {
      throw new Error('任务列表中存在多个批次号，无法进行分发');
    }

    // 当前分发批次关联的任务id
    const relatedTaskIds = await ctx.db.video_distribution_tasks
      .findMany({
        select: { id: true },
        where: { distribution_batch_no: distributionBatchNo },
      })
      .then((res) => res.map((t) => t.id));

    // 商品信息
    const goods = await ctx.db.goods.findMany({
      where: { name: { in: video_distribution_tasks.map((t) => t.goods.name!) } },
    });
    // 判定是否存在商品名重复，并给出重复的商品名
    const duplicateGoodsNames = goods.filter((g) => goods.filter((t) => t.name === g.name).length > 1);
    if (duplicateGoodsNames.length > 0) {
      throw new Error(
        `商品名重复，请先处理: ${uniq(duplicateGoodsNames)
          .map((g) => g.name)
          .join(', ')}`,
      );
    }

    // 查询账号信息
    const social_accounts = await ctx.db.social_accounts.findMany({
      where: {
        ip: { in: uniq(video_distribution_tasks.map((t) => t.goods.ip!)) },
        cloud_terminal: { cloud_host_cos_path: { not: null } },
      },
      include: { cloud_terminal: true },
    });

    // 订单信息
    const orders = await ctx.db.orders
      .findMany({
        where: {
          product_name: { in: video_distribution_tasks.map((t) => t.goods.name) },
          influencer_douyin_id: { in: social_accounts.map((sa) => sa.account_identity!).filter(Boolean) },
        },
      })
      .then((res) => {
        return res.map((o) => {
          return {
            ...o,
            goods_id: video_distribution_tasks.find((t) => t.goods.name === o.product_name)?.goods.id,
          };
        });
      });

    // 待分发的子任务
    const sub_tasks = await ctx.db.video_distribution_sub_tasks
      .findMany({
        include: { material: true },
        where: {
          status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片,
          OR: video_distribution_tasks.map((t) => ({ ip: t.goods.ip!, goods_name: t.goods.name })),
        },
      })
      .then((res) => {
        return res.map((st) => {
          return {
            ...st,
            goods_id: video_distribution_tasks.find((t) => t.goods.name === st.goods_name)?.goods.id,
          };
        });
      });

    // 已分发的子任务 需要获取本批所有的子任务
    const distributed_sub_tasks = await ctx.db.video_distribution_sub_tasks
      .findMany({ where: { task_id: { in: relatedTaskIds } } })
      .then((res) => {
        return res.map((st) => {
          return {
            ...st,
            goods_id: video_distribution_tasks.find((t) => t.id === st.task_id)?.goods.id,
          };
        });
      });

    // 历史分发记录， 取前后各一天加当天
    const historical_distributions = await ctx.db.video_distribution_sub_tasks
      .findMany({
        include: { task: { select: { distribution_batch_no: true } } },
        where: {
          task: {
            distribution_batch_no: {
              in: [
                dayjs(distributionBatchNo).subtract(1, 'day').format('YYYY-MM-DD'),
                dayjs(distributionBatchNo).format('YYYY-MM-DD'),
                dayjs(distributionBatchNo).add(1, 'day').format('YYYY-MM-DD'),
              ],
            },
          },
        },
      })
      .then((res) => {
        return res.map((st) => {
          return {
            ...st,
            goods_id: video_distribution_tasks.find((t) => t.id === st.task_id)?.goods.id,
          };
        });
      });

    const ips = uniq(video_distribution_tasks.map((t) => t.goods.ip!).filter(Boolean));

    for (const ip of ips) {
      const userForVideoDistribution: UserForVideoDistribution[] = social_accounts
        .filter((sa) => sa.ip === ip)
        .map((sa) => {
          const should_distributed = (() => {
            if (!sa.fans_count || sa.fans_count < 1000) {
              return 0;
            }
            if (sa.fans_count < 3000) {
              return 2;
            }
            if (sa.fans_count < 10000) {
              return 5;
            }
            return 10;
          })();

          const distributed = distributed_sub_tasks.filter((st) => st.social_account_id === sa.id).length;

          return {
            id: sa.id,
            qualityScore: sa.quality || 0,
            fansCount: sa.fans_count || 0,
            sealQuota: should_distributed - distributed,
            noSealQuota: 0,
          };
        });

      const productForVideoDistribution: ProductForVideoDistribution[] = video_distribution_tasks
        .filter((t) => t.goods.ip === ip)
        .map((t) => {
          return {
            id: t.goods.id,
            priorityScore: t.goods.hot_product ? 100 : 0,
            distributeLimit: Math.max(
              0,
              (t.plan_count || 100) - distributed_sub_tasks.filter((st) => st.task_id === t.id).length,
            ),
          };
        });

      const videoForVideoDistribution: VideoForVideoDistribution[] = sub_tasks
        .filter((st) => st.ip === ip)
        .map((st) => {
          return {
            id: st.id!,
            priorityScore: 0,
            productId: st.goods_id!,
            timeWeight: 0,
            isSeal: true,
          };
        });

      const orderForVideoDistribution: OrderForVideoDistribution[] = orders
        .filter((o) => productForVideoDistribution.some((t) => t.id === o.goods_id))
        .map((o) => {
          return {
            userId: o.influencer_douyin_id!,
            productId: o.product_name!,
            priorityScore: 0,
          };
        });

      const historicalDistributionForVideoDistribution: HistoricalDistributionForVideoDistribution[] =
        historical_distributions
          .filter((hd) => hd.ip === ip)
          .map((hd) => {
            return {
              userId: hd.social_account_id!,
              productId: hd.goods_id!,
              priorityScore: 0,
            };
          });

      const weightConfigForVideoDistribution: WeightConfigForVideoDistribution = {
        userQualityWeight: 1,
        productPriorityWeight: 1,
        videoPriorityWeight: 0,
        orderPriorityWeight: 1000,
        timeWeight: 0,
      };

      const videoDistributionModel = new VideoDistributionModel({
        users: userForVideoDistribution,
        products: productForVideoDistribution,
        videos: videoForVideoDistribution,
        orders: orderForVideoDistribution,
        historicalDistributions: historicalDistributionForVideoDistribution,
        weightConfig: weightConfigForVideoDistribution,
      });

      const distributionResult = videoDistributionModel.distribute();
      for (const item of distributionResult) {
        const { userId: accountId, videoIds: subTaskIds } = item;
        for (const subTaskId of subTaskIds) {
          const goodsId = sub_tasks.find((st) => st.id === subTaskId)?.goods_id;
          const task = video_distribution_tasks.find((t) => t.goods.id === goodsId);
          if (!task) {
            throw new Error(`任务 ${goodsId} 不存在`);
          }
          const subTask = sub_tasks.find((st) => st.id === subTaskId);
          if (!subTask) {
            throw new Error(`子任务 ${subTaskId} 不存在`);
          }
          // 使用 cos 将视频推送到指定路径
          const socialAccount = social_accounts.find((sa) => sa.id === accountId);
          if (!socialAccount) {
            throw new Error(`账号 ${accountId} 不存在`);
          }
          ctx.logger.log(`将视频 ${subTask.wait_cos_path} 推送到 ${socialAccount.nickname}`);
          const fileName = subTask.wait_cos_path!.split('/').pop();
          const dist = `roasmax/${ctx.tenant.id}/自动分发/分发素材/${socialAccount.cloud_terminal!.cloud_host_cos_path!}/${video_distribution_tasks[0]!.distribution_batch_no}/${fileName}`;
          const accountShouldPublishCount = calcDailyVideoCountByFansCount(socialAccount.fans_count);
          const publishTimes = calculateEvenlySpacedTimes({
            a: new Date(`${task.distribution_batch_no} 06:00:00`),
            b: new Date(`${task.distribution_batch_no} 22:00:00`),
            n: accountShouldPublishCount,
          });
          const exists = await ctx.db.video_distribution_sub_tasks.findMany({
            where: {
              social_account_id: socialAccount.id,
              publish_plan_at: { in: publishTimes },
            },
          });
          await ctx.db.video_distribution_sub_tasks.update({
            where: { id: subTask.id },
            data: {
              status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.分发中,
              task_id: task.id,
              batch_no: requestSn,
              social_account_id: socialAccount.id,
              distributed_cos_path: dist,
              publish_plan_at: publishTimes.find((t) => !exists.some((e) => e.publish_plan_at === t)),
            },
          });
          // 存储对象复制
          ctx.logger.log(`存储对象复制: ${subTask.wait_cos_path!} -> ${dist}`);
          const putRes = await ctx.cos.putObjectCopy({
            MetadataDirective: 'Copy',
            Bucket: process.env.COS_BUCKET!,
            Region: process.env.COS_REGION!,
            Key: dist,
            CopySource: `${process.env.COS_BUCKET!}.cos.${process.env.COS_REGION!}.myqcloud.com/${encodeURIComponent(subTask.wait_cos_path!)}`,
          });
          ctx.logger.log(`存储对象复制结果: ${JSON.stringify(putRes)}`);
          // 删除已分发的视频
          ctx.logger.log(`删除原始存储对象: ${subTask.wait_cos_path!} -> ${dist}`);
          const deleteRes = await ctx.cos.deleteObject({
            Bucket: process.env.COS_BUCKET!,
            Region: process.env.COS_REGION!,
            Key: subTask.wait_cos_path!,
          });
          ctx.logger.log(`删除原始存储对象结果: ${JSON.stringify(deleteRes)}`);
        }
      }
    }
    return true;
  },
);

/**
 * 撤回视频分发任务
 */
export const withdrawVideoDistributionTask = server(
  '撤回视频分发任务',
  async (ctx: ActionContext<{ taskIds: string[]; socialAccountId: string }>) => {
    const { taskIds, socialAccountId } = ctx.data;
    const subTasks = await ctx.db.video_distribution_sub_tasks.findMany({
      where: { task_id: { in: taskIds }, social_account_id: socialAccountId },
    });
    for (const subTask of subTasks) {
      await ctx.trx(async (ctx) => {
        await ctx.db.video_distribution_sub_tasks.update({
          where: { id: subTask.id },
          data: {
            status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片,
            task_id: null,
            social_account_id: null,
            distributed_cos_path: null,
            publish_plan_at: null,
          },
        });
        // 复制到原路径
        await ctx.cos.putObjectCopy({
          MetadataDirective: 'Copy',
          Bucket: process.env.COS_BUCKET!,
          Region: process.env.COS_REGION!,
          Key: subTask.wait_cos_path!,
          CopySource: `${process.env.COS_BUCKET!}.cos.${process.env.COS_REGION!}.myqcloud.com/${encodeURIComponent(subTask.distributed_cos_path!)}`,
        });
        // 删除分发路径
        await ctx.cos.deleteObject({
          Bucket: process.env.COS_BUCKET!,
          Region: process.env.COS_REGION!,
          Key: subTask.distributed_cos_path!,
        });
      });
    }
    return true;
  },
);

/**
 * 查看视频分发任务详情
 */
export const getVideoDistributionTaskDetail = server(
  '查看分发任务详情',
  async (ctx: ActionContext<{ taskId: string }>) => {
    return await ctx.trx(async (ctx) => {
      const task = await ctx.db.video_distribution_tasks.findUnique({
        where: { id: ctx.data.taskId, goods: { ip: { not: null } } },
        include: { goods: true },
      });

      if (!task) {
        throw new Error('任务不存在');
      }

      // 查询所有当前商品+直播间的分发子任务 不包含已经分配给其他任务的
      const sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
        where: {
          ip: task.goods.ip!,
          goods_name: task.goods.name,
          OR: [{ task_id: task.id }, { task_id: null, status: TaskStatus.PROCESSING }],
        },
      });

      // 子任务关联的账号
      const social_accounts = await ctx.db.social_accounts.findMany({
        where: {
          id: {
            in: sub_tasks
              .filter((st) => st.social_account_id && st.task_id)
              .map((st) => st.social_account_id!)
              .filter(Boolean),
          },
        },
      });

      // 子任务关联的视频
      const materials = await ctx.db.materials.findMany({
        where: { id: { in: [...sub_tasks.map((st) => st.material_id!).filter(Boolean)] } },
      });

      return {
        ...task,
        sub_tasks: sub_tasks.map((st) => {
          const social_account = social_accounts.find((sa) => sa.id === st.social_account_id);
          const material = materials.find((m) => m.id === st.material_id);
          return { ...st, social_account, material };
        }),
      };
    });
  },
);

/**
 * 获取指定分发任务的待分发视频
 */
export const getVideoDistributionSubTasksByTaskIds = server(
  '获取指定分发任务的待分发视频',
  async (ctx: ActionContext<{ taskIds?: string[] }>) => {
    return await ctx.execute(listVideoDistributionSubTasksByTaskIds, ctx.data);
  },
);

/**
 * 获取指定分发任务的待分发账号
 */
export const getSocialAccountsByIps = server(
  '获取指定IP的待分发账号',
  async (
    ctx: ActionContext<{
      ips?: string[];
      /** 指定批次号，可以通过today_distribution_sub_tasks获取到指定批次号的视频数，不填则查询当日 */
      distributionBatchNo?: string;
    }>,
  ) => {
    const social_accounts = await ctx.db.social_accounts.findMany({
      where: { ip: { in: ctx.data.ips }, cloud_terminal: { cloud_host_cos_path: { not: null } } },
      include: { cloud_terminal: true },
    });

    // 获取账号+批次已经分发的视频数
    const sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
      where: {
        social_account_id: { in: social_accounts.map((item) => item.id) },
        // 为了缩减查询范围，只查询当前任务的批次号
        task: { distribution_batch_no: ctx.data.distributionBatchNo || dayjs().format('YYYY-MM-DD') },
      },
      include: { task: true },
    });

    return social_accounts.map((item) => {
      return {
        ...item,
        today_distribution_sub_tasks: sub_tasks.filter((sub) => {
          return sub.social_account_id === item.id;
        }),
      };
    });
  },
);
