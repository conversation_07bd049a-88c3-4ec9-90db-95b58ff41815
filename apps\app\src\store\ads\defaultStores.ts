import { cloneDeep } from 'lodash';
import type { AdRightDrawerState, AdStore, CurrentView } from './storeTypes';

export const DEFAULT_PAGE_SIZE = 10;
export const defaultAdRightDrawerValue: AdRightDrawerState = {
  type: 'create',
  show: false,
  title: '',
  formValue: {},
  createType: 'normal',
};

export const defaultQuickCreateFormValue = {
  createType: 'quick',
  generateRound: '1',
  speed: '1X',
  subtitle: true,
  language: 'zh',
  quantization: false,
  darkPostStatus: false,
  operationStatus: false,
  selectedVideoItem: [],
  adgroupIds: [],
} as const;

export const defaultStatus = {
  loading: false,
  error: false,
};
export const dateRange = {
  startTime: '',
  endTime: '',
};
export const defaultAdStore: Omit<AdStore, 'actions'> = {
  dateRange: undefined,
  currentView: 'advertisers' as CurrentView,
  currentAdvertiser: null,
  currentCampaign: null,
  currentAdGroup: null,
  currentAd: null,
  cloudSheetOpen: false,
  campaignStatus: defaultStatus,
  advertiserStatus: defaultStatus,
  campaignModal: {
    type: 'create',
    show: false,
    title: '',
    formValue: null,
  },
  adGroups: {
    list: [],
    // @ts-ignore
    page_info: {
      page: 1,
      page_size: DEFAULT_PAGE_SIZE,
      total: 0,
    },
  },
  adGroupStatus: defaultStatus,
  adsIdentityStatus: defaultStatus,
  adRightDrawer: cloneDeep(defaultAdRightDrawerValue),
  createAdStatus: defaultStatus,
  generateAdTextStatus: {
    loading: false,
    error: false,
  },
};
