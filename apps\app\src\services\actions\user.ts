'use server';
import { mutex } from '@/utils/mutex';
import { ActionContext, server } from '@roasmax/serve';
import { Prisma } from '@roasmax/database';
import { AuthorizeResourceItem } from 'authing-node-sdk/dist/models/AuthorizeResourceItem';

/**
 * 获取用户列表
 */
export const getUserList = server('获取用户列表', async (ctx: ActionContext<{ page: number; limit: number }>) => {
  const { id: tenantId } = await ctx.fetchTenant();

  const members = await ctx.authingManage.listGroupMembers({
    code: tenantId,
    page: ctx.data.page,
    limit: ctx.data.limit,
  });

  if (members.statusCode !== 200) {
    throw new Error(members.message);
  }

  const promises = members.data.list.map(async (member) => {
    const roles = await ctx.authingManage.getUserRoles({ userId: member.userId, namespace: tenantId });
    if (roles.statusCode !== 200) {
      throw new Error(roles.message);
    }

    return {
      userId: member.userId,
      nickname: member.nickname,
      username: member.username,
      phone: member.phone,
      // TODO: 保持原来的逻辑，角色只取第一个
      roleName: roles.data.list?.[0]?.name,
    };
  });

  const results = await Promise.all(promises);

  return {
    data: results,
    pageInfo: {
      totalCount: members.data.totalCount,
      page: ctx.data.page,
      limit: ctx.data.limit,
    },
  };
});

/**
 * 获取用户信息
 * 这个接口是管理员获取所管理的用户信息的
 */
export const getUserInfo = server('获取用户信息', async (ctx: ActionContext<{ userId: string }>) => {
  const { id: tenantId } = await ctx.fetchTenant();

  const userData = await ctx.authingManage.getUser({ userId: ctx.data.userId, userIdType: 'user_id' });
  if (userData.statusCode !== 200) {
    throw new Error(userData.message);
  }

  const roleData = await ctx.authingManage.getUserRoles({
    userId: ctx.data.userId,
    userIdType: 'user_id',
    namespace: tenantId,
  });
  if (roleData.statusCode !== 200) {
    throw new Error(roleData.message);
  }

  return {
    userData: userData.data,
    roleData: roleData.data,
  };
});

/**
 * 获取用户信息
 * @todo 这个接口的实现逻辑有问题，它通过调用managementClient获取到了当前租户下的所有角色，这应该是不对的。这个接口实际是用于获取当前用户信息的
 */
export const getUserLoginInfo = server('获取用户登录信息', async (ctx) => {
  const userId = ctx.user.id;
  const { id: tenantId } = await ctx.fetchTenant();
  const res = await ctx.authingManage.getUserAuthorizedResources({
    userId: userId,
    userIdType: 'user_id',
    namespace: tenantId,
  });
  if (res.statusCode !== 200) {
    throw new Error(res.message);
  }

  const resRole = await ctx.authingManage.getUserRoles({
    userId: userId,
    userIdType: 'user_id',
    namespace: tenantId,
  });
  if (resRole.statusCode !== 200) {
    throw new Error(resRole.message);
  }

  const data = {
    resourceData: res.data,
    roleData: resRole.data,
  };
  return data;
});

/**
 * 创建用户
 */
export const createUser = server(
  '创建用户',
  async (
    ctx: ActionContext<{
      username: string;
      password: string;
      nickname: string;
      email: string;
      quota: number;
      roleCode: string;
    }>,
  ) => {
    return await mutex.runExclusive(async () => {
      const body = ctx.data;
      const nickname: string = body.nickname;
      const username: string = body.username;
      const password: string = body.password;
      const email: string = body.email;
      const quota: number = body.quota;
      const roleCode: string = body.roleCode;

      const AdminUserId: string = ctx.user.id;
      const { id: tenantId } = await ctx.fetchTenant();

      const wallet = await ctx.db.member_wallets.findUnique({
        where: { tenant_id_user_id: { tenant_id: tenantId, user_id: ctx.user.id } },
      });
      // todo 这个balance哪来的
      // @ts-ignore
      if (!wallet || wallet.quota - quota < 0) {
        throw '账号创建失败管理员额度不够或者充值数量为负数';
      }

      // 创建账号
      const res = await ctx.authingManage.createUser({
        nickname: nickname,
        username: username,
        email: email,
        password: password,
        tenantIds: [tenantId],
      });
      if (res.statusCode !== 200) {
        throw new Error(res.message);
      }

      const userUserId = res.data.userId;

      // 配置用户组
      const addGroupRes = await ctx.authingManage.addGroupMembers({
        code: tenantId,
        userIds: [userUserId],
      });
      if (addGroupRes.statusCode !== 200) {
        throw new Error(addGroupRes.message);
      }

      // 添加权限
      const addRoleRes = await ctx.authingManage.assignRole({
        code: roleCode,
        namespace: tenantId,
        targets: [{ targetType: AuthorizeResourceItem.targetType.USER, targetIdentifier: userUserId }],
      });
      if (addRoleRes.statusCode !== 200) {
        throw new Error(addRoleRes.message);
      }

      // 转移额度 从租户到用户
      const transaction = {
        from: AdminUserId,
        quota: quota,
        tenant_id: tenantId,
        userId: userUserId,
      };
      await ctx.trx(async (ctx) => {
        const transactionRecords: Prisma.member_wallet_changelogsCreateManyInput[] = [];
        // 先为新的用户创建钱包
        await ctx.db.member_wallets.create({ data: { user_id: userUserId, tenant_id: tenantId, quota: 0 } });
        if (transaction.from !== 'SYSTEM') {
          // 如果不是系统操作，表明from是一个具体的用户，这是额度的转移，需要扣除对应的额度
          const fromWallet = await ctx.db.member_wallets.findUnique({
            where: { tenant_id_user_id: { tenant_id: tenantId, user_id: transaction.from } },
          });
          if (!fromWallet || fromWallet.quota - transaction.quota < 0) {
            throw new Error(`${transaction.from} doesn't have enough to send ${-transaction.quota}`);
          }
          transactionRecords.push({
            tenant_id: transaction.tenant_id,
            user_id: transaction.from,
            wallet_id: fromWallet.id,
            change_type: 'SET_MENU_OUT',
            quota: -transaction.quota,
            result_quota: fromWallet.quota - transaction.quota,
            change_reason: '',
            from_tenant_id: null,
            from_user_id: null,
            to_tenant_id: transaction.tenant_id,
            to_user_id: transaction.userId,
          });
        }

        // 这是新用户的钱包
        const receiverBalance = await ctx.db.member_wallets.findUnique({
          where: { tenant_id_user_id: { tenant_id: tenantId, user_id: transaction.userId } },
        });
        if (!receiverBalance || receiverBalance.quota + transaction.quota < 0) {
          throw new Error(`${transaction.userId} doesn't have enough to receive ${transaction.quota}`);
        }
        transactionRecords.push({
          tenant_id: transaction.tenant_id,
          user_id: transaction.userId,
          wallet_id: receiverBalance.id,
          change_type: 'SET_MENU_IN',
          quota: transaction.quota,
          result_quota: receiverBalance.quota + transaction.quota,
          change_reason: '',
          from_tenant_id: transaction.tenant_id,
          from_user_id: transaction.from,
          to_tenant_id: null,
          to_user_id: null,
        });
        await ctx.db.member_wallet_changelogs.createMany({
          data: transactionRecords,
        });
        return;
      });
    });
  },
);

/**
 * 修改用户信息
 */
export const updateUser = server(
  '修改用户信息',
  async (ctx: ActionContext<{ userId: string; nickname: string; password: string }>) => {
    const res = await ctx.authingManage.updateUser({
      userId: ctx.data.userId,
      nickname: ctx.data.nickname,
      password: ctx.data.password,
    });
    if (res.statusCode !== 200) {
      throw new Error(res.message);
    }
    if (ctx.data.password) {
      await ctx.authingManage.kickUsers({ userId: ctx.data.userId, appIds: [process.env.APPID] });
    }
    return res.data;
  },
);

/**
 * 删除用户
 * 账号下线 账号停用 余额回滚 账号的删除
 * @todo 余额退回主账号，但是这里注意目前只有一个主账号就是根管理员。如果存在多个管理员可以执行删除，就意味着退回的余额可能不是根管理员的，而是谁删除的就退回给谁
 */
export const deleteUser = server('删除用户', async (ctx: ActionContext<{ userId: string }>) => {
  // TODO: 由于钱包系统改造，这里需要重新实现
  console.log('🚀 ~ deleteUser ~ ctx:', ctx);
  throw new Error('暂时不支持删除用户');
  // const { id: tenantId } = await getTenantByUserId(ctx.user.id);
  // const userId = ctx.data.userId;

  // const result = await ctx.authingManage.kickUsers({
  //   appIds: [process.env.APPID],
  //   userId: userId,
  // });
  // if (result.statusCode !== 200) {
  //   throw new Error(result.message);
  // }

  // // 查询用户额度
  // const userQuota = await prisma.transaction.findMany({ where: { userId: userId } });
  // const quota = userQuota.length >= 0 ? userQuota.reduce((acc: any, curr: any) => acc + curr.quota, 0) : 0;

  // // 给老板加钱
  // await prisma.transaction.create({
  //   data: {
  //     userId: ctx.user.id,
  //     quota: quota,
  //     from: 'REMOVE_ACCOUNT_RETURN',
  //     tenant_id: tenantId,
  //   },
  // });

  // // 给用户扣钱
  // await prisma.transaction.create({
  //   data: {
  //     userId: userId,
  //     quota: quota * -1,
  //     from: 'REMOVE_ACCOUNT_RETURN',
  //     tenant_id: tenantId,
  //   },
  // });

  // const res = await ctx.authingManage.deleteUsersBatch({ userIds: [userId] });
  // if (res.statusCode !== 200) {
  //   throw new Error(res.message);
  // }
});
