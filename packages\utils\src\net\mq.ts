import axios, { AxiosInstance } from 'axios';

/**
 * MQ推送选项
 */
type MqPushOptions = {
  priority: number;
  attempts: number;
  backoff: {
    type: string;
    delay: number;
  };
};

/**
 * MQ 客户端
 */
export class MqClient {
  private host: string;
  private authorization: string;
  private request: AxiosInstance;

  constructor(config: { host: string; authorization: string; request?: AxiosInstance }) {
    this.host = config.host;
    this.authorization = config.authorization;
    this.request = config.request || axios.create();
  }

  /**
   * MQ推送
   */
  async push<T>(params: {
    /**
     * MQ地址 默认使用构造函数中的host
     */
    host?: string;
    /**
     * MQ主题 对应HTTP请求的路径
     */
    topic: string;
    /**
     * 任务名称 仅用于标识任务
     */
    name: string;
    /**
     * 任务数据
     */
    data: T;
    /**
     * 推送选项
     */
    options: MqPushOptions;
  }) {
    const { name, data, options, host = this.host } = params;
    const opts: MqPushOptions = {
      priority: options.priority ?? 5,
      attempts: options.attempts ?? 3,
      backoff: { type: options.backoff.type ?? 'exponential', delay: options.backoff.delay ?? 1000 },
    };

    // TODO: 没有对mq的返回值进行处理，无法判断是否成功
    await this.request.post(host + params.topic, [{ name: name, data: data, opts }], {
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${this.authorization}` },
    });
  }
}
