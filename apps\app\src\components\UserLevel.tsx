import React, { useState, useEffect, useMemo } from 'react';
import { Triangle } from '@/components/icon';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { VipData } from './ButtonAvatar';
import { ProImage } from './pro/pro-image';
enum UserLevelStatus {
  Active = 1,
  Expiring = 2,
  Expired = 3,
}

const vipComponents: Record<string, string> = {
  尝鲜版: 'icons/vips/vip_fresh.png',
  基础版: 'icons/vips/vip_base.png',
  标准版: 'icons/vips/vip_standard.png',
  专业版: 'icons/vips/vip_major.png',
};
export default function UserLevel({
  userLevel,
  formatDate,
}: {
  userLevel: VipData | undefined;
  formatDate: (date: Date) => string;
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [levelStatus, setLevelStatus] = useState<UserLevelStatus>(UserLevelStatus.Active);
  const [remainingTime, setRemainingTime] = useState<string>('');
  useEffect(() => {
    if (!userLevel) return;
    const now = new Date();
    const endTime = new Date(userLevel.end_time);
    const differenceInDays = (endTime.getTime() - now.getTime()) / (1000 * 3600 * 24);

    if (differenceInDays <= 7) {
      setLevelStatus(UserLevelStatus.Expiring);

      const updateRemainingTime = () => {
        const timeLeft = endTime.getTime() - new Date().getTime();
        if (timeLeft <= 0) {
          setLevelStatus(UserLevelStatus.Expired);
          return;
        }
        const days = Math.floor(timeLeft / (1000 * 3600 * 24));
        const hours = Math.floor((timeLeft % (1000 * 3600 * 24)) / (1000 * 3600));
        const minutes = Math.floor((timeLeft % (1000 * 3600)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        setRemainingTime(`${days}天${hours}小时${minutes}分钟${seconds}秒`);
      };
      updateRemainingTime();
      const interval = setInterval(updateRemainingTime, 1000);
      return () => clearInterval(interval);
    }
  }, [userLevel]);
  const formattedEndDate = useMemo(() => (userLevel ? formatDate(userLevel.end_time) : ''), [userLevel, formatDate]);

  const renderTooltipContent = () => {
    switch (levelStatus) {
      case UserLevelStatus.Active:
        return <div className="text-[#B2B6C3]">使用权益将于 {formattedEndDate} 到期</div>;
      case UserLevelStatus.Expiring:
        return (
          <div className="max-w-[200px] break-words text-[#9FA4B2]">
            会员权益还剩
            <span className="text-[#F3A93C]">{remainingTime}到期</span>
            ，账号内还剩
            <span className="text-[#F3A93C]"> {userLevel?.quota}点数，到期将会清零。</span>
            <span className="text-white">请联系销售充值</span>
          </div>
        );
      case UserLevelStatus.Expired:
        return (
          <div>
            <span className="text-[#9FA4B2]">会员权益已于{formattedEndDate} 到期，</span>
            请联系销售充值
          </div>
        );
    }
  };
  return (
    <TooltipProvider>
      <Tooltip
        open={
          levelStatus === UserLevelStatus.Expiring ||
          levelStatus === UserLevelStatus.Expired ||
          (levelStatus === UserLevelStatus.Active && isHovered)
        }
      >
        <TooltipTrigger asChild>
          <div
            className="flex items-center justify-center p-[6px] text-[11px]"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {!!(userLevel?.plan_name && vipComponents[userLevel.plan_name]) && (
              <ProImage
                width={64}
                height={64}
                alt="vip"
                src={vipComponents[userLevel.plan_name]!}
                className="h-[18px] w-[18px]"
              />
            )}
            {userLevel?.plan_name ? (
              <div className="text-primary font-medium"> {userLevel?.plan_name}</div>
            ) : (
              <div className="h-[16px] w-[36px] bg-gray-700/50"></div>
            )}
          </div>
        </TooltipTrigger>
        {userLevel?.plan_name !== '普通用户' && (
          <TooltipContent className="mr-20 text-[13px]">{renderTooltipContent()}</TooltipContent>
        )}
        {(levelStatus === UserLevelStatus.Expiring || levelStatus === UserLevelStatus.Expired) && (
          <div className="tooltip-container relative">
            <div className="transform-translateX-minus-50 absolute right-10 top-[9px]">
              <Triangle />
            </div>
          </div>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}
