'use client';

import { GENERATE_MATERIAL_LIBRARY, UPLOAD_MATERIAL_LIBRARY } from '@/common/statics/zh_cn';
import { Highlight } from '@/components/icon';
import LibraryContainer from '@/components/LibraryContainer';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui';
import useMaterialStore from '@/store/materialStore';
import { preventTab } from '@/utils/common';
import { useEffect } from 'react';

const DEFAULT_PANEL_STYLE = {
  height: 'calc(100vh - 32px)',
};

export default function CloudTabs() {
  const { setCloudTab, cloudTab, setBatchMode } = useMaterialStore();
  const handleTabChange = (value: string) => {
    setCloudTab(Number(value));
  };

  useEffect(() => {
    preventTab();

    return () => {
      setBatchMode(false);
    };
  }, []);

  return (
    <div style={DEFAULT_PANEL_STYLE} className="w-full rounded-2xl border border-[#EFEDFD] border-opacity-10 py-5">
      <Tabs value={String(cloudTab)} onValueChange={handleTabChange} className="ring-transparent">
        <TabsList className="mb-5 gap-8 bg-transparent px-8" onChange={(value) => setCloudTab(Number(value))}>
          <div className="relative">
            <TabsTrigger
              className="bg-transparent p-0 text-base text-[#9FA4B2] data-[state=active]:bg-transparent data-[state=active]:text-[#00e1ff] data-[state=active]:shadow-none"
              value="1"
            >
              {UPLOAD_MATERIAL_LIBRARY}
            </TabsTrigger>
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
              {cloudTab === 1 && <Highlight />}
            </div>
          </div>
          <div className="relative">
            <TabsTrigger
              className="bg-transparent p-0 text-base text-[#9FA4B2] data-[state=active]:bg-transparent data-[state=active]:text-[#00e1ff] data-[state=active]:shadow-none"
              value="2"
              // style={{ color: currentStep === 5 ? '#00e1ff' : 'none' }}
            >
              {GENERATE_MATERIAL_LIBRARY}
            </TabsTrigger>
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
              {cloudTab === 2 && <Highlight />}
            </div>
          </div>
        </TabsList>
        <TabsContent
          value="1"
          className="mt-0 h-full pl-8 shadow-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
        >
          <LibraryContainer />
        </TabsContent>
        <TabsContent
          value="2"
          className="mt-0 h-full pl-8 shadow-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
        >
          <LibraryContainer />
        </TabsContent>
      </Tabs>
    </div>
  );
}
