'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogFooter,
  <PERSON>alogTitle,
  DialogTrigger,
  Button,
  DialogClose,
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui';
import { useEffect } from 'react';
import { Close } from '@/components/icon/close';
import { CANCEL, UPLOAD_TO_CLOUD_LIBRARY, CONFIRM, CREATE_FOLDER, MOVE_TO, SAVE_TO } from '@/common/statics/zh_cn';
import { UploadToCloud } from '@/components/icon/uploadToCloud';
import { ArrowLeftAndRight } from '@/components/icon/arrowLeftAndRight';
import { Plus } from 'lucide-react';
import DirList from './Dir<PERSON>ist';
import { UploadStatus } from '@/types/upload';
import useMaterialStore from '@/store/materialStore';
import { useBreadcrumbs } from '@/hooks/useBreadcrumbs';
import { useDirs } from '@/hooks/useDirs';
import { DirItemType } from '@/types/material';
import { Loader } from '@/components/icon/loader';
import { useRoots } from '@/hooks/useRoots';

export function DirModal({
  showTrigger = true,
  onConfirm,
  disabledDir,
  mode = 'save',
}: {
  showTrigger?: boolean;
  onConfirm?: (id: string) => void;
  disabledDir?: string;
  mode?: 'move' | 'save';
}) {
  const { uploadList, setUploadTargetDir, setDirModalOpen, dirModalOpen, cloudTab } = useMaterialStore();
  const { dirList, loading, getDirs, addTmpDir, createDir, cancelCreating, isSubmitting } = useDirs();
  const { data: roots } = useRoots();
  const currRoot = roots?.find((item) => item.name === (cloudTab === 1 ? '原始素材' : '生成素材'));

  const { breadcrumbItems, handleBreadcrumbClick, handleAddBreadcrumb, resetBreadcrumb } = useBreadcrumbs();

  const [displayTargetDir, setDisplayTargetDir] = useState<any>(currRoot);

  const renderBreadcrumbItem = (item: DirItemType, index: number) => {
    const isRoot = index === 0;
    const isLast = index === breadcrumbItems.length - 1;
    const showEllipsis = breadcrumbItems.length > 3 && index === 1;

    if (breadcrumbItems.length > 3 && !isRoot && !isLast && !showEllipsis) {
      return null;
    }

    if (showEllipsis) {
      return (
        <BreadcrumbItem key="ellipsis" className="cursor-pointer hover:text-white">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-5 p-0">
                <BreadcrumbEllipsis />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {breadcrumbItems.slice(1, -1).map((dropdownItem, dropdownIndex) => (
                <DropdownMenuItem
                  key={dropdownIndex}
                  onClick={() => {
                    handleBreadcrumbClick(dropdownIndex + 1);
                    getDirs({
                      parentId: dropdownItem?.id === 'root' ? currRoot?.id : (dropdownItem?.id ?? ''),
                    });
                  }}
                >
                  {dropdownItem.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </BreadcrumbItem>
      );
    }
    if (isLast) {
      return (
        <BreadcrumbItem key={index} className="cursor-pointer text-white hover:text-white">
          <BreadcrumbPage>{item.name}</BreadcrumbPage>
        </BreadcrumbItem>
      );
    }

    return (
      <BreadcrumbItem key={index} className="cursor-pointer hover:text-white">
        <span
          onClick={() => {
            handleBreadcrumbClick(index);
            getDirs({
              parentId: item.id === 'root' ? currRoot?.id : (item?.id ?? ''),
            });
          }}
        >
          {item.name}
        </span>
      </BreadcrumbItem>
    );
  };

  const handleConfirm = () => {
    if (breadcrumbItems.length > 1) {
      setUploadTargetDir(breadcrumbItems[breadcrumbItems.length - 1]);
      setDisplayTargetDir(breadcrumbItems[breadcrumbItems.length - 1]);
    } else {
      setDisplayTargetDir(currRoot);
    }
    onConfirm?.(breadcrumbItems[breadcrumbItems.length - 1]?.id ?? '');
  };

  useEffect(() => {
    getDirs({ parentId: currRoot?.id ?? '' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (dirModalOpen) {
      getDirs({ parentId: currRoot?.id ?? '' });
      resetBreadcrumb();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dirModalOpen]);

  const uploading = uploadList.some((item) => item.status === UploadStatus.UPLOADING);

  return (
    <Dialog
      open={dirModalOpen}
      onOpenChange={(open) => {
        if (!open) {
          setUploadTargetDir({
            id: currRoot?.id ?? '',
            name: currRoot?.name ?? '',
          });
          setDirModalOpen(false);
        }
      }}
    >
      {showTrigger && (
        <DialogTrigger asChild>
          <Button
            variant="secondary"
            disabled={uploading || loading}
            className="flex h-8 items-center rounded-lg bg-[#CCDDFF] bg-opacity-10 p-3 pr-2.5 hover:bg-[#CCDDFF] hover:bg-opacity-10"
            onClick={() => setDirModalOpen(true)}
          >
            {loading ? <Loader className="h-4 w-4" /> : <UploadToCloud />}
            <div className="ml-1 flex items-center">
              <span className="mr-2.5 text-xs font-normal text-[#9FA4B2] hover:text-white">
                {displayTargetDir?.name === '原始素材' ? UPLOAD_TO_CLOUD_LIBRARY : displayTargetDir?.name}
              </span>
              <ArrowLeftAndRight />
            </div>
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="flex h-[590px] w-[720px] max-w-[720px] flex-col justify-start gap-0 rounded-2xl border-none bg-[#151c29] p-0 pl-8">
        <DialogClose asChild>
          <Close className="absolute right-4 top-4 cursor-pointer" />
        </DialogClose>
        <DialogTitle className="mb-8 mt-6 text-center text-base font-medium text-white">
          {mode === 'move' ? MOVE_TO : SAVE_TO}
        </DialogTitle>
        <div className="flex h-full flex-col justify-between pb-6">
          <div className="flex flex-grow flex-col divide-[#1f2434]">
            <div className="relative mx-2 mb-4 flex items-center justify-between pr-8">
              <Breadcrumb>
                <BreadcrumbList className="h-5">
                  {breadcrumbItems.map((item, index) => (
                    <React.Fragment key={index}>
                      {renderBreadcrumbItem(item, index)}
                      {index < breadcrumbItems.length - 1 && renderBreadcrumbItem(item, index) && (
                        <BreadcrumbSeparator />
                      )}
                    </React.Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
              {mode === 'save' && breadcrumbItems.length < 5 && (
                <Button
                  variant="link"
                  disabled={dirList?.some((item) => item.status === 'creating')}
                  className="flex h-5 items-center gap-1 p-0 text-center text-sm text-white hover:text-[#00E1FF] hover:no-underline"
                  onClick={addTmpDir}
                >
                  <Plus className="h-3 w-3" />
                  {CREATE_FOLDER}
                </Button>
              )}
            </div>
            <div className="mr-8 border border-[#3A4053] border-opacity-50"></div>
            <div className="flex max-h-[400px] flex-grow flex-col overflow-y-auto pr-8 pt-5">
              <DirList
                onSelectDir={(item: DirItemType) => {
                  handleAddBreadcrumb(item);
                  getDirs({ parentId: item?.id ?? '' });
                }}
                dirs={dirList}
                loading={loading}
                cancelCreating={cancelCreating}
                submitCreating={(name: string) => {
                  const isOnMaterialRoot = breadcrumbItems[breadcrumbItems.length - 1]?.id === 'root';
                  createDir({
                    name,
                    parentId: isOnMaterialRoot ? currRoot?.id : (breadcrumbItems[breadcrumbItems.length - 1]?.id ?? ''),
                  });
                }}
                isSubmitting={isSubmitting}
              />
            </div>
          </div>
          <DialogFooter className="flex items-center justify-end pr-8 pt-4">
            <DialogClose asChild>
              <Button
                variant="secondary"
                className="h-8 w-[92px] rounded-lg bg-[#CCDDFF] bg-opacity-10 text-white hover:bg-[#CCDDFF] hover:bg-opacity-10"
              >
                {CANCEL}
              </Button>
            </DialogClose>
            <Button
              variant="default"
              type="button"
              disabled={isSubmitting || disabledDir === breadcrumbItems[breadcrumbItems.length - 1]?.id}
              className="h-8 w-[92px] rounded-lg bg-[#4BEAFF] bg-opacity-85 text-black hover:bg-[#4BEAFF] hover:bg-opacity-85"
              onClick={handleConfirm}
            >
              {CONFIRM}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
