import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { pageCloudTerminals } from '@/services/actions/cloud-terminal';
import { ActionParams, ActionResult } from '@/utils/server-action/action';

export const createInbuiltColumns = (): ProTableColumnType<
  ActionResult<typeof pageCloudTerminals>['list'][number]
>[] => {
  return [
    {
      key: 'bind_social_account',
      title: '绑定账号',
      render: (_, row) => row.social_accounts?.nickname,
    },
    { key: 'status', title: '终端状态' },
    { key: 'cloud_host_identity', title: '云主机ID' },
    { key: 'cloud_host_browser_identity', title: '浏览器ID' },
    { key: 'cloud_host_cos_path', title: 'COS路径' },
    { key: 'remark', title: '备注', editable: true },
  ];
};

export const filterColumns: ProFilterColumnType<ActionParams<typeof pageCloudTerminals>['filters']>[] = [
  {
    key: 'cloud_host_identity',
    title: '云主机ID',
  },
  {
    key: 'remark',
    title: '备注',
  },
  {
    key: 'status',
    title: '终端状态',
    type: 'enum',
    config: {
      options: [
        { value: '空闲', label: '空闲' },
        { value: '锁定', label: '锁定' },
        { value: '已绑定', label: '已绑定' },
      ],
    },
  },
];
