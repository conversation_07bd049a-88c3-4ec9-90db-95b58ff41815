import { getUserTotalStore } from '@/services/actions/materials';
import { getQuotas } from '@/services/actions/pricing_plans';
import { action, ActionResult } from '@/utils/server-action/action';
import { pricing_plans } from '@roasmax/database';
import { useCallback, useMemo } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

const walletStore = create<{
  quota: number | null;
  plan: pricing_plans | null;
  setQuota: (value: number) => void;
  setPlan: (value: pricing_plans) => void;
}>()(
  devtools((set) => ({
    quota: null,
    plan: null,
    setQuota: (value) => set({ quota: value }),
    setPlan: (value) => set({ plan: value }),
  })),
);

/**
 * 获取/更新钱包信息
 * @returns
 */
export const useWallet = () => {
  const { quota: quota, plan, setQuota, setPlan } = walletStore();

  const refresh = useCallback(async () => {
    const wallet = await action(getQuotas, undefined);
    if (!wallet) return;
    setQuota(wallet.quota);
    setPlan(wallet.grade);
  }, [setQuota, setPlan]);

  return useMemo(() => ({ quota, refresh, plan }), [quota, refresh, plan]);
};

const cloudStorageSizeStore = create<{
  storage: ActionResult<typeof getUserTotalStore>;
  setStorage: (value: ActionResult<typeof getUserTotalStore>) => void;
}>()(
  devtools((set) => ({
    storage: { used: 0, total: 0 },
    setStorage: (value) => set({ storage: value }),
  })),
);

/**
 * 获取/更新云盘存储信息
 * @returns
 */
export const useCloudStorageSize = () => {
  const { storage, setStorage } = cloudStorageSizeStore();

  // 刷新本地存储信息
  const refresh = useCallback(async () => {
    const res = await action(getUserTotalStore, undefined);
    setStorage(res ? { used: res.used, total: res.total } : { used: 0, total: 0 });
  }, [setStorage]);

  return useMemo(() => ({ storage, refresh }), [storage, refresh]);
};
