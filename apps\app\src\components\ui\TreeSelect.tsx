import { cva, type VariantProps } from 'class-variance-authority';
import { CheckIcon, ChevronUp, ChevronDown, XIcon, ChevronRight } from 'lucide-react';

import { cn } from '@/utils/cn';
import { Separator } from '@/components/ui/Separator';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/Command';
import * as React from 'react';

const treeSelectVariants = cva(
  'mr-2 transition ease-in-out delay-150 hover:translate-y-0 hover:scale-110 duration-300',
  {
    variants: {
      variant: {
        default: 'border-foreground/10 text-foreground bg-card hover:bg-card/80',
        secondary: 'border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        inverted: 'inverted',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export interface TreeNode {
  /** The text to display for the option. */
  label: string;
  /** The unique value associated with the option. */
  value: string;
  /** Optional icon component to display alongside the option. */
  icon?: React.ComponentType<{ className?: string }>;
  /** Child nodes for tree structure */
  children?: TreeNode[];
  /** Whether the node is disabled */
  disabled?: boolean;
}

interface TreeSelectProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof treeSelectVariants> {
  /**
   * An array of TreeNode objects to be displayed in the tree-select component.
   */
  options: TreeNode[];

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange: (value: string[]) => void;

  /** The default selected values when the component mounts. */
  defaultValue?: string[];

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string;

  /**
   * Animation duration in seconds for the visual effects.
   * Optional, defaults to 0.
   */
  animation?: number;

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number;

  /**
   * The modality of the popover.
   * Optional, defaults to false.
   */
  modalPopover?: boolean;

  /**
   * If true, renders the tree-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean;

  /**
   * Additional class names to apply custom styles.
   */
  className?: string;

  /**
   * Title for the placeholder.
   */
  placeholderTitle?: string;
  test?: boolean;
}

export const TreeSelect = React.forwardRef<HTMLButtonElement, TreeSelectProps>(
  (
    {
      options,
      onValueChange,
      variant,
      defaultValue = [],
      placeholder = 'Select options',
      animation = 0,
      maxCount = 3,
      modalPopover = false,
      asChild = false,
      test,
      className,
      placeholderTitle,
      ...props
    },
    ref,
  ) => {
    const [selectedValues, setSelectedValues] = React.useState<string[]>(defaultValue);
    const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
    const [expandedNodes, setExpandedNodes] = React.useState<Set<string>>(new Set());
    const [searchTerm, setSearchTerm] = React.useState('');

    // 切换节点展开/折叠状态
    const toggleNodeExpand = (value: string) => {
      const newExpanded = new Set(expandedNodes);
      if (newExpanded.has(value)) {
        newExpanded.delete(value);
      } else {
        newExpanded.add(value);
      }
      setExpandedNodes(newExpanded);
    };

    // 切换选中状态
    const toggleOption = (node: TreeNode) => {
      const allChildValues = getAllChildValues(node);
      const isFullySelected = allChildValues.every((value) => selectedValues.includes(value));

      let newSelectedValues: string[];
      if (node.children && node.children.length > 0) {
        // 如果是父节点，则切换所有子节点的选中状态
        if (isFullySelected) {
          // 如果所有子节点都被选中，则取消选中所有
          newSelectedValues = selectedValues.filter((value) => !allChildValues.includes(value));
        } else {
          // 否则选中所有子节点
          const valuesToAdd = allChildValues.filter((value) => !selectedValues.includes(value));
          newSelectedValues = [...selectedValues, ...valuesToAdd];
        }
      } else {
        // 如果是子节点，则只切换当前节点的选中状态
        if (selectedValues.includes(node.value)) {
          newSelectedValues = selectedValues.filter((value) => value !== node.value);
        } else {
          newSelectedValues = [...selectedValues, node.value];
        }
      }

      setSelectedValues(newSelectedValues);
      onValueChange(newSelectedValues);
    };

    // 递归获取所有子节点的值
    const getAllChildValues = (node: TreeNode): string[] => {
      let values: string[] = [node.value];
      if (node.children) {
        node.children.forEach((child) => {
          values = values.concat(getAllChildValues(child));
        });
      }
      return values;
    };

    // 递归获取所有父节点的值
    const getAllParentValues = (value: string, nodes: TreeNode[]): string[] => {
      for (const node of nodes) {
        if (node.value === value) return [node.value];
        if (node.children) {
          const childResult = getAllParentValues(value, node.children);
          if (childResult.length > 0) {
            return [node.value, ...childResult];
          }
        }
      }
      return [];
    };

    // 递归渲染树节点
    const renderTreeNode = (node: TreeNode, level: number = 0): React.ReactNode => {
      const isExpanded = expandedNodes.has(node.value);
      const hasChildren = node.children && node.children.length > 0;
      const isSelected = selectedValues.includes(node.value);
      const isPartiallySelected = hasChildren && node.children!.some((child) => selectedValues.includes(child.value));

      return (
        <React.Fragment key={node.value}>
          <div className="relative flex items-center">
            <CommandItem
              onSelect={() => toggleOption(node)}
              className={cn(
                'flex w-full cursor-pointer items-center',
                node.disabled && 'cursor-not-allowed opacity-50',
                level > 0 && `ml-${level * 4}`,
              )}
            >
              <div className="flex items-center">
                {hasChildren && (
                  <div
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      toggleNodeExpand(node.value);
                    }}
                    className="cursor-pointer"
                  >
                    <ChevronRight
                      className={cn('mr-1 h-4 w-4 shrink-0 transition-transform', isExpanded ? 'rotate-90' : '')}
                    />
                  </div>
                )}
                <div
                  className={cn(
                    'border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border',
                    isSelected
                      ? 'bg-primary text-primary-foreground'
                      : isPartiallySelected
                        ? 'bg-primary/50 text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                  )}
                >
                  <CheckIcon className="h-4 w-4" />
                </div>
                {node.icon && <node.icon className="mr-2 h-4 w-4 text-[#9FA4B2]" />}
                <span className="truncate">{node.label}</span>
              </div>
            </CommandItem>
          </div>
          {hasChildren && isExpanded && (
            <div className="ml-4">{node.children!.map((child) => renderTreeNode(child, level + 1))}</div>
          )}
        </React.Fragment>
      );
    };

    const handleClear = () => {
      setSelectedValues([]);
      onValueChange([]);
    };
    // 获取选中项的标签
    const getSelectedLabels = (
      nodes: TreeNode[],
    ): { label: string; value: string; icon?: React.ComponentType<{ className?: string }> }[] => {
      const result: { label: string; value: string; icon?: React.ComponentType<{ className?: string }> }[] = [];
      const traverse = (node: TreeNode) => {
        if (selectedValues.includes(node.value)) {
          result.push({ label: node.label, value: node.value, icon: node.icon });
        }
        node.children?.forEach(traverse);
      };
      nodes.forEach(traverse);
      return result;
    };
    const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        setIsPopoverOpen(true);
      } else if (event.key === 'Backspace' && !event.currentTarget.value) {
        const newSelectedValues = [...selectedValues];
        newSelectedValues.pop();
        setSelectedValues(newSelectedValues);
        onValueChange(newSelectedValues);
      }
    };
    const selectedLabels = getSelectedLabels(options);
    return (
      <div
        className={cn(
          'flex items-center rounded border border-[#363D54] text-[12px] text-white',
          test ? 'h-10 w-[360px]' : 'h-8 flex-1',
        )}
      >
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen} modal={modalPopover}>
          <PopoverTrigger asChild>
            <Button
              ref={ref}
              {...props}
              onClick={() => setIsPopoverOpen(true)}
              className={cn(
                'flex h-9 w-[204px] items-center justify-between rounded-md border-none bg-inherit p-1 hover:bg-inherit [&_svg]:pointer-events-auto',
                className,
              )}
            >
              {selectedValues.length > 0 ? (
                <div className="flex h-9 w-full items-center justify-between">
                  <div className="flex flex-wrap items-center">
                    {selectedLabels.slice(0, maxCount).map(({ label, value, icon: Icon }) => (
                      <Badge
                        key={value}
                        className={cn(
                          treeSelectVariants({ variant }),
                          'rounded bg-[#CCDDFF0D] text-sm text-[#9FA4B2] hover:bg-transparent',
                        )}
                      >
                        {label}
                        <XIcon
                          className="mx-2 h-4 cursor-pointer text-[#9FA4B2]"
                          onClick={(event) => {
                            event.stopPropagation();
                            toggleOption({ label, value, icon: Icon });
                          }}
                        />
                      </Badge>
                    ))}
                    {selectedLabels.length > maxCount && (
                      <Badge
                        className={cn(
                          'text-foreground border-foreground/1 rounded bg-[#CCDDFF0D] text-sm text-[#9FA4B2] hover:bg-transparent',
                          'max-w-[120px] truncate',
                        )}
                        style={{ animationDuration: `${animation}s` }}
                      >
                        {`已选 ${selectedValues.length}`}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <XIcon
                      className="mx-2 h-4 cursor-pointer text-[#9FA4B2]"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleClear();
                      }}
                    />
                    <Separator orientation="vertical" className="flex h-full min-h-6" />
                    {isPopoverOpen ? (
                      <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                    ) : (
                      <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                    )}
                  </div>
                </div>
              ) : (
                <div className="mx-auto flex w-full items-center justify-between">
                  <span className="pl-3 text-sm text-[#9FA4B2]">{placeholder}</span>
                  {isPopoverOpen ? (
                    <ChevronUp className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                  ) : (
                    <ChevronDown className="mx-2 h-4 cursor-pointer text-[#9FA4B2]" />
                  )}
                </div>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0" align="start">
            <Command>
              <CommandInput
                placeholder="搜索选项"
                className={cn('placeholder:text-gray-500', test ? 'text-xs' : 'text-sm')}
                onKeyDown={handleInputKeyDown}
                value={searchTerm}
                onValueChange={setSearchTerm}
              />
              <CommandList>
                <CommandEmpty>暂无数据</CommandEmpty>
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      const allValues = options.flatMap(getAllChildValues);
                      if (selectedValues.length === allValues.length) {
                        setSelectedValues([]);
                        onValueChange([]);
                      } else {
                        setSelectedValues(allValues);
                        onValueChange(allValues);
                      }
                    }}
                    className="flex-1 cursor-pointer justify-center"
                  >
                    <div className={cn('flex w-full items-center justify-between', test ? 'text-xs' : 'text-sm')}>
                      <div className="text-left text-[#9FA4B2]">
                        全部选项({options.flatMap(getAllChildValues).length})
                      </div>
                      <div className="flex justify-end">
                        <div
                          className={cn(
                            'border-primary',
                            selectedValues.length === options.flatMap(getAllChildValues).length
                              ? 'text-[#00E1FF]'
                              : 'text-[#00E1FF] opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <span>全选</span>
                        </div>
                      </div>
                    </div>
                  </CommandItem>
                </CommandGroup>
                <CommandGroup>
                  {options.map((node) => {
                    const renderFilteredNode = (node: TreeNode, searchTerm: string): React.ReactNode | null => {
                      const matchesSearch = node.label.toLowerCase().includes(searchTerm.toLowerCase());
                      const filteredChildren = node.children
                        ?.map((child) => renderFilteredNode(child, searchTerm))
                        .filter(Boolean);

                      if (matchesSearch || (filteredChildren && filteredChildren.length > 0)) {
                        return renderTreeNode({
                          ...node,
                          children: filteredChildren?.length
                            ? node.children?.filter((_, index) => filteredChildren[index])
                            : undefined,
                        });
                      }
                      return null;
                    };

                    const searchTerm = (document.querySelector('.cm-input') as HTMLInputElement)?.value || '';
                    return renderFilteredNode(node, searchTerm);
                  })}
                </CommandGroup>
                <CommandGroup>
                  <div className="flex items-center justify-between">
                    {selectedValues.length > 0 && (
                      <>
                        <CommandItem
                          // disabled={disabled}
                          onSelect={handleClear}
                          className="flex-1 cursor-pointer justify-center"
                        >
                          清空
                        </CommandItem>
                        <Separator orientation="vertical" className="flex h-full min-h-6" />
                      </>
                    )}
                    <CommandItem
                      onSelect={() => setIsPopoverOpen(false)}
                      className="max-w-full flex-1 cursor-pointer justify-center"
                    >
                      取消
                    </CommandItem>
                  </div>
                </CommandGroup>
                <CommandSeparator />
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    );
  },
);

TreeSelect.displayName = 'TreeSelect';
