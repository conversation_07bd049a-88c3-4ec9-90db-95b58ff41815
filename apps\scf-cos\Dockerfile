FROM docker-regsitry.tencentcloudcr.com/wuwang/node:18-alpine AS builder

RUN npm install -g pnpm --registry=https://mirrors.cloud.tencent.com/npm/

WORKDIR /app

COPY package.json .
COPY pnpm-lock.yaml .
COPY pnpm-workspace.yaml .
COPY turbo.json .
COPY .npmrc .

COPY apps/scf-cos/package.json apps/scf-cos/package.json
COPY packages packages/

RUN pnpm install --frozen-lockfile

COPY apps/scf-cos apps/scf-cos

RUN pnpm build --filter @roasmax/scf-cos...

FROM docker-regsitry.tencentcloudcr.com/wuwang/node:18-alpine AS runner

WORKDIR /app

COPY --from=builder /app/apps/scf-cos/dist ./dist
RUN mv dist/client/libquery_engine-*.node .

ENV NODE_ENV=production

EXPOSE 9000

CMD ["node", "dist/index.js"]