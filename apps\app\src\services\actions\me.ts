'use server';
import { ActionContext, server } from '@roasmax/serve';

export const getMeInfo = server('获取当前用户信息', async (ctx) => {
  const res = await ctx.authing.getProfile({
    withCustomData: true,
    withIdentities: true,
    withDepartmentIds: true,
  });
  if (res.statusCode !== 200) {
    throw new Error('获取用户信息失败');
  }

  const roleResult = await ctx.authing.getRoleList({ namespace: ctx.tenant.id });
  if (roleResult.statusCode !== 200) {
    throw new Error('获取用户角色失败');
  }

  return {
    userBaseData: res.data,
    userRoles: roleResult.data,
    userCompany: ctx.tenant.name,
    enabledSubSystems: ctx.tenant.config.enabled_sub_systems || [],
  };
});

/**
 * 修改自身信息
 */
export const updateMe = server('修改当前用户信息', async (ctx: ActionContext<{ nickname: string }>) => {
  const res = await ctx.authingManage.updateUser({
    userId: ctx.user.id,
    nickname: ctx.data.nickname,
  });
  if (res.statusCode !== 200) {
    throw new Error('账号修改失败');
  }
  return true;
});
/**
 * 修改自身密码
 */
export const updatePassword = server(
  '修改当前用户密码',
  async (ctx: ActionContext<{ newPassword: string; oldPassword: string }>) => {
    const newPassword: string = ctx.data.newPassword;
    const oldPassword: string = ctx.data.oldPassword;
    const res = await ctx.authing.updatePassword({ newPassword: newPassword, oldPassword: oldPassword });
    if (res.statusCode !== 200) {
      throw new Error(res.message);
    }
    ctx.authing.setAccessToken('');
    const result = await ctx.authing.revokeToken(ctx.token.replace('Bearer ', ''));
    if (!result) {
      throw new Error('密码修改成功，失效token失败，请退出重新登录: ' + res.message);
    }
    return true;
  },
);
