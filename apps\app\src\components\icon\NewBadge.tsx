export const NewBadge = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="34" height="16" viewBox="0 0 34 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 5C0 2.23858 2.23858 0 5 0H27C30.866 0 34 3.13401 34 7C34 10.866 30.866 14 27 14H0V5Z"
        fill="url(#paint0_linear_3686_1181)"
      />
      <g filter="url(#filter0_d_3686_1181)">
        <path
          d="M4.77 3.146H5.98L9.995 8.987H10.039V3.146H11.238V11H10.072L6.013 5.093H5.969V11H4.77V3.146ZM12.7837 3.146H18.4267V4.169H13.9827V6.457H18.1627V7.48H13.9827V9.977H18.6137V11H12.7837V3.146ZM19.1155 3.146H20.4685L21.9975 9.262H22.0415L23.6475 3.146H24.9015L26.5075 9.262H26.5515L28.0805 3.146H29.4335L27.1675 11H25.8915L24.2965 4.939H24.2525L22.6575 11H21.3705L19.1155 3.146Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_3686_1181"
          x="0.769531"
          y="0.146484"
          width="32.6641"
          height="15.8535"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3686_1181" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3686_1181" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_3686_1181"
          x1="-2.09341e-07"
          y1="5.83333"
          x2="34.0257"
          y2="9.68958"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#54FFE0" />
          <stop offset="0.2" stopColor="#00E1FF" />
          <stop offset="1" stopColor="#9D81FF" />
        </linearGradient>
      </defs>
    </svg>
  );
};
