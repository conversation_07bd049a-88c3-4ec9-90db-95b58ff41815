'use client';
import { Button } from '@/components/ui';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/ToggleGroup';
import { cn } from '@/utils/cn';
import React, { useEffect, useRef } from 'react';

export function ProToggleGroup({
  toggles,
  toggleItemProps,
  eclipse,
  className,
  ...props
}: Parameters<typeof ToggleGroup>[0] & {
  toggles?: { value: string; label: React.ReactNode; disabled?: boolean }[];
  toggleItemProps?: Omit<Parameters<typeof ToggleGroupItem>[0], 'key' | 'value' | 'children'>;
  eclipse?: { lines?: number };
}) {
  const [open, setOpen] = React.useState(false);
  const [heights, setHeights] = React.useState<{ lineCount: number; itemHeight: number }>({
    lineCount: 0,
    itemHeight: 0,
  });
  const toggleGroupRef = useRef<React.ElementRef<typeof ToggleGroup>>(null);

  useEffect(() => {
    if (toggleGroupRef.current && eclipse?.lines) {
      const toggleGroupHeight = toggleGroupRef.current.clientHeight;
      const itemHeight = toggleGroupRef.current.children.item(0)?.clientHeight || 0;
      const lineCount = itemHeight === 0 ? 0 : Math.floor(toggleGroupHeight / itemHeight);
      setHeights({ lineCount, itemHeight });
    }
  }, [eclipse?.lines]);

  return (
    <div>
      <div
        className={cn(
          'overflow-hidden',
          eclipse?.lines && !open ? `h-[${eclipse.lines * heights.itemHeight || 0}px]` : '',
        )}
      >
        <ToggleGroup ref={toggleGroupRef} {...props} className={cn(className)}>
          {toggles?.map((toggle) => (
            <ToggleGroupItem {...toggleItemProps} key={toggle.value} value={toggle.value} disabled={toggle.disabled}>
              {toggle.label}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
      </div>
      {!!(eclipse?.lines && heights.lineCount && heights.lineCount > eclipse.lines) && (
        <Button type="button" variant="link" onClick={() => setOpen(!open)} className="w-full">
          {open ? '收起' : '展开'}
        </Button>
      )}
    </div>
  );
}
