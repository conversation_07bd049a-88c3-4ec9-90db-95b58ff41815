/**
 * 计算切片数量
 * @param originDuration 原始素材时长
 * @param sliceDuration 切片时长
 * @returns
 */
const calcSliceCount = (originDuration: number, sliceDuration: number) => {
  if (sliceDuration !== 300) {
    return 1;
  }
  const slice = originDuration / sliceDuration;
  return slice < 1 ? 1 : Math.max(1, Math.floor(slice));
};

/**
 * 计算生成视频的切片数量
 * @param params
 * @returns
 */
export const calcGenerateCount = (params: {
  method: 'normal' | 'gc_imitate';
  sliceDuration: number;
  materialDurations: number[];
  generateRound: number;
  prompts: string[] | undefined;
  generationType?: string;
  templateCount?: number;
}) => {
  const { sliceDuration, materialDurations, generateRound, prompts, generationType = '', templateCount = 1 } = params;
  const count = materialDurations
    // 计算每个视频的切片数量
    .map((duration) => calcSliceCount(duration, sliceDuration))
    // 计算总切片数量
    .reduce((pre, cur) => pre + cur, 0);

  return (
    count *
    generateRound *
    (params.method === 'gc_imitate'
      ? generationType && generationType === '大卖推荐'
        ? 1 * templateCount
        : 1
      : prompts?.length || 0)
  );
};

/**
 * 计算成本配额
 * @param params
 * @returns
 */
export const calcCostQuota = (params: {
  method: 'normal' | 'gc_imitate';
  sliceDuration: number;
  materialDurations: number[];
  generateRound: number;
  generationType?: string;
  prompts: string[] | undefined;
  templateCount?: number;
}) => {
  const count = calcGenerateCount(params);
  return count * 10;
};

/**
 * 根据粉丝数计算每日可发视频数量
 */
export const calcDailyVideoCountByFansCount = (fansCount: number | undefined | null) => {
  if (!fansCount || fansCount < 1000) {
    return 0;
  }
  if (fansCount < 3000) {
    return 2;
  }
  if (fansCount < 10000) {
    return 5;
  }
  return 10;
};

/**
 * @param params
 */
export const calculateEvenlySpacedTimes = (params: { a: Date; b: Date; n: number }): Date[] => {
  const { a, b, n } = params;

  // 数量为0，返回空数组
  if (n <= 0) {
    return [];
  }

  const totalHours = b.getTime() - a.getTime();

  // 计算间隔
  const interval = totalHours / (n + 1);

  // 生成时间点
  return Array.from({ length: n }, (_, index) => {
    // index + 1 确保不会在起始点和终点生成时间
    return new Date(a.getTime() + interval * (index + 1));
  });
};
