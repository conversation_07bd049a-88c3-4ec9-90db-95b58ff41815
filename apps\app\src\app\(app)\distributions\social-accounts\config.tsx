'use client';
import { confirm } from '@/components/ConfirmDialog';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { SocialAccount } from './page';
import { Button } from '@/components/ui/Button';
import { action, ActionParams } from '@/utils/server-action/action';
import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { getShareLinkByAccountIds, pageSocialAccounts, removeSocialAccount } from '@/services/actions/social';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { Badge } from '@/components/ui';
import { calcDailyVideoCountByFansCount } from '@roasmax/utils';
import { useCallback, useMemo } from 'react';

export const buildSocialAccountColumns = (config: {
  displayColumns?: string[];
  onRefresh?: () => void;
}): ProTableColumnType<SocialAccount>[] => {
  const { displayColumns, onRefresh } = config;
  const columns: ProTableColumnType<SocialAccount>[] = [
    { key: 'ip', title: 'IP', editable: true },
    { key: 'nickname', title: '昵称' },
    { key: 'status', title: '状态' },
    {
      key: 'quality',
      title: '优质账号',
      type: 'boolean',
      editable: true,
      render: (value) =>
        value ? <span className="text-[#00e1ff]">是</span> : <span className="text-[#FF4D4D]">否</span>,
    },
    { key: 'fans_count', title: '平台粉丝数' },
    { key: 'account_identity', title: '账号ID' },
    { key: 'account_unique_identity', title: '账号UID' },
    { key: 'cooperation_code', title: '合作码' },
    {
      key: 'cloud_terminal_id',
      title: '云终端',
      render: (_, record) => {
        if (record.cloud_terminal?.type === '云盘') {
          return <Badge className="text-xs font-normal text-[#050A1C]">云盘</Badge>;
        }
        if (record.cloud_terminal?.type === '云主机') {
          return <Badge className="text-xs font-normal text-[#050A1C]">云主机</Badge>;
        }
        return '-';
      },
    },
    { key: 'phone_no', title: '手机号', editable: true },
    { key: 'remark', title: '备注', width: 200, editable: true },
    { key: 'level', title: '达人等级' },
    {
      key: 'daily_should_distribute',
      title: '当日应发',
      render: (_, record) => {
        const count = calcDailyVideoCountByFansCount(record.fans_count);
        return <div className="w-full text-right">{count}</div>;
      },
    },
    {
      key: 'daily_distributed',
      title: '当日已发',
      render: (_, record) => {
        return <div className="w-full text-right">{record.today_distribution_sub_tasks.length}</div>;
      },
    },
    { key: 'owner_name', title: '号主' },
    {
      key: 'action',
      title: '操作',
      fixed: 'right',
      render: (_, row) => {
        const onDelete = async (row: SocialAccount) => {
          const res = await action(removeSocialAccount, { accountId: row.id }, { errorType: 'return' });
          if (!res?.success) {
            toast.error(res?.message || '删除失败');
            return;
          }
          onRefresh?.();
          return res?.success;
        };

        const onGenerateShareLink = async (row: SocialAccount) => {
          const res = await action(getShareLinkByAccountIds, { accountIds: [row.id] });
          if (res?.[0]) {
            confirm({
              content: (
                <div>
                  <div className="text-sm font-bold">账户名</div>
                  <div className="text-sm">{row.nickname}</div>
                  <div className="text-sm font-bold">抖音号</div>
                  <div className="text-sm">{row.account_identity}</div>
                  <div className="text-sm font-bold">分享链接</div>
                  <Link className="break-all p-1 text-sm" href={res[0].shareLink} target="_blank">
                    {res[0].shareLink}
                  </Link>
                </div>
              ),
            });
          }
        };

        return (
          <div className="flex gap-2">
            <Button
              variant="link"
              size="sm"
              disabled={!row.cloud_terminal}
              onClick={() => {
                window.open(
                  `bw-file://L:/${row.tenant_id}/自动分发/分发素材/${row.cloud_terminal?.cloud_host_cos_path}`,
                );
              }}
            >
              查看素材
            </Button>
            <Button
              variant="link"
              size="sm"
              disabled={row.cloud_terminal?.type !== '云盘'}
              onClick={() => onGenerateShareLink?.(row)}
            >
              分享链接
            </Button>
            <Button
              variant="link"
              size="sm"
              onClick={() => {
                const props = {
                  content: '确定删除该账号吗？',
                  buttonText: { confirm: '删除' },
                  onConfirm: async () => {
                    await onDelete?.(row);
                  },
                };
                confirm(props);
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  return displayColumns ? columns.filter((column) => displayColumns.includes(column.key as string)) : columns;
};

export const buildFilterColumns = () => {
  return [
    {
      key: 'ip',
      title: 'IP',
      type: 'text',
    },
    {
      key: 'nickname',
      title: '昵称',
      type: 'text',
    },
    {
      key: 'account_identity',
      title: '账号ID',
      type: 'text',
    },
    {
      key: 'account_unique_identity',
      title: '账号UID',
      type: 'text',
    },
  ] as ProFilterColumnType<ActionParams<typeof pageSocialAccounts>['filters']>[];
};

// 导入表头配置
export const importHeaderConfig = [
  { key: 'platform', content: '平台', required: false, type: 'string' },
  { key: 'nickname', content: '昵称', required: true, type: 'string' },
  { key: 'ip', content: 'IP', required: false, type: 'string' },
  { key: 'status', content: '状态', required: false, type: 'string' },
  { key: 'account_identity', content: '账号ID', required: true, type: 'string' },
  { key: 'master_id', content: '团长ID', required: false, type: 'string' },
  { key: 'cloud_terminal_id', content: '云终端ID', required: false, type: 'string' },
  { key: 'phone_no', content: '手机号', required: false, type: 'string' },
  { key: 'remark', content: '备注', required: false, type: 'string' },
  { key: 'level', content: '达人等级', required: false, type: 'string' },
  { key: 'fans_count', content: '平台粉丝数', required: false, type: 'number' },
  { key: 'share_ratio', content: '平台分成比', required: false, type: 'number' },
  { key: 'last_live_at', content: '平台最近开播时间', required: false, type: 'date' },
  { key: 'live_count_30d', content: '平台30日开播场次', required: false, type: 'number' },
  { key: 'live_duration_30d', content: '平台30日开播时长', required: false, type: 'string' },
  { key: 'video_count_30d', content: '平台30日视频数', required: false, type: 'number' },
  { key: 'ip_authorization_at', content: '授权开始时间', required: false, type: 'date' },
  { key: 'mcn_bind_at', content: '绑定MCN开始时间', required: false, type: 'date' },
  { key: 'mcn_unbind_at', content: '绑定MCN结束时间', required: false, type: 'date' },
];
