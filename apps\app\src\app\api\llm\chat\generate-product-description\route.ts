import { NextRequest } from 'next/server';
import { DifyService } from '@/utils/dify';
import { api } from '@roasmax/serve';

export interface DifyResponse {
  id: string;
  workflow_id: string;
  status: 'succeeded' | 'failed' | 'pending';
  outputs: {
    result: string;
  };
  error: null | string;
  elapsed_time: number;
  total_tokens: number;
  total_steps: number;
  created_at: number;
  finished_at: number;
}

export interface ResultContent {
  title: string;
}

interface RequestBody {
  productName?: string;
  language?: string;
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 12);
}

export const POST = api(
  async (ctx, req: NextRequest) => {
    const requestId = generateRequestId();
    const requestIp = req.headers.get('x-forwarded-for') || 'unknown';
    const difyService = new DifyService(requestId, requestIp);

    try {
      const body = ctx.data as RequestBody;
      const { productName = 'test', language = 'en' } = body;

      if (!productName) {
        throw new Error('Product name is required');
      }

      const result = await difyService.request({
        apiUrl: process.env.DIFY_API_URL!,
        apiKey: process.env.DIFY_API_KEY2!,
        inputs: {
          product_name: productName,
          language: language,
        },
        responseMode: 'streaming',
        user: requestId,
        onMessage: (chunk: string) => {
          // @ts-ignore
          ctx.send(chunk);
        },
      });
      console.log('result', result);

      return result;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  },
  { sse: true },
);
