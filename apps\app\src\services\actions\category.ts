'use server';
import { ActionContext, server } from '@roasmax/serve';

/**
 * 获取成员订阅的分类列表
 */
export const getMemberSubscribedCategories = server(
  '获取成员订阅的分类列表',
  async (ctx: ActionContext<{ userId: string }>) => {
    // 查询该成员订阅的所有分类
    const subscribedCategories = await ctx.db.member_subscribed_categories.findMany({
      where: { user_id: ctx.data.userId },
      orderBy: { tmp_created_at: 'asc' },
    });

    return {
      list: subscribedCategories,
    };
  },
);

/**
 * 更新用户订阅分类列表
 */
export const modifySubscribedCategories = server(
  '修改成员订阅的分类',
  async (ctx: ActionContext<{ userId: string; categoryIds: string[] }>) => {
    // 1. 获取用户当前的订阅
    const existingSubscriptions = await ctx.db.member_subscribed_categories.findMany({
      where: { user_id: ctx.data.userId },
      select: { category_id: true },
    });
    const existingCategoryIds = existingSubscriptions.map((sub) => sub.category_id);

    // 2. 计算需要新增和删除的分类
    const categoryIdsToAdd = ctx.data.categoryIds.filter((id) => !existingCategoryIds.includes(id));
    const categoryIdsToRemove = existingCategoryIds.filter((id) => !ctx.data.categoryIds.includes(id));

    // 3. 删除不再需要的分类
    if (categoryIdsToRemove.length > 0) {
      await ctx.db.member_subscribed_categories.deleteMany({
        where: {
          user_id: ctx.data.userId,
          category_id: { in: categoryIdsToRemove },
        },
      });
    }

    // 4. 添加新的分类
    if (categoryIdsToAdd.length > 0) {
      await ctx.db.member_subscribed_categories.createMany({
        data: categoryIdsToAdd.map((categoryId) => ({
          tenant_id: ctx.tenant.id,
          user_id: ctx.data.userId,
          category_id: categoryId,
          tmp_created_at: new Date(),
          tmp_updated_at: new Date(),
        })),
      });
    }

    return {
      added: categoryIdsToAdd.length,
      removed: categoryIdsToRemove.length,
    };
  },
);

/**
 * 获取品类趋势分析
 */
export const getCategoryTrendsAnalysis = server(
  '获取品类趋势分析',
  async (ctx: ActionContext<{ categoryNames: string[] }>) => {
    const hosts = [
      'https://1324682537-hi0uwxfp68.ap-shanghai.tencentscf.com',
      'https://1324682537-lzdxbfbl3u.ap-shanghai.tencentscf.com',
    ];

    let lastError: Error | null = null;

    for (const host of hosts) {
      try {
        const response = await fetch(`${host}/config/weekly/hot/trends`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            category_names: ctx.data.categoryNames,
          }),
        });

        if (!response.ok) {
          throw new Error('获取趋势分析失败');
        }

        return await response.json();
      } catch (error) {
        lastError = error as Error;
        continue; // 如果当前host失败，继续尝试下一个
      }
    }

    // 如果所有host都失败了，抛出最后一个错误
    throw new Error('获取趋势分析失败: ' + (lastError?.message || '所有服务器均无响应'));
  },
);

/**
 * 获取品类视频
 */
export const getCategoryVideos = server('获取品类视频', async (ctx: ActionContext<{ videoIds: string[] }>) => {
  const hosts = [
    'https://1324682537-hi0uwxfp68.ap-shanghai.tencentscf.com',
    'https://1324682537-lzdxbfbl3u.ap-shanghai.tencentscf.com',
  ];
  let lastError: Error | null = null;
  for (const host of hosts) {
    try {
      const response = await fetch(`${host}/config/batch/video/key_index`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          video_ids: ctx.data.videoIds,
        }),
      });
      if (!response.ok) {
        throw new Error('获取趋势分析视频失败');
      }
      return await response.json();
    } catch (error) {
      lastError = error as Error;
      continue; // 如果当前host失败，继续尝试下一个
    }
  }

  // 如果所有host都失败了，抛出最后一个错误
  throw new Error('获取趋势分析失败: ' + (lastError?.message || '所有服务器均无响应'));
});
