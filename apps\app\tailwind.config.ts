import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: ['./src/components/**/*.{js,ts,jsx,tsx,mdx}', './src/app/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['PingFang SC', 'sans-serif'],
        roboto: ['Roboto', 'sans-serif'],
      },

      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'gradient-primary': 'linear-gradient(125.83deg, #54FFE0 11.66%, #00E1FF 29.32%, #9D81FF 100%)',
        'gradient-secondary': 'linear-gradient(143.4deg, #1F273D 9.97%, #273048 57.9%, #36435E 93.33%)',
        'gradient-accent': 'linear-gradient(88.65deg, #FFE1A5 16.05%, #FFB697 72.93%)',
        'custom-radial-gradient':
          'radial-gradient(97.48% 360.69% at 2.36% 50%, #9BF7FE 0%, #DADAFF 57.5%, #F6F6F9 96.5%)',
        'custom-gradient': 'linear-gradient(105.78deg, #1D293F 1.55%, rgba(42, 52, 77, 0.1) 96.63%)',
      },
      colors: {
        DEFAULT: '#FFFFFF',
        popover: {
          foreground: '#FFFFFF',
        },
        card: {
          foreground: '#FFFFFF',
        },
        primary: {
          foreground: '#FFFFFF',
        },
        secondary: {
          foreground: '#FFFFFF',
        },
        accent: {
          foreground: '#00E1FF',
        },
        muted: {
          foreground: '#81889D',
        },
        destructive: {
          foreground: '#FFFFFF',
        },
        checkbox: {
          DEFAULT: 'transparent',
          checked: '#000',
        },
        ring: 'transparent',
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
          glass: {
            DEFAULT: 'hsl(var(--sidebar-glass-background))',
            foreground: 'hsl(var(--sidebar-glass-foreground))',
            accent: 'hsl(var(--sidebar-glass-accent))',
            'accent-foreground': 'hsl(var(--sidebar-glass-accent-foreground))',
          },
        },
        text: {
          // 主要文本
          DEFAULT: '#FFFFFF',
          primary: '#FFFFFF',
          secondary: '#FFFFFFCC',

          // 次要文本
          muted: {
            DEFAULT: '#9FA4B2',
            secondary: '#9FA4B2CC',
            light: '#81889D',
            lighter: '#81889D99',
          },

          // 功能色文本
          accent: {
            DEFAULT: '#00E1FF',
            secondary: '#00E1FF1A',
          },
          success: '#60D2A7',
          warning: '#F3A93C',
          error: '#FF6161',

          // 特殊用途
          dark: '#050A1C',
          'dark-secondary': '#050A1CCC',
          disabled: '#727485',
          placeholder: '#7E8495',
          description: '#95A0AA',
          link: '#B2B6C3',
        },
      },
      backgroundColor: {
        // 背景色不存在会覆盖其他图层的情况，可以使用透明度。否则不可出现透明度的背景色，或须加模糊效果。
        // #1F2434 : Input背景色, Select选择框背景色，Select选项列表背景色（弹出层），Toggle背景色，文件夹图标背景色，SecondaryButton背景色，Card背景色
        // #151C29 ：Dialog背景色, Drawer背景色
        // #FFFFFF1A : Header标签背景色(这个能和Panel背景色一致吗)，新手引导背景色（这个能和弹出层背景色一致吗）
        // #FFFFFF0D ：首页面板背景色，视频预览按钮背景色（这个能和SecondaryButton一致吗）
        // #272D3E : 任务进行中卡片背景色（能和DisabledButton一致吗）
        // #2A2F3E80 ：Header头像弹出框背景色（和弹出层保持一致，最好不要有透明色）
        // #2D3245 : DisabledButton

        // 层级背景色：表层级关系，颜色越亮饱和度越高，层级越高。
        // #030A18 : 层级1 默认背景色
        // #FFFFFF0D ：层级2 首页面板背景色，Header标签背景色，这个颜色是特有颜色，主要为了叠加默认背景色，所以有透明背景
        // #151C29：层级3 全局弹出层背景色
        // #1F2434 : 层级4.1 Input背景色, Select选择框背景色，Toggle背景色，文件夹图标背景色，SecondaryButton背景色，Card背景色，弹出层背景色，新手引导背景色
        // #2D3245 ：层级4.2 任务进行中卡片背景色，视频预览按钮背景色（这个UI上展示效果本质是DisabledButton）
        // 失能：50%透明度

        // 面板色
        DEFAULT: '#030A18',
        panel: '#FFFFFF0D', // Bowong 定制
        background: '#1F2434',
        popover: '#1F2434',
        card: '#1F2434CC',
        // 功能色
        primary: '#00E1FF',
        secondary: '#273048',
        accent: '#273048',
        muted: '#030A1855',
        destructive: '#FF4D4D',
        // 组件色
        input: '#2D3245',
        checkbox: {
          DEFAULT: 'transparent',
          checked: '#fff',
        },
      },
      borderColor: {
        DEFAULT: '#242938',
      },
      radius: '0.5rem',
      keyframes: {
        'border-flow': {
          '0%, 100%': { borderColor: 'rgba(239, 237, 253, 0.1)' },
          '50%': { borderColor: 'rgba(239, 237, 253, 0.3)' },
        },
      },
      animation: {
        'border-flow': 'border-flow 2s ease-in-out infinite',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
};

export default config;
