import React, { memo, useCallback, useMemo, useRef, useState, useLayoutEffect, useEffect } from 'react';
import { throttle, minBy, maxBy, noop } from 'lodash';
import { ArrowBigUp } from 'lucide-react';

import { getBoxSizeByMedia, BoxMediaType, BoxMedia } from './getBoxSizes';
import MediaBox from './MediaBox';

/**
 * 单个渲染数据源
 */
type RawDataSourceType = {
  w: number;
  h: number;
  data: any;
};
/**
 * 处理好定位的数据
 */
type RawDataType = RawDataSourceType & {
  x: number;
  y: number;
};

const BOTTOM_LOAD_LIMIT_PIXEL = 150;
// 截流限制
const THROTTLE_TIME = 128;

const BackTop = ({ getScrollElement }: { getScrollElement: () => HTMLElement }) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const element = getScrollElement();

    const handleScroll = () => {
      const scrollTop = element.scrollTop;
      setShow(scrollTop > 200);
    };

    element.addEventListener('scroll', handleScroll);
    // 初始检查
    handleScroll();

    return () => {
      element.removeEventListener('scroll', handleScroll);
    };
  }, [getScrollElement]);

  const handleClick = () => {
    const element = getScrollElement();
    element.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  if (!show) return null;

  return (
    <div
      onClick={handleClick}
      className="animate-fade-in fixed bottom-20 right-10 flex cursor-pointer items-center justify-center rounded-full bg-black/20 p-2"
    >
      <ArrowBigUp size={24} />
    </div>
  );
};

const getRange = <T extends { y: number; h: number }>(
  wList: T[],
  topHeight: number,
  scrollTop: number,
  offsetHeight: number,
  paddingHeight: number,
  space: number,
) => {
  let firstIndex = -1;
  let lastIndex = 0;
  for (let i = 0; i < wList.length; i++) {
    const item = wList[i]!;
    // 设置 PADDING_TRAIL 满足条件 > item.h
    if (firstIndex === -1 && topHeight + item.y + item.h - scrollTop > -paddingHeight) {
      firstIndex = i;
    }
    if (topHeight + item.y + space - scrollTop < offsetHeight + paddingHeight) {
      lastIndex = i;
    }
  }

  return [firstIndex < 0 ? 0 : firstIndex, lastIndex];
};

export const useWaterfall: (
  waterfallRef: React.RefObject<HTMLDivElement>,
  payload: {
    containerId: string;
    getData: () => void;
    list: RawDataSourceType[];
    component: React.ComponentType<any>;
    maxH: number; // 允许盒子最高
    column: number; // 列数量
    display?: boolean; // 是否触发渲染
    componentProps?: { [key: string]: any }; // 组件props
    topHeight?: number; // 距离顶部空白间距
    itemPadding: number; // 盒子之前的间距
    itemPaddingY: number;
  },
) => React.ReactNode = (
  waterfallRef,
  {
    containerId,
    getData,
    list,
    display = true,
    component: WaterfallItem,
    componentProps = {},
    topHeight = 0,
    itemPadding,
    itemPaddingY,
    maxH = 200,
    column,
  },
) => {
  const waterOptionsRef = useRef<{
    renderIndex: [number, number];
    columns: { list: any; h: number }[];
  }>({
    renderIndex: [-1, -1],
    columns: [],
  });

  const listRef = useRef<HTMLDivElement>(null);

  // 待渲染列表
  // @ts-ignore
  const [wList, setWList] = useState<any>(() => []);
  // 渲染列表
  const [renderList, setRenderList] = useState<RawDataType[]>([]);
  // 计算内置容器高度
  const [gridHeight, setGridHeight] = useState(0);

  const dispatchScroll = useCallback(() => {
    const scrollEvent = new Event('scroll');
    waterfallRef.current?.dispatchEvent(scrollEvent);
  }, [waterfallRef]);

  const handleScroll = useMemo(() => {
    return throttle(() => {
      if (!wList.length) {
        return;
      }
      const waterfallDom = document.getElementById(containerId) || waterfallRef.current;
      if (!waterfallDom) return;
      const { scrollTop } = waterfallDom;
      const waterfallHeight = waterfallDom.offsetHeight || window.innerHeight;
      const { renderIndex: prevRenderIndex = [-1, -1], columns } = waterOptionsRef.current;
      const minColumn = minBy(columns, (column: any) => column.h)!;
      // const columnsLength = columns.length;
      const listOffsetTop = listRef.current?.offsetTop ?? 0;

      const renderIndex = getRange(wList, topHeight + listOffsetTop, scrollTop, waterfallHeight, maxH, itemPadding);

      if (renderIndex[0] !== prevRenderIndex[0] || renderIndex[1] !== prevRenderIndex[1]) {
        const renderList = wList.slice(renderIndex[0], renderIndex[1]! + 1);
        setRenderList(renderList);
        Object.assign(waterOptionsRef.current, {
          renderIndex,
        });
      }

      if (scrollTop + waterfallHeight > minColumn.h + topHeight - BOTTOM_LOAD_LIMIT_PIXEL) {
        getData();
      }
    }, THROTTLE_TIME);
  }, [wList, waterfallRef, topHeight, maxH, itemPadding, itemPaddingY, getData, containerId]);

  // 初始化
  useLayoutEffect(() => {
    const dom = document.getElementById(containerId);
    dom?.addEventListener('scroll', handleScroll);
    return () => {
      dom?.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, containerId]);

  // 触发滚动逻辑
  useLayoutEffect(() => {
    if (display && waterOptionsRef.current.columns.length) {
      dispatchScroll();
    }
  }, [dispatchScroll, wList, display]);

  useLayoutEffect(() => {
    // 计算需要分配的列数
    const columns: { h: number; list: RawDataType[] }[] = new Array(column).fill(true).map(() => ({ h: 0, list: [] }));
    const wList: RawDataType[] = [];

    list?.forEach((item, index) => {
      const minColumn = minBy(columns, (i) => i.h)!;
      const n = columns.indexOf(minColumn);
      // const columnCount = columns.length;
      const itemW = item.w;
      const itemH = item.h;
      const listItem = {
        x: n * (itemW + itemPadding),
        y: minColumn.h > 0 ? minColumn.h + itemPadding : minColumn.h,
        w: itemW,
        h: itemH,
        data: item.data,
        index,
      };
      minColumn.h = minColumn.h > 0 ? minColumn.h + listItem.h + itemPaddingY : listItem.h;
      minColumn.list.push(listItem);
      wList.push(listItem);
    });
    // 获取最高的列
    const gridHeight = maxBy(columns, (i) => i.h)!.h;

    Object.assign(waterOptionsRef.current, {
      columns,
      renderIndex: [-1, -1], // 一旦list触发了变更 renderList 必定会伴随的着变更
    });
    if (!list.length) {
      setRenderList([]);
    }
    setGridHeight(gridHeight);
    setWList(wList);
  }, [itemPadding, list, column, itemPaddingY]);

  const style = useMemo<React.CSSProperties>(
    () => ({
      position: 'relative',
      transform: 'translate3d(0, 0, 0)',
      height: gridHeight,
    }),
    [gridHeight],
  );

  return useMemo(() => {
    return (
      <div style={style} ref={listRef}>
        {renderList.map((item, index) => {
          return (
            <MediaBox
              key={item.data.id || index}
              x={item.x}
              y={item.y}
              w={item.w}
              h={item.h}
              component={WaterfallItem}
              componentProps={componentProps}
              data={item.data}
            />
          );
        })}
      </div>
    );
  }, [WaterfallItem, componentProps, renderList, style]);
};

type WaterfallProps = {
  containerId: string;
  className?: string;
  style?: React.CSSProperties;
  maxH?: number;
  topHeight?: number;
  itemPadding?: number;
  itemPaddingY?: number;
  column?: number;
  list: { w: number; h: number; data: any }[];
  getData: (...params: any[]) => void;
  component: React.ComponentType<any>;
  componentProps?: { [key: string]: any };
  beforeSlot?: React.ReactNode;
  afterSlot?: React.ReactNode;
};

const defaultList: { w: number; h: number; data: any }[] = [];

const defaultStyle: React.CSSProperties = {};

const defaultComponentProps: { [key: string]: any } = {};

const Waterfall: React.FC<WaterfallProps> = ({
  containerId,
  className = '',
  style = defaultStyle,
  maxH = 600,
  topHeight = 0,
  itemPadding = 16,
  itemPaddingY = 0,
  column = 2,
  getData = noop,
  list = defaultList,
  component,
  componentProps = defaultComponentProps,
  beforeSlot,
  afterSlot,
}) => {
  const waterRef = useRef<HTMLDivElement>(document.getElementById(containerId) as HTMLDivElement);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [hasDom, setHasDom] = useState(false);

  useLayoutEffect(() => {
    setHasDom(true);
  }, []);

  const grids = useWaterfall(waterRef, {
    getData,
    containerId,
    list,
    maxH,
    component,
    column,
    componentProps,
    topHeight,
    itemPadding,
    itemPaddingY,
  });

  const waterfallStyle = useMemo(() => {
    return {
      // overflowX: 'hidden',
      // overflowY: 'scroll', // 滚动条绘制在页面上方，而非独占一段宽度
      willChange: 'auto',
      height: '100%',
    };
  }, []);

  const waterfallWrapperStyle = useMemo(() => {
    return {
      width: '100%',
      height: '100%',
      ...style,
    };
  }, [style]);

  const getScrollElement = useCallback(() => {
    return hasDom ? waterRef.current! : window.document.documentElement;
  }, [hasDom]);

  return (
    <div style={waterfallWrapperStyle} className={className}>
      {/* <div ref={waterRef} style={waterfallStyle} className="scroll-container"> */}
      {/*  这里先把container从外部获取，不从scroll-container获取了 */}
      <div style={waterfallStyle} className="scroll-container">
        {beforeSlot ? beforeSlot : null}
        {grids}
        {afterSlot ? afterSlot : null}
      </div>
      {hasDom && <BackTop getScrollElement={getScrollElement} />}
    </div>
  );
};

const memoWaterfall = memo(Waterfall);
export { memoWaterfall as Waterfall, getBoxSizeByMedia, BoxMediaType, BoxMedia };
