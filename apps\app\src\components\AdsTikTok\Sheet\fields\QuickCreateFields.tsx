import { ProForm<PERSON>ield } from '@/components/pro/pro-form';
import { ProSelect } from '@/components/pro/pro-select';
import {
  Skeleton,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  Switch,
  RadioGroup,
  RadioGroupItem,
  Label,
} from '@/components/ui';
import { GENERATE_ROUND_OPTIONS, GENERATE_SPEED_OPTIONS } from '@/hooks/useTaskCreator';
import { UPLOAD_VIDEO_LANGUAGES } from '@/common/statics/languages';

interface QuickCreateFieldsProps {
  form: any;
  loadingPrompts: boolean;
  promptTree: any[];
}

export const QuickCreateFields = ({ form, loadingPrompts, promptTree }: QuickCreateFieldsProps) => {
  return (
    <>
      <ProFormField
        name="industry"
        label={
          <div className="w-1/5 whitespace-nowrap text-sm text-white">
            行业选择 <span className="ml-2 text-red-500">*</span>
          </div>
        }
        layout="horizontal"
        className="flex items-center justify-start gap-[70px]"
        form={form}
        renderFormControl={(field) => {
          if (loadingPrompts) {
            return (
              <div className="w-4/5">
                <Skeleton className="h-8 w-full" />
              </div>
            );
          }

          if (!promptTree || promptTree.length === 0) {
            return null;
          }

          return (
            <ProSelect
              value={field.value}
              onValueChange={(v) => {
                field.onChange(v);
                form.setValue('prompts', []);
                const selectedIndustry = promptTree.find((item) => item.industry === v);
                if (selectedIndustry && selectedIndustry.durations.length > 0) {
                  const firstModel = selectedIndustry.durations[0]?.model[0]?.value;
                  if (firstModel) {
                    form.setValue('prompts', [firstModel]);
                  }
                }
              }}
              placeholder={<span className="font-normal text-[#9FA4B2]">请选择行业类目</span>}
              options={promptTree?.map((v) => ({ value: v.industry, label: v.industry }))}
              className="w-[360px] bg-[#CCDDFF1A]"
            />
          );
        }}
      />

      <ProFormField
        name="generateRound"
        label={
          <div className="w-1/5 whitespace-nowrap text-sm text-white">
            生成轮次 <span className="ml-2 text-red-500">*</span>
          </div>
        }
        form={form}
        layout="horizontal"
        className="flex items-center justify-start gap-[70px]"
        renderFormControl={(field) => (
          <ProSelect
            value={field.value}
            onValueChange={field.onChange}
            placeholder={<span className="font-normal text-[#9FA4B2]">请选择生成轮次</span>}
            options={GENERATE_ROUND_OPTIONS.map((value) => ({ value, label: value }))}
            className="w-[360px] bg-[#CCDDFF1A]"
          />
        )}
      />

      <ProFormField
        name="speed"
        label={
          <div className="w-1/5 whitespace-nowrap text-sm text-white">
            视频加速 <span className="ml-2 text-red-500">*</span>
          </div>
        }
        form={form}
        layout="horizontal"
        className="flex items-center justify-start gap-[70px]"
        renderFormControl={(field) => (
          <ProSelect
            value={field.value}
            onValueChange={field.onChange}
            placeholder={<span className="font-normal text-[#9FA4B2]">请选择视频加速</span>}
            options={GENERATE_SPEED_OPTIONS.map((value) => ({ value, label: value }))}
            className="w-[360px] bg-[#CCDDFF1A]"
          />
        )}
      />

      <ProFormField
        name="language"
        label={
          <div className="w-1/5 whitespace-nowrap text-sm text-white">
            原视频语言 <span className="ml-2 text-red-500">*</span>
          </div>
        }
        form={form}
        layout="horizontal"
        className="flex items-center justify-start gap-[55px]"
        renderFormControl={(field) => (
          <ProSelect
            value={field.value}
            onValueChange={field.onChange}
            placeholder={<span className="font-normal text-[#9FA4B2]">请选择语言</span>}
            options={UPLOAD_VIDEO_LANGUAGES.map(({ value, label }) => ({ value, label }))}
            className="w-[360px] bg-[#CCDDFF1A]"
          />
        )}
      />

      <FormField
        control={form.control}
        name="subtitle"
        render={({ field }) => (
          <FormItem className="mt-6 flex h-8 items-center">
            <FormLabel className="w-1/5 text-sm text-white">生成字幕</FormLabel>
            <FormControl>
              <Switch checked={field?.value ?? false} onCheckedChange={field.onChange} />
            </FormControl>
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="transitionMode"
        render={({ field }) => (
          <FormItem className="mt-6 flex h-8 items-center">
            <FormLabel className="w-1/5 text-sm text-white">叠化效果</FormLabel>
            <FormControl>
              <Switch checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="adTextType"
        render={({ field }) => (
          <FormItem className="mt-6 flex h-8 items-center">
            <FormLabel className="w-1/5 text-sm text-white">广告文案</FormLabel>
            <RadioGroup defaultValue="1" className="flex w-[360px] items-center gap-2">
              <RadioGroupItem value="1" id="1" />
              <Label htmlFor="1" className="text-sm text-white">
                AI智能生成
              </Label>
            </RadioGroup>
          </FormItem>
        )}
      />
    </>
  );
};
