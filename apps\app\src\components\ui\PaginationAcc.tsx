'use client';

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui';

interface CustomPaginationProps {
  totalItems: number;
  currentPage: number;
  pageSize: number;
  handlePaginationChange: (page: number, pageSize: number) => void;
}

export default function PaginationAcc({
  totalItems = 0,
  currentPage = 1,
  pageSize = 10,
  handlePaginationChange,
}: CustomPaginationProps) {
  const totalPages = Math.ceil(totalItems / pageSize);

  // 生成要显示的页码数组
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      // 如果总页数小于等于5，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 如果总页数大于5，显示当前页附近的页码和省略号
      if (currentPage <= 3) {
        // 当前页靠近开始
        pages.push(1, 2, 3, 4, 5);
      } else if (currentPage >= totalPages - 2) {
        // 当前页靠近结束
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        for (let i = currentPage - 2; i <= currentPage + 2; i++) {
          pages.push(i);
        }
      }
    }
    return pages;
  };

  const pageNumbers = getPageNumbers();

  if (!totalItems || Number(totalItems) === 0 || Number(totalItems) <= 10) return null;

  return (
    <div className="flex items-center justify-between px-2">
      <div className="whitespace-nowrap text-xs text-white">共 {totalItems} 条</div>

      <Pagination>
        <PaginationContent>
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePaginationChange(currentPage - 1, pageSize);
                }}
                className="h-8 w-8 flex-shrink-0 rounded border border-[#363D54] text-center text-xs font-normal leading-8 text-white"
              />
            </PaginationItem>
          )}

          {/* 显示第一页 */}
          {currentPage > 3 && totalPages > 5 && (
            <>
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePaginationChange(1, pageSize);
                  }}
                  className="flex-shrink-0 text-xs font-normal leading-8 text-white"
                >
                  1
                </PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            </>
          )}

          {/* 显示页码 */}
          {pageNumbers.map((pageNumber) => (
            <PaginationItem key={pageNumber}>
              <PaginationLink
                href="#"
                isActive={pageNumber === currentPage}
                onClick={(e) => {
                  e.preventDefault();
                  handlePaginationChange(pageNumber, pageSize);
                }}
                className={`h-8 w-8 flex-shrink-0 rounded text-xs leading-8 ${
                  pageNumber === currentPage
                    ? 'border-[#00E1FF] bg-transparent text-[#00E1FF]'
                    : 'border border-[#363D54] font-normal text-white'
                }`}
              >
                {pageNumber}
              </PaginationLink>
            </PaginationItem>
          ))}

          {/* 显示最后一页 */}
          {currentPage < totalPages - 2 && totalPages > 5 && (
            <>
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePaginationChange(totalPages, pageSize);
                  }}
                  className="flex-shrink-0 text-xs font-normal leading-8 text-white"
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            </>
          )}

          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePaginationChange(currentPage + 1, pageSize);
                }}
                className="h-8 w-8 flex-shrink-0 rounded border border-[#363D54] text-center text-xs font-normal leading-8 text-white"
              />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>

      <Select value={pageSize.toString()} onValueChange={(value) => handlePaginationChange(currentPage, Number(value))}>
        <SelectTrigger className="h-8 w-[140px] rounded border border-[#363D54] bg-transparent text-xs font-normal leading-8 text-white">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="w-[100px]">
          <SelectItem value="10" className="h-8 text-center text-xs font-normal leading-8 text-white">
            10 条/页
          </SelectItem>
          <SelectItem value="20" className="h-8 text-center text-xs font-normal leading-8 text-white">
            20 条/页
          </SelectItem>
          <SelectItem value="30" className="h-8 text-center text-xs font-normal leading-8 text-white">
            30 条/页
          </SelectItem>
          <SelectItem value="50" className="h-8 text-center text-xs font-normal leading-8 text-white">
            50 条/页
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
