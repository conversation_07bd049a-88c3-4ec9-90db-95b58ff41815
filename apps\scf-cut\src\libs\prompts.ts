export const DETAILED_PROMPT = `分析以下直播文字内容中的产品切换点。要求：

1. 识别并标记所有产品/服装切换的位置，包括：
   - 明确的切换词（如"下一件"、"下一套"、"接下来"、"换一个"、"换一套"、"下一个链接"、"下一个"、"再讲解一下"等类似词语）
   - 产品引入段落（如"给大家推荐"、"今天分享"、"来看看"、"跟大家介绍"等开场白）
   - 产品描述特征词（如谈论面料、版型、价格、尺码等具体特征）
   - 隐含的产品转换（通过上下文对比判断是否切换到新产品）
   - 短暂提及其他产品(1-2句)后返回原产品介绍的不计入
   - 产品链接相关词（如"上链接"、"弹一下链接"、"链接发出来"等）
   
2. 对于每个切换点，请提供：
      （前面不要有序号，比如1，2，3）原文序号:[开始序号(一个数字)] 原文序号:[开始序号(一个数字)] 
   切换文字：[切换点的文字]
   本段产品名称：[仅限当前正在讲解的主要产品名称，如未直接提及则描述产品特征"]
   切换特征：[明确切换/引入段落/隐含切换]
   持续程度：[详细介绍/短暂提及]
   相关上下文：[包含引入段落和具体产品描述的相关文字]

3. 特别注意：
   - 每段只标注当前正在讲解的主要产品
   - 不要把上一个或下一个产品名称放入本段产品名称中
   - 如果提到多个产品，只记录当前重点讲解的那个
   - 产品名称要简洁准确，不要包含描述性文字
   - 一个产品的介绍可能会被其他产品信息打断后再继续
   - 注意区分主要产品和临时提及的配套产品
   - 一个产品不一定是最开始提到的，要综合每段进行判断


4. 示例格式：
   原文序号:[123]
   切换文字："我们来看下一件"
   本段产品名称：羊绒大衣
   切换特征：确切切换
   持续程度：详细介绍
   相关上下文：开始详细介绍羊绒大衣的面料和款式特点

请严格按照以上格式输出切换点信息。

{content}
`;

export const GENERAL_PROMPT = `分析以下直播文字内容中的产品切换点，从第一句开始。要求：

1. 判定标准：
   主要通过以下方式判断产品切换:
   
   A. 产品切换识别:
   - 即使因技术问题未完整介绍，只要明确提到新产品也要记录
   - 包括未成功上链接或临时取消的产品
   - 即使很快切回原产品，如果超过4-5句话以上的，产品切换也要记录
   - 所有尝试切换的产品都需要记录，无论是否成功展示
   
   B. 明确切换标志:
   - 明显的切换词（"下一件"、"下一套"、"接下来"、"换一个"等）
   - 产品链接相关词（"上链接"、"车"、"上一下"等）
   - 突然开始介绍另一个产品
   - "给大家讲讲解一下"等介绍性语句
   
   C. 上下文分析(需要分析前后5-6句话):
   - 产品类型完全不同（如从服装变成护肤品）
   - 即使只有短暂几句也要记录切换
   - 介绍中断后返回原产品要重新记录
   - 介绍同一个产品的不同颜色，不算切换
   - 同一个商品，但是某种颜色没有的不算切换
   
   D. 特殊情况处理:
   - 技术问题导致的产品切换中断
   - 快速切换回原产品的情况
   - 未成功上链接的产品
   
2. 每个切换点必须包含：
   原文序号:[开始序号(一个数字)] 
   切换文字：[切换点的文字]
   本段产品名称：[当前主要产品名称]
   切换特征：[第一句/明确切换/上下文切换]
   持续程度：[详细介绍/短暂提及/中断]
   相关上下文：[前后5-6句关键内容]

3. 特别注意：
   - 必须从第一句开始分析并记录
   - 所有产品切换都要记录，即使是短暂或未成功的
   - 技术问题导致的中断也要记录
   - 返回继续讲解原产品时要标记为新的切换点

4. 示例格式：
   原文序号:[419]
   切换文字："好我给大家再试一下"
   本段产品名称：毛领外套
   切换特征：明确切换
   持续程度：详细介绍
   相关上下文：介绍带毛领外套的款式和特点

   原文序号:[428]
   切换文字："好来把面膜给我上一下呗"
   本段产品名称：面膜
   切换特征：明确切换
   持续程度：中断
   相关上下文：尝试介绍面膜但因链接问题未能完整展示

   原文序号:[455]
   切换文字："嗯行了行了"
   本段产品名称：羊皮毛一体连衣裙
   切换特征：上下文切换
   持续程度：详细介绍
   相关上下文：放弃面膜介绍，转而讲解羊皮毛一体连衣裙

请严格按照以上格式输出切换点信息，务必记录所有产品切换，包括未成功展示或中断的产品。

{content}
`;
