import {
  AdListItem,
  AdvertiserListItem,
  CampaignListItem,
  ProductListResponse,
  StoreProduct,
} from '@/services/interfaces/ads/res';
import type { BasicStatus, PermissionType } from './enum';
import { ToCamelCase } from '@/utils/camel';
import { AdvertiserListReq, CampaignListReq } from '@/services/interfaces/ads/req';

export interface TokenInfo {
  id: number;
  tkId: string;
  uid: string;
  tkUserName: string;
  accessToken: string;
  gmtCreate: Record<string, unknown>;
  gmtModified: Record<string, unknown>;
}

export interface UserToken {
  accessToken?: string;
  refreshToken?: string;
}

export interface UserInfo {
  id: string;
  sub: string; // 租户id
  email: string;
  username: string;
  password?: string;
  avatar?: string;
  role?: Role;
  status?: BasicStatus;
  permissions?: Permission[];
}

export interface Organization {
  id: string;
  name: string;
  status: 'enable' | 'disable';
  desc?: string;
  order?: number;
  children?: Organization[];
}

export interface Permission {
  id: string;
  parentId: string;
  name: string;
  label: string;
  type: PermissionType;
  route: string;
  status?: BasicStatus;
  order?: number;
  icon?: string;
  component?: string;
  hide?: boolean;
  hideTab?: boolean;
  frameSrc?: string;
  newFeature?: boolean;
  children?: Permission[];
}

export interface Role {
  id: string;
  name: string;
  label: string;
  status: BasicStatus;
  order?: number;
  desc?: string;
  permission?: Permission[];
}

export type CampaignItem = ToCamelCase<CampaignListItem>;
export type AdvertiserItem = ToCamelCase<AdvertiserListItem>;
export type AdItem = ToCamelCase<AdListItem>;

// 创建广告请求参数接口
export interface ICreateAdRequest {
  advertiser_id: string;
  adgroup_id: string;
  creatives: Array<{
    ad_name: string;
    identity_type: string;
    identity_id: string;
    ad_format: string;
    video_id: string;
    image_ids: string[];
    ad_text: string;
    call_to_action: string;
    landing_page_url: string;
  }>;
}
// 更新广告请求参数接口
export interface IUpdateAdRequest {
  advertiser_id: string;
  adgroup_id: string;
  creatives: Array<{
    ad_id: string;
    ad_name: string;
    identity_id: string;
    identity_type: string;
    ad_format: string;
    video_id: string;
    image_ids: string[];
    ad_text: string;
    call_to_action: string;
    landing_page_url: string;
  }>;
}

export type AgeGroup = 'AGE_18_24' | 'AGE_25_34' | 'AGE_35_44' | 'AGE_45_54' | 'AGE_55_100';
export type Gender = 'GENDER_UNLIMITED' | 'MALE' | 'FEMALE';
export type BudgetMode =
  // | "BUDGET_MODE_DYNAMIC_DAILY_BUDGET"
  'BUDGET_MODE_TOTAL' | 'BUDGET_MODE_DAY';
export type ScheduleType = 'SCHEDULE_FROM_NOW' | 'SCHEDULE_START_END';
export type BidType = 'BID_TYPE_NO_BID' | 'BID_TYPE_CUSTOM';
export type OptimizationGoal = 'VALUE' | 'CONVERSION' | 'CLICK' | 'REACH';
export type DeepBidType = 'VO_HIGHEST_VALUE' | 'VO_LOWEST_COST';
export type PromotionType = 'VIDEO_SHOPPING' | 'APP_ANDROID' | 'APP_IOS' | 'WEBSITE';
export type BillingEvent = 'OCPM' | 'CPC' | 'CPM';
export type PlacementType = 'PLACEMENT_TYPE_NORMAL' | 'PLACEMENT_TYPE_AUTOMATIC';
export type OptimizationEvent = 'SHOPPING' | 'CONVERSION' | 'CLICK' | 'INSTALL';

export type CampaignListFilterParams = ToCamelCase<CampaignListReq>;
export type AdvertiserListFilterParams = ToCamelCase<AdvertiserListReq>;
export interface IUploadVideoRequest {
  advertiser_ids: string;
  file_name?: string;
  upload_type: 'UPLOAD_BY_URL' | 'UPLOAD_BY_FILE'; // 根据实际支持的上传类型定义
  video_url: string;
  is_third_party: boolean;
  flaw_detect: boolean;
  auto_fix_enabled: boolean;
  auto_bind_enabled: boolean;
}

export type IUploadVideoResponse = {
  displayable: boolean;
  height: number;
  width: number;
  bit_rate: string;
  duration: number;
  video_id: string;
  size: string;
  allow_download: boolean;
};

export interface IUploadImageRequest {
  advertiser_ids: string;
  file_name: string;
  upload_type: 'UPLOAD_BY_URL';
  image_url: string;
}

export interface IUploadImageResponse {
  image_id: string;
  image_url: string;
  material_id: string;
  is_carousel_usable: boolean;
  displayable: boolean;
  width: number;
  height: number;
  format: string;
  signature: string;
  size: string;
  file_name: string;
}

// Material 相关接口
export interface MaterialListParams {
  directoryId?: string;
  page?: number;
  pageSize?: number;
}

export interface MaterialListResponse {
  list: MaterialItem[];
  page: number;
  pageSize: number;
  total: number;
}

export interface SplitMaterial {
  audio: string;
  video: string;
  [property: string]: any;
}

export interface MaterialItem {
  directory_id: string;
  id: string;
  name: string;
  properties: any;
  size: number;
  split_materials: SplitMaterial[] | null;
  tenant_id: string;
  tmp_created_at: string;
  tmp_deleted_at: null;
  tmp_updated_at: string;
  type: string;
  usage: boolean;
  video_duration: number;
  vod_audio_file_id: null | string;
  vod_category_id: string;
  vod_cover_url: string;
  vod_file_id: string;
  vod_media_url: string;
  vod_sub_app_id: string;
  [property: string]: any;
}

export interface DirListParams {
  parentId?: string;
  page?: number;
  pageSize?: number;
  keyword?: string;
}

export interface DirListResponse {
  list: any[];
  page: number;
  pageSize: number;
  total: number;
}

export interface ICopyRequest {
  advertiser_id: string;
  campaign_id: string;
  schedule_type?: 'SCHEDULE_START_TIME' | 'SCHEDULE_FROM_NOW';
  campaign_name: string;
}

export interface IProductListRequest {
  advertiser_id: string;
  bc_id: string;
  store_id: string;
  page: number;
  page_size: number;
}

export type VoidPromise = () => Promise<void>;

export type StoreProductItemType = ToCamelCase<StoreProduct>;
