import { Button, FormField, FormItem, FormLabel, FormControl } from '@/components/ui';
import { GalleryHorizontal } from 'lucide-react';
import { GoodsFieldProps } from '../types';

export const GoodsField = ({
  form,
  type,
  formValue,
  selectedRowKeys,
  setSelectedRowKeys,
  setIsModalOpen,
}: GoodsFieldProps) => {
  const handleOpenGoodsDialog = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsModalOpen(true);
  };

  return (
    <FormField
      control={form.control}
      name="itemGroupIds"
      render={({ field }) => (
        <FormItem className="mt-6 flex h-auto items-start">
          <FormLabel className="w-1/5 text-sm text-white">
            商品详情 <span className="ml-2 text-red-500">*</span>
          </FormLabel>
          <FormControl className="w-[440px]">
            <div className="items-center">
              {type === 'edit' ? (
                <div className="mb-2 overflow-y-auto">
                  {formValue?.jsonDate?.itemGroupIds?.map((itemGroupId: string) => (
                    <div
                      key={itemGroupId}
                      className="mb-2 flex h-[56px] w-[440px] flex-shrink-0 items-center bg-[#CCDDFF1A] p-2"
                    >
                      <div className="ml-2">
                        <div className="flex text-xs text-[#9FA4B2]">
                          <span className="w-16">SPU 编号：</span>
                          <span>{itemGroupId}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  <div className={`${selectedRowKeys.length > 0 ? 'mb-2 w-[440px] overflow-y-auto' : ''}`}>
                    {selectedRowKeys.map((record) => (
                      <div
                        key={record.itemGroupId}
                        className="mb-2 flex h-16 w-full flex-shrink-0 items-center rounded-md bg-[#CCDDFF1A]"
                      >
                        <img
                          src={record.productImageUrl}
                          alt=""
                          className="h-full w-16 rounded-bl-md rounded-tl-md object-cover"
                        />
                        <div className="ml-2 flex-1">
                          <div className="text-sm">
                            {record.title.length > 40 ? record.title.slice(0, 40) + '...' : record.title}
                          </div>
                          <div className="flex text-xs text-[#9FA4B2]">
                            <span className="w-16">SPU 编号：</span>
                            <span>{record.itemGroupId}</span>
                          </div>
                        </div>
                        <div
                          className="mr-4 cursor-pointer text-[#9FA4B2] hover:text-white"
                          onClick={() => {
                            const newSelectedRowKeys = selectedRowKeys.filter(
                              (item) => item.itemGroupId !== record.itemGroupId,
                            );
                            setSelectedRowKeys(newSelectedRowKeys);
                          }}
                        >
                          ✕
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <div className="flex items-center">
                      <Button
                        onClick={handleOpenGoodsDialog}
                        type="button"
                        className="h-8 rounded-md border border-[#363D54] bg-transparent text-xs text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-white"
                        variant="outline"
                        disabled={selectedRowKeys.length >= 20}
                      >
                        <GalleryHorizontal className="mr-1 h-4 w-4" />
                        {selectedRowKeys.length >= 20 ? '(已达上限)' : '添加商品'}
                      </Button>
                      <div className="ml-2 text-sm">已添加 {selectedRowKeys.length}个 商品</div>
                    </div>
                    {selectedRowKeys.length !== 0 && (
                      <div className="flex items-center justify-between space-x-2">
                        <div
                          className="cursor-pointer text-center text-[14px] font-normal text-[#00E1FF]"
                          onClick={() => setSelectedRowKeys([])}
                        >
                          全部清除
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
};
