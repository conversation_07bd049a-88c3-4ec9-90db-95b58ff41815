{"editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": ".prettier<PERSON>", "cSpell.words": ["Authing", "baiyin", "bowong", "BULLMQ", "bwai", "<PERSON><PERSON><PERSON>an", "chanxuan", "DIFY", "dismissable", "do<PERSON><PERSON>", "FEISHU", "hookform", "<PERSON><PERSON>", "nextstepjs", "nodownload", "qcloud", "quicktime", "roasmax", "tailwindcss", "tencentcloud", "tiktok", "uploaders", "vaul", "zustand"], "typescript.tsdk": "node_modules/typescript/lib", "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}}