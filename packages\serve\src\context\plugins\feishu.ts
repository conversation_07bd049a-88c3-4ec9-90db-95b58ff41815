import { FeishuRobot } from '@roasmax/utils/feishu';
import { ActionContextPluginLoader } from '../../types';

const feishuRobotPlugin: ActionContextPluginLoader = () => {
  return {
    name: 'feishuRobot',
    plugin: new FeishuRobot({ host: process.env.FEISHU_NOTIFY_CALLBACK_URL }),
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    feishuRobot: FeishuRobot;
  }
}

export default feishuRobotPlugin;
