const cache = new Map<string, { data: any; timestamp: number }>();

export function setCache(key: string, data: any, expirationTime: number = 5 * 60 * 1000) {
  cache.set(key, { data, timestamp: Date.now() + expirationTime });
}

export function getCache(key: string): any | null {
  const cached = cache.get(key);
  if (cached && cached.timestamp > Date.now()) {
    return cached.data;
  }
  cache.delete(key);
  return null;
}
