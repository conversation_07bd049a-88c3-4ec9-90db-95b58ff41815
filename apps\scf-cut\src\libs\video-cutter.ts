import axios from 'axios';
import * as fs from 'fs/promises';
import * as path from 'path';
import { create } from 'xmlbuilder2';
import { DETAILED_PROMPT, GENERAL_PROMPT } from './prompts';
import ffmpeg from 'fluent-ffmpeg';
import { withRetry } from '@roasmax/utils';

export type SplitMode = 'detailed' | 'general';

export interface VideoCutterConfig {
  apiKey: string;
  apiUrl: string;
  mode: SplitMode;
  outputDir: string | ((videoPath: string) => string);
}

export class VideoCutter {
  private subtitleContent?: string;
  private readonly config: VideoCutterConfig;
  constructor(config: VideoCutterConfig) {
    this.config = config;
  }
  /**
   * 处理视频并生成分段
   */
  public async cut(videoPath: string, subtitleContent: string, productNames: string[] = []) {
    try {
      this.subtitleContent = subtitleContent;
      // 1. 清理字幕
      console.log('开始清理字幕...');
      const { cleanedContent } = this.cleanSubtitle();
      console.log('字幕清理完成！');

      // 2. 分析内容
      console.log('开始分析内容...');
      const analysisResult = await this.analyzeContent(cleanedContent, this.config.mode, productNames);
      console.log('内容分析完成！');

      // 3. 组装xml
      console.log('开始组装xml...');
      const xml = this.saveToXml(analysisResult);
      console.log('xml组装完成！');

      // 4.切割视频
      console.log('开始切割视频...');
      const result = await this.cutVideo(videoPath, xml);
      console.log('视频切割完成！');

      return result;
    } catch (error) {
      console.error('视频处理失败:', error);
      throw error;
    }
  }
  /**
   * 清理字幕内容
   * 减少字幕内容，减少Claude的负担
   */
  private cleanSubtitle() {
    const lines = this.subtitleContent!.split('\n');
    const timeDict: Record<string, string> = {};
    let currentIndex = '';
    let currentTime = '';
    let currentText: string[] = [];
    lines.forEach((line) => {
      line = line.trim();
      if (/^\d+$/.test(line)) {
        // 序号
        currentIndex = line;
      } else if (line.includes('-->')) {
        // 时间
        currentTime = line;
      } else if (line && !/^\d+$/.test(line)) {
        // 文本
        currentText.push(line);
      } else if (!line && currentTime && currentText.length) {
        // 保存时间点
        const text = currentText.join(' ');
        timeDict[`[${currentIndex}] ${text}`] = currentTime;
        currentText = [];
        currentTime = '';
        currentIndex = '';
      }
    });
    const cleanedContent = Object.keys(timeDict).join('\n');
    return { cleanedContent, timeDict };
  }

  /**
   * 调用 Claude API 分析内容
   */
  private async analyzeContent(content: string, mode: SplitMode, productNames: string[]) {
    const prompt = mode === 'detailed' ? DETAILED_PROMPT : GENERAL_PROMPT;
    let finalPrompt = prompt;
    if (productNames.length > 0) {
      const productList = productNames.map((name) => `- ${this.extractChineseName(name)}`).join('\n');
      finalPrompt = prompt.replace('{content}', `相关的产品名：\n${productList}\n\n{content}`);
    }

    const chunks = this.splitContent(content);
    console.log('开始处理文本内容，共', chunks.length, '段');

    const results: string[] = [];
    for (const chunk of chunks) {
      const response = await withRetry(() =>
        axios.post(
          this.config.apiUrl,
          {
            model: 'claude-3-5-sonnet-20241022',
            messages: [{ role: 'user', content: finalPrompt.replace('{content}', chunk) }],
            stream: false,
          },
          { headers: { Authorization: `Bearer ${this.config.apiKey}`, 'Content-Type': 'application/json' } },
        ),
      );
      results.push(response.data.choices[0].message.content);
    }
    return results;
  }

  /**
   * 将 Claude 的分析结果保存为 XML 格式
   */
  private saveToXml(claudeResults: string[]): string {
    // 创建根节点
    const root = create({ version: '1.0', encoding: 'UTF-8' }).ele('products');

    let productInfo: Record<string, string> = {};

    for (const result of claudeResults) {
      for (const line of result.split('\n')) {
        const trimmedLine = line.trim();

        if (trimmedLine.startsWith('原文序号:')) {
          // 保存之前的产品信息
          if (Object.keys(productInfo).length > 0) {
            const productNode = root.ele('product');

            // 设置序号属性
            if (productInfo.srtIndex) {
              productNode.att('srt_index', productInfo.srtIndex);
            }

            // 添加其他信息作为子节点
            for (const [key, value] of Object.entries(productInfo)) {
              if (key !== 'srtIndex' && value) {
                productNode.ele(key).txt(value);
              }
            }
          }

          // 重置产品信息
          productInfo = {};

          // 提取序号信息
          const indexMatch = trimmedLine.match(/\[(\d+)\]/);
          if (indexMatch) {
            productInfo.srtIndex = indexMatch[1]!;
          }
        } else {
          // 根据不同模式处理不同的字段
          if (this.config.mode === 'detailed') {
            if (trimmedLine.includes('切换文字：')) {
              productInfo.switch_text = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('本段产品名称：')) {
              productInfo.product_name = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('切换特征：')) {
              productInfo.switch_type = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('相关上下文：')) {
              productInfo.context = trimmedLine.split('：')[1]?.trim() || '';
            }
          } else {
            // general 模式
            if (trimmedLine.includes('切换文字：')) {
              productInfo.switch_text = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('本段产品名称：')) {
              productInfo.product_name = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('切换特征：')) {
              productInfo.switch_type = trimmedLine.split('：')[1]?.trim() || '';
            } else if (trimmedLine.includes('相关上下文：')) {
              productInfo.context = trimmedLine.split('：')[1]?.trim() || '';
            }
          }
        }
      }
    }

    // 保存最后一个产品的信息
    if (Object.keys(productInfo).length > 0) {
      const productNode = root.ele('product');

      if (productInfo.srtIndex) {
        productNode.att('srt_index', productInfo.srtIndex);
      }

      for (const [key, value] of Object.entries(productInfo)) {
        if (key !== 'srtIndex' && value) {
          productNode.ele(key).txt(value);
        }
      }
    }

    // 返回格式化的 XML 字符串
    return root.end({ prettyPrint: true, indent: '  ' });
  }

  /**
   * 切割视频
   */
  private async cutVideo(videoPath: string, xmlContent: string) {
    try {
      const videoDir = path.dirname(videoPath);
      const videoName = path.parse(videoPath).name;

      // 创建输出目录
      let baseOutputDir = path.join(
        videoDir,
        typeof this.config.outputDir === 'function' ? this.config.outputDir(videoPath) : this.config.outputDir,
      );
      let outputDir = baseOutputDir;
      let counter = 1;

      // 处理目录已存在的情况
      while (
        await fs
          .access(outputDir)
          .then(() => true)
          .catch(() => false)
      ) {
        outputDir = `${baseOutputDir}_${counter}`;
        counter++;
      }

      await fs.mkdir(outputDir, { recursive: true });

      // 解析XML字符串
      interface Product {
        srtIndex: number;
        productName: string;
      }

      const products: Product[] = [];

      interface XMLProduct {
        '@srt_index': string;
        product_name: string;
      }

      interface XMLRoot {
        products: {
          product: XMLProduct | XMLProduct[];
        };
      }

      // 使用 xmlbuilder2 解析 XML
      const doc = create(xmlContent);
      const obj = doc.end({ format: 'object' }) as unknown as XMLRoot;
      const productNodes = Array.isArray(obj.products.product) ? obj.products.product : [obj.products.product];

      for (const node of productNodes) {
        const srtIndex = node['@srt_index'];
        const productName = node.product_name;

        if (srtIndex && productName) {
          try {
            products.push({
              srtIndex: parseInt(srtIndex),
              productName: productName.trim(),
            });
          } catch (error: any) {
            console.warn(`警告: 无效的序号 ${srtIndex}; 原因: ${error.message}`);
            continue;
          }
        }
      }

      if (products.length === 0) {
        throw new Error('XML内容中没有找到有效的产品信息');
      }

      // 按序号排序
      products.sort((a, b) => a.srtIndex - b.srtIndex);

      // 读取字幕文件获取时间点
      const lines = this.subtitleContent!.split('\n');
      const timePoints: number[] = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]!.trim();
        if (/^\d+$/.test(line) && products.some((p) => p.srtIndex === parseInt(line))) {
          const timeLine = lines[i + 1]?.trim();
          if (timeLine) {
            const startTime = timeLine.split(' --> ')[0]!;
            timePoints.push(this.parseTime(startTime));
          }
        }
      }

      console.log(`找到 ${products.length} 个产品切换点`);

      // 切割视频
      const outputFiles: string[] = [];
      for (let i = 0; i < products.length; i++) {
        const startTime = timePoints[i]!;
        const endTime = timePoints[i + 1];
        const { productName, srtIndex } = products[i]!;
        const fileName = productName.replace(/\//g, '_');
        console.log(`处理第 ${i + 1}/${products.length} 个片段`);
        console.log(`产品名: ${productName}`);
        console.log(`文件名: ${fileName}`);
        console.log(`开始时间: ${this.formatTime(startTime)}`);
        if (endTime) {
          console.log(`结束时间: ${this.formatTime(endTime)}`);
        }

        const fileIndex = srtIndex.toString().padStart(8, '0');

        // 处理输出文件名
        let baseOutputFile = path.join(outputDir, `[素材]${fileIndex}_${fileName}.mp4`);
        let outputFile = baseOutputFile;
        let fileCounter = 1;

        while (
          await fs
            .access(outputFile)
            .then(() => true)
            .catch(() => false)
        ) {
          outputFile = path.join(outputDir, `[素材]${fileIndex}_${fileName}_${fileCounter}.mp4`);
          fileCounter++;
        }

        // 执行 FFmpeg 切割
        await this.executeFFmpeg(videoPath, outputFile, startTime, endTime || -1);
        console.log(`第 ${i + 1} 个片段处理完成`);
        outputFiles.push(outputFile);
      }

      console.log('所有视频切割完成!');
      return { outputDir, outputFiles };
    } catch (error) {
      console.error('视频切割失败:', error);
      throw error;
    }
  }
  /**
   * 执行 FFmpeg 命令
   */
  private executeFFmpeg(inputPath: string, outputPath: string, startTime: number, endTime: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const command = ffmpeg(inputPath)
        .setStartTime(startTime)
        // 使用 copy 编码器以避免重新编码
        .videoCodec('copy')
        .audioCodec('copy')
        // 添加快速寻道选项
        .inputOptions(['-accurate_seek'])
        // 使用硬件加速（如果可用）
        .outputOptions(['-movflags', '+faststart']);

      // 只有当 endTime 大于 0 时才设置持续时间
      if (endTime > 0) {
        command.setDuration(endTime - startTime);
      }

      command
        .output(outputPath)
        .on('end', () => resolve())
        .on('error', (err) => {
          reject(new Error(`FFmpeg 处理失败: ${err.message}`));
        })
        .run();
    });
  }

  private splitContent(content: string, maxLines: number = 700): string[] {
    const lines = content.split('\n');
    const chunks: string[] = [];
    let currentChunk: string[] = [];

    for (const line of lines) {
      currentChunk.push(line);
      if (currentChunk.length >= maxLines) {
        chunks.push(currentChunk.join('\n'));
        currentChunk = [];
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.join('\n'));
    }

    return chunks;
  }

  private extractChineseName(fullName: string): string {
    const chinesePattern = /[""][^""]*[""]|[\u4e00-\u9fff]+/g;
    const matches = fullName.match(chinesePattern);
    return matches ? matches.join('').trim() : '';
  }

  /**
   * 解析时间字符串为秒数
   */
  private parseTime(timeStr: string): number {
    const [h, m, s] = timeStr.split(':');
    const [seconds, ms] = s?.split(',') || [];
    return parseInt(h!) * 3600 + parseInt(m!) * 60 + parseInt(seconds || '0') + parseInt(ms || '0') / 1000;
  }
  /**
   * 格式化时间为 HH:MM:SS.mmm
   */
  private formatTime(seconds: number): string {
    seconds = Math.max(0, seconds);

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toFixed(3)}`;
  }
}
