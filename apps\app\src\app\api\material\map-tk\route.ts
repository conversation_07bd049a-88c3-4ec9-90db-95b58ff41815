import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const prisma = new PrismaClient();

  try {
    const body = await request.json();

    // 确保输入数据是数组
    if (!Array.isArray(body)) {
      return NextResponse.json({
        code: 400,
        message: '输入数据必须是数组格式',
      });
    }

    // 处理所有更新
    const results = await Promise.all(
      body.map(async ({ material_id, tk_video_id, tk_image_id }) => {
        if (!material_id) {
          throw new Error(`material_id is required`);
        }

        const material = await prisma.materials.findUnique({
          where: {
            id: material_id,
            tmp_deleted_at: null,
          },
        });

        if (!material) {
          throw new Error(`Material not found: ${material_id}`);
        }

        const newMapInfo = {
          map_platform: 'TIKTOK',
          map_platform_info: {
            tk_video_id,
            tk_image_id,
          },
        };

        const updatedProperties = {
          ...((material.properties as object) || {}),
          ...newMapInfo,
        };

        await prisma.materials.update({
          where: { id: material_id },
          data: {
            properties: updatedProperties,
          },
        });

        return {
          material_id,
          code: 200,
          message: 'success',
        };
      }),
    );

    return NextResponse.json({
      code: 200,
      message: 'success',
      data: results,
    });
  } catch (error) {
    return NextResponse.json({
      code: 500,
      message: error instanceof Error ? error.message : '处理失败',
    });
  } finally {
    await prisma.$disconnect();
  }
}
