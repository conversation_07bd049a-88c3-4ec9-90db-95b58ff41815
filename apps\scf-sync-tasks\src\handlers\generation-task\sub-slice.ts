import { feishuRobot } from '@/utils/feishu';
import { prisma } from '@/utils/prisma';
import { createGenerationSubTask } from './sub-generation';
import { TaskStatus } from '../utils';

/**
 * 完成视频处理子任务（slice）
 * 生成任务的子任务（slice）完成，需要靠对应素材的预切分任务完成，所以这里的入参是素材的id
 * @param params
 */
export const completeGenerationSubSliceTask = async (params: { materialId: string }) => {
  try {
    const originMaterial = await prisma.materials.findUnique({ where: { id: params.materialId } });
    if (!originMaterial) {
      throw new Error('原始素材不存在');
    }

    // 获取预切片类型的子任务
    const subs = await prisma.video_generation_sub_tasks.findMany({
      where: { origin_material_id: originMaterial.id, sub_task_type: 'slice', status: TaskStatus.PENDING },
    });

    // 获取对应的任务，用来取得对应的参数
    // 这里是通过物料id来获取任务，一个物料可能对应多个等待中的任务
    const tasks = await prisma.video_generation_tasks.findMany({
      where: { id: { in: subs.map((t) => t.task_id) } },
    });

    for (const subSliceTask of subs) {
      try {
        const task = tasks.find((t) => t.id === subSliceTask.task_id);
        if (!task) {
          throw new Error('生成子任务对应的任务不存在');
        }

        for (const split of originMaterial.split_materials as { video: string; audio: string }[]) {
          // 创建视频处理子任务
          await createGenerationSubTask({
            taskId: task.id,
            originMaterialId: originMaterial.id,
            splitMaterialVideoId: split.video,
            splitMaterialAudioId: split.audio,
          });
        }

        // 最后完成掉预切分任务
        await prisma.video_generation_sub_tasks.update({
          where: { id: subSliceTask.id },
          data: { status: TaskStatus.SUCCESS },
        });
      } catch (e: any) {
        const m = `预切分任务触发失败 ${e.message}, 子任务ID: ${subSliceTask.id}`;
        await feishuRobot.error('预切分任务触发失败', [m]);
        console.error(m);
      }
    }
  } catch (e: any) {
    const m = `预切分任务触发失败 ${e.message}, 素材ID: ${params.materialId}`;
    await feishuRobot.error('预切分任务触发失败', [m]);
    console.error(m);
  }
};
