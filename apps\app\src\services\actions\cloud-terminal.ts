'use server';

import { cloud_terminals, Prisma } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import { bindAccount } from '../domains/cloud-terminal';

/**
 * 分页查询云主机列表
 */
export const pageCloudTerminals = server(
  '分页查询云主机列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters: { cloud_host_identity?: string; remark?: string; status?: string };
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      const [list = [], total = 0] = await Promise.all([
        ctx.db.cloud_terminals.findMany({
          skip: (ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
          take: ctx.data.pagination.pageSize,
          where: ctx.data.filters,
          orderBy: { tmp_created_at: 'desc' },
          include: {
            social_accounts: true,
          },
        }),
        ctx.db.cloud_terminals.count({ where: ctx.data.filters }),
      ]);

      return {
        list: list,
        pagination: {
          total,
          page: ctx.data.pagination.page,
          pageSize: ctx.data.pagination.pageSize,
        },
      };
    });
  },
);

/**
 * 获取所有云终端
 */
export const getAllCloudTerminals = server('获取所有云终端', async (ctx: ActionContext<any>) => {
  return await ctx.trx(async (ctx) => {
    const cloudTerminals = await ctx.db.cloud_terminals.findMany({});
    return cloudTerminals;
  });
});

/**
 * 创建云终端
 */
export const batchCreateCloudTerminals = server(
  '批量创建云终端',
  async (ctx: ActionContext<{ data: Prisma.cloud_terminalsCreateManyInput[] }>) => {
    return await ctx.trx(async (ctx) => {
      try {
        // 对每条数据进行处理
        for (const terminalData of ctx.data.data) {
          // 查找是否存在相同 cloud_host_cos_path 的记录
          const existingTerminal = await ctx.db.cloud_terminals.findFirst({
            where: {
              tenant_id: ctx.tenant.id,
              cloud_host_cos_path: terminalData.cloud_host_cos_path,
            },
          });

          if (existingTerminal) {
            // 如果存在，则更新记录
            await ctx.db.cloud_terminals.update({
              where: { id: existingTerminal.id },
              data: terminalData,
            });
          } else {
            // 如果不存在，则创建新记录
            await ctx.db.cloud_terminals.create({
              data: { ...terminalData, tenant_id: ctx.tenant.id },
            });
          }
        }
        return true;
      } catch (error) {
        console.error('创建/更新数据时出错:', error);
        throw error;
      }
    });
  },
);

/**
 * 锁定云终端
 */
export const lockCloudTerminals = server('锁定云终端', async (ctx: ActionContext<{ ids: string[] }>) => {
  return await ctx.trx(async (ctx) => {
    await ctx.db.cloud_terminals.updateMany({
      where: {
        tenant_id: ctx.tenant.id,
        id: { in: ctx.data.ids },
      },
      data: {
        status: '锁定',
      },
    });
  });
});

export const unlockCloudTerminals = server('解锁云终端', async (ctx: ActionContext<{ ids: string[] }>) => {
  return await ctx.trx(async (ctx) => {
    await ctx.db.cloud_terminals.updateMany({
      where: {
        tenant_id: ctx.tenant.id,
        id: { in: ctx.data.ids },
      },
      data: {
        status: '空闲',
      },
    });
  });
});

/**
 * 批量更新云终端绑定的社交账号
 */
export const batchUpdateCloudTerminalBindAccount = server(
  '批量更新云终端绑定账号',
  async (
    ctx: ActionContext<{
      terminals: Array<{
        id: string;
        cloud_host_cos_path: string;
        bind_social_account: string;
      }>;
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      // 批量更新每个终端的绑定账号
      for (const terminal of ctx.data.terminals) {
        const account = await ctx.db.social_accounts.findFirst({
          where: { account_identity: terminal.bind_social_account },
        });
        if (!account) {
          throw new Error('未找到对应的社交账号');
        }
        await ctx.execute(bindAccount, { cloudTerminalId: terminal.id, socialAccountId: account.id });
      }
      return true;
    });
  },
);

/**
 * 更新云终端信息
 */
export const updateCloudTerminal = server(
  '更新云终端',
  async (ctx: ActionContext<{ id: string; data: Partial<cloud_terminals> }>) => {
    return await ctx.trx(async (ctx) => {
      const cloudTerminal = await ctx.db.cloud_terminals.findUnique({ where: { id: ctx.data.id } });
      if (!cloudTerminal) {
        throw new Error('未找到指定的云终端');
      }

      const updatedTerminal = await ctx.db.cloud_terminals.update({ where: { id: ctx.data.id }, data: ctx.data.data });
      return updatedTerminal;
    });
  },
);
