export const Look2_1 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <g clipPath="url(#clip0_5119_696)">
        <path
          d="M16.9913 15.2603V9.50846C16.9913 9.36402 16.9339 9.2255 16.8318 9.12337C16.7296 9.02124 16.5911 8.96387 16.4467 8.96387C16.3022 8.96387 16.1637 9.02124 16.0615 9.12337C15.9594 9.2255 15.902 9.36402 15.902 9.50846V15.2603C15.3947 15.382 14.9435 15.6719 14.622 16.0827C14.3005 16.4935 14.1275 17.0011 14.1313 17.5228C14.1351 18.0445 14.3154 18.5495 14.6429 18.9556C14.9703 19.3618 15.4256 19.645 15.9347 19.7594C15.9155 19.8137 15.9045 19.8706 15.902 19.9282V21.4914C15.902 21.6359 15.9594 21.7744 16.0615 21.8765C16.1637 21.9787 16.3022 22.036 16.4467 22.036C16.5911 22.036 16.7296 21.9787 16.8318 21.8765C16.9339 21.7744 16.9913 21.6359 16.9913 21.4914V19.9282C16.9898 19.87 16.9768 19.8126 16.9532 19.7594C17.4624 19.6457 17.9181 19.363 18.2461 18.9572C18.574 18.5515 18.755 18.0467 18.7594 17.525C18.7638 17.0033 18.5914 16.4955 18.2704 16.0842C17.9494 15.673 17.4985 15.3826 16.9913 15.2603ZM16.4467 18.7299C16.2043 18.7299 15.9673 18.658 15.7658 18.5234C15.5643 18.3887 15.4072 18.1973 15.3144 17.9734C15.2217 17.7494 15.1974 17.503 15.2447 17.2653C15.292 17.0276 15.4087 16.8092 15.5801 16.6378C15.7515 16.4664 15.9698 16.3497 16.2076 16.3024C16.4453 16.2551 16.6917 16.2794 16.9157 16.3721C17.1396 16.4649 17.331 16.622 17.4657 16.8235C17.6003 17.025 17.6722 17.262 17.6722 17.5044C17.6722 17.8294 17.5431 18.1411 17.3132 18.371C17.0834 18.6008 16.7717 18.7299 16.4467 18.7299ZM11.8713 11.6872V9.50846C11.8713 9.36402 11.8139 9.2255 11.7118 9.12337C11.6096 9.02124 11.4711 8.96387 11.3267 8.96387C11.1822 8.96387 11.0437 9.02124 10.9415 9.12337C10.8394 9.2255 10.782 9.36402 10.782 9.50846V11.6872C10.2752 11.8051 9.82325 12.091 9.49968 12.4985C9.17612 12.906 9 13.411 9 13.9313C9 14.4516 9.17612 14.9566 9.49968 15.364C9.82325 15.7715 10.2752 16.0575 10.782 16.1754V21.4914C10.782 21.6359 10.8394 21.7744 10.9415 21.8765C11.0437 21.9787 11.1822 22.036 11.3267 22.036C11.4711 22.036 11.6096 21.9787 11.7118 21.8765C11.8139 21.7744 11.8713 21.6359 11.8713 21.4914V16.1754C12.3781 16.0575 12.8301 15.7715 13.1537 15.364C13.4772 14.9566 13.6533 14.4516 13.6533 13.9313C13.6533 13.411 13.4772 12.906 13.1537 12.4985C12.8301 12.091 12.3781 11.8051 11.8713 11.6872ZM11.3267 15.1568C11.0843 15.1568 10.8473 15.0849 10.6458 14.9503C10.4443 14.8156 10.2872 14.6242 10.1944 14.4003C10.1017 14.1763 10.0774 13.9299 10.1247 13.6922C10.172 13.4545 10.2887 13.2361 10.4601 13.0647C10.6315 12.8933 10.8498 12.7766 11.0876 12.7293C11.3253 12.682 11.5717 12.7063 11.7957 12.799C12.0196 12.8918 12.211 13.0489 12.3457 13.2504C12.4803 13.4519 12.5522 13.6889 12.5522 13.9313C12.5522 14.2544 12.4246 14.5645 12.1971 14.794C11.9696 15.0235 11.6607 15.1539 11.3376 15.1568H11.3267ZM24.0014 13.9313C24.0005 13.4113 23.8241 12.9069 23.5009 12.4997C23.1776 12.0924 22.7264 11.8061 22.2203 11.6872V9.50846C22.2203 9.36402 22.1629 9.2255 22.0607 9.12337C21.9586 9.02124 21.82 8.96387 21.6756 8.96387C21.5312 8.96387 21.3926 9.02124 21.2905 9.12337C21.1883 9.2255 21.1309 9.36402 21.1309 9.50846V11.6872C20.6241 11.8051 20.1722 12.091 19.8486 12.4985C19.5251 12.906 19.3489 13.411 19.3489 13.9313C19.3489 14.4516 19.5251 14.9566 19.8486 15.364C20.1722 15.7715 20.6241 16.0575 21.1309 16.1754V21.4914C21.1309 21.6359 21.1883 21.7744 21.2905 21.8765C21.3926 21.9787 21.5312 22.036 21.6756 22.036C21.82 22.036 21.9586 21.9787 22.0607 21.8765C22.1629 21.7744 22.2203 21.6359 22.2203 21.4914V16.1754C22.726 16.0555 23.1766 15.769 23.4997 15.362C23.8228 14.9549 23.9995 14.451 24.0014 13.9313ZM21.6865 15.1568C21.4441 15.1568 21.2072 15.0849 21.0056 14.9503C20.8041 14.8156 20.647 14.6242 20.5543 14.4003C20.4615 14.1763 20.4372 13.9299 20.4845 13.6922C20.5318 13.4545 20.6485 13.2361 20.8199 13.0647C20.9913 12.8933 21.2097 12.7766 21.4474 12.7293C21.6851 12.682 21.9316 12.7063 22.1555 12.799C22.3794 12.8918 22.5708 13.0489 22.7055 13.2504C22.8402 13.4519 22.912 13.6889 22.912 13.9313C22.912 14.2563 22.7829 14.568 22.5531 14.7979C22.3232 15.0277 22.0115 15.1568 21.6865 15.1568Z"
          fill="#9FA4B2"
        />
      </g>
      <defs>
        <clipPath id="clip0_5119_696">
          <rect width="15" height="15" fill="white" transform="translate(9 8)" />
        </clipPath>
      </defs>
    </svg>
  );
};
