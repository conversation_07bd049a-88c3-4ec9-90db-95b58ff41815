-- CreateTable
CREATE TABLE `roles` (
    `id` VARCHAR(191) NOT NULL,
    `tenant_id` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `name` VA<PERSON>HAR(191) NOT NULL,
    `description` VA<PERSON>HAR(191) NULL,
    `is_system` BO<PERSON>EAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    `deleted_at` DATETIME(3) NULL,

    UNIQUE INDEX `unique_tenant_code`(`tenant_id`, `code`),
    INDEX `idx_roles_tenant_id`(`tenant_id`),
    INDEX `idx_roles_code`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_roles` (
    `id` VARCHAR(191) NOT NULL,
    `tenant_id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `role_code` VARCHAR(191) NOT NULL,
    `assigned_by` VARCHAR(191) NULL,
    `assigned_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `expires_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `unique_user_role`(`tenant_id`, `user_id`, `role_code`),
    INDEX `idx_user_roles_tenant_user`(`tenant_id`, `user_id`),
    INDEX `idx_user_roles_role_code`(`role_code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permissions` (
    `id` VARCHAR(191) NOT NULL,
    `tenant_id` VARCHAR(191) NOT NULL,
    `role_code` VARCHAR(191) NOT NULL,
    `resource_type` ENUM('MENU', 'API', 'DATA') NOT NULL,
    `resource_code` VARCHAR(191) NOT NULL,
    `actions` JSON NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),

    INDEX `idx_permissions_tenant_role`(`tenant_id`, `role_code`),
    INDEX `idx_permissions_resource`(`resource_type`, `resource_code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_sessions` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `tenant_id` VARCHAR(191) NOT NULL,
    `token_hash` VARCHAR(191) NOT NULL,
    `device_info` JSON NULL,
    `ip_address` VARCHAR(45) NULL,
    `user_agent` TEXT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `last_active_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `expires_at` DATETIME(3) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,

    INDEX `idx_user_sessions_user_id`(`user_id`),
    INDEX `idx_user_sessions_token_hash`(`token_hash`),
    INDEX `idx_user_sessions_expires_at`(`expires_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AlterTable
ALTER TABLE `members` ADD COLUMN `authing_user_id` VARCHAR(191) NULL,
    ADD COLUMN `email_verified` BOOLEAN NOT NULL DEFAULT true,
    ADD COLUMN `is_migrated` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `last_login_at` DATETIME(3) NULL,
    ADD COLUMN `locked_until` DATETIME(3) NULL,
    ADD COLUMN `login_attempts` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `migration_date` DATETIME(3) NULL,
    ADD COLUMN `password_hash` VARCHAR(191) NULL,
    ADD COLUMN `password_reset_required` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `phone` VARCHAR(191) NULL,
    ADD COLUMN `salt` VARCHAR(191) NULL;

-- CreateIndex
CREATE INDEX `idx_members_email_tenant` ON `members`(`email`, `tenant_id`);

-- CreateIndex
CREATE INDEX `idx_members_authing_user_id` ON `members`(`authing_user_id`);

-- CreateIndex
CREATE INDEX `idx_members_migration_status` ON `members`(`is_migrated`, `password_reset_required`);

-- CreateIndex
CREATE INDEX `idx_members_login_status` ON `members`(`last_login_at`, `login_attempts`);

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_tenant_id_user_id_fkey` FOREIGN KEY (`tenant_id`, `user_id`) REFERENCES `members`(`tenant_id`, `user_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_role_code_tenant_id_fkey` FOREIGN KEY (`role_code`, `tenant_id`) REFERENCES `roles`(`code`, `tenant_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permissions` ADD CONSTRAINT `permissions_role_code_tenant_id_fkey` FOREIGN KEY (`role_code`, `tenant_id`) REFERENCES `roles`(`code`, `tenant_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_sessions` ADD CONSTRAINT `user_sessions_tenant_id_user_id_fkey` FOREIGN KEY (`tenant_id`, `user_id`) REFERENCES `members`(`tenant_id`, `user_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Insert default roles
INSERT INTO `roles` (`id`, `tenant_id`, `code`, `name`, `description`, `is_system`) VALUES
('156ae7b8-fe65-4c93-9c39-58a2fa93f67a', 'default', 'admin', '管理员', '系统管理员角色', true),
('b8ff7310-a9d6-4b34-b332-607fe922eff7', 'default', 'user', '普通用户', '普通用户角色', true),
('fce52c0b-e6f4-4c02-8648-e867ee15828b', 'default', 'editor', '编辑者', '内容编辑角色', true),
('690fc26d-06e2-4a72-89d7-bb61583d34e9', 'default', 'viewer', '查看者', '只读查看角色', true);

-- Insert default permissions for admin role
INSERT INTO `permissions` (`id`, `tenant_id`, `role_code`, `resource_type`, `resource_code`, `actions`) VALUES
('f1ea647f-7afd-429c-aa72-260d4ffbf148', 'default', 'admin', 'MENU', 'user_management', JSON_ARRAY('read', 'write', 'delete')),
('044a493b-52f6-436d-8e9b-3e6f82c6f056', 'default', 'admin', 'MENU', 'role_management', JSON_ARRAY('read', 'write', 'delete')),
('7f574b03-ab32-41bc-a708-3c0c3013c068', 'default', 'admin', 'MENU', 'system_settings', JSON_ARRAY('read', 'write')),
('a9019fcf-073a-4490-9737-4cd70f6e1ec8', 'default', 'admin', 'API', 'user_api', JSON_ARRAY('*')),
('e0a82b81-3aba-40e7-ac72-716f79f1f047', 'default', 'admin', 'API', 'role_api', JSON_ARRAY('*')),
('e2dfb2d7-4dff-40de-965f-b9f20abf12f8', 'default', 'admin', 'DATA', 'all_data', JSON_ARRAY('*'));

-- Insert default permissions for editor role
INSERT INTO `permissions` (`id`, `tenant_id`, `role_code`, `resource_type`, `resource_code`, `actions`) VALUES
('ee30cf19-9c3b-4e30-b04b-ddca116b783e', 'default', 'editor', 'MENU', 'content_management', JSON_ARRAY('read', 'write')),
('3a41cec5-87d5-41b9-afea-ae6515abd581', 'default', 'editor', 'MENU', 'material_library', JSON_ARRAY('read', 'write')),
('8c51ef44-393a-4404-8658-e7ec3f717532', 'default', 'editor', 'API', 'content_api', JSON_ARRAY('read', 'write')),
('236b1965-1409-43b4-9ea9-97448c3fff55', 'default', 'editor', 'DATA', 'own_data', JSON_ARRAY('read', 'write'));

-- Insert default permissions for user role
INSERT INTO `permissions` (`id`, `tenant_id`, `role_code`, `resource_type`, `resource_code`, `actions`) VALUES
('feb4de2b-4571-4953-add0-f48da86fcb0f', 'default', 'user', 'MENU', 'dashboard', JSON_ARRAY('read')),
('a1cf4960-7f15-4a32-92e0-4590f26b1e57', 'default', 'user', 'MENU', 'profile', JSON_ARRAY('read', 'write')),
('0476e2a5-355d-4afa-b8c8-d303cab43c56', 'default', 'user', 'API', 'profile_api', JSON_ARRAY('read', 'write')),
('b84dca20-bf37-4c3c-9227-79b79b9d6c14', 'default', 'user', 'DATA', 'own_data', JSON_ARRAY('read'));

-- Insert default permissions for viewer role
INSERT INTO `permissions` (`id`, `tenant_id`, `role_code`, `resource_type`, `resource_code`, `actions`) VALUES
('0dd2c2ed-020f-4a39-b832-265fdc6f60e5', 'default', 'viewer', 'MENU', 'dashboard', JSON_ARRAY('read')),
('27e5eca3-1811-4e43-aede-a40e672d16d7', 'default', 'viewer', 'MENU', 'reports', JSON_ARRAY('read')),
('575cc9d6-5edd-4ce8-93ff-5efe48690794', 'default', 'viewer', 'API', 'read_api', JSON_ARRAY('read')),
('3cb56d78-76ee-49fc-9a3f-2ee3117dbc9c', 'default', 'viewer', 'DATA', 'public_data', JSON_ARRAY('read'));
