import { useEffect, useRef } from 'react';

type CallbackFunction = () => void;

export const useInterval = (callback: CallbackFunction, delay: number | null) => {
  const savedCallback = useRef<CallbackFunction>();

  // 记住最新的回调函数
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // 设置定时器
  useEffect(() => {
    // 如果 delay 为 null，则不启动定时器
    if (delay === null) return;

    const tick = () => {
      if (savedCallback.current) {
        savedCallback.current();
      }
    };

    const id = setInterval(tick, delay);
    return () => clearInterval(id);
  }, [delay]);
};
