'use client';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/Tabs';
import GroupContent from '@/components/AdsTikTok/GroupContent';
import AdContent from '@/components/AdsTikTok/AdContent';
import AccountContent from '@/components/AdsTikTok/AccountContent';
import CampaignContent from '@/components/AdsTikTok/CampaignContent';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

import {
  useAdActions,
  useCurrentAd,
  useCurrentAdGroup,
  useCurrentAdvertiser,
  useCurrentCampaign,
  useCurrentView,
} from '@/store/ads/adStore';
import DateRangePicker from '@/components/ui/DateRangePicker'; // 添加这行
import useAdStore from '@/store/ads/adStore';
import { cn } from '@/utils/cn';
import { useState } from 'react';
import { Button } from '@/components/ui';
import { RefreshCw } from 'lucide-react';

export default function AdManagement() {
  const currentView = useCurrentView();
  const currentAdvertiser = useCurrentAdvertiser();
  const currentCampaign = useCurrentCampaign();
  const currentAdGroup = useCurrentAdGroup();
  const currentAd = useCurrentAd();
  const {
    setCurrentAd,
    setCurrentAdGroup,
    fetchAdGroups,
    setCurrentCampaign,
    setCurrentAdvertiser,
    syncCampaigns,
    setCurrentView,
  } = useAdActions();
  const { setDateRange } = useAdStore((state) => state.actions);
  const { dateRange } = useAdStore();
  const [showRedBadge, setShowRedBadge] = useState<string | null>(null);

  const switchView = (view: string, event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const clearButton = target.closest('[data-clear-selection]');
    if (clearButton) {
      return;
    }
    setCurrentView(view as any);
    setShowRedBadge(null);
  };
  const handleDateRangeChange = (dates: { startTime: string; endTime: string } | undefined) => {
    if (dates) {
      setDateRange(dates);
    } else {
      setDateRange(undefined);
    }
  };

  const handleClearSelection = (type: string, event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const clearButton = target.closest('[data-clear-selection]');
    if (!clearButton) {
      return;
    }
    event.stopPropagation();

    if (showRedBadge === type) {
      if (currentView === type) {
        switch (type) {
          case 'advertisers':
            setCurrentAdvertiser([]);
            break;
          case 'campaigns':
            setCurrentCampaign([]);
            break;
          case 'group':
            setCurrentAdGroup(null);
            break;
          case 'ad':
            setCurrentAd(null);
            break;
          default:
        }
      }
      setShowRedBadge(null);
    } else {
      setShowRedBadge(type);
    }
  };

  const renderCloseIcon = (count: number) => {
    return (
      <div data-clear-selection="true" className="flex h-5 w-5 items-center justify-center rounded-full">
        <span className="group-hover:hidden">{count}</span>
        <svg
          className="hidden h-3 w-3 flex-shrink-0 group-hover:block"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
    );
  };

  const renderCountIcon = (count: number) => {
    return (
      <div className="flex h-5 w-5 items-center justify-center rounded-full">
        <span>{count}</span>
      </div>
    );
  };

  const renderBadge = (type: string, count: number) => {
    const isActiveTab = currentView === type;
    return (
      <span
        className={cn(
          'group ml-2 inline-flex flex-shrink-0 cursor-pointer items-center justify-center rounded-full text-xs text-white',
          showRedBadge === type ? 'bg-red-500' : isActiveTab ? 'bg-cyan-500 hover:bg-cyan-600' : 'bg-cyan-500',
        )}
        onClick={(e) => handleClearSelection(type, e)}
      >
        {isActiveTab ? renderCloseIcon(count) : renderCountIcon(count)}
      </span>
    );
  };

  const synchronous = async () => {
    if (!currentAdvertiser) {
      toast.error('请先选择广告账户');
      return;
    }
    // if (!dateRange?.startTime || !dateRange?.endTime) {
    //   toast.error('请选择日期范围');
    //   return;
    // }
    // await syncCampaigns({
    //   start_date: dateRange?.startTime,
    //   end_date: dateRange?.endTime,
    // });
    // 获取当前时间
    const now = new Date();
    // 设置结束时间为当前日期的 23:59:59
    const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    // 计算开始时间为前一年的同一时间
    const startTime = new Date(endTime);
    startTime.setFullYear(startTime.getFullYear() - 1);

    // console.log(dayjs(startTime).format('YYYY/MM/DD HH:mm:ss'));

    try {
      await syncCampaigns({
        start_date: dayjs(startTime).format('YYYY/MM/DD HH:mm:ss'),
        end_date: dayjs(endTime).format('YYYY/MM/DD HH:mm:ss'),
      });
    } catch (error) {
      console.error('同步活动时出错:', error);
    }
  };
  return (
    <div className="mt-0 h-[calc(100vh-32px)] w-full space-y-4 rounded-2xl border border-gray-800 bg-[#FFFFFF0D] py-4">
      {/* 顶部标签页 */}
      <div className="relative">
        <Tabs value={currentView} className="w-full">
          <TabsList className="h-auto w-full justify-start border-b border-gray-800 bg-transparent pl-2">
            <TabsTrigger
              value="advertisers"
              onClick={(e) => switchView('advertisers', e)}
              className="custom-border data-[state=active]:custom-border-active relative rounded-none px-4 py-2 text-sm font-semibold data-[state=active]:bg-opacity-0 data-[state=active]:text-white"
            >
              广告账户
              <div className="relative">
                {currentAdvertiser && currentAdvertiser.length > 0 && (
                  <span className="absolute -right-2 -top-3 -translate-y-1 translate-x-1 transform">
                    {renderBadge('advertisers', currentAdvertiser.length)}
                  </span>
                )}
              </div>
            </TabsTrigger>

            <TabsTrigger
              value="campaigns"
              onClick={(e) => switchView('campaigns', e)}
              className="custom-border data-[state=active]:custom-border-active relative rounded-none px-4 py-2 text-sm font-semibold data-[state=active]:bg-opacity-0 data-[state=active]:text-white"
            >
              广告系列
              <div className="relative">
                {currentCampaign && currentCampaign.length > 0 && (
                  <span className="absolute -right-2 -top-3 -translate-y-1 translate-x-1 transform">
                    {renderBadge('campaigns', currentCampaign.length)}
                  </span>
                )}
              </div>
            </TabsTrigger>

            <TabsTrigger
              value="group"
              onClick={(e) => switchView('group', e)}
              className="custom-border data-[state=active]:custom-border-active relative rounded-none px-4 py-2 text-sm font-semibold data-[state=active]:bg-opacity-0 data-[state=active]:text-white"
            >
              广告组
              <div className="relative">
                {currentAdGroup && currentAdGroup.length > 0 && (
                  <span className="absolute -right-2 -top-3 -translate-y-1 translate-x-1 transform">
                    {renderBadge('group', currentAdGroup.length)}
                  </span>
                )}
              </div>
            </TabsTrigger>

            <TabsTrigger
              value="ad"
              onClick={(e) => switchView('ad', e)}
              className="custom-border data-[state=active]:custom-border-active relative rounded-none px-4 py-2 font-semibold data-[state=active]:bg-opacity-0 data-[state=active]:text-white"
            >
              广告
              {/* {currentAd && currentAd?.length > 0 && renderBadge('ad', currentAd.length)} */}
              <div className="relative">
                {currentAd && currentAd.length > 0 && (
                  <span className="absolute right-0 top-0 -translate-y-1 translate-x-1 transform">
                    {renderBadge('ad', currentAd.length)}
                  </span>
                )}
              </div>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="advertisers" className="pl-4">
            <AccountContent />
          </TabsContent>
          <TabsContent value="campaigns" className="pl-4">
            <CampaignContent />
          </TabsContent>
          <TabsContent value="group" className="pl-4">
            <GroupContent />
          </TabsContent>
          <TabsContent value="ad" className="pl-4">
            <AdContent />
          </TabsContent>
        </Tabs>
      </div>
      <div className="absolute right-10 top-6 mb-[1px] flex border-gray-800 text-xs">
        {currentView === 'advertisers' && (
          <Button
            variant="outline"
            className="ml-4 mr-4 h-8 rounded border-[#363D54] bg-transparent text-xs"
            onClick={synchronous}
            disabled={
              !currentAdvertiser || currentAdvertiser.length <= 0
              //|| !dateRange.startTime ||
              // !dateRange.endTime
              // !dateRange ||
            }
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            同步
          </Button>
        )}

        {/* <DateRangePicker value={dateRange} onChange={handleDateRangeChange} test={true} /> */}
      </div>
    </div>
  );
}
