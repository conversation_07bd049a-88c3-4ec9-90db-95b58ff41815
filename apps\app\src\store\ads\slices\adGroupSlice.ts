import tiktokService from '@/services/tiktokService';
import type { StoreApi } from 'zustand';
import type { AdStore } from '../storeTypes';
import type { StoreSlice } from './types';
export interface AdGroupSlice extends StoreSlice<'setCurrentAdGroup' | 'fetchAdGroups'> {}
import { recursiveUnderscoreToCamel } from '@/utils/camel';
export const createAdGroupSlice = (set: StoreApi<AdStore>['setState'], get: () => AdStore): AdGroupSlice => ({
  actions: {
    setCurrentAdGroup: (adGroup) => {
      set({ currentAdGroup: adGroup, currentAd: null });
    },

    fetchAdGroups: async (params) => {
      set((state) => ({
        adGroupStatus: {
          ...state.adGroupStatus,
          loading: true,
          error: false,
        },
      }));
      const currentAdvertiser = get().currentAdvertiser;
      try {
        const requestParams = {
          ...params,
        };
        if (currentAdvertiser && currentAdvertiser.length > 0) {
          requestParams.advertiser_ids = currentAdvertiser.map((advertiser) => String(advertiser?.advertiserId));
        }
        const response = await tiktokService.getAdGroupList(requestParams);
        const responseToCamel = recursiveUnderscoreToCamel(response, ['json_date']);
        set({
          adGroups: responseToCamel,
          adGroupStatus: { loading: false, error: false },
        });
      } catch (error) {
        console.error('获取广告组失败:', error);
        set({
          adGroupStatus: { loading: false, error: true },
        });
      }
    },
  },
});
