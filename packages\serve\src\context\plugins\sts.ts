import { sts as stsLib } from 'tencentcloud-sdk-nodejs';
import { ActionContextPluginLoader } from '../../types';

const StsClient = stsLib.v20180813.Client;

const buildStsClient = () => {
  return new StsClient({
    credential: { secretId: process.env.COS_SECRET_ID, secretKey: process.env.COS_SECRET_KEY },
    region: 'ap-shanghai',
    profile: { httpProfile: { endpoint: 'sts.ap-shanghai.tencentcloudapi.com' } },
  });
};

const stsPlugin: ActionContextPluginLoader<'sts', ReturnType<typeof buildStsClient>> = async () => {
  const stsClient = buildStsClient();
  return {
    name: 'sts',
    plugin: stsClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 消息队列操作API
     */
    sts: ReturnType<typeof buildStsClient>;
  }
}

export default stsPlugin;
