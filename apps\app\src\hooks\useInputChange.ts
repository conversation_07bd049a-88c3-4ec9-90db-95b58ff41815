import type React from 'react';

import { useState } from 'react';

const useInputChange = (initialValue = '') => {
  const [value, setValue] = useState(initialValue);

  const handleChange = (v: string | React.ChangeEvent<HTMLInputElement>) => {
    if (typeof v !== 'string') {
      const res = v.target.value;
      setValue(res);
    } else {
      setValue(v);
    }
  };

  return {
    value,
    setValue,
    onChange: handleChange,
  };
};

export { useInputChange };
