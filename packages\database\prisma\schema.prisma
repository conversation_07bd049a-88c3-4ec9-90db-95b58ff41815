generator client {
    provider        = "prisma-client-js"
    binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
    previewFeatures = ["views"]
}

generator json {
    provider = "prisma-json-types-generator"
    allowAny = "true"
}

datasource db {
    provider = "mysql"
    url      = env("DATABASE_URL")
}

model members {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 用户id
    user_id        String
    /// 用户状态
    user_status    String
    /// 用户昵称
    nickname       String
    /// 用户邮箱
    email          String
    /// 登录账号
    account        String
    /// 是否为管理员
    admin          Int
    /// 用户密码
    password       String

    // 新增本地认证相关字段
    /// 密码哈希值
    password_hash           String?
    /// 密码盐值
    salt                    String?
    /// 是否已迁移
    is_migrated             Boolean   @default(false)
    /// 是否需要重置密码
    password_reset_required Bo<PERSON>an   @default(false)
    /// 迁移时间
    migration_date          DateTime?
    /// 原 Authing 用户ID
    authing_user_id         String?
    /// 最后登录时间
    last_login_at           DateTime?
    /// 登录尝试次数
    login_attempts          Int       @default(0)
    /// 锁定到期时间
    locked_until            DateTime?
    /// 邮箱是否已验证
    email_verified          Boolean   @default(true)
    /// 手机号
    phone                   String?

    // 关联关系
    user_roles    user_roles[]
    user_sessions user_sessions[]

    @@unique([tenant_id, user_id])
    @@index([email, tenant_id], map: "idx_members_email_tenant")
    @@index([authing_user_id], map: "idx_members_authing_user_id")
    @@index([is_migrated, password_reset_required], map: "idx_members_migration_status")
    @@index([last_login_at, login_attempts], map: "idx_members_login_status")
}

model member_subscribed_categories {
    id             String    @id @default(uuid())
    tmp_created_at DateTime  @default(now())
    tmp_updated_at DateTime
    tmp_deleted_at DateTime?
    tenant_id      String
    user_id        String
    category_id    String

    @@unique([tenant_id, user_id, category_id])
}

model tenants {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 租户名称
    name           String
    /// 租户管理员邮箱
    admin_email    String
}

/// 成员钱包 每一个成员有且只有一个钱包 租户和用户自身没有钱包
model member_wallets {
    id             String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime @updatedAt
    /// 租户id 与用户体系关联 memeber_id 的唯一性等价于 tenant_id + user_id 的唯一性
    tenant_id      String
    /// 用户id 与用户体系关联 memeber_id 的唯一性等价于 tenant_id + user_id 的唯一性
    user_id        String
    /// 额度
    quota          Int

    @@unique([tenant_id, user_id])
}

/// 钱包变更记录表
model member_wallet_changelogs {
    id             String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime @updatedAt
    /// 钱包id
    wallet_id      String
    /// 所属成员的租户id
    tenant_id      String
    /// 所属成员的用户id
    user_id        String
    /// 变更类型
    change_type    String
    /// 变更原因
    change_reason  String
    /// 变更额度
    quota          Int
    /// 变更后额度
    result_quota   Int
    /// 来源钱包id 当变更类型为 TRANSFER_IN 时
    from_wallet_id String?
    /// 来源租户id 当变更类型为 TRANSFER_IN 时
    from_tenant_id String?
    /// 来源用户id 当变更类型为 TRANSFER_IN 时
    from_user_id   String?
    /// 来源钱包id 当变更类型为 TRANSFER_OUT 时
    to_wallet_id   String?
    /// 目标租户id 当变更类型为 TRANSFER_OUT 时
    to_tenant_id   String?
    /// 目标用户id 当变更类型为 TRANSFER_OUT 时
    to_user_id     String?
}

/// 配置信息表，跟租户关联
model source_configs {
    id                           String    @id @default(uuid())
    /// 租户id
    tenant_id                    String    @unique
    /// 删除时间
    tmp_deleted_at               DateTime?
    /// 最大存储空间 单位：MB
    max_storage_size             Int       @default(0)
    /// 已使用存储空间 单位：MB
    used_storage_size            Int       @default(0)
    /// langfuse 机构 id
    langfuse_org_id              String
    /// langfuse 项目 id
    langfuse_project_id          String
    /// langfuse 项目公钥
    langfuse_public_key          String
    /// langfuse 项目私钥
    langfuse_secret_key          String
    /// vod 子应用 id
    vod_sub_app_id               String
    /// vod 分类-屏幕录制
    vod_c_screen_record          String
    /// vod 分类-屏幕录制-最终素材
    vod_c_screen_record_final    String
    /// vod 分类-屏幕录制-合并素材
    vod_c_screen_record_merge    String
    /// vod 分类-屏幕录制-原始素材
    vod_c_screen_record_original String
    /// vod 分类-屏幕录制-拆分素材
    vod_c_screen_record_split    String
    /// cos 路径
    cos_path                     String?
    /// [TenantTaskPoolType] 切片任务池配置
    slice_task_pool              Json?
    /// [StringList] 子系统
    enabled_sub_systems          Json?
}

/// 素材库表
model materials {
    id                String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at    DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at    DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at    DateTime?
    /// 所属租户id
    tenant_id         String
    /// 素材名称
    name              String    @db.VarChar(1000)
    /// 素材自定义唯一标识
    unique_identifier String?   @db.VarChar(1000)
    /// 素材大小 单位： KB
    size              Int       @db.UnsignedInt
    /// 素材类型 目前只有视频 VIDEO
    type              String
    /// 视频长度 单位 秒
    video_duration    Int
    /// 所属 VOD 子应用 id
    vod_sub_app_id    String
    /// 所属 VOD 分类
    vod_category_id   String
    /// VOD fileId
    vod_file_id       String    @unique
    /// VOD mediaUrl
    vod_media_url     String
    /// VOD coverUrl
    vod_cover_url     String
    /// 素材库文件夹id
    directory_id      String    @default("")
    // 是否有音频
    is_audio          Boolean?
    /// [SliceMaterialListType] 切分后素材id列表
    split_materials   Json?
    /// VOD 音频 fileId
    vod_audio_file_id String?
    /// [GenerationParams] 如果是生成视频，需要存储对应生成参数
    generation_params Json?
    /// [Properties] 自定义属性
    properties        Json?

    as_origin_in_generation_sub_tasks video_generation_sub_tasks[]
    video_distribution_sub_tasks      video_distribution_sub_tasks[]
}

/// 素材库文件夹表
model material_directories {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 文件夹名称
    name           String
    /// 直属父文件夹id
    parent_id      String?
    /// 2级父文件夹id
    parent2_id     String?
    /// 3级父文件夹id
    parent3_id     String?
    /// 4级父文件夹id
    parent4_id     String?
    /// 5级父文件夹id
    parent5_id     String?
    /// 6级父文件夹id
    prent6_id      String?
}

/// 视频生成任务表
model video_generation_tasks {
    id                        String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at            DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at            DateTime @updatedAt
    /// 所属租户id
    tenant_id                 String
    /// 所属用户id/发起该任务的用户id
    user_id                   String
    /// 任务名称
    name                      String   @db.VarChar(1000)
    /// 切分长视频/短视频 300/1800 单位s
    slice_duration            String
    /// 方式
    method                    String
    /// 原视频语言 见语言代码
    video_language            String?
    /// 生成方式
    generation_type           String?
    /// 行业
    industry                  String?
    /// 行业id
    industry_id               String?
    /// kol
    kol_style                 String?
    /// 商品链接
    product_url               String?
    /// 商品标题
    product_title             String?
    /// 商品最低价格
    product_min_price         Int?
    /// 商品最高价格
    product_max_price         Int?
    /// 商品币种
    product_currency          String?
    /// [StringList] 视频风格 Prompt的key，可多选
    prompts                   Json
    /// 模块视频 vod id
    template_video_vod_id     String?
    /// [StringList] 模板视频id列表
    template_video_tiktok_ids Json?
    /// 商品分析
    product_analysis          String?  @db.Text
    /// 模块视频语言
    template_video_language   String?
    /// 目标语言
    target_language           String?
    /// 核心突出点
    custom_prompt             String?
    /// 语音克隆类型 default 默认克隆，只能选择固定音色; template 模板克隆， 可以克隆上传的文件音色 original 原视频克隆， 可以克隆原视频音色
    target_voice_clone_type   String?
    /// 目标语音
    target_voice              String?
    /// 目标语音参考时长
    target_video_duration     Int?
    /// 场景植入
    scene_implantation        Boolean?
    /// 节日氛围
    festive_atmosphere        Boolean?
    /// 生成轮次
    generate_round            Int
    /// 视频加速 枚举 1X 1.3X 1.5X 2.0X
    video_speed               String
    /// 字幕 0:无字幕 1:有字幕
    subtitle                  Int
    /// 转场效果
    transition_mode           String?
    /// dify workflow key
    dify_workflow_key         String?
    /// 生成来源 roasmax ADS 等
    generation_source         String?
    /// 生成来源的 ADS 批量创建批次ID, 广告id等
    generation_source_info    Json?

    sub_tasks video_generation_sub_tasks[]
}

/// 视频生成子任务表 一个切片任务下有多个子任务，每个子任务对应一个trace，一个trace是一个完整的切片任务，一个原始素材可能对应多个任务
model video_generation_sub_tasks {
    id                      String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at          DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at          DateTime @updatedAt
    /// 所属租户id
    tenant_id               String
    /// 所属用户id/发起该任务的用户id
    user_id                 String
    /// 所属切片任务id
    task_id                 String
    /// 子任务类型 slice generate slice是等待切分完成，generate是等待生成完成
    sub_task_type           String   @default("generate")
    /// 视频素材id
    origin_material_id      String?
    /// 切分后素材fileId
    slice_vod_file_id       String?
    /// 切分后素材音频fileId
    slice_vod_audio_file_id String?
    /// [IdList] 生成素材id
    generated_material_ids  Json
    /// 任务执行状态
    status                  String
    /// 任务执行状态描述
    status_desc             String?
    /// 使用的traceId
    trace_id                String?

    origin_material materials?             @relation(fields: [origin_material_id], references: [id])
    task            video_generation_tasks @relation(fields: [task_id], references: [id])

    @@index([origin_material_id], map: "video_generation_sub_tasks_origin_material_id_fkey")
    @@index([task_id], map: "video_generation_sub_tasks_task_id_fkey")
}

/// 视频切分任务表
model video_slice_tasks {
    id                 String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at     DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at     DateTime @updatedAt
    /// 所属租户id
    tenant_id          String
    /// 所属用户id/发起该任务的用户id
    user_id            String
    /// 任务名称
    name               String   @db.VarChar(1000)
    /// 切分单位时长 每 x 秒一个切分单位
    slice_duration     Int
    /// 视频素材id
    origin_material_id String?
    /// [SliceMaterialListType] 切分素材id列表
    split_material_ids Json
    /// 任务执行状态
    status             String
    /// 任务执行状态描述
    status_desc        String?
    /// 使用的traceId
    trace_id           String?
}

/// 视频录制配置表
model video_recording_configs {
    id                 String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at     DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at     DateTime @updatedAt
    /// 所属租户id
    tenant_id          String
    /// 任务名称
    name               String   @db.VarChar(1000)
    /// 所属IP
    ip                 String
    /// 直播间名称
    live_room          String
    /// 直播间ID
    live_room_identity String
    /// 录制模板ID
    record_template_id String
}

/// 视频录制任务表
model video_recording_tasks {
    id             String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime @updatedAt
    /// 所属租户id
    tenant_id      String
    /// 状态
    status         String
    /// 状态描述
    status_desc    String?
    /// 所属配置id
    config_id      String
    /// 直播流
    stream_url     String
    /// COS路径
    cos_path       String   @db.VarChar(1000)
}

/// 视频预切分任务表
model video_cut_tasks {
    id             String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime @updatedAt
    /// 所属租户id
    tenant_id      String
    /// 任务名称
    name           String   @db.VarChar(1000)
    /// 任务执行状态
    status         String
    /// 任务执行状态描述
    status_desc    String
    /// 视频链接
    origin_url     String   @db.VarChar(4000)
    /// [StringList] 商品列表
    goods_list     Json
    /// IP
    ip             String
    /// 直播间
    live_room      String
    /// 直播场次
    live_session   String
    /// 语言
    language       String
    /// 切割模式
    cut_mode       String
}

/// 统一异步清理任务
model async_clean_tasks {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 所属租户id
    tenant_id      String
    /// 清理任务类型
    task_type      String
    /// 清理资源id 该字段具体含义取决于 task_type
    target_id      String
    /// 预期开始时间
    expect_start   DateTime
    /// 实际开始时间
    actual_start   DateTime?
    /// 实际结束时间
    actual_end     DateTime?
    /// 清理任务状态 待执行/执行中/已完成/已失败
    task_status    String
}

/// 会员等级
model pricing_plans {
    id             String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime @updatedAt
    /// 所属租户id
    tenant_id      String
    /// 会员类型  
    plan_name      String
    /// 额度
    quota          Int
    /// 开始日期
    start_time     DateTime
    /// 结束日期
    end_time       DateTime
}

/// 点数变更记录
model quota_changelogs {
    id              String   @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at  DateTime @default(now())
    /// 审计字段，不可修改
    tmp_updated_at  DateTime @updatedAt
    /// 所属成员的租户id
    tenant_id       String
    /// 会员等级id
    pricing_plan_id String
    /// 变更类型
    change_type     String
    /// 变更原因
    change_reason   String
    /// 变更额度
    quota           Int
    /// 变更后额度
    result_quota    Int
}

/// 视频分发任务表
model video_distribution_tasks {
    id                       String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at           DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at           DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at           DateTime?
    /// 所属租户id
    tenant_id                String
    /// 商品id
    goods_id                 String
    /// 关联生成任务id
    video_generation_task_id String
    /// 状态 审核中/待审核
    status                   String
    /// 批次号（目前按照东八区日期作为批次号）
    distribution_batch_no    String
    /// 直播间
    live_room                String?
    /// 转推到COS的路径 有则转推，无则不转推
    cos_push_path            String?
    /// 计划分发数量
    plan_count               Int?

    goods                        goods                          @relation(fields: [goods_id], references: [id])
    video_distribution_sub_tasks video_distribution_sub_tasks[]

    @@index([goods_id], map: "video_distribution_tasks_goods_id_fkey")
}

/// 视频分发子任务表
model video_distribution_sub_tasks {
    id                   String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at       DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at       DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at       DateTime?
    /// 所属租户id
    tenant_id            String
    /// 素材id
    material_id          String?
    /// IP
    ip                   String?
    /// 商品id
    goods_id             String?
    /// 商品名 这里先不用goods_id 因为生成任务时，商品可能还不存在
    goods_name           String?   @db.VarChar(1000)
    /// 直播间
    live_room            String?
    /// 直播场次
    live_session         String?
    /// 分发任务id
    task_id              String?
    /// 分发批次 每一次点击分发时，都会生成一个批次号。该批次号会跨越多个分发任务，且一个分发任务可以有多个批次号
    batch_no             String?
    /// 分发账号id
    social_account_id    String?
    /// 分发前所在cos路径
    wait_cos_path        String?   @db.VarChar(1000)
    /// 分发后所在cos路径
    distributed_cos_path String?   @db.VarChar(1000)
    /// 分发状态
    status               String
    /// 状态描述
    status_desc          String?
    /// 发布时间
    publish_at           DateTime?
    /// 发布计划时间
    publish_plan_at      DateTime?
    /// 发布标题
    publish_title        String?
    /// [StringList] 发布话题
    publish_topic        Json?
    /// 发布商品名称
    publish_product_name String?
    /// 发布商品链接
    publish_product_link String?

    material materials?                @relation(fields: [material_id], references: [id])
    task     video_distribution_tasks? @relation(fields: [task_id], references: [id])
}

/// 商品表
model goods {
    id               String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at   DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at   DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at   DateTime?
    /// 所属租户id
    tenant_id        String
    /// 商品外部key 用于同步外部数据时定位唯一数据
    out_key          String?
    /// 商品名称
    name             String    @db.VarChar(1000)
    /// 状态 在售/下架/未知
    status           String?
    /// [StringList] 商品别名
    alias            Json?
    /// 默认推广链接
    link             String?   @db.VarChar(1000)
    /// [StringList] 商品图片（第一个为首图）
    images           Json?
    /// [StringList] 商品标签
    tags             Json?
    /// [Properties] 商品自定义属性
    properties       Json?
    /// [StringList] 直播间
    live_rooms       Json?
    /// 所属IP
    ip               String?
    /// 平台
    platform         String?
    /// 短编号
    short_sn         String?
    /// 产品id
    product_identity String?
    /// 产品链接
    product_url      String?
    /// 爆品标签
    hot_product      Int?
    /// 店铺名称
    shop_name        String?
    /// 佣金率
    commission_rate  Int?
    /// 备注
    remark           String?   @db.Text
    /// 价格
    price            Int?
    /// 库存
    stock_amount     Int?

    /// 内容分发任务
    video_distribution_tasks video_distribution_tasks[]
}

/// 社交媒体账号渠道
model social_account_channels {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 渠道名称
    name           String
}

/// 社交媒体账号团长
model social_account_masters {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 名称
    name           String
    /// 渠道id
    channel_id     String
}

/// 社交媒体账号
model social_accounts {
    id                       String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at           DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at           DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at           DateTime?
    /// 所属租户id
    tenant_id                String
    /// 状态
    status                   String
    /// 团长id
    master_id                String?
    /// 所属IP
    ip                       String?
    /// 优质账号
    quality                  Int?
    /// [StringList] 标签
    tags                     Json?
    /// 平台账号昵称
    nickname                 String?
    /// 平台粉丝数
    fans_count               Int?
    /// 平台账号id
    account_identity         String?
    /// 平台账号unique id
    account_unique_identity  String?
    /// 合作码
    cooperation_code         String?
    /// 账号手机号
    phone_no                 String?
    /// 账号所有者
    owner_name               String?
    /// 绑定时间
    bind_at                  DateTime?
    /// 解绑时间
    unbind_at                DateTime?
    /// 带货时间
    begin_sale_at            DateTime?
    /// 结算方式（线上，线下）
    settle_type              String?
    /// 佣金设置-机构
    commission_agency_ratio  Int?
    /// 佣金设置-达人
    commission_account_ratio Int?
    /// 佣金设置-团长
    commission_master_ratio  Int?
    /// 分享密钥
    share_secret             String?
    /// 分享链接
    share_link               String?
    /// 分享提取码
    share_extract            String?
    /// 分享过期时间
    share_expired_at         DateTime?
    /// 备注
    remark                   String?
    /// [Properties] 自定义属性
    properties               Json
    /// 账号外部key 用于同步外部数据时定位唯一数据
    out_key                  String?
    /// 授权开始时间
    ip_authorization_at      DateTime?
    /// 平台
    platform                 String?
    /// 绑定的云终端
    cloud_terminal_id        String?   @unique
    /// 平台等级
    level                    String?
    /// 平台分成比
    share_ratio              Int?
    /// 平台最近开播时间
    last_live_at             DateTime?
    /// 平台30日开播场次
    live_count_30d           Int?
    /// 平台30日开播时长
    live_duration_30d        Int?
    /// 平台30日视频数
    video_count_30d          Int?
    /// 绑定MCN开始时间
    mcn_bind_at              DateTime?
    /// 绑定MCN结束时间
    mcn_unbind_at            DateTime?

    cloud_terminal cloud_terminals? @relation(fields: [cloud_terminal_id], references: [id])
}

/// 云终端
model cloud_terminals {
    id                          String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at              DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at              DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at              DateTime?
    /// 所属租户id
    tenant_id                   String
    /// 终端名称
    name                        String
    /// 终端类型(云主机浏览器+COS: cloud_host_browser_cos 云主机浏览器: cloud_host_browser)
    type                        String
    /// 终端SN（云主机ID+浏览器ID+COS）
    sn                          String
    /// 终端状态（空闲，锁定，已绑定）
    status                      String
    /// 云主机ID
    cloud_host_identity         String?
    /// 云主机浏览器ID
    cloud_host_browser_identity String?
    /// COS文件夹路径 当 type 为 cloud_host_browser_cos 时
    cloud_host_cos_path         String?
    /// 已绑定的社交账号id
    bind_social_account_id      String?
    /// 终端备注
    remark                      String?

    social_accounts social_accounts?
}

model orders {
    id                                              String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at                                  DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at                                  DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at                                  DateTime?
    /// 所属租户id
    tenant_id                                       String
    /// 平台
    platform                                        String
    /// 订单外部key 用于同步外部数据时定位唯一数据
    out_key                                         String?
    /// 订单id
    order_id                                        String
    /// 所属商品id
    product_identity                                String?
    /// 商品名称
    product_name                                    String?   @db.VarChar(1000)
    /// 订单状态 
    order_status                                    String
    /// 达人名称
    influencer_name                                 String?
    /// 达人抖音号
    influencer_douyin_id                            String?
    /// 成交金额
    transaction_amount                              Int?
    /// 销量
    sales_amount                                    Int?
    /// 佣金率
    commission_rate                                 Int?
    /// 佣金收入
    commission_amount                               Int?
    /// 结算金额
    settlement_amount                               Int?
    /// 结算佣金收入-达人
    influencer_account_settlement_commission_amount Int?
    /// 结算佣金收入-机构
    agency_settlement_commission_amount             Int?
    /// 订单支付时间
    order_pay_at                                    DateTime?
    /// 订单收货时间
    order_receipt_at                                DateTime?
    /// 订单结算时间
    order_settlement_at                             DateTime?
    /// 分成比例
    commission_split_ratio                          Int?
    /// 店铺id
    shop_id                                         String?
    /// 店铺名称
    shop_name                                       String?
    /// 细分流量来源
    traffic_source                                  String?
    /// 备注
    remark                                          String?   @db.Text
    /// [Properties] 自定义字段
    properties                                      Json
}

/// 对接ads的tiktok账号
model tiktok_ads_accounts {
    id             String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 备注
    remark         String?
    /// 账号uid
    tiktok_uid     String
}

/// 自定义属性的字段定义表，全局通用
model property_definitions {
    id             String    @id @default(uuid())
    /// 所属资源名 建议使用表名
    resource_name  String
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 所属租户id
    tenant_id      String
    /// 属性名称（相当于key）
    name           String
    /// 属性展示名称
    display_name   String
    /// 属性类型 (文本、数字、枚举、链接)
    type           String
    /// [StringList] 枚举值，若属性类型为枚举时，该字段有值
    enum_values    Json?
}

model data_import_templates {
    id             String    @id @default(uuid())
    /// 所属租户id
    tenant_id      String
    /// 审计字段，不可修改
    tmp_created_at DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at DateTime?
    /// 资源名
    resource_name  String
    /// 模板名称
    name           String
    /// 模板描述
    description    String?
    /// [DataImportTemplateContent] 模板内容
    content        Json
}

model contact_inquiries {
    id          String    @id
    name        String    @db.VarChar(100)
    company     String?   @db.VarChar(100)
    phone_no    String?   @db.VarChar(100)
    email       String?   @db.VarChar(100)
    commited_at DateTime?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribute_videos {
    id           BigInt    @id @default(autoincrement())
    ip_name      String?   @db.VarChar(255)
    task_id      String?   @db.VarChar(255)
    product_id   String?   @db.VarChar(255)
    product_name String?   @db.VarChar(255)
    product_type String?   @db.VarChar(255)
    group_id     String?   @db.VarChar(255)
    uid          String?   @db.VarChar(255)
    user_type    String?   @db.VarChar(255)
    user_name    String?   @db.VarChar(255)
    cos_src      String?   @db.Text
    cos_dest     String?   @db.Text
    gmt_create   DateTime? @default(now()) @db.DateTime(0)
    gmt_modified DateTime? @default(now()) @db.DateTime(0)
}

model sys_contact_inquiries {
    id               String  @id
    contact_name     String?
    contact_company  String?
    contact_phone_no String? @db.VarChar(100)
    contact_email    String? @db.VarChar(100)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_usage {
    id                         BigInt    @id @default(autoincrement())
    uid                        String?   @db.VarChar(255)
    seal_video_count           Int?
    seal_remaining_quantity    Int?
    no_seal_video_count        Int?
    no_seal_remaining_quantity Int?
    gmt_create                 DateTime? @default(now()) @db.DateTime(0)
    gmt_modified               DateTime? @default(now()) @db.DateTime(0)
}

view v_duplicate_goods {
    id                 String @id
    name               String @db.VarChar(1000)
    tenant_id          String
    goods_count        BigInt @default(0)
    /// [IdList]
    ids                Json?
    /// [IdList]
    tenant_ids         Json?
    /// [StringList]
    images             Json?
    /// [StringList]
    tags               Json?
    /// [StringList]
    live_rooms         Json?
    /// [StringList]
    aliases            Json?
    /// [PropertiesList]
    properties         Json?
    /// [StringList]
    ips                Json?
    /// [StringList]
    platforms          Json?
    /// [StringList]
    short_sns          Json?
    /// [StringList]
    product_identities Json?
    /// [StringList]
    product_urls       Json?
    /// [NumberList]
    hot_products       Json?
    /// [StringList]
    shop_names         Json?
    /// [NumberList]
    commission_rates   Json?
    /// [StringList]
    remarks            Json?
    /// [NumberList]
    prices             Json?
    /// [NumberList]
    stock_amounts      Json?
    /// [StringList]
    links              Json?
}

view v_goods_with_tasks {
    id                                           String    @id
    tmp_created_at                               DateTime  @default(now())
    tmp_updated_at                               DateTime
    tmp_deleted_at                               DateTime?
    tenant_id                                    String
    out_key                                      String?
    name                                         String    @db.VarChar(1000)
    status                                       String?
    alias                                        Json?
    images                                       Json?
    tags                                         Json?
    properties                                   Json?
    live_rooms                                   Json?
    ip                                           String?
    platform                                     String?
    short_sn                                     String?
    product_identity                             String?
    product_url                                  String?
    hot_product                                  Int?
    shop_name                                    String?
    commission_rate                              Int?
    remark                                       String?   @db.Text
    price                                        Int?
    stock_amount                                 Int?
    link                                         String?   @db.VarChar(1000)
    links                                        Json?
    video_distribution_sub_tasks_wait_count      BigInt    @default(0)
    video_distribution_sub_tasks_generated_count BigInt    @default(0)
    video_distribution_sub_tasks_done_count      BigInt    @default(0)
}

model rednote_cookies {
    cookie         Json
    tmp_created_at DateTime? @default(now())
    id             String    @id @default(uuid())
}

model product_analysis_records {
    id               String    @id @db.VarChar(36)
    tmp_created_at   DateTime  @default(now()) @db.DateTime(0)
    tmp_updated_at   DateTime  @default(now()) @db.DateTime(0)
    tmp_deleted_at   DateTime? @db.DateTime(0)
    tenant_id        String    @db.VarChar(255)
    user_id          String    @db.VarChar(255)
    product_url      String    @db.VarChar(1000)
    share_id         String    @unique(map: "idx_share_id") @db.VarChar(255)
    share_expired_at DateTime? @db.DateTime(0)
    status           String    @db.VarChar(50)
    error_message    String?   @db.Text
    completed_steps  Int       @default(1)
    total_steps      Int       @default(4)
    product_title    String?   @db.VarChar(1000)
    product_analysis String?   @db.Text
    video_ids        Json?
    template_info    Json?
    suggestion       String?   @db.Text
    price            String?   @db.VarChar(255)
    description      String?   @db.Text
    playback_steps   Json?
    picturesUrl      Json?
    playback_version String?   @default("1.0") @db.VarChar(10)

    @@index([product_title(length: 255)], map: "idx_product_title")
    @@index([status], map: "idx_status")
    @@index([tenant_id, user_id], map: "idx_tenant_user")
}

/// AI视频生成任务表
model ai_video_generation_tasks {
    id               String    @id @default(uuid())
    /// 审计字段，不可修改
    tmp_created_at   DateTime  @default(now())
    /// 审计字段，不可修改
    tmp_updated_at   DateTime  @updatedAt
    /// 删除时间
    tmp_deleted_at   DateTime?
    /// 所属租户id
    tenant_id        String
    /// 所属用户id/发起该任务的用户id
    user_id          String
    /// 任务名称
    name             String    @db.VarChar(1000)
    /// 提示词
    prompt           String    @db.Text
    /// 视频类型
    video_type       String
    /// 参考图片URL
    reference_images Json?
    /// 外部任务ID（第三方API返回的task_id）
    external_task_id String?
    /// 任务状态 pending/processing/completed/failed
    status           String    @default("pending")
    /// 状态描述
    status_desc      String?
    /// 进度百分比 0-100
    progress         Int       @default(0)
    /// 生成的视频URL
    video_url        String?   @db.VarChar(1000)
    /// 生成的缩略图URL
    thumbnail_url    String?   @db.VarChar(1000)
    /// 错误信息
    error_message    String?   @db.Text
    /// 完成时间
    completed_at     DateTime?
    /// [Properties] 扩展属性，存储API响应的其他数据
    properties       Json?

    @@index([tenant_id, user_id], map: "idx_tenant_user")
    @@index([status], map: "idx_status")
    @@index([external_task_id], map: "idx_external_task_id")
}

/// 角色定义表
model roles {
    id          String    @id @default(uuid())
    /// 租户ID
    tenant_id   String
    /// 角色代码
    code        String
    /// 角色名称
    name        String
    /// 角色描述
    description String?
    /// 是否系统角色
    is_system   Boolean   @default(false)
    /// 创建时间
    created_at  DateTime  @default(now())
    /// 更新时间
    updated_at  DateTime  @default(now()) @updatedAt
    /// 删除时间
    deleted_at  DateTime?

    // 关联关系
    user_roles  user_roles[]
    permissions permissions[]

    @@unique([tenant_id, code], map: "unique_tenant_code")
    @@unique([code, tenant_id], map: "unique_code_tenant")
    @@index([tenant_id], map: "idx_roles_tenant_id")
    @@index([code], map: "idx_roles_code")
}

/// 用户角色关联表
model user_roles {
    id          String    @id @default(uuid())
    /// 租户ID
    tenant_id   String
    /// 用户ID
    user_id     String
    /// 角色代码
    role_code   String
    /// 分配者ID
    assigned_by String?
    /// 分配时间
    assigned_at DateTime  @default(now())
    /// 过期时间
    expires_at  DateTime?
    /// 创建时间
    created_at  DateTime  @default(now())

    // 关联关系
    member members @relation(fields: [tenant_id, user_id], references: [tenant_id, user_id])
    role   roles   @relation(fields: [role_code, tenant_id], references: [code, tenant_id])

    @@unique([tenant_id, user_id, role_code], map: "unique_user_role")
    @@index([tenant_id, user_id], map: "idx_user_roles_tenant_user")
    @@index([role_code], map: "idx_user_roles_role_code")
}

/// 权限资源表
model permissions {
    id            String                        @id @default(uuid())
    /// 租户ID
    tenant_id     String
    /// 角色代码
    role_code     String
    /// 资源类型
    resource_type permission_resource_type_enum
    /// 资源代码
    resource_code String
    /// 允许的操作
    actions       Json
    /// 创建时间
    created_at    DateTime                      @default(now())
    /// 更新时间
    updated_at    DateTime                      @default(now()) @updatedAt

    // 关联关系
    role roles @relation(fields: [role_code, tenant_id], references: [code, tenant_id])

    @@index([tenant_id, role_code], map: "idx_permissions_tenant_role")
    @@index([resource_type, resource_code], map: "idx_permissions_resource")
}

/// 用户会话表
model user_sessions {
    id             String   @id @default(uuid())
    /// 用户ID
    user_id        String
    /// 租户ID
    tenant_id      String
    /// Token哈希值
    token_hash     String
    /// 设备信息
    device_info    Json?
    /// IP地址
    ip_address     String?  @db.VarChar(45)
    /// 用户代理
    user_agent     String?  @db.Text
    /// 创建时间
    created_at     DateTime @default(now())
    /// 最后活跃时间
    last_active_at DateTime @default(now())
    /// 过期时间
    expires_at     DateTime
    /// 是否活跃
    is_active      Boolean  @default(true)

    // 关联关系
    member members @relation(fields: [tenant_id, user_id], references: [tenant_id, user_id])

    @@index([user_id], map: "idx_user_sessions_user_id")
    @@index([token_hash], map: "idx_user_sessions_token_hash")
    @@index([expires_at], map: "idx_user_sessions_expires_at")
}

/// 权限资源类型枚举
enum permission_resource_type_enum {
    MENU
    API
    DATA
}
