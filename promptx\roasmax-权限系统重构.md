# Roasmax - 权限系统重构

## 📋 任务概述

**任务ID**: hA5pGnnPkWx28Kwx39Zj6g  
**任务名称**: 权限系统重构  
**优先级**: 高  
**预估工时**: 3-4天  
**前置依赖**: 首次登录密码重置功能  

## 🎯 任务目标

重构基于角色的访问控制（RBAC）系统，替换 Authing 权限管理，实现本地化的权限验证和管理功能。

## 📊 详细任务分解

### 1. 权限系统核心架构

#### 1.1 权限管理服务
```typescript
// packages/serve/src/services/PermissionService.ts
export interface Permission {
  id: string;
  tenantId: string;
  roleCode: string;
  resourceType: 'MENU' | 'API' | 'DATA';
  resourceCode: string;
  actions: string[];
}

export interface UserPermissions {
  userId: string;
  tenantId: string;
  roles: string[];
  permissions: Permission[];
  resources: Record<string, string[]>; // resourceCode -> actions
}

export class PermissionService {
  private prisma: PrismaClient;
  private cache: Map<string, UserPermissions> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  
  constructor() {
    this.prisma = new PrismaClient();
  }
  
  async getUserPermissions(userId: string, tenantId: string): Promise<UserPermissions> {
    const cacheKey = `${tenantId}:${userId}`;
    
    // 检查缓存
    if (this.cache.has(cacheKey) && this.cacheExpiry.get(cacheKey)! > Date.now()) {
      return this.cache.get(cacheKey)!;
    }
    
    // 获取用户角色
    const userRoles = await this.prisma.user_roles.findMany({
      where: {
        tenant_id: tenantId,
        user_id: userId
      },
      include: {
        role: true
      }
    });
    
    const roles = userRoles.map(ur => ur.role_code);
    
    // 获取角色权限
    const permissions = await this.prisma.permissions.findMany({
      where: {
        tenant_id: tenantId,
        role_code: { in: roles }
      }
    });
    
    // 构建资源权限映射
    const resources: Record<string, string[]> = {};
    permissions.forEach(perm => {
      if (!resources[perm.resource_code]) {
        resources[perm.resource_code] = [];
      }
      resources[perm.resource_code].push(...(perm.actions as string[]));
    });
    
    // 去重权限
    Object.keys(resources).forEach(key => {
      resources[key] = [...new Set(resources[key])];
    });
    
    const userPermissions: UserPermissions = {
      userId,
      tenantId,
      roles,
      permissions,
      resources
    };
    
    // 缓存结果（5分钟）
    this.cache.set(cacheKey, userPermissions);
    this.cacheExpiry.set(cacheKey, Date.now() + 5 * 60 * 1000);
    
    return userPermissions;
  }
  
  async hasPermission(
    userId: string, 
    tenantId: string, 
    resourceCode: string, 
    action: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, tenantId);
    
    const resourceActions = userPermissions.resources[resourceCode];
    if (!resourceActions) {
      return false;
    }
    
    return resourceActions.includes(action) || resourceActions.includes('*');
  }
  
  async hasRole(userId: string, tenantId: string, roleCode: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, tenantId);
    return userPermissions.roles.includes(roleCode);
  }
  
  async hasAnyRole(userId: string, tenantId: string, roleCodes: string[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, tenantId);
    return roleCodes.some(role => userPermissions.roles.includes(role));
  }
  
  clearUserCache(userId: string, tenantId: string): void {
    const cacheKey = `${tenantId}:${userId}`;
    this.cache.delete(cacheKey);
    this.cacheExpiry.delete(cacheKey);
  }
  
  clearAllCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }
}
```

#### 1.2 权限验证中间件
```typescript
// packages/serve/src/middleware/permission.ts
export interface PermissionConfig {
  resource: string;
  action: string;
  roles?: string[];
  skipPermissionCheck?: boolean;
}

export function requirePermission(config: PermissionConfig) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(this: any, ctx: ActionContext<any>, ...args: any[]) {
      // 跳过权限检查（用于公开接口）
      if (config.skipPermissionCheck) {
        return originalMethod.apply(this, [ctx, ...args]);
      }
      
      const permissionService = new PermissionService();
      
      // 检查角色权限
      if (config.roles && config.roles.length > 0) {
        const hasRole = await permissionService.hasAnyRole(
          ctx.user.id,
          ctx.tenant.id,
          config.roles
        );
        
        if (!hasRole) {
          throw new Error(`权限不足：需要角色 ${config.roles.join(' 或 ')}`);
        }
      }
      
      // 检查资源权限
      const hasPermission = await permissionService.hasPermission(
        ctx.user.id,
        ctx.tenant.id,
        config.resource,
        config.action
      );
      
      if (!hasPermission) {
        throw new Error(`权限不足：无法访问资源 ${config.resource}:${config.action}`);
      }
      
      return originalMethod.apply(this, [ctx, ...args]);
    };
    
    return descriptor;
  };
}
```

### 2. 角色管理功能重构

#### 2.1 本地角色管理适配器
```typescript
// packages/serve/src/adapters/LocalRoleManager.ts
export class LocalRoleManager {
  private prisma: PrismaClient;
  private permissionService: PermissionService;
  
  constructor() {
    this.prisma = new PrismaClient();
    this.permissionService = new PermissionService();
  }
  
  async listRoles(params: {
    tenantId: string;
    page: number;
    limit: number;
    keywords?: string;
  }): Promise<{
    list: any[];
    totalCount: number;
  }> {
    const { tenantId, page, limit, keywords } = params;
    
    const where: any = {
      tenant_id: tenantId,
      deleted_at: null
    };
    
    if (keywords) {
      where.OR = [
        { name: { contains: keywords } },
        { code: { contains: keywords } },
        { description: { contains: keywords } }
      ];
    }
    
    const [roles, totalCount] = await Promise.all([
      this.prisma.roles.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { created_at: 'desc' }
      }),
      this.prisma.roles.count({ where })
    ]);
    
    return {
      list: roles.map(role => ({
        id: role.id,
        code: role.code,
        name: role.name,
        description: role.description,
        isSystem: role.is_system,
        createdAt: role.created_at,
        updatedAt: role.updated_at
      })),
      totalCount
    };
  }
  
  async createRole(params: {
    tenantId: string;
    code: string;
    name: string;
    description?: string;
    permissions: Array<{
      resourceType: 'MENU' | 'API' | 'DATA';
      resourceCode: string;
      actions: string[];
    }>;
  }): Promise<any> {
    const { tenantId, code, name, description, permissions } = params;
    
    return await this.prisma.$transaction(async (tx) => {
      // 创建角色
      const role = await tx.roles.create({
        data: {
          id: this.generateUUID(),
          tenant_id: tenantId,
          code,
          name,
          description,
          is_system: false
        }
      });
      
      // 创建权限
      if (permissions.length > 0) {
        await tx.permissions.createMany({
          data: permissions.map(perm => ({
            id: this.generateUUID(),
            tenant_id: tenantId,
            role_code: code,
            resource_type: perm.resourceType,
            resource_code: perm.resourceCode,
            actions: perm.actions
          }))
        });
      }
      
      return {
        id: role.id,
        code: role.code,
        name: role.name,
        description: role.description
      };
    });
  }
  
  async assignRole(params: {
    tenantId: string;
    roleCode: string;
    userIds: string[];
  }): Promise<void> {
    const { tenantId, roleCode, userIds } = params;
    
    // 检查角色是否存在
    const role = await this.prisma.roles.findFirst({
      where: {
        tenant_id: tenantId,
        code: roleCode,
        deleted_at: null
      }
    });
    
    if (!role) {
      throw new Error(`角色 ${roleCode} 不存在`);
    }
    
    // 批量分配角色
    const userRoleData = userIds.map(userId => ({
      id: this.generateUUID(),
      tenant_id: tenantId,
      user_id: userId,
      role_code: roleCode
    }));
    
    await this.prisma.user_roles.createMany({
      data: userRoleData,
      skipDuplicates: true
    });
    
    // 清除用户权限缓存
    userIds.forEach(userId => {
      this.permissionService.clearUserCache(userId, tenantId);
    });
  }
  
  async revokeRole(params: {
    tenantId: string;
    roleCode: string;
    userIds: string[];
  }): Promise<void> {
    const { tenantId, roleCode, userIds } = params;
    
    await this.prisma.user_roles.deleteMany({
      where: {
        tenant_id: tenantId,
        role_code: roleCode,
        user_id: { in: userIds }
      }
    });
    
    // 清除用户权限缓存
    userIds.forEach(userId => {
      this.permissionService.clearUserCache(userId, tenantId);
    });
  }
  
  async getUserRoles(params: {
    userId: string;
    tenantId: string;
  }): Promise<{
    list: any[];
  }> {
    const { userId, tenantId } = params;
    
    const userRoles = await this.prisma.user_roles.findMany({
      where: {
        user_id: userId,
        tenant_id: tenantId
      },
      include: {
        role: true
      }
    });
    
    return {
      list: userRoles.map(ur => ({
        id: ur.role.id,
        code: ur.role.code,
        name: ur.role.name,
        description: ur.role.description,
        assignedAt: ur.assigned_at
      }))
    };
  }
  
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}
```

### 3. 权限验证集成

#### 3.1 Server Actions 权限验证
```typescript
// apps/app/src/services/actions/user.ts (重构后)
import { requirePermission } from '@roasmax/serve/middleware/permission';

export class UserService {
  @requirePermission({
    resource: 'user_management',
    action: 'read',
    roles: ['admin', 'user_manager']
  })
  async getUserList(ctx: ActionContext<{ page: number; limit: number }>) {
    // 原有逻辑，但使用本地数据库查询
    const { page, limit } = ctx.data;
    const { id: tenantId } = await ctx.fetchTenant();
    
    const [users, totalCount] = await Promise.all([
      ctx.db.members.findMany({
        where: {
          tenant_id: tenantId,
          tmp_deleted_at: null
        },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user_roles: {
            include: {
              role: true
            }
          }
        }
      }),
      ctx.db.members.count({
        where: {
          tenant_id: tenantId,
          tmp_deleted_at: null
        }
      })
    ]);
    
    return {
      data: users.map(user => ({
        userId: user.user_id,
        nickname: user.nickname,
        username: user.account,
        email: user.email,
        phone: user.phone,
        roles: user.user_roles.map(ur => ur.role.name)
      })),
      pageInfo: {
        totalCount,
        page,
        limit
      }
    };
  }
  
  @requirePermission({
    resource: 'user_management',
    action: 'create',
    roles: ['admin', 'user_manager']
  })
  async createUser(ctx: ActionContext<CreateUserParams>) {
    // 使用本地用户创建逻辑
    const { username, password, nickname, email, quota, roleCode } = ctx.data;
    const { id: tenantId } = await ctx.fetchTenant();
    
    // 检查邮箱是否已存在
    const existingUser = await ctx.db.members.findFirst({
      where: {
        email,
        tenant_id: tenantId,
        tmp_deleted_at: null
      }
    });
    
    if (existingUser) {
      throw new Error('邮箱已存在');
    }
    
    // 加密密码
    const { hash, salt } = await PasswordManager.hashPassword(password);
    
    return await ctx.db.$transaction(async (tx) => {
      // 创建用户
      const user = await tx.members.create({
        data: {
          id: generateUUID(),
          tenant_id: tenantId,
          user_id: generateLocalUserId(),
          email,
          nickname,
          account: username,
          user_status: 'active',
          admin: 0,
          password: hash,
          password_hash: hash,
          salt,
          password_reset_required: false,
          is_migrated: true,
          email_verified: true
        }
      });
      
      // 分配角色
      if (roleCode) {
        await tx.user_roles.create({
          data: {
            id: generateUUID(),
            tenant_id: tenantId,
            user_id: user.user_id,
            role_code: roleCode
          }
        });
      }
      
      // 创建钱包记录
      await tx.member_wallets.create({
        data: {
          id: generateUUID(),
          tenant_id: tenantId,
          user_id: user.user_id,
          quota: quota || 0,
          used_quota: 0
        }
      });
      
      return {
        userId: user.user_id,
        email: user.email,
        nickname: user.nickname
      };
    });
  }
}
```

#### 3.2 API Routes 权限验证
```typescript
// apps/app/src/app/api/users/route.ts
import { api } from '@roasmax/serve';
import { PermissionService } from '@roasmax/serve/services/PermissionService';

export const GET = api(async (ctx, request) => {
  const permissionService = new PermissionService();
  
  // 检查权限
  const hasPermission = await permissionService.hasPermission(
    ctx.user.id,
    ctx.tenant.id,
    'user_management',
    'read'
  );
  
  if (!hasPermission) {
    throw new Error('权限不足：无法查看用户列表');
  }
  
  // 执行业务逻辑
  const users = await ctx.db.members.findMany({
    where: {
      tenant_id: ctx.tenant.id,
      tmp_deleted_at: null
    }
  });
  
  return {
    success: true,
    data: users
  };
});
```

### 4. 前端权限控制

#### 4.1 权限 Hook
```typescript
// apps/app/src/hooks/usePermissions.ts
import { useEffect, useState } from 'react';
import { action } from '@/utils/server-action/action';
import { getUserLoginInfo } from '@/services/actions/me';

interface UserPermissions {
  roles: string[];
  resources: Record<string, string[]>;
}

export function usePermissions() {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        const result = await action(getUserLoginInfo, undefined);
        if (result) {
          setPermissions({
            roles: result.roleData.list.map((role: any) => role.code),
            resources: result.resourceData.list.reduce((acc: any, resource: any) => {
              acc[resource.code] = resource.actions;
              return acc;
            }, {})
          });
        }
      } catch (error) {
        console.error('加载权限失败:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadPermissions();
  }, []);
  
  const hasPermission = (resource: string, action: string): boolean => {
    if (!permissions) return false;
    
    const resourceActions = permissions.resources[resource];
    if (!resourceActions) return false;
    
    return resourceActions.includes(action) || resourceActions.includes('*');
  };
  
  const hasRole = (role: string): boolean => {
    if (!permissions) return false;
    return permissions.roles.includes(role);
  };
  
  const hasAnyRole = (roles: string[]): boolean => {
    if (!permissions) return false;
    return roles.some(role => permissions.roles.includes(role));
  };
  
  return {
    permissions,
    loading,
    hasPermission,
    hasRole,
    hasAnyRole
  };
}
```

#### 4.2 权限控制组件
```typescript
// apps/app/src/components/PermissionGuard.tsx
import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

interface PermissionGuardProps {
  resource?: string;
  action?: string;
  roles?: string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionGuard({
  resource,
  action,
  roles,
  fallback = null,
  children
}: PermissionGuardProps) {
  const { hasPermission, hasAnyRole, loading } = usePermissions();
  
  if (loading) {
    return <div>加载中...</div>;
  }
  
  // 检查角色权限
  if (roles && roles.length > 0) {
    if (!hasAnyRole(roles)) {
      return <>{fallback}</>;
    }
  }
  
  // 检查资源权限
  if (resource && action) {
    if (!hasPermission(resource, action)) {
      return <>{fallback}</>;
    }
  }
  
  return <>{children}</>;
}
```

## ✅ 验收标准

1. **功能完整性**
   - [ ] 权限验证功能正常
   - [ ] 角色管理功能完整
   - [ ] 用户权限分配正确
   - [ ] 前端权限控制有效

2. **性能要求**
   - [ ] 权限查询响应时间 < 100ms
   - [ ] 权限缓存机制有效
   - [ ] 批量操作性能良好

3. **安全性要求**
   - [ ] 权限验证严格可靠
   - [ ] 无权限绕过漏洞
   - [ ] 敏感操作保护到位

## 🔧 Augment Code 提示词

```
请帮我重构 Roasmax 项目的权限系统：

1. 开发本地化的权限管理服务
2. 实现基于角色的访问控制（RBAC）
3. 创建权限验证中间件和装饰器
4. 重构角色管理功能
5. 集成前端权限控制
6. 优化权限查询性能

要求：
- 实现完整的 RBAC 权限模型
- 提供高性能的权限验证
- 支持细粒度的权限控制
- 包含权限缓存机制
- 提供友好的前端权限组件
- 确保权限验证的安全性

请提供完整的权限系统实现代码。
```

## 📅 时间安排

- **第1天**: 权限系统核心架构开发
- **第2天**: 角色管理功能重构
- **第3天**: 权限验证集成
- **第4天**: 前端权限控制和测试

## 🚨 风险提示

1. **权限漏洞风险**: 权限验证可能存在绕过漏洞
2. **性能风险**: 权限查询可能影响系统性能
3. **兼容性风险**: 可能与现有业务逻辑存在冲突
