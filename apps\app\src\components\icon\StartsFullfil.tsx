import React from 'react';

export const StarsFullfil = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.25372 2.08252L8.24232 5.38763C8.60249 6.59176 9.50768 7.55659 10.6864 7.99276L13.0319 8.86066L10.6864 9.72855C9.50768 10.1647 8.60249 11.1296 8.24232 12.3337L7.25372 15.6388L6.26513 12.3337C5.90495 11.1295 4.99977 10.1647 3.82103 9.72855L1.47559 8.86066L3.82103 7.99276C4.99977 7.55659 5.90495 6.59176 6.26513 5.38763L7.25372 2.08252Z"
        fill="currentColor"
      />
      <path
        d="M13.2054 2.92725L13.4041 3.59142C13.4941 3.89245 13.7204 4.13366 14.0151 4.24271L14.4705 4.41119L14.0151 4.57968C13.7204 4.68872 13.4941 4.92993 13.4041 5.23096L13.2054 5.89514L13.0068 5.23096C12.9167 4.92993 12.6904 4.68872 12.3958 4.57968L11.9404 4.41119L12.3958 4.24271C12.6904 4.13366 12.9167 3.89245 13.0068 3.59142L13.2054 2.92725Z"
        fill="currentColor"
      />
      <path
        d="M13.1879 9.77393L13.5338 11.1273C13.6186 11.4592 13.8674 11.7246 14.1931 11.8307L15.1507 12.1424L14.188 12.4558C13.8651 12.5609 13.6175 12.8228 13.5308 13.1512L13.1879 14.4486L12.8457 13.1533C12.7586 12.8237 12.5096 12.5613 12.1852 12.4568L11.208 12.1424L12.1801 11.8296C12.5073 11.7243 12.7575 11.4583 12.8426 11.1252L13.1879 9.77393Z"
        fill="currentColor"
      />
    </svg>
  );
};
