import * as XLSX from 'xlsx';

export class ExcelManager {
  private static instance: ExcelManager;
  private headerMapper: Record<string, string> = {};

  private constructor() {}

  public static getInstance(): ExcelManager {
    if (!ExcelManager.instance) {
      ExcelManager.instance = new ExcelManager();
    }
    return ExcelManager.instance;
  }

  /**
   * 设置表头映射关系
   * @param mapper 表头映射配置，key为中文表头，value为数据库字段名
   */
  public setHeaderMapper(mapper: Record<string, string>): void {
    this.headerMapper = mapper;
  }

  public getHeaderMapper(): Record<string, string> {
    return this.headerMapper;
  }

  /**
   * 解析Excel文件为对象数组
   * @param file Excel文件Buffer或文件路径
   * @param sheetName 工作表名称，可选
   * @returns 解析后的对象数组
   */
  public parseExcel(file: Buffer | string, sheetName?: string): any[] {
    const workbook = XLSX.read(file, { type: file instanceof Buffer ? 'buffer' : 'file' });
    const sheet = sheetName ? workbook.Sheets[sheetName] : workbook.Sheets[workbook.SheetNames[0]!];

    const rawData = XLSX.utils.sheet_to_json(sheet!, { header: 1 });
    if (rawData.length < 2) return [];

    // 创建反向映射：中文 -> 英文
    const reverseMapper: Record<string, string> = {};
    Object.entries(this.headerMapper).forEach(([englishKey, chineseHeader]) => {
      reverseMapper[chineseHeader] = englishKey;
    });

    const headers = rawData[0] as string[];
    const mappedHeaders = headers.map((header) => reverseMapper[header] || header);

    return rawData.slice(1).map((row) => {
      const obj: Record<string, any> = {};
      (row as any[]).forEach((cell, index) => {
        if (mappedHeaders[index]) {
          obj[mappedHeaders[index]] = cell;
        }
      });
      return obj;
    });
  }

  /**
   * 将数据导出为Excel
   * @param data 要导出的数据数组，key为英文
   * @returns Excel文件的Buffer
   */
  public generateExcel(data: Record<string, any>[]): Buffer {
    if (data.length === 0) {
      const workbook = XLSX.utils.book_new();
      const headers =
        Object.keys(this.headerMapper).length > 0
          ? Object.values(this.headerMapper) // 使用中文表头
          : [''];
      const worksheet = XLSX.utils.json_to_sheet([{}], {
        header: headers,
      });
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    }

    // 将数据中的英文key转换为中文表头，并且只保留mapper中存在的字段
    const mappedData = data.map((item) => {
      const mappedItem: Record<string, any> = {};
      Object.entries(this.headerMapper).forEach(([englishKey, chineseHeader]) => {
        // 只处理mapper中存在的字段
        if (item.hasOwnProperty(englishKey)) {
          mappedItem[chineseHeader] = item[englishKey];
        }
      });
      return mappedItem;
    });

    const worksheet = XLSX.utils.json_to_sheet(mappedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * 生成Excel并触发浏览器下载
   * @param data 要导出的数据数组
   * @param headers 表头配置，key为字段名，value为表头显示文字
   * @param fileName 下载的文件名（包括.xlsx后缀）
   */
  public generateAndDownloadExcel(data: Record<string, any>[], fileName: string): void {
    const buffer = this.generateExcel(data);

    // 创建 Blob 对象
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
