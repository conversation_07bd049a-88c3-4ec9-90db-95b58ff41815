import React, { memo, useMemo } from 'react';

type MediaBoxType<Props extends object = {}, Data extends object = {}> = {
  x: number;
  y: number;
  w: number;
  h: number;
  component: React.ComponentType<Props & { data: Data; width: number; height: number }>;
  componentProps?: Props;
  data: Data;
};

const MediaBox: React.FC<MediaBoxType> = ({ x, y, w, h, component: Component, componentProps, data }) => {
  const style = useMemo<React.CSSProperties>(() => {
    return {
      position: 'absolute',
      top: 0,
      left: 0,
      transform: `translate3D(${x}px, ${y}px, 0)`,
      width: w,
      height: h,
    };
  }, [x, y, w, h]);

  return (
    <div style={style}>
      <Component {...componentProps} data={data} width={w} height={h} />
    </div>
  );
};

export default memo(MediaBox);
