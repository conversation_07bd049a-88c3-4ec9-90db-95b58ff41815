'use client';

import useMaterialStore from '@/store/materialStore';
import React from 'react';
import { CloudDriveManage } from '../icon/cloudDrive';
import { Button } from '../ui';

export const GlobalUploadModalStatusBarItem: React.FC = () => {
  const { uploadList, setUploadListModalOpen } = useMaterialStore();

  const globalProcessing = `${uploadList.filter((i) => i.progress === 100).length} / ${uploadList.length}`;

  if (!uploadList.length) {
    return null;
  }

  return (
    <Button
      className="mr-3 flex h-8 items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 px-4 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
      variant="secondary"
      onClick={() => {
        setUploadListModalOpen(true);
      }}
    >
      <CloudDriveManage />
      <div className="ml-2 cursor-pointer">正在上传 ({globalProcessing})</div>
    </Button>
  );
};
