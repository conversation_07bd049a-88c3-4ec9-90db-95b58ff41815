import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form';
import { TreeSelect } from '@/components/ui/TreeSelect';
import { Control } from 'react-hook-form';

interface FormTreeSelectProps {
  name: string;
  control: Control<any>;
  label?: string;
  options: any[];
  placeholder?: string;
  disabled?: boolean;
  maxCount?: number;
  rules?: any;
  defaultValue?: any[];
  layout?: 'default' | 'horizontal';
  test: boolean;
}

export function FormTreeSelect({
  name,
  control,
  label,
  options,
  placeholder = '请选择',
  disabled = false,
  maxCount = 1,
  rules,
  defaultValue,
  layout = 'default',
  test,
}: FormTreeSelectProps) {
  return (
    <FormField
      control={control}
      name={name}
      rules={rules}
      render={({ field }) => (
        // <FormItem className="mt-2 items-center">
        //   {label && <FormLabel className="text-xs text-white">{label}</FormLabel>}
        <FormItem className={layout === 'horizontal' ? 'mt-6 flex items-center' : 'mt-4 flex items-center'}>
          {label && (
            <FormLabel className={layout === 'horizontal' ? 'w-1/5 text-sm text-white' : 'mr-2 text-sm text-white'}>
              {label}
              {rules?.required && <span className="ml-2 text-red-500">*</span>}
            </FormLabel>
          )}
          <FormControl className={layout === 'horizontal' ? 'w-4/5' : 'w-full'}>
            <TreeSelect
              options={options}
              onValueChange={(values) => {
                field.onChange(values);
              }}
              value={field.value || defaultValue}
              defaultValue={defaultValue}
              placeholder={placeholder}
              className="w-full"
              maxCount={maxCount}
              disabled={disabled}
              test={test}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
