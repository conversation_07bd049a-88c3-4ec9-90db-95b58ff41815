'use server';

import { ActionContext, server } from '@roasmax/serve';
import { Prisma } from '@roasmax/database';

/**
 * 获取AI视频生成任务列表
 */
export const listAiVideoGenerationTasks = server(
  '获取AI视频生成任务列表',
  async (
    ctx: ActionContext<{
      pagination?: { page: number; limit: number };
      filters?: {
        name?: string;
        status?: string;
        video_type?: string;
        tmp_created_at?: string;
      };
    }>,
  ) => {
    const { pagination = { page: 1, limit: 20 }, filters } = ctx.data;

    const where: Prisma.ai_video_generation_tasksWhereInput = {
      tenant_id: ctx.tenant.id,
      tmp_deleted_at: null,
      ...(filters?.name ? { name: { contains: filters.name } } : {}),
      ...(filters?.status ? { status: filters.status } : {}),
      ...(filters?.video_type ? { video_type: filters.video_type } : {}),
      ...(filters?.tmp_created_at ? { tmp_created_at: { gte: new Date(filters.tmp_created_at) } } : {}),
    };

    const [total, tasks] = await Promise.all([
      ctx.db.ai_video_generation_tasks.count({ where }),
      ctx.db.ai_video_generation_tasks.findMany({
        where,
        take: pagination.limit,
        skip: (pagination.page - 1) * pagination.limit,
        orderBy: { tmp_created_at: 'desc' },
      }),
    ]);

    return {
      list: tasks,
      pagination: {
        current: pagination.page,
        pageSize: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
      },
    };
  },
);

/**
 * 获取AI视频生成任务详情
 */
export const getAiVideoGenerationTaskById = server(
  '获取AI视频生成任务详情',
  async (ctx: ActionContext<{ taskId: string }>) => {
    const task = await ctx.db.ai_video_generation_tasks.findFirst({
      where: {
        id: ctx.data.taskId,
        tenant_id: ctx.tenant.id,
        tmp_deleted_at: null,
      },
    });

    if (!task) {
      throw new Error('任务不存在');
    }

    return task;
  },
);

/**
 * 创建AI视频生成任务
 */
export const createAiVideoGenerationTask = server(
  '创建AI视频生成任务',
  async (
    ctx: ActionContext<{
      name: string;
      prompt: string;
      video_type: string;
      reference_images?: string[];
      external_task_id?: string;
    }>,
  ) => {
    const { name, prompt, video_type, reference_images, external_task_id } = ctx.data;

    const task = await ctx.db.ai_video_generation_tasks.create({
      data: {
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        name,
        prompt,
        video_type,
        reference_images: reference_images || [],
        external_task_id,
        status: 'pending',
        progress: 0,
      },
    });

    return task;
  },
);

/**
 * 更新AI视频生成任务
 */
export const updateAiVideoGenerationTask = server(
  '更新AI视频生成任务',
  async (
    ctx: ActionContext<{
      taskId: string;
      data: {
        external_task_id?: string;
        status?: string;
        status_desc?: string;
        progress?: number;
        video_url?: string;
        thumbnail_url?: string;
        error_message?: string;
        completed_at?: Date;
        properties?: any;
      };
    }>,
  ) => {
    const { taskId, data } = ctx.data;

    const task = await ctx.db.ai_video_generation_tasks.findFirst({
      where: {
        id: taskId,
        tenant_id: ctx.tenant.id,
        tmp_deleted_at: null,
      },
    });

    if (!task) {
      throw new Error('任务不存在');
    }

    const updatedTask = await ctx.db.ai_video_generation_tasks.update({
      where: { id: taskId },
      data: {
        ...data,
        tmp_updated_at: new Date(),
      },
    });

    return updatedTask;
  },
);

/**
 * 根据外部任务ID查询任务
 */
export const getAiVideoGenerationTaskByExternalId = server(
  '根据外部任务ID查询任务',
  async (ctx: ActionContext<{ externalTaskId: string }>) => {
    const task = await ctx.db.ai_video_generation_tasks.findFirst({
      where: {
        external_task_id: ctx.data.externalTaskId,
        tenant_id: ctx.tenant.id,
        tmp_deleted_at: null,
      },
    });

    return task;
  },
);

/**
 * 删除AI视频生成任务（软删除）
 */
export const deleteAiVideoGenerationTask = server(
  '删除AI视频生成任务',
  async (ctx: ActionContext<{ taskId: string }>) => {
    const task = await ctx.db.ai_video_generation_tasks.findFirst({
      where: {
        id: ctx.data.taskId,
        tenant_id: ctx.tenant.id,
        tmp_deleted_at: null,
      },
    });

    if (!task) {
      throw new Error('任务不存在');
    }

    const deletedTask = await ctx.db.ai_video_generation_tasks.update({
      where: { id: ctx.data.taskId },
      data: {
        tmp_deleted_at: new Date(),
        tmp_updated_at: new Date(),
      },
    });

    return deletedTask;
  },
);

/**
 * 获取任务统计信息
 */
export const getAiVideoGenerationTaskStats = server('获取AI视频生成任务统计信息', async (ctx: ActionContext<{}>) => {
  const where = {
    tenant_id: ctx.tenant.id,
    tmp_deleted_at: null,
  };

  const [total, pending, processing, completed, failed] = await Promise.all([
    ctx.db.ai_video_generation_tasks.count({ where }),
    ctx.db.ai_video_generation_tasks.count({ where: { ...where, status: 'pending' } }),
    ctx.db.ai_video_generation_tasks.count({ where: { ...where, status: 'processing' } }),
    ctx.db.ai_video_generation_tasks.count({ where: { ...where, status: 'completed' } }),
    ctx.db.ai_video_generation_tasks.count({ where: { ...where, status: 'failed' } }),
  ]);

  return {
    total,
    pending,
    processing,
    completed,
    failed,
  };
});

// API Base URL
const API_BASE_URL = 'https://bowongai--text2video-api-dev-fastapi-app.modal.run';

// 查询任务状态的server action
export async function queryVideoGenerationTaskStatus(taskId: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/task/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;

    let status = 'processing';
    let progress = 50;
    let videoUrl = '';
    let thumbnailUrl = '';
    let errorMessage = '';

    // 处理最新的返回格式: {status: "completed", data: {video_url: "...", image_url: "..."}}
    if (
      (parsedResult.status === 'completed' || parsedResult.status === 'failed') &&
      parsedResult.data &&
      typeof parsedResult.data === 'object'
    ) {
      if (parsedResult.status === 'completed') {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data.video_url;
        thumbnailUrl = parsedResult.data.image_url;
      } else {
        status = 'failed';
        progress = 0;
        errorMessage = parsedResult.data.error || parsedResult.message || '任务失败';
      }
    }
    // 处理新的返回格式
    else if (parsedResult.status === true && parsedResult.task_status === 'completed' && parsedResult.data) {
      status = 'completed';
      progress = 100;
      videoUrl = parsedResult.data.generated_video_url || parsedResult.data.result_url;
      thumbnailUrl = parsedResult.data.generated_image_url;
    }
    // 检查新的完整响应格式
    else if (parsedResult.result) {
      const resultStatus = parsedResult.result.status;

      if (resultStatus === 'failed') {
        status = 'failed';
        progress = 0;
        errorMessage = parsedResult.result.error || parsedResult.message;
      } else if (resultStatus === 'completed' && parsedResult.result.video_url) {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.result.video_url;
        thumbnailUrl = parsedResult.result.thumbnail_url || '';
      }
    }
    // 检查旧的API响应格式
    else if (parsedResult.status === true && parsedResult.data) {
      if (typeof parsedResult.data === 'string' && parsedResult.data.startsWith('http')) {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data;
      } else if (typeof parsedResult.data === 'object') {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data.video_url || parsedResult.data.videoUrl;
        thumbnailUrl = parsedResult.data.thumbnail_url || parsedResult.data.thumbnailUrl || '';
      }
    }

    return {
      taskId,
      status,
      progress,
      videoUrl,
      thumbnailUrl,
      errorMessage,
      apiResponse: parsedResult,
    };
  } catch (error) {
    console.error('查询任务失败:', error);

    throw new Error(`查询任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}
