import { CampaignSheet } from '@/components/AdsTikTok/Sheet/CampaignSheet';
import SelectAccount from '@/components/AdsTikTok/selectTable/selectAccount/Select';
import SelectStatus from '@/components/AdsTikTok/selectTable/selectAccount/Selectstu';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';

import Pagination from '@/components/ui/PaginationAcc';
import { useAdvertisers } from '@/hooks/useAdvertisers';
import { useCampaignList } from '@/hooks/useCampaignList';
import { useCheckboxLogic } from '@/hooks/useCheckboxLogic';
import { usePagination } from '@/hooks/usePagination';
import { useTableLogic } from '@/hooks/useTableLogic';
import { useAdActions, useCampaignModal, useCurrentAdvertiser, useCurrentCampaign } from '@/store/ads/adStore';
import { CampaignItem } from '@/types/ads';
import debounce from 'lodash/debounce';
import { Plus, RefreshCw, XIcon } from 'lucide-react';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Switch,
  Input,
  Button,
  Checkbox,
} from '@/components/ui';
import { getPromotionSecondaryStatusText } from '@/types/enum';
import { confirm } from '@/components/ConfirmDialog';

export default function CampaignContent() {
  const { setCurrentCampaign, setCurrentView, setCampaignModal, setCurrentAdvertiser } = useAdActions();
  const currentCampaign = useCurrentCampaign();
  const campaignModal = useCampaignModal();
  const { campaigns, isLoading, refresh, mutate } = useCampaignList(1, 10, { withDateRange: true });
  const currentAdvertiser = useCurrentAdvertiser();
  const { createCampaign, updateCampaign, updateCampaignStatus, syncCampaigns } = useAdActions();
  const { selectedRows, sortState, sortedData, handleSelectAll, handleSelect, handleSort } = useTableLogic(
    campaigns?.list || [],
    'id',
    currentCampaign?.map((item) => item.id) || [],
  );
  const { advertisers, isLoading: advertiserLoading } = useAdvertisers(1, 999);
  const [searchTagText, setSearchTagText] = useState('');
  const [updatingRows, setUpdatingRows] = useState<string[]>([]);
  const [operationStatus, setOperationStatus] = useState<string>('All');
  const { currentPage, pageSize, handlePaginationChange } = usePagination({
    totalItems: campaigns?.total || 0,
    initialPageSize: 10,
    initialPage: 1,
    onPaginationChange: (newPage, newPageSize) => {
      mutate({
        newPage,
        newPageSize,
        total: campaigns?.total || 0,
      });
    },
  });

  const { handleCheckboxChange, handleAllCheckboxChange } = useCheckboxLogic({
    selectedRows,
    sortedData,
    currentItems: currentCampaign || [],
    setCurrentItems: (items) => {
      const successItems = items.filter((item) => item.pubStatus === 'SUCCESS');
      setCurrentCampaign(successItems);
    },
    itemKey: 'id',
    handleSelect,
    handleSelectAll,
  });
  const columns = [
    {
      key: 'checkbox',
      title: (
        <Checkbox
          className="h-[14px] w-[14px] border-[#9FA4B2]"
          checked={selectedRows.length === sortedData.length}
          onCheckedChange={(checked) => handleAllCheckboxChange(!!checked)}
        />
      ),
      width: 50,
    },
    { key: 'switch', title: '开关', width: 60, fixed: 'left' as 'left' },
    {
      key: 'campaignName',
      title: '广告系列',
      width: 230,
      fixed: 'left' as 'left',
      tooltip: {
        enable: true,
        contentKey: 'campaignName',
        placement: 'right' as const,
      },
    },
    {
      key: 'accountName',
      title: '广告账户',
      width: 230,
      tooltip: {
        enable: true,
        contentKey: 'advertiserName',
        placement: 'right' as const,
      },
    },
    { key: 'actions', title: '操作', width: 150, fixed: 'left' as 'left' },
    { key: 'pubStatus', title: '创建状态', width: 100 },
    { key: 'operationStatus', title: '投放状态', width: 120, fixed: 'left' as 'left' },
    { key: 'budget', title: '预算', width: 80, sortable: true },
    { key: 'spend', title: '消耗', width: 80, sortable: true },
    { key: 'cpc', title: '平均点击成本(CPC)', width: 120, sortable: true },
    { key: 'cpm', title: '千次展示成本(CPM)', width: 120, sortable: true },
    { key: 'impressions', title: '展示量', width: 85, sortable: true },
    { key: 'clicks', title: '点击量', width: 80, sortable: true },
    { key: 'purchases', title: '付费数', width: 80, sortable: true },
    { key: 'onsiteShoppingRate', title: '点击转化率', width: 80, sortable: true },
    { key: 'onsiteShoppingRoas', title: 'ROAS', width: 80, sortable: true },
    { key: 'totalOnsiteShoppingValue', title: '总收入', width: 90, sortable: true },
    { key: 'onsiteOnWebCart', title: '加入购物车次数', width: 90, sortable: true },
    { key: 'orderValue', title: '平均订单价值', width: 120, sortable: true },
  ];
  const handleAdCampaignsClick = (campaign: CampaignItem) => {
    if (campaign.pubStatus !== 'SUCCESS') return;
    setCurrentView('group');
    setCurrentCampaign([campaign]);
  };
  const renderCell = (key: string, record: any) => {
    const isUpdating = updatingRows.includes(record.campaignId);

    switch (key) {
      case 'checkbox':
        return (
          <Checkbox
            className="h-[14px] w-[14px] border-[#9FA4B2]"
            checked={selectedRows.includes(record.id)}
            onCheckedChange={(checked) => handleCheckboxChange(record, !!checked)}
          />
        );
      case 'switch':
        return (
          <div className="w-[60px]">
            <Switch
              className="h-[16px] w-[28px]"
              thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
              checked={record?.operationStatus === 'ENABLE'}
              disabled={record?.pubStatus !== 'SUCCESS' || isUpdating}
              onCheckedChange={(checked) => handleSwitchChange(record, checked)}
            />
          </div>
        );
      case 'campaignName':
        return (
          <div
            className={`cursor-pointer ${
              isUpdating || record?.pubStatus !== 'SUCCESS' ? 'pointer-events-none opacity-50' : ''
            }`}
            onClick={() => handleAdCampaignsClick(record)}
          >
            {record.campaignName}
            <div className="mt-[2px] text-xs text-gray-500">{record.campaignId}</div>
          </div>
        );
      case 'actions':
        return (
          <div className="flex w-full items-center gap-6">
            <Button
              variant="link"
              size="sm"
              disabled={isUpdating || record.pubStatus === 'CREATING' || !record?.campaignId}
              className="px-0 text-sm text-[#00E1FF] hover:no-underline disabled:opacity-50"
              onClick={() => handleEditClick(record)}
            >
              编辑
            </Button>
            <Button
              variant="link"
              size="sm"
              disabled={isUpdating}
              className="px-0 text-sm text-[#00E1FF] hover:no-underline disabled:opacity-50"
              onClick={() => {
                confirm({
                  content: (
                    <div className="space-y-2">
                      <div className="text-base font-medium">确认删除该广告系列?</div>
                      <div className="text-sm text-gray-400">删除后将无法恢复，请谨慎操作</div>
                    </div>
                  ),
                  onConfirm: async () => {
                    try {
                      setUpdatingRows((prev) => [...prev, record?.campaignId]);
                      await updateCampaignStatus({
                        ids: [Number(record.id)],
                        ...(record.advertiserId && { advertiserId: record.advertiserId }),
                        ...(record.campaignId && { campaignIds: [record.campaignId] }),
                        operationStatus: 'DELETE',
                      });
                      const newCampaigns =
                        currentCampaign?.filter((item) => item.campaignId !== record.campaignId) || [];
                      setCurrentCampaign(newCampaigns);
                    } catch (error) {
                      console.error('删除广告系列失败:', error);
                    } finally {
                      setUpdatingRows((prev) => prev.filter((id) => id !== record.campaignId));
                    }
                  },
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      case 'accountName':
        const advertiserName =
          advertisers?.list.find((item) => item.advertiserId === record?.advertiserId)?.advertiserName ??
          record?.advertiserId;
        return (
          <div>
            {advertiserName}
            <div className="mt-[2px] text-xs text-gray-500">{record?.advertiserId}</div>
          </div>
        );
      case 'pubStatus':
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 flex-shrink-0 rounded-full ${
                record?.pubStatus === 'SUCCESS'
                  ? 'bg-green-500'
                  : record?.pubStatus === 'FAIL'
                    ? 'bg-red-500'
                    : 'bg-orange-500'
              }`}
            />
            <div className="flex flex-col items-start overflow-hidden">
              {record?.pubStatus === 'SUCCESS' ? '成功' : record?.pubStatus === 'FAIL' ? '失败' : '创建中'}
              {record?.message && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="mt-[2px] block truncate text-xs text-gray-500">{record?.message}</span>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{record?.message}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        );
      case 'operationStatus':
        const isEnabled = record?.operationStatus === 'ENABLE';
        const DISABLE = record?.operationStatus === 'DISABLE';
        const FROZEN = record?.operationStatus === 'FROZEN';
        return (
          <div className="flex flex-col">
            <div className={`text-sm ${isEnabled ? 'text-green-500' : DISABLE ? 'text-[#F3A93C]' : 'text-gray-500'}`}>
              {isEnabled ? '开启' : DISABLE ? '已暂停' : FROZEN ? '已冻结' : '未知状态'}
            </div>
            {record?.secondaryStatus && (
              <div className="mt-[2px]">
                <div className="text-xs text-[#9FA4B2]">{getPromotionSecondaryStatusText(record?.secondaryStatus)}</div>
              </div>
            )}
          </div>
        );
      case 'budget':
        return record?.jsonDate?.budget;
      case 'spend':
        return record?.metricsResult.spend;
      case 'cpc':
        return record?.metricsResult.cpc;
      case 'cpm':
        return record?.metricsResult.cpm;
      case 'impressions':
        return record?.metricsResult.impressions;
      case 'clicks':
        return record?.metricsResult.clicks;
      case 'purchases':
        return record?.metricsResult.onsiteShopping;
      case 'onsiteShoppingRate':
        return record?.metricsResult.onsiteShoppingRate;
      case 'onsiteShoppingRoas':
        return record?.metricsResult.onsiteShoppingRoas;
      case 'totalOnsiteShoppingValue':
        return record?.metricsResult.totalOnsiteShoppingValue;
      case 'onsiteOnWebCart':
        return record?.metricsResult.onsiteOnWebCart;
      case 'orderValue':
        return record?.metricsResult.valuePerOnsiteShopping;
      default:
        return '';
    }
  };
  const handleCreateClick = () => {
    setCampaignModal({
      show: true,
      type: 'create',
      title: '新建广告系列',
      formValue: null,
    });
  };

  const handleEditClick = (record: CampaignItem) => {
    setCampaignModal({
      show: true,
      type: 'edit',
      title: '编辑广告系列',
      formValue: record,
    });
  };
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      mutate({
        newPage: 1,
        newPageSize: pageSize,
        total: campaigns?.total || 0,
        newCampaignName: value,
        operationStatus: operationStatus,
      });
    }, 1000),
    [operationStatus, pageSize, campaigns?.total],
  );
  const onSearch = (value: string) => {
    setSearchTagText(value);
    debouncedSearch(value);
  };
  const handleSubmit = async (values: any) => {
    try {
      if (campaignModal.type === 'create') {
        await createCampaign(values);
      } else {
        await updateCampaign(values);
      }
      setCampaignModal({ show: false });
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  const handleSwitchChange = async (record: CampaignItem, checked: boolean) => {
    if (!currentAdvertiser) return;

    try {
      setUpdatingRows((prev) => [...prev, record.campaignId]);

      await updateCampaignStatus({
        ids: [Number(record.id)],
        advertiserId: currentAdvertiser[0]?.advertiserId ?? '',
        operationStatus: checked ? 'ENABLE' : 'DISABLE',
      });
    } finally {
      setUpdatingRows((prev) => prev.filter((id) => id !== record.campaignId));
    }
  };

  const handleSync = async () => {
    if (!currentAdvertiser) {
      toast.error('请先选择广告账户');
      return;
    }
    await syncCampaigns();
  };

  const handleAdvertiserChange = (value: string[]) => {
    const newAdvertisers = advertisers?.list?.filter((item) => value.includes(item.advertiserId));
    setCurrentAdvertiser(newAdvertisers || []);
    mutate({
      newPage: 1,
      newPageSize: pageSize,
      total: campaigns?.total || 0,
      newCampaignName: searchTagText,
      operationStatus: operationStatus,
    });
  };
  const onOperationStatusChange = (value: string) => {
    setOperationStatus(value);
    mutate({
      newPage: 1,
      newPageSize: pageSize,
      total: campaigns?.total || 0,
      newCampaignName: searchTagText,
      operationStatus: value,
    });
  };
  const handleRefresh = () => {
    refresh();
  };

  return (
    <div className="mt-6">
      <div className="flex items-center">
        {advertisers?.list?.length ? (
          <SelectAccount
            onValueChange={handleAdvertiserChange}
            currentAdvertiser={currentAdvertiser?.map((item) => item.advertiserId) || []}
            advertisers={
              advertisers?.list.map((item) => ({
                label: item.advertiserName,
                value: item.advertiserId,
              })) || []
            }
            placeholderTitle="广告账户"
          />
        ) : (
          advertiserLoading && <div className="mr-4 h-8 w-[200px] animate-pulse rounded-md bg-gray-700/50" />
        )}
        <SelectStatus onChange={(value) => onOperationStatusChange(value)} />
        <div className="flex h-8 w-[240px] items-center rounded border border-[#1C2A3F] px-3 text-[12px] text-white">
          <div className="w-24 border-r border-gray-700 text-[12px] text-white">广告系列</div>
          <Input
            value={searchTagText}
            placeholder="请输入名称"
            className="h-full border-0 bg-transparent placeholder:text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
            onChange={(e) => onSearch?.(e.target.value)}
          />
          {searchTagText && (
            <XIcon
              className="mx-2 h-8 cursor-pointer text-[#9FA4B2]"
              onClick={(event) => {
                event.stopPropagation();
                setSearchTagText('');
                debouncedSearch('');
              }}
            />
          )}
        </div>
      </div>
      <div className="mt-6 flex items-center">
        <Button
          className="h-8 rounded px-4 text-xs font-medium text-[#050A1C]"
          onClick={() => {
            if (!currentAdvertiser || !currentAdvertiser.length) {
              toast.error('请先选择广告账户');
              return;
            }
            handleCreateClick();
          }}
        >
          <Plus className="mr-1.5 h-4 w-4 flex-shrink-0" />
          新建广告系列
        </Button>
        <Button
          variant="outline"
          className="ml-4 h-8 rounded border-[#363D54] bg-transparent text-xs"
          onClick={handleRefresh}
        >
          <RefreshCw className="mr-1.5 h-4 w-4" />
          获取实时数据
        </Button>
      </div>
      <CommonTable
        columns={columns}
        dataSource={sortedData}
        selectedRows={selectedRows}
        sortState={sortState}
        rowKey="campaignId"
        onSort={handleSort}
        renderCell={renderCell}
        loading={isLoading}
      />
      <div className="flex justify-end">
        <Pagination
          totalItems={campaigns?.total || 0}
          currentPage={currentPage}
          pageSize={pageSize}
          handlePaginationChange={handlePaginationChange}
        />
      </div>

      <CampaignSheet onSubmit={handleSubmit} />
    </div>
  );
}
