export const MenuItemIcon = () => {
  return (
    <svg width="70" height="47" viewBox="0 0 70 47" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.2" filter="url(#filter0_f_4192_663)">
        <rect x="12" y="12" width="46" height="23" rx="11.5" fill="#00E1FF" />
      </g>
      <defs>
        <filter
          id="filter0_f_4192_663"
          x="0"
          y="0"
          width="70"
          height="47"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_4192_663" />
        </filter>
      </defs>
    </svg>
  );
};
