import React, { useRef, ChangeEventHand<PERSON> } from 'react';
import { Input } from '@/components/ui';

interface FilePickerProps {
  disabled?: boolean;
  children: React.ReactElement;
  onChange?: ChangeEventHandler<HTMLInputElement>;
}

export const FilePicker: React.FC<FilePickerProps> = (props) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadClick: React.MouseEventHandler<HTMLElement> = () => {
    if (props.disabled) return;
    fileInputRef.current?.click();
  };

  const children = React.Children.map(props.children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as any, { onClick: handleUploadClick });
    }
    return child;
  });

  return (
    <>
      {children}
      <Input
        className="hidden"
        id="bwai-material-upload"
        multiple
        type="file"
        accept=".mp4"
        onChange={props.onChange}
        ref={fileInputRef}
      />
    </>
  );
};
