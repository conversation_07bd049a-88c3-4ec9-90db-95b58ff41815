import { useState, useCallback } from 'react';

interface UsePaginationProps {
  // 初始总条目数
  totalItems?: number;
  // 初始每页条目数
  initialPageSize?: number;
  // 初始当前页码
  initialPage?: number;
  // 页码/每页条目数改变时的回调函数
  onPaginationChange?: (page: number, pageSize: number) => void;
}

interface UsePaginationReturn {
  // 当前页码
  currentPage: number;
  // 每页条目数
  pageSize: number;
  // 总条目数
  totalItems: number;
  // 总页数
  totalPages: number;
  // 设置当前页码
  setCurrentPage: (page: number) => void;
  // 设置每页条目数
  setPageSize: (size: number) => void;
  // 设置总条目数
  setTotalItems: (total: number) => void;
  // 处理页码变化
  handlePaginationChange: (page: number, pageSize: number) => void;
  // 更新分页信息
  updatePagination: (pageInfo: { newPage?: number; newPageSize?: number; total: number }) => void;
  // 获取当前页的起始索引
  startIndex: number;
  // 获取当前页的结束索引
  endIndex: number;
}

export function usePagination({
  totalItems = 0,
  initialPageSize = 10,
  initialPage = 1,
  onPaginationChange,
}: UsePaginationProps = {}): UsePaginationReturn {
  // 状态管理
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [total, setTotalItems] = useState(totalItems);

  // 计算总页数
  const totalPages = Math.ceil(total / pageSize);

  // 处理页码变化
  const handlePaginationChange = useCallback(
    (page: number, currentPageSize: number) => {
      // 如果 pageSize 发生变化，重置页码到第一页
      if (currentPageSize !== pageSize) {
        setCurrentPage(1);
        setPageSize(currentPageSize);
        onPaginationChange?.(1, currentPageSize);
      } else {
        setCurrentPage(page);
        setPageSize(currentPageSize);
        onPaginationChange?.(page, currentPageSize);
      }
    },
    [pageSize, onPaginationChange],
  );

  const updatePagination = useCallback(
    (pageInfo: { newPage?: number; newPageSize?: number; total: number }) => {
      setCurrentPage(pageInfo.newPage ?? currentPage);
      setPageSize(pageInfo.newPageSize ?? pageSize);
      setTotalItems(pageInfo.total ?? total);
    },
    [currentPage, pageSize, total],
  );

  // 计算当前页的数据范围
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, total);

  return {
    currentPage,
    pageSize,
    totalItems: total,
    totalPages,
    setCurrentPage,
    setPageSize,
    setTotalItems,
    handlePaginationChange,
    updatePagination,
    startIndex,
    endIndex,
  };
}
