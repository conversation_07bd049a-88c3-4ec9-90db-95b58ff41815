import { useEffect, useState } from 'react';

const ImgCrash = '';

// 图片加载方法，加载失败时会自动重新加载，3次均失败后执行自定义的handleError
async function loadImg(img: HTMLImageElement, src: string, handleError: () => void, reloadCount: number = 3) {
  if (reloadCount > 0) {
    img.onerror = function () {
      // 如果获取失败，下次会等待更长时间再次尝试
      setTimeout(
        () => {
          loadImg(img, src, handleError, reloadCount - 1);
        },
        300 * (4 - reloadCount),
      );
    };

    img.src = src;
  } else {
    handleError();
    img.onerror = null;
    img.src = ImgCrash;
  }
}

const useImageLoad = (
  originSrc?: string,
  width?: number,
): [
  (
    | {
        src: string;
        width: number;
        height: number;
        isError: boolean;
      }
    | undefined
  ),
  boolean,
] => {
  const [loading, setLoading] = useState(true);
  const [imgMeta, setImgMeta] = useState<{
    src: string;
    width: number;
    height: number;
    isError: boolean;
  }>();

  useEffect(() => {
    if (!originSrc)
      return () => {
        setLoading(false);
      };

    const img = new Image();

    const url = originSrc;

    function handleError() {
      setImgMeta({
        src: '',
        width: 0,
        height: 0,
        isError: true,
      });
      setLoading(false);
    }

    function handleLoad() {
      setImgMeta({
        src: originSrc ?? '',
        width: img.width,
        height: img.height,
        isError: false,
      });
      setLoading(false);
      img.onerror = null;
    }

    loadImg(img, url, handleError, 3);
    img.onload = handleLoad;

    return () => {
      img.src = '';
      img.onerror = null;
      setLoading(false);
    };
  }, [originSrc, width]);

  return [imgMeta, loading];
};

export { useImageLoad };
