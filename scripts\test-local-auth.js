#!/usr/bin/env node

/**
 * 本地认证适配器测试脚本
 * 验证本地认证系统的基本功能
 */

const { PrismaClient } = require('@roasmax/database');
const crypto = require('crypto');

// 模拟导入（实际使用时需要编译 TypeScript）
async function testLocalAuth() {
  console.log('🚀 开始测试本地认证适配器...\n');

  const prisma = new PrismaClient();

  try {
    // 1. 测试数据库连接
    console.log('1. 测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 2. 创建测试用户
    console.log('2. 创建测试用户...');
    const testUserId = crypto.randomUUID();
    const testEmail = '<EMAIL>';
    
    // 检查用户是否已存在
    const existingUser = await prisma.members.findFirst({
      where: { email: testEmail }
    });

    if (existingUser) {
      console.log('⚠️ 测试用户已存在，跳过创建');
    } else {
      await prisma.members.create({
        data: {
          id: crypto.randomUUID(),
          tenant_id: 'default',
          user_id: testUserId,
          user_status: 'Activated',
          nickname: 'Test User',
          email: testEmail,
          account: testEmail,
          admin: 0,
          password: 'test123456', // 临时密码，实际使用时会被哈希
          password_hash: null,
          salt: null,
          is_migrated: false,
          password_reset_required: true,
          authing_user_id: null,
          last_login_at: null,
          login_attempts: 0,
          locked_until: null,
          email_verified: true,
          phone: null,
          tmp_created_at: new Date(),
          tmp_updated_at: new Date(),
        }
      });
      console.log('✅ 测试用户创建成功');
    }

    // 3. 测试角色和权限数据
    console.log('\n3. 验证角色和权限数据...');
    
    const roles = await prisma.roles.findMany({
      where: { tenant_id: 'default' }
    });
    console.log(`✅ 找到 ${roles.length} 个默认角色:`, roles.map(r => r.code).join(', '));

    const permissions = await prisma.permissions.findMany({
      where: { tenant_id: 'default' }
    });
    console.log(`✅ 找到 ${permissions.length} 个默认权限`);

    // 4. 测试用户会话表
    console.log('\n4. 测试用户会话表结构...');
    const sessionCount = await prisma.user_sessions.count();
    console.log(`✅ 用户会话表可用，当前有 ${sessionCount} 条记录`);

    // 5. 测试用户角色关联
    console.log('\n5. 测试用户角色关联...');
    
    // 为测试用户分配一个角色
    const userRole = await prisma.user_roles.findFirst({
      where: {
        user_id: existingUser?.user_id || testUserId,
        tenant_id: 'default'
      }
    });

    if (!userRole) {
      await prisma.user_roles.create({
        data: {
          id: crypto.randomUUID(),
          tenant_id: 'default',
          user_id: existingUser?.user_id || testUserId,
          role_code: 'user',
          assigned_by: 'system',
          assigned_at: new Date(),
          expires_at: null,
          created_at: new Date(),
        }
      });
      console.log('✅ 为测试用户分配了默认角色');
    } else {
      console.log('✅ 测试用户已有角色分配');
    }

    // 6. 测试数据完整性
    console.log('\n6. 测试数据完整性...');
    
    // 检查外键关系
    const userWithRoles = await prisma.members.findFirst({
      where: { user_id: existingUser?.user_id || testUserId },
      include: {
        user_roles: {
          include: {
            roles: {
              include: {
                permissions: true
              }
            }
          }
        }
      }
    });

    if (userWithRoles && userWithRoles.user_roles.length > 0) {
      console.log('✅ 用户角色关联正常');
      console.log(`✅ 用户权限数量: ${userWithRoles.user_roles[0].roles?.permissions?.length || 0}`);
    } else {
      console.log('⚠️ 用户角色关联可能有问题');
    }

    // 7. 测试索引性能
    console.log('\n7. 测试索引性能...');
    
    const start = Date.now();
    await prisma.members.findFirst({
      where: {
        email: testEmail,
        tenant_id: 'default'
      }
    });
    const queryTime = Date.now() - start;
    
    if (queryTime < 100) {
      console.log(`✅ 查询性能良好: ${queryTime}ms`);
    } else {
      console.log(`⚠️ 查询性能可能需要优化: ${queryTime}ms`);
    }

    // 8. 环境变量检查
    console.log('\n8. 检查环境变量配置...');
    
    const requiredEnvVars = [
      'DATABASE_URL',
      'JWT_SECRET',
      'APPSECRET'
    ];

    let envVarsOk = true;
    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar} 已配置`);
      } else {
        console.log(`❌ ${envVar} 未配置`);
        envVarsOk = false;
      }
    }

    if (envVarsOk) {
      console.log('✅ 所有必需的环境变量都已配置');
    } else {
      console.log('⚠️ 部分环境变量未配置，可能影响功能');
    }

    console.log('\n🎉 本地认证适配器测试完成！');
    console.log('\n📋 测试总结:');
    console.log('- ✅ 数据库连接正常');
    console.log('- ✅ 用户表结构完整');
    console.log('- ✅ 角色权限系统就绪');
    console.log('- ✅ 会话管理表可用');
    console.log('- ✅ 数据关联正常');
    
    console.log('\n🔧 下一步操作:');
    console.log('1. 更新插件配置以使用本地认证适配器');
    console.log('2. 测试登录和用户管理功能');
    console.log('3. 执行用户数据迁移');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testLocalAuth().catch(console.error);
}

module.exports = { testLocalAuth };
