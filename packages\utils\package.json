{"name": "@roasmax/utils", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"browser": "./dist/browser.mjs", "node": "./dist/index.js", "default": "./dist/browser.mjs"}, "./feishu": {"browser": "./dist/feishu/browser.mjs", "node": "./dist/feishu/index.js", "default": "./dist/feishu/browser.mjs"}, "./langfuse": {"browser": "./dist/langfuse/browser.mjs", "node": "./dist/langfuse/index.js", "default": "./dist/langfuse/browser.mjs"}, "./net": {"browser": "./dist/net/browser.mjs", "node": "./dist/net/index.js", "default": "./dist/net/browser.mjs"}, "./tencentcloud": {"browser": "./dist/tencentcloud/browser.mjs", "node": "./dist/tencentcloud/index.js", "default": "./dist/tencentcloud/browser.mjs"}, "./llm": {"browser": "./dist/llm/browser.mjs", "node": "./dist/llm/index.js", "default": "./dist/llm/browser.mjs"}}, "files": ["dist/**"], "scripts": {"dev": "tsup --watch", "build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"axios": "^1.7.9", "chalk": "^5.3.0", "cos-nodejs-sdk-v5": "^2.14.6", "dayjs": "^1.11.13", "openai": "^4.86.1", "tencentcloud-sdk-nodejs-live": "^4.0.1000", "tencentcloud-sdk-nodejs-tmt": "^4.0.975", "tencentcloud-sdk-nodejs-vod": "^4.0.891", "vod-node-sdk": "^1.1.0"}, "devDependencies": {"@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*"}}