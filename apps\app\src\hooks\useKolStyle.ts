import { listKolStyles } from '@/services/actions/video-generation-task';
import { action, ActionResult } from '@/utils/server-action/action';
import { uniq } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

type KolStylesStore = {
  list: ActionResult<typeof listKolStyles>;
  loading: boolean;
  fetch: () => Promise<void>;
};

const kolStylesStore = create<KolStylesStore>()(
  devtools((set) => ({
    list: [],
    loading: false,
    fetch: async () => {
      set({ loading: true });
      const res = await action(listKolStyles, {});
      set({ list: res, loading: false });
    },
  })),
);

let hasFetched = false;

export const useKolStyles = () => {
  const { list = [], loading, fetch } = kolStylesStore();

  useEffect(() => {
    if (!hasFetched) {
      fetch();
      hasFetched = true;
    }
  }, [fetch]);

  const tree = useMemo(() => {
    const industries = uniq(list.map((prompt) => prompt.industry));

    return industries.map((industry) => {
      const styles = list.filter((prompt) => prompt.industry === industry);
      return {
        industry,
        styles: styles.map((style) => {
          return { kol: style.kol, category: style.category };
        }),
      };
    });
  }, [list]);

  const getByValue = useCallback(
    (v: string | undefined) => {
      return list.find((p) => p.kol === v);
    },
    [list],
  );

  return useMemo(() => ({ list: list, tree, loading, getByValue }), [list, tree, loading, getByValue]);
};
