'use client';
import React from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Footer, DialogTitle, Button } from '@/components/ui';
import { CONFIRM } from '@/common/statics/zh_cn';
import { UploadStatus } from '@/types/upload';
import useMaterialStore from '@/store/materialStore';
import { UploadingList } from '@/components/UploadingList';

export function UploadListModal() {
  const { uploadList, setUploadListModalOpen, uploadListModalOpen } = useMaterialStore();

  const onConfirm = () => {
    setUploadListModalOpen(false);
  };

  const uploading = uploadList.some((item) => item.status === UploadStatus.UPLOADING);

  return (
    <Dialog
      open={uploadListModalOpen}
      onOpenChange={(open) => {
        // if (uploading) {
        //   toast.error('请等待上传完成');
        //   return;
        // }
        setUploadListModalOpen(open);
      }}
    >
      <DialogContent className="flex h-[590px] w-[720px] max-w-[720px] flex-col justify-start gap-0 rounded-2xl border-none bg-[#151c29]">
        <DialogTitle className="mb-4 text-center text-base font-medium text-white">文件上传中</DialogTitle>
        <div className="flex h-full flex-col justify-between">
          <div className="flex flex-grow flex-col divide-y divide-[#1f2434]">
            <div className="flex max-h-[418px] flex-grow flex-col overflow-y-auto pt-5">
              <UploadingList />
            </div>
          </div>
          <DialogFooter className="flex items-center justify-end pt-4">
            <Button
              variant="default"
              type="button"
              disabled={uploading}
              className="h-8 w-[92px] rounded-lg bg-[#00e1ff] text-black"
              onClick={onConfirm}
            >
              {CONFIRM}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
