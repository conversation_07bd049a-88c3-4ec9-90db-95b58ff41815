import { confirm } from '@/components/ConfirmDialog';
import { ProField } from '@/components/pro/pro-field';
import { ProImage } from '@/components/pro/pro-image';
import ProTable from '@/components/pro/pro-table';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui';
import { autoMergeDuplicateGoods, listDuplicateGoods } from '@/services/actions/goods';
import { action, ActionResult } from '@/utils/server-action/action';
import { uniq } from 'lodash';
import React, { useImperativeHandle, useState } from 'react';
import toast from 'react-hot-toast';

export interface MergeDuplicatedGoodsDialogRef {
  open: () => void;
}
export interface MergeDuplicatedGoodsDialogProps {}

export const MergeDuplicatedGoodsDialog = React.forwardRef<
  MergeDuplicatedGoodsDialogRef,
  MergeDuplicatedGoodsDialogProps
>((props, ref) => {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<ActionResult<typeof listDuplicateGoods>>([]);
  const [onlyAbleAutoMergeData, setOnlyAbleAutoMergeData] = useState<ActionResult<typeof listDuplicateGoods>>([]);

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      action(listDuplicateGoods, {}).then((res) => {
        setData(res || []);
      });
      action(listDuplicateGoods, { onlyAbleAutoMerge: true }).then((res) => {
        setOnlyAbleAutoMergeData(res || []);
      });
    },
  }));

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-[80vw]">
        <DialogHeader>
          <DialogTitle>
            重复商品
            <Button
              variant="link"
              onClick={() => {
                confirm({
                  className: 'w-[70vw] max-w-[70vw]',
                  content: (
                    <div className="w-[calc(70vw-40px)] whitespace-pre-wrap">
                      <div className="mb-4">发现重复商品，系统将按以下规则自动合并：</div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="mb-2 font-medium text-red-500">【保留唯一】</div>
                          <div className="pl-4">
                            以下字段在最多只有一个值时，将自动合并：
                            <ul className="list-disc pl-4">
                              <li>推广链接</li>
                              <li>IP</li>
                              <li>平台</li>
                              <li>产品ID</li>
                              <li>产品链接</li>
                              <li>店铺名称</li>
                              <li>佣金率</li>
                              <li>价格</li>
                              <li>库存</li>
                            </ul>
                          </div>
                        </div>
                        <div className="mb-4">
                          <div className="mb-2 font-medium text-blue-500">【自动合并】</div>
                          <div className="pl-4">
                            以下字段将自动去重并合并：
                            <ul className="list-disc pl-4">
                              <li>商品名称</li>
                              <li>商品图片</li>
                              <li>商品标签</li>
                              <li>直播间</li>
                              <li>商品别名</li>
                              <li>自定义属性</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 font-medium text-red-500">
                        确认合并后，系统将保留一条主记录，其他重复记录将被删除。此操作不可撤销，请谨慎操作。
                      </div>
                      <ProTable
                        className="h-[30vh]"
                        columns={[
                          {
                            title: '名称',
                            key: 'name',
                            width: 200,
                            render: (_, row) => <div className="text-wrap">{row.name}</div>,
                          },
                          {
                            title: '推广链接',
                            key: 'links',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.links?.filter(Boolean)).map((link) => (
                                    <div key={link}>
                                      <a href={link} target="_blank">
                                        {link}
                                      </a>
                                    </div>
                                  ))}
                                </div>
                              );
                            },
                          },
                          {
                            title: '图片',
                            key: 'images',
                            render: (_, row) => {
                              return uniq(row.images?.filter(Boolean)).map((image) => (
                                <ProImage src={image} alt="图片" key={image} width={32} height={32} />
                              ));
                            },
                          },
                          {
                            title: '标签',
                            key: 'tags',
                            width: 150,
                            render: (_, row) => {
                              return uniq(row.tags?.filter(Boolean)).map((tag) => (
                                <Badge key={tag} variant="outline">
                                  {tag}
                                </Badge>
                              ));
                            },
                          },
                          {
                            title: '直播间',
                            key: 'live_rooms',
                            width: 150,
                            render: (_, row) => {
                              return uniq(row.live_rooms?.filter(Boolean)).map((live_room) => (
                                <Badge key={live_room} variant="outline">
                                  {live_room}
                                </Badge>
                              ));
                            },
                          },
                          {
                            title: '别名',
                            key: 'aliases',
                            render: (_, row) => {
                              return uniq(row.aliases?.filter(Boolean)).map((alias) => (
                                <Badge key={alias} variant="outline">
                                  {alias}
                                </Badge>
                              ));
                            },
                          },
                          {
                            title: 'IP',
                            key: 'ips',
                            render: (_, row) => {
                              return uniq(row.ips?.filter(Boolean)).map((ip) => (
                                <Badge key={ip} variant="outline">
                                  {ip}
                                </Badge>
                              ));
                            },
                          },
                          {
                            title: '平台',
                            key: 'platforms',
                            render: (_, row) => {
                              return uniq(row.platforms?.filter(Boolean)).map((platform) => (
                                <Badge key={platform} variant="outline">
                                  {platform}
                                </Badge>
                              ));
                            },
                          },
                          {
                            title: '产品ID',
                            key: 'product_identities',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.product_identities?.filter(Boolean)).map((product_identity) => (
                                    <div key={product_identity}>{product_identity}</div>
                                  ))}
                                </div>
                              );
                            },
                          },
                          {
                            title: '产品链接',
                            key: 'product_urls',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.product_urls?.filter(Boolean)).map((product_url) => (
                                    <div
                                      key={product_url}
                                      className="block max-w-[200px] overflow-hidden text-ellipsis"
                                    >
                                      {product_url}
                                    </div>
                                  ))}
                                </div>
                              );
                            },
                          },
                          {
                            title: '爆品',
                            key: 'hot_products',
                            render: (_, row) => {
                              return uniq(row.hot_products?.filter(Boolean)).map((hot_product) => (
                                <div key={hot_product}>{hot_product ? '爆品' : ''}</div>
                              ));
                            },
                          },
                          {
                            title: '店铺名',
                            key: 'shop_names',
                            render: (_, row) => {
                              return uniq(row.shop_names?.filter(Boolean)).map((shop_name) => (
                                <div key={shop_name}>{shop_name}</div>
                              ));
                            },
                          },
                          {
                            title: '佣金率',
                            key: 'commission_rates',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.commission_rates?.filter(Boolean)).map((commission_rate) => (
                                    <div key={commission_rate}>
                                      <ProField value={commission_rate} type="percent" />
                                    </div>
                                  ))}
                                </div>
                              );
                            },
                          },
                          {
                            title: '备注',
                            key: 'remarks',
                            render: (_, row) => {
                              return uniq(row.remarks?.filter(Boolean)).map((remark) => (
                                <div key={remark}>{remark}</div>
                              ));
                            },
                          },
                          {
                            title: '价格',
                            key: 'prices',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.prices?.filter(Boolean)).map((price) => (
                                    <div key={price}>
                                      <ProField value={price} type="currency" />
                                    </div>
                                  ))}
                                </div>
                              );
                            },
                          },
                          {
                            title: '库存',
                            key: 'stock_amounts',
                            render: (_, row) => {
                              return (
                                <div>
                                  {uniq(row.stock_amounts?.filter(Boolean)).map((stock_amount) => (
                                    <div key={stock_amount}>{stock_amount}</div>
                                  ))}
                                </div>
                              );
                            },
                          },
                        ]}
                        dataSource={onlyAbleAutoMergeData}
                        rowKey="id"
                      />
                    </div>
                  ),
                  onConfirm: async () => {
                    const data = await action(autoMergeDuplicateGoods, {});
                    if (data) {
                      toast.success(
                        `自动合并成功，${data.before} 条重复商品被自动合并，新增了 ${data.after.length} 条商品`,
                      );
                      action(listDuplicateGoods, {}).then((res) => {
                        setData(res || []);
                      });
                    }
                  },
                });
              }}
            >
              自动合并
            </Button>
          </DialogTitle>
        </DialogHeader>
        <ProTable
          className="h-[80vh]"
          columns={[
            {
              title: '名称',
              key: 'name',
              width: 200,
              render: (_, row) => <div className="text-wrap">{row.name}</div>,
            },
            {
              title: '推广链接',
              key: 'links',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.links?.filter(Boolean)).map((link) => (
                      <div key={link}>
                        <a href={link} target="_blank">
                          {link}
                        </a>
                      </div>
                    ))}
                  </div>
                );
              },
            },
            {
              title: '图片',
              key: 'images',
              render: (_, row) => {
                return uniq(row.images?.filter(Boolean)).map((image) => (
                  <ProImage src={image} alt="图片" key={image} width={32} height={32} />
                ));
              },
            },
            {
              title: '标签',
              key: 'tags',
              width: 150,
              render: (_, row) => {
                return uniq(row.tags?.filter(Boolean)).map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ));
              },
            },
            {
              title: '直播间',
              key: 'live_rooms',
              width: 150,
              render: (_, row) => {
                return uniq(row.live_rooms?.filter(Boolean)).map((live_room) => (
                  <Badge key={live_room} variant="outline">
                    {live_room}
                  </Badge>
                ));
              },
            },
            {
              title: '别名',
              key: 'aliases',
              render: (_, row) => {
                return uniq(row.aliases?.filter(Boolean)).map((alias) => (
                  <Badge key={alias} variant="outline">
                    {alias}
                  </Badge>
                ));
              },
            },
            {
              title: 'IP',
              key: 'ips',
              render: (_, row) => {
                return uniq(row.ips?.filter(Boolean)).map((ip) => (
                  <Badge key={ip} variant="outline">
                    {ip}
                  </Badge>
                ));
              },
            },
            {
              title: '平台',
              key: 'platforms',
              render: (_, row) => {
                return uniq(row.platforms?.filter(Boolean)).map((platform) => (
                  <Badge key={platform} variant="outline">
                    {platform}
                  </Badge>
                ));
              },
            },
            {
              title: '产品ID',
              key: 'product_identities',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.product_identities?.filter(Boolean)).map((product_identity) => (
                      <div key={product_identity}>{product_identity}</div>
                    ))}
                  </div>
                );
              },
            },
            {
              title: '产品链接',
              key: 'product_urls',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.product_urls?.filter(Boolean)).map((product_url) => (
                      <div key={product_url} className="block max-w-[200px] overflow-hidden text-ellipsis">
                        {product_url}
                      </div>
                    ))}
                  </div>
                );
              },
            },
            {
              title: '爆品',
              key: 'hot_products',
              render: (_, row) => {
                return uniq(row.hot_products?.filter(Boolean)).map((hot_product) => (
                  <div key={hot_product}>{hot_product ? '爆品' : ''}</div>
                ));
              },
            },
            {
              title: '店铺名',
              key: 'shop_names',
              render: (_, row) => {
                return uniq(row.shop_names?.filter(Boolean)).map((shop_name) => <div key={shop_name}>{shop_name}</div>);
              },
            },
            {
              title: '佣金率',
              key: 'commission_rates',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.commission_rates?.filter(Boolean)).map((commission_rate) => (
                      <div key={commission_rate}>
                        <ProField value={commission_rate} type="percent" />
                      </div>
                    ))}
                  </div>
                );
              },
            },
            {
              title: '备注',
              key: 'remarks',
              render: (_, row) => {
                return uniq(row.remarks?.filter(Boolean)).map((remark) => <div key={remark}>{remark}</div>);
              },
            },
            {
              title: '价格',
              key: 'prices',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.prices?.filter(Boolean)).map((price) => (
                      <div key={price}>
                        <ProField value={price} type="currency" />
                      </div>
                    ))}
                  </div>
                );
              },
            },
            {
              title: '库存',
              key: 'stock_amounts',
              render: (_, row) => {
                return (
                  <div>
                    {uniq(row.stock_amounts?.filter(Boolean)).map((stock_amount) => (
                      <div key={stock_amount}>{stock_amount}</div>
                    ))}
                  </div>
                );
              },
            },
          ]}
          dataSource={data}
          rowKey="id"
        />
      </DialogContent>
    </Dialog>
  );
});

MergeDuplicatedGoodsDialog.displayName = 'MergeDuplicatedGoodsDialog';
