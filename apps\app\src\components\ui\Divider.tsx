import { cn } from '@/utils/cn';
import React from 'react';

export const Divider = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { color?: string; variant?: 'default' | 'gradation' }
>(({ className, children, variant = 'default', color, ...props }, ref) => {
  return (
    <div {...props} className={cn('divider flex w-full items-center', variant, className)}>
      <div ref={ref} content="divider" className={cn('start h-[1px] flex-1', color ? `bg-[${color}]` : '')}></div>
      {children ? (
        <div ref={ref} content="divider" className={cn('mx-4 text-xs text-[#9FA4B2CC]')}>
          {children}
        </div>
      ) : null}
      <div ref={ref} content="divider" className={cn('end h-[1px] flex-1', color ? `bg-[${color}]` : '')}></div>
    </div>
  );
});
Divider.displayName = 'Divider';
