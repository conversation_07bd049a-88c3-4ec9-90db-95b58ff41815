import { createHmac } from 'crypto';
import { stringify } from 'querystring';
import TcVod from 'vod-js-sdk-v6';
import type { UploaderOptions } from 'vod-js-sdk-v6/lib/src/uploader';

// VOD配置常量
export const VOD_CONFIG = {
  APP_ID: '1500034234',
  SECRET_ID: 'AKIDsrihIyjZOBsjimt8TsN8yvv1AMh5dB44',
  SECRET_KEY: 'CPZcxdk6W39Jd4cGY95wvupoyMd0YFqW',
  STORAGE_REGION: 'ap-seoul',
} as const;

export const vod = new TcVod({
  getSignature: async () => {
    const currentTime = Math.floor(Date.now() / 1000);
    const expiredTime = currentTime + 3600;

    const arg_list = {
      secretId: VOD_CONFIG.SECRET_ID,
      currentTimeStamp: currentTime,
      vodSubAppId: VOD_CONFIG.APP_ID,
      storageRegion: VOD_CONFIG.STORAGE_REGION,
      classId: 0,
      expireTime: expiredTime,
      random: Math.round(Math.random() * 2 ** 32),
    };

    const original = stringify(arg_list);
    const original_buffer = Buffer.from(original, 'utf8');

    const hmac = createHmac('sha1', VOD_CONFIG.SECRET_KEY);
    const hmac_buffer = hmac.update(original_buffer).digest();
    const signature: string = Buffer.concat([
      hmac_buffer,
      original_buffer,
    ]).toString('base64');
    return Promise.resolve(signature);
  },
});

export type Uploader = ReturnType<typeof vod.upload>;

export type VodUploaderOptions = Omit<UploaderOptions, 'getSignature'> & {
  /** 应用请求重试次数 */
  applyRequestRetryCount?: number;
  /** 提交请求重试次数 */
  commitRequestRetryCount?: number;
};

// 腾讯云VOD实际返回的数据结构
export interface VodUploadResponse {
  UploadId: string;
  Location: string;
  Bucket: string;
  Key: string;
  ETag: string;
  statusCode: number;
  headers: {
    'content-length': string;
    'content-type': string;
    'x-cos-request-id': string;
  };
  RequestId: string;
}

// 保留原有接口定义（可能某些情况下还会用到）
export interface UploadDoneInfo {
  fileId: string;
  video: { url: string; verify_content: string };
  cover?: { url: string; verify_content: string };
}

export type VodUploaderEvent =
  | 'media_upload'
  | 'media_progress'
  | 'cover_upload'
  | 'cover_progress';

export type VodUploaderEventHandlerParams<T extends VodUploaderEvent> =
  //
  T extends 'media_progress'
    ? { percent: number }
    : T extends 'media_upload'
      ? VodUploadResponse
      : T extends 'cover_progress'
        ? { percent: number }
        : T extends 'cover_upload'
          ? VodUploadResponse
          : never;
