import { materials } from '@roasmax/database';
import { ActionContext } from '@roasmax/serve';
import llm from '@roasmax/utils/llm';

/**
 * VOD 上传文件完成后，调用此方法以更新或添加素材库记录信息
 * @param ctx
 * @returns
 */
export const onFileUploaded = async (
  ctx: ActionContext<{
    forceUpdate: boolean;
    info: { list: { name?: string; vodFileId: string }[]; directoryId: string; properties?: any; isAudio?: boolean };
  }>,
): Promise<materials[]> => {
  if (!ctx.data.info.list.length) {
    throw new Error('文件ID不能为空');
  }

  // 只获取基础信息和元数据就够了
  const vodFileInfos = await ctx.vod.DescribeMediaInfos({
    FileIds: ctx.data.info.list.map((item) => item.vodFileId),
    SubAppId: Number(ctx.tenant.config.vod_sub_app_id),
    Filters: ['basicInfo', 'metaData'],
  });
  if (vodFileInfos.NotExistFileIdSet?.length) {
    throw new Error(`未找到对应的视频信息: ${vodFileInfos.NotExistFileIdSet.join(', ')}`);
  }

  return await ctx.trx(async (ctx) => {
    // 查找对应fileId的素材库记录
    const materials = await ctx.db.materials.findMany({
      where: { vod_file_id: { in: ctx.data.info.list.map((item) => item.vodFileId) } },
    });

    const uploadedMaterials: materials[] = [];
    for (const { name, vodFileId } of ctx.data.info.list) {
      const material = materials.find((m) => m.vod_file_id === vodFileId);
      const mediaInfo = vodFileInfos.MediaInfoSet?.find((info) => info.FileId === vodFileId);
      if (!mediaInfo) {
        throw new Error(`未找到对应视频信息: ${vodFileId}`);
      }

      const record = {
        tenant_id: ctx.tenant.id,
        name: (name ?? mediaInfo.BasicInfo!.Name!).trim(),
        size: Math.round(mediaInfo.MetaData!.Size / 1024),
        video_duration: mediaInfo.MetaData!.Duration,
        type: 'VIDEO', // 默认视频类型
        vod_sub_app_id: ctx.tenant.config.vod_sub_app_id,
        vod_category_id: String(mediaInfo.BasicInfo!.ClassId!),
        vod_file_id: mediaInfo.FileId!,
        vod_media_url: mediaInfo.BasicInfo!.MediaUrl!,
        vod_cover_url: mediaInfo.BasicInfo!.CoverUrl!,
        directory_id: ctx.data.info.directoryId,
        properties: ctx.data.info.properties,
        is_audio: ctx.data.info.isAudio,
      };

      if (!material) {
        // 不存在则创建
        const uploadedMaterial = await ctx.db.materials.create({ data: record });
        uploadedMaterials.push(uploadedMaterial);
        continue;
      } else if (ctx.data.forceUpdate) {
        // 强制更新
        const uploadedMaterial = await ctx.db.materials.update({ where: { id: material.id }, data: record });
        uploadedMaterials.push(uploadedMaterial);
        continue;
      } else {
        // 不强制更新
        uploadedMaterials.push(material);
        continue;
      }
    }
    await ctx.execute(refreshStoreSize, {});
    return uploadedMaterials;
  });
};

/**
 * 用户剩余总存储空间
 */
export const userTotalStore = async (ctx: ActionContext<any>) => {
  const storage = {
    used: ctx.tenant.config.used_storage_size || 0,
    total: ctx.tenant.config.max_storage_size || 0,
  };
  return storage;
};

/**
 *
 * 刷新已用容量
 * @param ctx
 * @returns
 */
export const refreshStoreSize = async (ctx: ActionContext<any>) => {
  const storageList = await ctx.db.materials.findMany({ select: { size: true } });
  const totalSize = storageList.reduce((acc, item) => acc + item.size, 0);
  const totalSizeFloor = Math.round(Number(totalSize) / 1024);
  await ctx.db.source_configs.update({
    where: { tenant_id: (await ctx.fetchTenant()).id },
    data: { used_storage_size: totalSizeFloor },
  });
  return totalSizeFloor;
};
/**
 * 获取字幕
 */
const parseWebvttToText = (webvttContent: string) => {
  const lines = webvttContent.split('\n');
  const subtitles = [];
  let currentSubtitle = '';

  lines.forEach((line: string) => {
    line = line.trim();

    // 检查是否为空行或WEBVTT文件头
    if (line === '' || line === 'WEBVTT') {
      return;
    }

    // 检查是否为时间戳行（以数字开头）
    if (/^\d+/.test(line)) {
      // 如果当前已经有正在构建的字幕文本，则将其添加到结果数组中
      if (currentSubtitle.trim() !== '') {
        subtitles.push(currentSubtitle.trim());
      }
      // 重置当前字幕文本
      currentSubtitle = '';
    } else {
      // 将非时间戳行的内容添加到当前字幕文本中
      currentSubtitle += line + '\n';
    }
  });

  // 添加最后一个字幕文本（如果存在）
  if (currentSubtitle.trim() !== '') {
    subtitles.push(currentSubtitle.trim());
  }

  return subtitles;
};

/**
 * 获取视频的字幕内容
 *
 * @param ctx ActionContext对象，包含视频文件ID列表
 * @returns 返回翻译后的字幕内容或错误信息
 * @throws 如果未找到租户配置信息，则抛出错误
 */
export const getSubtitles = async (ctx: ActionContext<{ vodFileIds: string[] }>) => {
  if (!ctx.tenant.config) {
    throw new Error('未找到租户配置信息');
  }
  const vodFileInfos = await ctx.vod.DescribeMediaInfos({
    FileIds: ctx.data.vodFileIds,
    SubAppId: Number(ctx.tenant.config.vod_sub_app_id),
  });
  // 检查 MediaInfoSet 是否存在且不为空
  if (vodFileInfos.MediaInfoSet && vodFileInfos.MediaInfoSet.length > 0) {
    const mediaInfo = vodFileInfos.MediaInfoSet[0]!;
    // 检查 SubtitleInfo 是否存在
    if (mediaInfo.SubtitleInfo && mediaInfo.SubtitleInfo.SubtitleSet && mediaInfo.SubtitleInfo.SubtitleSet.length > 0) {
      const subtitleUrl = mediaInfo.SubtitleInfo.SubtitleSet[0]!.Url;
      const subtitleLanguage = mediaInfo.SubtitleInfo.SubtitleSet[0]!.Language;
      try {
        // 请求字幕URL
        // @ts-ignore
        const response = await fetch(subtitleUrl);
        if (!response.ok) {
          throw new Error(`未能获取字幕： ${response.statusText}`);
        }

        // 获取字幕内容
        const subtitleContent = await response.text();
        const subtitleTexts = subtitleContent;
        return {
          subtitleText: subtitleTexts,
          subtitleLanguage,
        };
      } catch (error) {
        return {
          subtitleText: '没有字幕',
          subtitleLanguage: null,
        };
      }
    } else {
      return {
        subtitleText: '没有有效的字幕信息',
        subtitleLanguage: null,
      };
    }
  } else {
    return {
      subtitleText: '取不到有效值',
      subtitleLanguage: null,
    };
  }
};
/**
 * 翻译成中文
 *
 */
export const translateSubtitles = async (ctx: ActionContext<{ captions: string; languages: string }>) => {
  if (!ctx.tenant.config) {
    throw new Error('未找到租户配置信息');
  }
  const concatenatedSubtitleText = ctx.data.captions;
  const subtitleLanguage = ctx.data.languages;
  try {
    const result = await llm.invokeModel(
      `请将以下VTT格式的字幕翻译成中文：${concatenatedSubtitleText}，并返回翻译后的文本。请自行识别每段字幕的上下文来确保翻译通顺，对应翻译的时间点；请按照VTT格式返回，每个时间点和原文件时间点一致，不要返回任何其他内容。待翻译的语言是：${subtitleLanguage}，翻译后的语言是：中文。`,
      'ds-v3-hs',
    );
    return result;
  } catch (error) {
    return '翻译失败';
  }
};
