export const CircleClose = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <circle cx="12" cy="12" r="12" fill="white" fillOpacity="0.2" />
      <path
        d="M16.1557 16.5C16.066 16.5 15.9798 16.4655 15.9109 16.4L7.60476 8.09046C7.47029 7.95599 7.47029 7.73532 7.60476 7.60085C7.73923 7.46638 7.95645 7.46638 8.09092 7.60085L16.4005 15.9104C16.497 16.0104 16.528 16.1587 16.4729 16.2862C16.4212 16.4138 16.297 16.5 16.1557 16.5Z"
        fill="white"
        stroke="white"
        strokeWidth="0.5"
      />
      <path
        d="M7.84773 16.5005C7.70982 16.5005 7.58224 16.4178 7.52707 16.2867C7.47191 16.1592 7.50294 16.0109 7.60293 15.9109L15.9125 7.60136C16.0504 7.48069 16.2538 7.49103 16.3848 7.6186C16.5124 7.74618 16.5193 7.95305 16.4021 8.09097L8.09254 16.4005C8.02703 16.4626 7.94083 16.5005 7.84773 16.5005Z"
        fill="white"
        stroke="white"
        strokeWidth="0.5"
      />
    </svg>
  );
};
