# Roasmax - 首次登录密码重置功能

## 📋 任务概述

**任务ID**: 2oEnkQAAtMG6DE7B82NzWw  
**任务名称**: 首次登录密码重置功能  
**优先级**: 高  
**预估工时**: 2-3天  
**前置依赖**: 用户数据迁移实施  

## 🎯 任务目标

实现用户首次登录时的密码重置流程，确保从 Authing 迁移的用户能够安全地设置新密码并正常使用系统。

## 📊 详细任务分解

### 1. 后端密码重置接口开发

#### 1.1 密码重置令牌生成
```typescript
// apps/app/src/app/api/password-reset-token/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { JWTManager } from '@/utils/jwt';
import { prisma } from '@/utils/prisma';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ message: '邮箱不能为空' }, { status: 400 });
    }
    
    // 查找用户
    const user = await prisma.members.findFirst({
      where: { 
        email,
        tmp_deleted_at: null 
      }
    });
    
    if (!user) {
      return NextResponse.json({ message: '用户不存在' }, { status: 404 });
    }
    
    // 检查是否需要密码重置
    if (!user.password_reset_required) {
      return NextResponse.json({ message: '该用户不需要重置密码' }, { status: 400 });
    }
    
    // 生成重置令牌
    const resetToken = JWTManager.generatePasswordResetToken(user.user_id);
    
    return NextResponse.json({
      message: '重置令牌生成成功',
      data: {
        resetToken,
        userId: user.user_id,
        email: user.email,
        expiresIn: '24h'
      }
    });
    
  } catch (error) {
    console.error('生成重置令牌失败:', error);
    return NextResponse.json({ message: '服务器错误' }, { status: 500 });
  }
}
```

#### 1.2 密码重置执行接口
```typescript
// apps/app/src/app/api/reset-password/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { JWTManager } from '@/utils/jwt';
import { PasswordManager } from '@/utils/password';
import { prisma } from '@/utils/prisma';

export async function POST(request: NextRequest) {
  try {
    const { resetToken, newPassword, confirmPassword } = await request.json();
    
    // 验证输入
    if (!resetToken || !newPassword || !confirmPassword) {
      return NextResponse.json({ message: '参数不完整' }, { status: 400 });
    }
    
    if (newPassword !== confirmPassword) {
      return NextResponse.json({ message: '两次输入的密码不一致' }, { status: 400 });
    }
    
    // 验证密码强度
    const passwordValidation = PasswordManager.validatePasswordStrength(newPassword);
    if (!passwordValidation.valid) {
      return NextResponse.json({ 
        message: '密码强度不符合要求',
        errors: passwordValidation.errors 
      }, { status: 400 });
    }
    
    // 验证重置令牌
    const userId = JWTManager.verifyPasswordResetToken(resetToken);
    if (!userId) {
      return NextResponse.json({ message: '重置令牌无效或已过期' }, { status: 400 });
    }
    
    // 查找用户
    const user = await prisma.members.findUnique({
      where: { user_id: userId }
    });
    
    if (!user) {
      return NextResponse.json({ message: '用户不存在' }, { status: 404 });
    }
    
    // 加密新密码
    const { hash, salt } = await PasswordManager.hashPassword(newPassword);
    
    // 更新用户密码
    const updatedUser = await prisma.members.update({
      where: { user_id: userId },
      data: {
        password_hash: hash,
        salt,
        password: hash, // 兼容现有字段
        password_reset_required: false,
        is_migrated: true,
        login_attempts: 0,
        locked_until: null,
        last_login_at: new Date()
      }
    });
    
    // 生成登录 JWT
    const loginToken = JWTManager.generateToken({
      user_id: user.user_id,
      email: user.email,
      nickname: user.nickname,
      tenant_id: user.tenant_id
    });
    
    const response = NextResponse.json({
      message: '密码设置成功',
      data: {
        user: {
          userId: user.user_id,
          email: user.email,
          nickname: user.nickname,
          tenantId: user.tenant_id
        }
      }
    });
    
    // 设置登录 Cookie
    response.cookies.set('token', loginToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24小时
    });
    
    response.cookies.set('Authorization', loginToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60
    });
    
    return response;
    
  } catch (error) {
    console.error('密码重置失败:', error);
    return NextResponse.json({ message: '密码重置失败' }, { status: 500 });
  }
}
```

#### 1.3 登录接口改造
```typescript
// apps/app/src/app/api/login/route.ts (修改部分)
export async function POST(request: Request) {
  try {
    const { account: email, password } = await request.json();
    
    if (!email || !password) {
      return NextResponse.json({ message: '邮箱和密码不能为空' }, { status: 400 });
    }
    
    // 查找用户
    const user = await prisma.members.findFirst({
      where: { 
        email,
        tmp_deleted_at: null 
      }
    });
    
    if (!user) {
      return NextResponse.json({ message: '用户不存在' }, { status: 400 });
    }
    
    // 检查账户锁定状态
    if (user.locked_until && user.locked_until > new Date()) {
      const remainingTime = Math.ceil((user.locked_until.getTime() - Date.now()) / 1000 / 60);
      return NextResponse.json({ 
        message: `账户已被锁定，请 ${remainingTime} 分钟后再试` 
      }, { status: 423 });
    }
    
    // 检查是否需要密码重置
    if (user.password_reset_required) {
      const resetToken = JWTManager.generatePasswordResetToken(user.user_id);
      return NextResponse.json({
        code: 'PASSWORD_RESET_REQUIRED',
        message: '检测到您是首次登录新系统，请设置新密码',
        data: {
          resetToken,
          userId: user.user_id,
          email: user.email,
          nickname: user.nickname
        }
      }, { status: 200 });
    }
    
    // 验证密码
    if (!user.password_hash) {
      return NextResponse.json({ message: '账户异常，请联系管理员' }, { status: 400 });
    }
    
    const isValidPassword = await PasswordManager.verifyPassword(password, user.password_hash);
    
    if (!isValidPassword) {
      // 处理登录失败
      await handleFailedLogin(user.user_id);
      return NextResponse.json({ message: '密码错误' }, { status: 400 });
    }
    
    // 重置登录失败次数
    await resetFailedLoginAttempts(user.user_id);
    
    // 生成 JWT 并返回
    const token = JWTManager.generateToken({
      user_id: user.user_id,
      email: user.email,
      nickname: user.nickname,
      tenant_id: user.tenant_id
    });
    
    const response = NextResponse.json({ 
      data: { 
        user: {
          userId: user.user_id,
          email: user.email,
          nickname: user.nickname
        }
      }, 
      code: 200 
    });
    
    response.cookies.set('token', token);
    response.cookies.set('Authorization', token);
    
    // 更新最后登录时间
    await prisma.members.update({
      where: { user_id: user.user_id },
      data: { last_login_at: new Date() }
    });
    
    return response;
    
  } catch (error) {
    console.error('登录失败:', error);
    return NextResponse.json({ message: '登录失败' }, { status: 500 });
  }
}

async function handleFailedLogin(userId: string): Promise<void> {
  const user = await prisma.members.findUnique({
    where: { user_id: userId }
  });
  
  if (!user) return;
  
  const newAttempts = user.login_attempts + 1;
  const updateData: any = { login_attempts: newAttempts };
  
  // 如果失败次数达到5次，锁定账户1小时
  if (newAttempts >= 5) {
    updateData.locked_until = new Date(Date.now() + 60 * 60 * 1000);
  }
  
  await prisma.members.update({
    where: { user_id: userId },
    data: updateData
  });
}

async function resetFailedLoginAttempts(userId: string): Promise<void> {
  await prisma.members.update({
    where: { user_id: userId },
    data: {
      login_attempts: 0,
      locked_until: null
    }
  });
}
```

### 2. 前端密码重置组件开发

#### 2.1 密码重置对话框组件
```typescript
// apps/app/src/components/PasswordResetDialog.tsx
'use client';

import React, { useState } from 'react';
import { Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Label,
  Progress
} from '@/components/ui';
import { toast } from 'react-hot-toast';

interface PasswordResetDialogProps {
  isOpen: boolean;
  resetToken: string;
  userEmail: string;
  userName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
}

export function PasswordResetDialog({ 
  isOpen, 
  resetToken, 
  userEmail, 
  userName,
  onSuccess, 
  onCancel 
}: PasswordResetDialogProps) {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // 密码强度检查
  const checkPasswordStrength = (password: string): PasswordStrength => {
    let score = 0;
    const feedback: string[] = [];
    
    if (password.length >= 8) {
      score += 25;
    } else {
      feedback.push('密码长度至少8位');
    }
    
    if (/[A-Z]/.test(password)) {
      score += 25;
    } else {
      feedback.push('需要包含大写字母');
    }
    
    if (/[a-z]/.test(password)) {
      score += 25;
    } else {
      feedback.push('需要包含小写字母');
    }
    
    if (/\d/.test(password)) {
      score += 25;
    } else {
      feedback.push('需要包含数字');
    }
    
    let color = 'bg-red-500';
    if (score >= 75) color = 'bg-green-500';
    else if (score >= 50) color = 'bg-yellow-500';
    else if (score >= 25) color = 'bg-orange-500';
    
    return { score, feedback, color };
  };
  
  const passwordStrength = checkPasswordStrength(newPassword);
  const isPasswordValid = passwordStrength.score === 100;
  const isConfirmValid = newPassword === confirmPassword && confirmPassword.length > 0;
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isPasswordValid) {
      setError('密码强度不符合要求');
      return;
    }
    
    if (!isConfirmValid) {
      setError('两次输入的密码不一致');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resetToken,
          newPassword,
          confirmPassword
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('密码设置成功，正在跳转...');
        setTimeout(() => {
          onSuccess();
        }, 1000);
      } else {
        setError(data.message || '密码设置失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-500" />
            设置新密码
          </DialogTitle>
          <DialogDescription>
            欢迎 <span className="font-medium text-blue-600">{userName}</span>！
            <br />
            检测到您是首次登录新系统，为了账户安全，请设置一个新密码。
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 新密码输入 */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">新密码</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="请输入新密码"
                className="pr-10"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
            
            {/* 密码强度指示器 */}
            {newPassword && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">密码强度:</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all ${passwordStrength.color}`}
                      style={{ width: `${passwordStrength.score}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium">
                    {passwordStrength.score}%
                  </span>
                </div>
                
                {passwordStrength.feedback.length > 0 && (
                  <div className="space-y-1">
                    {passwordStrength.feedback.map((item, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-red-600">
                        <XCircle className="h-3 w-3" />
                        {item}
                      </div>
                    ))}
                  </div>
                )}
                
                {isPasswordValid && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    密码强度符合要求
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* 确认密码输入 */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">确认密码</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="请再次输入新密码"
                className="pr-10"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
            
            {/* 密码确认状态 */}
            {confirmPassword && (
              <div className="flex items-center gap-2 text-sm">
                {isConfirmValid ? (
                  <>
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span className="text-green-600">密码确认正确</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-3 w-3 text-red-600" />
                    <span className="text-red-600">两次输入的密码不一致</span>
                  </>
                )}
              </div>
            )}
          </div>
          
          {/* 错误信息 */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center gap-2 text-red-700">
                <XCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}
          
          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              稍后设置
            </Button>
            <Button 
              type="submit" 
              disabled={loading || !isPasswordValid || !isConfirmValid}
              className="min-w-[100px]"
            >
              {loading ? '设置中...' : '设置密码'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

#### 2.2 登录页面集成
```typescript
// apps/app/src/app/login/page.tsx (修改部分)
export default function Login() {
  // ... 现有状态
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [resetData, setResetData] = useState<{
    resetToken: string;
    email: string;
    userId: string;
    nickname: string;
  } | null>(null);
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!account || !password) {
      setError('请输入邮箱和密码');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ account, password }),
      });
      
      const data = await response.json();
      
      // 检查是否需要密码重置
      if (data.code === 'PASSWORD_RESET_REQUIRED') {
        setResetData({
          resetToken: data.data.resetToken,
          email: data.data.email,
          userId: data.data.userId,
          nickname: data.data.nickname
        });
        setShowPasswordReset(true);
        return;
      }
      
      if (!response.ok) {
        throw new Error(data.message || '登录失败');
      }
      
      // 正常登录流程
      const { data: loginData } = data;
      const { user } = loginData;
      setUserId(user.userId);
      
      const token = Cookies.get('token');
      if (token) {
        localStorage.setItem('token', token);
      }
      
      router.push('/');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };
  
  const handlePasswordResetSuccess = () => {
    setShowPasswordReset(false);
    setResetData(null);
    toast.success('密码设置成功，欢迎使用新系统！');
    router.push('/');
  };
  
  const handlePasswordResetCancel = () => {
    setShowPasswordReset(false);
    setResetData(null);
    setAccount('');
    setPassword('');
  };
  
  return (
    <>
      {/* 现有登录表单 */}
      
      {/* 密码重置对话框 */}
      {showPasswordReset && resetData && (
        <PasswordResetDialog
          isOpen={showPasswordReset}
          resetToken={resetData.resetToken}
          userEmail={resetData.email}
          userName={resetData.nickname || resetData.email}
          onSuccess={handlePasswordResetSuccess}
          onCancel={handlePasswordResetCancel}
        />
      )}
    </>
  );
}
```

## ✅ 验收标准

1. **功能完整性**
   - [ ] 首次登录检测正常
   - [ ] 密码重置流程完整
   - [ ] 密码强度验证有效
   - [ ] 登录状态正确设置

2. **用户体验**
   - [ ] 界面友好易用
   - [ ] 错误提示清晰
   - [ ] 操作流程顺畅
   - [ ] 响应速度快

3. **安全性要求**
   - [ ] 密码强度符合要求
   - [ ] 重置令牌安全可靠
   - [ ] 防止暴力破解
   - [ ] 会话管理安全

## 🔧 Augment Code 提示词

```
请帮我开发 Roasmax 项目的首次登录密码重置功能：

1. 开发后端密码重置接口，包括令牌生成和密码更新
2. 改造登录接口，支持密码重置检测
3. 开发前端密码重置对话框组件
4. 实现密码强度验证和用户体验优化
5. 集成到现有登录流程中
6. 添加安全防护机制

要求：
- 提供友好的用户界面和交互体验
- 实现强密码策略和验证
- 确保重置流程的安全性
- 支持实时密码强度检测
- 包含完整的错误处理
- 提供清晰的用户引导

请提供完整的前后端实现代码。
```

## 📅 时间安排

- **第1天**: 后端接口开发和测试
- **第2天**: 前端组件开发和集成
- **第3天**: 完整流程测试和优化

## 🚨 风险提示

1. **用户体验风险**: 密码重置流程可能影响用户首次使用体验
2. **安全性风险**: 重置令牌可能被恶意利用
3. **兼容性风险**: 可能与现有登录流程存在冲突
