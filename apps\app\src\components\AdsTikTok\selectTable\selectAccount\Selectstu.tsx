import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';

export default function SelectStatus({ onChange }: { onChange: (value: 'ENABLE' | 'DISABLE' | 'DELETE') => void }) {
  return (
    <div className="mr-4 flex h-8 w-[240px] items-center rounded-md border border-[#1C2A3F] pl-3 text-[12px] text-xs text-white">
      <div className="w-20 border-r border-gray-700 text-white">投放状态</div>
      <Select onValueChange={onChange} defaultValue="All">
        <SelectTrigger className="h-full border-0 bg-transparent text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0">
          <SelectValue placeholder="请选择状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem className="text-xs" value="ENABLE">
              投放中
            </SelectItem>
            <SelectItem className="text-xs" value="DISABLE">
              已暂停
            </SelectItem>
            <SelectItem className="text-xs" value="FROZEN">
              已冻结
            </SelectItem>
            <SelectItem className="text-xs" value="All">
              全部状态
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
