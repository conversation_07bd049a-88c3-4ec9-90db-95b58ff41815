export const MessageOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_3690_1370)">
        <path
          d="M7.36077 12.3119C6.96175 12.3119 6.5765 12.1147 6.30591 11.773L5.55604 10.8213C5.23499 10.4132 4.49659 10.0554 3.97834 10.0554H2.51529C1.63701 10.0554 0.923828 9.34224 0.923828 8.46396V2.74478C0.923828 1.8665 1.63701 1.15332 2.51529 1.15332H12.2085C13.0868 1.15332 13.8 1.8665 13.8 2.74478V8.46166C13.8 9.33995 13.0868 10.0531 12.2085 10.0531H10.7432C10.2249 10.0531 9.48654 10.4109 9.16549 10.819L8.41563 11.7707C8.14503 12.1147 7.75978 12.3119 7.36077 12.3119ZM2.51529 1.98345C2.09564 1.98345 1.75396 2.32513 1.75396 2.74478V8.46166C1.75396 8.88131 2.09564 9.223 2.51529 9.223H3.98063C4.75343 9.223 5.73261 9.69768 6.21189 10.3054L6.96175 11.257C7.07412 11.4015 7.21859 11.4795 7.36306 11.4795C7.50982 11.4795 7.652 11.3992 7.76436 11.257L8.51423 10.3054C8.99121 9.69768 9.97269 9.223 10.7455 9.223H12.2108C12.6305 9.223 12.9722 8.88131 12.9722 8.46166V2.74478C12.9722 2.32513 12.6305 1.98345 12.2108 1.98345H2.51529Z"
          fill="white"
          fillOpacity="0.8"
        />
      </g>
      <defs>
        <clipPath id="clip0_3690_1370">
          <rect width="13" height="13" fill="white" transform="translate(0.84375)" />
        </clipPath>
      </defs>
    </svg>
  );
};
