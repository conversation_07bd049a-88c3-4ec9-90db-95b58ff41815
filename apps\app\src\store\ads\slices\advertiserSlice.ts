import type { StoreApi } from 'zustand';
import type { AdStore } from '../storeTypes';
import type { StoreSlice } from './types';

export interface AdvertiserSlice extends StoreSlice<'clearCurrentAdvertiser' | 'setCurrentAdvertiser'> {}
// type AdStore

export const createAdvertiserSlice = (set: StoreApi<AdStore>['setState']): AdvertiserSlice => ({
  actions: {
    setCurrentAdvertiser: (advertiser) => {
      set({
        currentAdvertiser: advertiser,
        currentCampaign: null,
        currentAdGroup: null,
        currentAd: null,
      });
    },

    clearCurrentAdvertiser: () => {
      set({ currentAdvertiser: [] });
    },
  },
});
