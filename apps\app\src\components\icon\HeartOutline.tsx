export const HeartOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.50072 2.69185L6.21679 2.40571C5.5506 1.73435 4.6803 1.4 3.80783 1.4C2.93536 1.4 2.06506 1.73435 1.39887 2.40571C0.0671702 3.74776 0.0669229 5.92468 1.39885 7.26843L6.50072 2.69185ZM6.50072 2.69185L6.78466 2.40571C7.45085 1.73435 8.32115 1.4 9.19361 1.4C10.0661 1.4 10.9363 1.73438 11.601 2.40413C12.9331 3.74786 12.9329 5.92488 11.6012 7.26699C11.6011 7.26699 11.6011 7.267 11.6011 7.26701L7.41301 11.4869L7.41224 11.4877C7.16122 11.742 6.8318 11.8685 6.50072 11.8685C6.16982 11.8685 5.83978 11.7421 5.58683 11.4882C5.58673 11.4881 5.58664 11.488 5.58654 11.4879L1.39901 7.26859L6.50072 2.69185Z"
        stroke="white"
        strokeOpacity="0.8"
        strokeWidth="0.8"
      />
    </svg>
  );
};
