import { LangfuseRpc } from '@roasmax/utils/langfuse';
import { ActionContextPluginLoader } from '../../types';

const langfuseRpcPlugin: ActionContextPluginLoader = () => {
  if (!process.env.LANGFUSE_BASEURL || !process.env.LANGFUSE_RPC_EMAIL || !process.env.LANGFUSE_RPC_PASSWORD) {
    throw new Error('LANGFUSE_BASEURL, LANGFUSE_RPC_EMAIL, or LANGFUSE_RPC_PASSWORD is not set');
  }
  return {
    name: 'langfuseRpc',
    plugin: new LangfuseRpc({
      host: process.env.LANGFUSE_BASEURL,
      credential: { email: process.env.LANGFUSE_RPC_EMAIL, password: process.env.LANGFUSE_RPC_PASSWORD },
    }),
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 获取 langfuse rpc 实例 自动缓存
     * @returns langfuse rpc 实例
     */
    langfuseRpc: LangfuseRpc;
  }
}

export default langfuseRpcPlugin;
