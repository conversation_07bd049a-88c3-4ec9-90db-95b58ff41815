/**
 * 用户
 */
export interface UserForVideoDistribution {
  id: string;
  qualityScore: number; // [0,100] 用户优质度
  fansCount: number; // 粉丝数
  sealQuota: number; // 带货配额
  noSealQuota: number; // 不带货配额
}

/**
 * 商品
 */
export interface ProductForVideoDistribution {
  id: string;
  priorityScore: number; // [0,100] 商品优先级
  distributeLimit: number; // 可分发数量
}

/**
 * 视频
 */
export interface VideoForVideoDistribution {
  id: string;
  priorityScore: number; // [0,100] 视频优先级
  productId: string; // 所属商品
  timeWeight: number; // [0,100] 时间权重
  isSeal: boolean; // 是否带货
}

/**
 * 订单
 */
export interface OrderForVideoDistribution {
  userId: string;
  productId: string;
  priorityScore: number; // [0,100] 订单优先级
}

/**
 * 历史分配记录
 */
export interface HistoricalDistributionForVideoDistribution {
  userId: string;
  productId: string;
}

/**
 * 定义权重配置接口
 */
export interface WeightConfigForVideoDistribution {
  userQualityWeight: number; // 用户优质度权重
  productPriorityWeight: number; // 商品优先级权重
  videoPriorityWeight: number; // 视频优先级权重
  orderPriorityWeight: number; // 订单优先级权重
  timeWeight: number; // 时间权重
}

/**
 * 视频分发模型
 *
 * 1. 集合定义
 * U = {u1, u2, ..., um}    // 用户集合
 * P = {p1, p2, ..., pn}    // 商品集合
 * V = {v1, v2, ..., vk}    // 视频集合
 * T = {t1, t2, ..., tp}    // 时间集合,表示历史分配记录
 *
 * 2. 参数定义
 * 用户属性:
 * Q(u) ∈ [0,100]     // 用户优质度分数
 * F(u) ∈ N          // 用户粉丝数
 * C1(u) ∈ N         // 用户带货配额
 * C2(u) ∈ N         // 用户不带货配额
 *
 * 商品属性:
 * H(p) ∈ [0,100]    // 商品优先级分数
 * D(p) ∈ N          // 商品可分发数量
 *
 * 视频属性:
 * R(v) ∈ [0,100]    // 视频优先级分数
 * G(v) ∈ P          // 视频所属商品
 * W(v) ∈ [0,100]    // 视频时间权重
 * S(v) ∈ {0,1}      // 视频是否带货
 *
 * 订单关系:
 * O(u,p) ∈ [0,100]  // 用户对商品的订单优先级分数
 *
 * 历史分配记录:
 * H(u,p,t) = {      // 历史分配状态
 *     1, 如果商品p在时间t分配给过用户u
 *     0, 否则
 * }
 *
 * 3. 决策变量:
 * X(u,v) = {        // 注意这里不再需要时间维度
 *     1, 如果视频v分配给用���u
 *     0, 否则
 * }
 *
 * 4. 历史约束函数:
 * IsAllowed(u,p) = {
 *     1, 如果 Σt∈T H(u,p,t) = 0  // 历史上从未分配过
 *     0, 否则
 * }
 *
 * 5. 目标函数:
 * maximize Σu∈U Σv∈V (
 *    w1*Q(u) +           // 用户优质度权重
 *    w2*H(G(v)) +        // 商品优先级权重
 *    w3*R(v) +           // 视频优先级权重
 *    w4*O(u,G(v)) +      // 订单关系权重
 *    w5*W(v)             // 时间权重
 * ) * X(u,v)
 *
 * 6. 约束条件:
 * 6.1. 用户配额约束
 * 对于每个用户u:
 * Σv∈V(S(G(v))*X(u,v)) ≤ C1(u)     // 带货配额
 * Σv∈V((1-S(G(v)))*X(u,v)) ≤ C2(u) // 不带货配额
 *
 * 6.2. 视频唯一分配约束
 * 对于每个视频v:
 * Σu∈U X(u,v) ≤ 1
 *
 * 6.3. 历史分配约束
 * 对于每个用户u和视频v:
 * X(u,v) ≤ IsAllowed(u,G(v))
 *
 * 6.4. 商品分发数量约束
 * 对于每个商品p:
 * Σu∈U Σv∈V(X(u,v)*I(G(v)=p)) ≤ D(p)
 *
 * 6.5. 非负整数约束
 * X(u,v) ∈ {0,1}
 *
 * 6.6. 带货视频配额约束
 * 对于每个用户u:
 * Σv∈V(S(v)*X(u,v)) ≤ C1(u)
 *
 * 6.7. 不带货视频配额约束
 * 对于每个用户u:
 * Σv∈V((1-S(v))*X(u,v)) ≤ C2(u)
 *
 * 7. 启发式求解策略
 * 7.1. 获取历史分配记录
 * LoadHistoryDistribution(T)
 *
 * 7.2. 过滤可分配组合
 * For each u ∈ U:
 *     For each v ∈ V:
 *         if !IsAllowed(u,G(v)):
 *             从候选集中移除(u,v)
 *
 * 7.3. 计算优先级并排序
 * For each 剩余的(u,v):
 *     计算Priority(u,v)
 *
 * 7.4. 按优先级执行分配
 * While 存在未分配的视频 且 存在可用配额:
 *     选择最高优先级的(u,v)组合
 *     如果满足所有约束:
 *         执行分配
 */
export class VideoDistributionModel {
  private users: Map<string, UserForVideoDistribution>;
  private products: Map<string, ProductForVideoDistribution>;
  private videos: Map<string, VideoForVideoDistribution>;
  private orders: Map<string, OrderForVideoDistribution[]>;
  private historicalDistributions: HistoricalDistributionForVideoDistribution[];
  private weightConfig: WeightConfigForVideoDistribution;

  constructor(params: {
    users: UserForVideoDistribution[];
    products: ProductForVideoDistribution[];
    videos: VideoForVideoDistribution[];
    orders: OrderForVideoDistribution[];
    historicalDistributions: HistoricalDistributionForVideoDistribution[];
    weightConfig: WeightConfigForVideoDistribution;
  }) {
    const { users, products, videos, orders, historicalDistributions, weightConfig } = params;
    this.users = new Map(users.map((u) => [u.id, u]));
    this.products = new Map(products.map((p) => [p.id, p]));
    this.videos = new Map(videos.map((v) => [v.id, v]));
    this.orders = new Map();
    orders.forEach((o) => {
      if (!this.orders.has(o.userId)) {
        this.orders.set(o.userId, []);
      }
      this.orders.get(o.userId)?.push(o);
    });
    this.historicalDistributions = historicalDistributions;
    this.weightConfig = weightConfig;
  }

  /**
   * 检查历史分配
   * 检查用户是否已经分发过该商品的视频，包括历史记录
   */
  private isAllowed(userId: string, productId: string): boolean {
    // 检查历史记录中该用户是否已经分发过这个商品的视频
    const hasHistoricalDistribution = this.historicalDistributions.some(
      (dist) => dist.userId === userId && dist.productId === productId,
    );

    // 如果历史上已经分发过该商品的视频，则不允许再分发
    if (hasHistoricalDistribution) {
      console.log('存在历史分发记录，不进行关联', userId, productId);
      return false;
    }

    return true;
  }

  /**
   * 计算优先级分数
   */
  private calculatePriority(user: UserForVideoDistribution, video: VideoForVideoDistribution): number {
    const product = this.products.get(video.productId)!;
    const order = this.orders.get(user.id)?.find((o) => o.productId === video.productId);
    const orderScore = order?.priorityScore || 0;

    // 计算权重总和
    const totalWeight =
      this.weightConfig.userQualityWeight +
      this.weightConfig.productPriorityWeight +
      this.weightConfig.videoPriorityWeight +
      this.weightConfig.orderPriorityWeight +
      this.weightConfig.timeWeight;

    // 归一化处理
    return (
      (this.weightConfig.userQualityWeight * user.qualityScore +
        this.weightConfig.productPriorityWeight * product.priorityScore +
        this.weightConfig.videoPriorityWeight * video.priorityScore +
        this.weightConfig.orderPriorityWeight * orderScore +
        this.weightConfig.timeWeight * video.timeWeight) /
      totalWeight
    );
  }

  /**
   * 检查约束条件
   */
  private checkConstraints(
    user: UserForVideoDistribution,
    video: VideoForVideoDistribution,
    currentDistribution: Map<string, string[]>,
  ): boolean {
    // 检查视频是否已经被分配给其他用户
    const isVideoAlreadyDistributed = Array.from(currentDistribution.values()).flat().includes(video.id);

    if (isVideoAlreadyDistributed) {
      console.log('视频已被分配给其他用户', video.id);
      return false;
    }

    // 检查用户是否已经分发过该商品的视频
    const userVideos = currentDistribution.get(user.id) || [];
    const hasProductVideo = userVideos.some((videoId) => this.videos.get(videoId)?.productId === video.productId);

    if (hasProductVideo) {
      console.log('用户已被分配过该商品的视频', user.id, video.id);
      return false;
    }

    // 检查用户配额
    const userSealVideos = userVideos.filter((videoId) => this.videos.get(videoId)?.isSeal).length;
    const userNoSealVideos = userVideos.length - userSealVideos;

    if (video.isSeal && userSealVideos >= user.sealQuota) {
      console.log('用户带货配额约束不通过', user.id, video.id);
      return false;
    }
    if (!video.isSeal && userNoSealVideos >= user.noSealQuota) {
      console.log('用户不带货配额约束不通过', user.id, video.id);
      return false;
    }

    // 检查商品分发数量约束
    const productVideos = Array.from(currentDistribution.values())
      .flat()
      .filter((videoId) => this.videos.get(videoId)?.productId === video.productId);
    if (productVideos.length >= this.products.get(video.productId)!.distributeLimit) {
      console.log('商品分发数量约束不通过', user.id, video.id);
      return false;
    }

    return true;
  }

  /**
   * 计算调整后的优先级分数
   */
  private calculateAdjustedPriority(
    user: UserForVideoDistribution,
    video: VideoForVideoDistribution,
    currentDistribution: Map<string, string[]>,
  ): number {
    const basePriority = this.calculatePriority(user, video);
    const userCurrentVideos = currentDistribution.get(user.id)?.length || 0;

    // 计算用户的总配额
    const userTotalQuota = user.sealQuota + user.noSealQuota;

    // 分配率调整因子：未分配视频越多，优先级越高
    const distributionRateFactor = 1 + Math.max(0, (userTotalQuota - userCurrentVideos) / userTotalQuota);

    return basePriority * distributionRateFactor;
  }

  /**
   * 执行分发
   */
  public distribute(): { userId: string; videoIds: string[] }[] {
    const distribution = new Map<string, string[]>();

    // 第一轮：确保每个用户至少获得一个视频
    for (const user of this.users.values()) {
      const availableVideos = Array.from(this.videos.values())
        .filter((video) => this.isAllowed(user.id, video.productId))
        .sort((a, b) => this.calculatePriority(user, b) - this.calculatePriority(user, a));

      for (const video of availableVideos) {
        if (this.checkConstraints(user, video, distribution)) {
          if (!distribution.has(user.id)) {
            distribution.set(user.id, []);
          }
          distribution.get(user.id)?.push(video.id);
          break;
        }
      }
    }

    // 第二轮：继续分配剩余视频
    let hasNewDistribution;
    do {
      hasNewDistribution = false;
      const allPairs: Array<{
        userId: string;
        videoId: string;
        priority: number;
      }> = [];

      // 生成所有可能的分配组合
      for (const user of this.users.values()) {
        for (const video of this.videos.values()) {
          if (this.isAllowed(user.id, video.productId)) {
            allPairs.push({
              userId: user.id,
              videoId: video.id,
              priority: this.calculateAdjustedPriority(user, video, distribution),
            });
          }
        }
      }

      // 按调整后的优先级降序排序
      allPairs.sort((a, b) => b.priority - a.priority);

      // 尝试分配
      for (const pair of allPairs) {
        const user = this.users.get(pair.userId)!;
        const video = this.videos.get(pair.videoId)!;

        if (this.checkConstraints(user, video, distribution)) {
          if (!distribution.has(pair.userId)) {
            distribution.set(pair.userId, []);
          }
          distribution.get(pair.userId)?.push(pair.videoId);
          hasNewDistribution = true;
          break;
        }
      }
    } while (hasNewDistribution);

    return Array.from(distribution.entries()).map(([userId, videoIds]) => ({
      userId,
      videoIds,
    }));
  }
}
