'use client';
import { InformationOutline } from '@/components/icon/InformationOutline';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui';
import { cn } from '@/utils/cn';
import React from 'react';
import { ControllerRenderProps, FieldPath, FieldValues, UseFormReturn } from 'react-hook-form';

export function ProFormField<
  TFieldValues extends FieldValues = FieldValues,
  TFieldPath extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  formItemId,
  label,
  tooltip,
  extra,
  className,
  subField,
  hidden,
  layout,
  ...props
}: {
  formItemId?: string;
  label?: React.ReactNode;
  tooltip?: string;
  extra?: React.ReactNode;
  className?: string;
  subField?: boolean;
  layout?: 'vertical' | 'horizontal';
  hidden?: boolean;
} & (
  | {
      name: TFieldPath;
      form: UseFormReturn<TFieldValues, unknown, TFieldValues | undefined>;
      renderFormControl: (field: ControllerRenderProps<TFieldValues, TFieldPath>) => React.ReactNode;
    }
  | { children: React.ReactNode }
)) {
  if (hidden) return null;
  return (
    <FormField
      name={'name' in props ? props.name : ('' as any)}
      control={'form' in props ? props.form.control : undefined}
      render={({ field }) => (
        <FormItem
          id={formItemId}
          className={cn(
            !subField ? 'mb-[24px] font-medium' : 'mb-[16px] font-normal',
            layout === 'horizontal' ? 'flex items-center justify-between' : '',
            className,
          )}
        >
          <ProFormLabel
            label={label}
            tooltip={tooltip}
            extra={extra}
            subField={subField}
            className={layout === 'horizontal' ? '' : 'mb-3'}
          />
          {'renderFormControl' in props ? <FormControl>{props.renderFormControl(field)}</FormControl> : props.children}
        </FormItem>
      )}
    />
  );
}

export const ProFormLabel: React.FC<{
  label: React.ReactNode;
  tooltip?: string;
  extra?: React.ReactNode;
  subField?: boolean;
  className?: string;
}> = (props) => {
  return (
    <FormLabel>
      <div className={cn('flex items-center justify-between', props.className)}>
        <div className="flex items-center gap-[6px]">
          <div className={props.subField ? 'text-[13px] text-[#727485]' : ''}>{props.label}</div>
          {!!props.tooltip && <TooltipHelper message={props.tooltip} />}
        </div>
        <div className="text-xs text-[#BEC5D6]">{!!props.extra && props.extra}</div>
      </div>
    </FormLabel>
  );
};

const TooltipHelper: React.FC<{ message: string }> = (props) => {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger type="button">
          <InformationOutline className="text-muted-foreground" />
        </TooltipTrigger>
        <TooltipContent className="flex h-[32px] items-center bg-[white] text-[13px] text-[#282E43]">
          {props.message}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
