import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
} from '@/components/ui';
import { Trash } from 'lucide-react';
import { Warning } from '@/components/icon/Warning';

export function ConfirmDeleteDialog({
  selectedCount,
  handleDelete,
}: {
  selectedCount: number;
  handleDelete: () => void;
}) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          disabled={!selectedCount}
          className="h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 px-6 text-xs font-medium text-[#fff] hover:bg-[#CCDDFF33] hover:text-white"
        >
          <Trash className="h-3 w-3 flex-shrink-0" />
          <div className="ml-1.5">删除</div>
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="h-[160px] w-[360px] rounded-xl pb-5 pl-6 pr-4 pt-8">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center justify-start gap-2 text-sm text-white">
            <Warning />
            <span>确认删除吗?</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="ml-[26px] text-xs text-[#81889d]">
            所选内容将会被全部删除，无法找回
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="h-8 gap-2">
          <AlertDialogCancel className="h-8 w-[92px] rounded-lg bg-[#CCDDFF1A] text-white">取消</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} className="h-8 w-[92px] rounded-lg text-[#050A1C]">
            确认
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
