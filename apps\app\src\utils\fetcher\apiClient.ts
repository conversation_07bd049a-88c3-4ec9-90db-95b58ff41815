import type { ResultADS } from '@/types/api';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import { recursiveUnderscoreToCamel } from '../camel';

// 基础请求配置
const baseConfig: RequestInit = {
  credentials: 'include',
  cache: 'no-store',
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
};

// 处理请求头
const getHeaders = (customHeaders?: HeadersInit) => {
  const headers = new Headers(customHeaders);
  headers.set('Content-Type', 'application/json;charset=utf-8');
  headers.set('Auth-Token', Cookies.get('Authorization') || '');

  return headers;
};

// 处理响应
async function handleResponse<T>(response: Response): Promise<T> {
  // 处理文件下载
  if (response.headers.get('content-type')?.includes('application/octet-stream')) {
    return response as unknown as T;
  }

  const data = await response.json();
  if (!data) throw new Error('请求出错，请稍候重试');

  const camelData = recursiveUnderscoreToCamel(data, ['json_date']);

  const { code, message, success } = camelData as ResultADS<T>;
  const hasSuccess = code === 0 || code === 200 || (response.status >= 200 && response.status < 300);

  if (hasSuccess && success) {
    return camelData?.data ?? {};
  }

  throw new Error(message || '请求出错，请稍候重试');
}

class APIClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = '/api/ads';
  }

  async request<T>(config: RequestInit & { url: string }): Promise<T> {
    const { url, headers, ...rest } = config;
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
    if (!Cookies.get('Authorization') && window.location.pathname !== '/login') {
      toast.error('请先登录');
      window.location.href = '/login';
      return Promise.reject(new Error('请先登录'));
    }

    try {
      const response = await fetch(fullUrl, {
        ...baseConfig,
        ...rest,
        headers: getHeaders(headers),
      });

      return await handleResponse<T>(response);
    } catch (error) {
      console.error('详细错误信息:', error);
      toast.error((error as Error)?.message || '请求出错，请稍候重试');
      throw error;
    }
  }

  get<T>(config: { url: string; params?: Record<string, any> }): Promise<T> {
    let finalUrl = config.url;
    if (config.params) {
      const searchParams = new URLSearchParams();
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        finalUrl = `${finalUrl}?${queryString}`;
      }
    }
    return this.request<T>({ method: 'GET', url: finalUrl });
  }

  post<T>(config: { url: string; data?: any }): Promise<T> {
    return this.request<T>({
      method: 'POST',
      url: config.url,
      body: config.data ? JSON.stringify(config.data) : undefined,
    });
  }

  async download(config: {
    url: string;
    params?: URLSearchParams | Record<string, any>;
    headers?: HeadersInit;
  }): Promise<Blob> {
    if (!config.url) {
      throw new Error('URL is required');
    }

    let finalUrl = config.url.startsWith('http') ? config.url : `${this.baseUrl}${config.url}`;
    if (config.params) {
      if (config.params instanceof URLSearchParams) {
        const queryString = config.params.toString();
        if (queryString) {
          finalUrl = `${finalUrl}?${queryString}`;
        }
      } else {
        const searchParams = new URLSearchParams();
        Object.entries(config.params).forEach(([key, value]) => {
          if (value !== undefined) {
            searchParams.append(key, String(value));
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          finalUrl = `${finalUrl}?${queryString}`;
        }
      }
    }

    try {
      const response = await fetch(finalUrl, {
        ...baseConfig,
        headers: {
          ...getHeaders(config.headers),
          Accept: '*/*',
        },
      });

      // 检查是否返回 JSON 格式的错误信息
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const error = await response.json();
        toast.error(error.message || '下载失败');
        throw error;
      }

      const blob = await response.blob();
      let filename = 'download.xlsx';

      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = filenameRegex.exec(contentDisposition);
        if (matches?.[1]) {
          filename = matches[1].replace(/['"]/g, '');
          try {
            filename = decodeURIComponent(filename);
          } catch (e) {
            console.error('Failed to decode filename:', e);
          }
        }
      }

      // 下载文件
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);

      return blob;
    } catch (error) {
      console.error('下载失败:', error);
      toast.error('文件下载失败，请稍后重试');
      throw error;
    }
  }
}

export default new APIClient();
