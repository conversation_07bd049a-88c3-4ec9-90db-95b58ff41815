'use client';

import { useState, useEffect, useRef, createRef, useCallback } from 'react';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { cn } from '@/utils/cn';
import FilterCover from './FilterCover';
import StepsList from './StepsList';

import { RightLoading } from './RightLoading';
import { Step, SubStep } from '@/types/product-analysis';

interface LoadingProgressProps {
  isLoading: boolean;
  completedSteps: number;
  playbackSteps: Step[];
  fiilterCoverage?: string | null;
  link?: string;
  setIsLoading: (value: boolean) => void;
  videoIds: string[];
  setShowTitle?: (value: boolean) => void;
  picturesUrl: string[];
}

export const ProgressSharing = ({
  isLoading,
  completedSteps,
  playbackSteps,
  fiilterCoverage = null,
  setIsLoading,
  videoIds,
  setShowTitle,
  picturesUrl,
}: LoadingProgressProps) => {
  const [currentMainStep, setCurrentMainStep] = useState(0);
  const [currentSubStep, setCurrentSubStep] = useState(0);
  const [flattenedSteps, setFlattenedSteps] = useState<
    {
      mainIndex: number;
      subIndex: number | null;
      step: Step | SubStep;
      isSubStep: boolean;
    }[]
  >([]);
  const [currentFlatIndex, setCurrentFlatIndex] = useState(0);
  const [replayCompleted, setReplayCompleted] = useState(false);
  const lastUpdateTimeRef = useRef<number>(Date.now());
  const [isManualSwitching, setIsManualSwitching] = useState(false);
  const manualSwitchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastManualSwitchTimeRef = useRef<number>(Date.now());
  console.log('picturesUrl', picturesUrl);

  // 创建refs用于滚动到视图
  const stepRefs = useRef<{ [key: string]: React.RefObject<HTMLDivElement> }>({});
  const replayIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // 添加replayCompletedRef引用来解决闭包问题
  const replayCompletedRef = useRef(replayCompleted);
  // 实时更新replayCompletedRef引用的值
  useEffect(() => {
    replayCompletedRef.current = replayCompleted;
  }, [replayCompleted]);
  useEffect(() => {
    if (playbackSteps.length > 0) {
      const flattened: {
        mainIndex: number;
        subIndex: number | null;
        step: Step | SubStep;
        isSubStep: boolean;
      }[] = [];

      playbackSteps.forEach((step, mainIndex) => {
        // 添加主步骤
        flattened.push({
          mainIndex,
          subIndex: null,
          step,
          isSubStep: false,
        });

        // 添加子步骤
        if (step.sub_steps && step.sub_steps.length > 0) {
          step.sub_steps.forEach((subStep, subIndex) => {
            flattened.push({
              mainIndex,
              subIndex,
              step: subStep,
              isSubStep: true,
            });
          });
        }
      });

      setFlattenedSteps(flattened);
    }
  }, [playbackSteps]);

  // 添加组件卸载时的清理函数，重置到第一步
  useEffect(() => {
    return () => {
      setCurrentMainStep(0);
      setCurrentSubStep(0);
      setCurrentFlatIndex(0);
      setReplayCompleted(false);
      if (replayIntervalRef.current) {
        clearInterval(replayIntervalRef.current);
        replayIntervalRef.current = null;
      }
    };
  }, []);

  // 初始化refs - 直接使用 playbackSteps
  useEffect(() => {
    // 为每个步骤创建ref
    const refs: { [key: string]: React.RefObject<HTMLDivElement> } = {};

    playbackSteps.forEach((step, mainIndex) => {
      // 主步骤的ref
      refs[`main-${mainIndex}`] = createRef<HTMLDivElement>();

      // 子步骤的ref
      if (step && step.sub_steps && step.sub_steps.length > 0) {
        step.sub_steps.forEach((_, subIndex) => {
          refs[`sub-${mainIndex}-${subIndex}`] = createRef<HTMLDivElement>();
        });
      }
    });

    stepRefs.current = refs;
  }, [playbackSteps]);

  useEffect(() => {
    if (!isLoading) {
      setCurrentMainStep(0);
      return;
    }

    const targetMainStep = Math.min(completedSteps - 1, playbackSteps.length - 1);
    const targetFlatIndex = flattenedSteps.findIndex(
      (item) => item.mainIndex === targetMainStep && item.subIndex === null,
    );
    if (targetFlatIndex >= 0) {
      setCurrentFlatIndex(targetFlatIndex);
    }
  }, [isLoading, completedSteps, flattenedSteps, playbackSteps, replayCompleted]);

  // 更新 lastUpdateTimeRef
  useEffect(() => {
    lastUpdateTimeRef.current = Date.now();
  }, [currentMainStep, currentSubStep, currentFlatIndex]);

  const getMainStepMarkdownContent = (mainStep: Step, mainIndex: number) => {
    // 如果当前步骤的内容类型已经是markdown，直接返回
    if (mainStep.content_type === 'markdown') {
      return mainStep.content || '暂无数据';
    }
    // 如果当前步骤的编号大于已完成的步骤数，显示加载中
    if (mainStep.step_number > completedSteps) {
      return '文档准备中...';
    }

    // 根据步骤ID生成不同的markdown内容
    switch (mainStep.step_id) {
      case 'store_page_information':
        return (
          mainStep.content ||
          `## ①格式化参数：
          - **python分栋链接信息...**
          - **Host请求服务器...**
          - **product_url分析产品链接...**
          - **网站排序分析...**`
        );
      case 'product_analysis':
        return (
          mainStep.content ||
          `## ①大模型分析：
          - **文本解析...**
          - **意图识别...**
          - **模型推理计算...**
          - **多轮优化...**`
        );
      case 'template_information':
        return (
          mainStep.content ||
          `## ①API请求：
          - **API请求中...**
          - **HTTP请求...**`
        );
      case 'cloning_recommendations':
        const templateCount = Array.isArray(mainStep.processed_data?.template_info)
          ? mainStep.processed_data.template_info.length
          : 0;
        return `## 模板信息摘要
              系统已找到 ${templateCount} 个相关模板。
              *这些模板基于商品特点和市场定位进行筛选，可以作为您创建内容的参考。*`;
      default:
        return '正在处理数据...';
    }
  };

  const getCurrentStep = () => {
    if (flattenedSteps.length === 0) return playbackSteps[0];
    return flattenedSteps[currentFlatIndex]?.step;
  };

  const isCurrentStepMain = () => {
    if (flattenedSteps.length === 0) return true;
    return !flattenedSteps[currentFlatIndex]?.isSubStep;
  };
  // 修改renderContent函数，为主步骤强制使用markdown
  const renderContent = (step: Step | SubStep, isMainStep: boolean = false) => {
    // 如果是主步骤，强制使用markdown格式
    if (isMainStep) {
      return (
        <div className="relative">
          <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
            {getMainStepMarkdownContent(step as Step, step.step_number - 1)}
          </ReactMarkdown>
        </div>
      );
    }
    // 处理特定步骤显示对应的图片
    if (step.step_number === 4.2 && picturesUrl[0]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[0]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.3 && picturesUrl[1]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[1]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.4 && picturesUrl[2]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[2]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }

    if (step.step_number === 4.5 && picturesUrl[3]) {
      return (
        <div className="flex justify-center">
          <img
            src={picturesUrl[3]}
            alt={step.step_title}
            width={600}
            height={400}
            className="w-full rounded-md object-contain"
          />
        </div>
      );
    }
    // 子步骤使用原来的渲染逻辑
    switch (step.content_type) {
      case 'image':
        // 检查 content 是否是有效的 URL
        const imageUrl =
          step.content && (step.content.startsWith('/') || step.content.startsWith('http'))
            ? step.content
            : '/placeholder.svg';

        return (
          <div className="flex justify-center">
            <Image
              src={imageUrl}
              alt={step.step_title}
              width={600}
              height={400}
              className="w-full rounded-md object-contain"
            />
          </div>
        );
      case 'markdown':
        return (
          <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
            {step.content || '暂无数据'}
          </ReactMarkdown>
        );
      case 'animation':
        return (
          <div className="flex justify-center">
            <FilterCover
              cover={fiilterCoverage}
              isReplaying={false}
              videoIds={videoIds}
              onAnimationComplete={() => {
                setTimeout(() => {
                  setReplayCompleted(true);
                  // 仅当已完成的步骤达到4才设置isLoading为false
                  if (completedSteps >= 4) {
                    setIsLoading(false);
                    setShowTitle?.(false);
                  }
                }, 3000);
              }}
            />
          </div>
        );
      default:
        return <div>Unsupported content type</div>;
    }
  };
  useEffect(() => {
    // 当 currentMainStep 或 currentSubStep 变化时，更新 currentFlatIndex
    if (flattenedSteps.length > 0) {
      const targetIndex = flattenedSteps.findIndex(
        (item) =>
          item.mainIndex === currentMainStep &&
          (item.subIndex === currentSubStep || (item.subIndex === null && currentSubStep === 0)),
      );

      if (targetIndex >= 0) {
        setCurrentFlatIndex(targetIndex);
      }
    }
  }, [currentMainStep, currentSubStep, flattenedSteps]);
  const handleStepChange = useCallback(
    (mainIndex: number, subIndex: number | null) => {
      // 设置手动切换状态
      setIsManualSwitching(true);
      lastManualSwitchTimeRef.current = Date.now();

      // 更新当前主步骤和子步骤
      setCurrentMainStep(mainIndex);
      setCurrentSubStep(subIndex !== null ? subIndex : 0);
      console.log('flattenedSteps', flattenedSteps);
      // 查找对应的平铺索引并更新
      const targetFlatIndex = flattenedSteps.findIndex(
        (item) =>
          item.mainIndex === mainIndex && (item.subIndex === subIndex || (item.subIndex === null && subIndex === null)),
      );
      console.log('targetFlatIndex', targetFlatIndex);
      if (targetFlatIndex >= 0) {
        setCurrentFlatIndex(targetFlatIndex);
      } else {
        // 如果找不到精确匹配，尝试找到主步骤匹配的项
        const mainStepIndex = flattenedSteps.findIndex(
          (item) => item.mainIndex === mainIndex && item.subIndex === null,
        );
        if (mainStepIndex >= 0) {
          setCurrentFlatIndex(mainStepIndex);
        }
      }

      // 清除之前的超时
      if (manualSwitchTimeoutRef.current) {
        clearTimeout(manualSwitchTimeoutRef.current);
      }

      // 设置新的超时，2秒后解除手动切换状态
      manualSwitchTimeoutRef.current = setTimeout(() => {
        setIsManualSwitching(false);
      }, 2000);
    },
    [flattenedSteps],
  );

  if (!isLoading) return null;

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        // 'lg:bg-purple-500 xl:bg-orange-500 2xl:bg-red-500',
        'h-[345px] lg:h-[350px] xl:h-[430px] 2xl:h-[550px]',
        'overflow-auto py-6 pl-4 pr-6',
      )}
    >
      <div className="flex w-full flex-col gap-3 md:flex-row">
        <div className={cn('flex w-3/5 flex-col', 'h-[295px] lg:h-[300px] xl:h-[380px] 2xl:h-[485px]')}>
          <StepsList
            playbackSteps={playbackSteps}
            currentMainStep={currentMainStep}
            currentSubStep={currentSubStep}
            flattenedSteps={flattenedSteps}
            currentFlatIndex={currentFlatIndex}
            completedSteps={completedSteps}
            replayCompleted={replayCompleted}
            stepRefs={stepRefs}
            onStepChange={handleStepChange}
          />
        </div>
        <Card
          className={cn(
            'flex w-2/5 flex-col rounded-2xl border border-[#EFEDFD1A] bg-[#1B2435] p-4',
            'h-[295px] lg:h-[300px] xl:h-[380px] 2xl:h-[485px]',
          )}
        >
          <CardHeader className="mb-4 flex-shrink-0 p-0">
            <CardTitle className="text-left text-lg font-medium">【ROASMAX 分析展示】</CardTitle>
          </CardHeader>
          <RightLoading flattenedSteps={flattenedSteps} getCurrentStep={getCurrentStep} />
          <CardContent className="flex h-full flex-col justify-between p-0">
            <div
              className={cn(
                'grow overflow-auto p-4',
                'h-[calc(295px-290px)] lg:h-[calc(300px-282px)] xl:h-[calc(380px-282px)] 2xl:h-[calc(485px-282px)]',
                'rounded-lg border-[0.5px] border-[#363D54]',
              )}
            >
              {flattenedSteps.length > 0 && getCurrentStep() && renderContent(getCurrentStep()!, isCurrentStepMain())}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
