FROM docker-regsitry.tencentcloudcr.com/wuwang/node:18-alpine AS builder

RUN npm install -g pnpm --registry=https://mirrors.cloud.tencent.com/npm/

WORKDIR /app

COPY package.json .
COPY pnpm-lock.yaml .
COPY pnpm-workspace.yaml .
COPY turbo.json .
COPY .npmrc .

COPY apps/app/package.json apps/app/package.json
COPY packages packages/

RUN pnpm install --frozen-lockfile

COPY apps/app apps/app

RUN pnpm build --filter=@roasmax/app...

FROM docker-regsitry.tencentcloudcr.com/wuwang/node:18-alpine AS runner

WORKDIR /app

COPY --from=builder /app/apps/app/.next/standalone/apps/app .
COPY --from=builder /app/apps/app/.next/standalone/node_modules ./node_modules
COPY --from=builder /app/apps/app/.next/static .next/static
COPY --from=builder /app/apps/app/public ./public

EXPOSE 3000

CMD ["node", "server.js"]