@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  min-width: 1440px;
  overflow: hidden;
  overflow-x: auto;
  height: 100vh;
  background: #070f1f;
  pointer-events: auto !important;
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    <PERSON><PERSON>,
    'Helvetica Neue',
    <PERSON><PERSON>,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--tw-text-default);
}

.markdown {
  h1 {
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }
  h2 {
    font-size: 16px;
    font-weight: 600;
  }
  h3 {
    font-size: 14px;
    font-weight: 600;
  }
  h4 {
    font-size: 12px;
    font-weight: 600;
  }
  h5 {
    font-size: 12px;
    font-weight: 600;
  }
  strong {
    font-weight: 600;
    font-size: 14px;
  }
}
.markdownShouldShow {
  p {
    font-size: 12px;
    color: #fff;
    font-weight: 500;
    margin-top: 4px;
    margin-bottom: 2px;
  }
  h1 {
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 2px;
    margin-top: 12px;
    margin-left: 8px;
  }
  h2 {
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 2px;
    margin-top: 12px;
    margin-left: 8px;
  }
  ul {
    padding-left: 20px;
  }
  li {
    margin: 0px;
    height: 20px;
    strong {
      font-weight: 300;
      color: #9fa4b2;
      font-size: 12px;
    }
    margin-left: 8px;
  }
  p {
    font-size: 12px;
    color: #9fa4b2;
    font-weight: 300;
    margin-top: 1px;
    margin-bottom: 1px;
  }
}
body:not(.user-is-tabbing) button:focus,
body:not(.user-is-tabbing) input:focus,
body:not(.user-is-tabbing) select:focus,
body:not(.user-is-tabbing) textarea:focus {
  outline: none;
}

.bg-rainbow {
  background-image: linear-gradient(
    92.18deg,
    rgba(84, 255, 224, 0.1) 2.99%,
    rgba(0, 225, 255, 0.1) 22.61%,
    rgba(157, 129, 255, 0.1) 101.09%
  );
}

.bg-gradient-1 {
  border-color: transparent;
  background-clip: border-box;
  background-origin: border-box;
  background-image: linear-gradient(
    93.78deg,
    rgba(195, 180, 255, 0.4) 1.37%,
    rgba(195, 180, 255, 0) 50.14%,
    rgba(195, 180, 255, 0.4) 98.9%
  );
}

.custom-toast {
  height: 32px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #efedfd1a !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  color: #fff !important;
}
.rainbow-border {
  border-color: transparent;
  background-clip: border-box;
  background-origin: border-box;
  background-image: linear-gradient(106.54deg, #54ffe0 9.55%, #00e1ff 28.77%, #9d81ff 93.6%);
}

.rainbow-border-content {
  height: 100%;
  width: 100%;
  border-radius: inherit;
  background-image: linear-gradient(
    70.54deg,
    rgba(84, 255, 224, 0.1) 16.79%,
    rgba(0, 225, 255, 0.1) 31.93%,
    rgba(157, 129, 255, 0.1) 72.82%
  );
}
.rainbow-border1 {
  border-color: transparent;
  background-clip: border-box;
  background-origin: border-box;
  background-image: linear-gradient(
    97.1deg,
    rgba(195, 180, 255, 0.4) 3.8%,
    rgba(195, 180, 255, 0) 51.45%,
    rgba(195, 180, 255, 0.4) 99.1%
  );
}
.rainbow-border-avatar {
  border-color: transparent;
  background-clip: border-box;
  background-origin: border-box;
  background-image: linear-gradient(
    97.1deg,
    rgba(195, 180, 255, 0.4) 3.8%,
    rgba(195, 180, 255, 0) 51.45%,
    rgba(195, 180, 255, 0.4) 99.1%
  );
  -webkit-mask:
    linear-gradient(#fff 0 100%) content-box,
    linear-gradient(#fff 0 100%);
  -webkit-mask-composite: xor;
}

/* 导航栏圆角的渐变边框 中间镂空 */
.border-image-clip-path {
  border-color: transparent;
  background-origin: border-box;
  background-image: linear-gradient(
    165deg,
    rgba(0, 225, 255, 0.4) 17%,
    rgba(0, 225, 255, 0) 40%,
    rgba(0, 225, 255, 0) 80%
  );
  -webkit-mask:
    linear-gradient(#fff 0 100%) content-box,
    linear-gradient(#fff 0 100%);
  -webkit-mask-composite: xor;
}
/* 圆角的渐变边框 中间镂空 */
.rounded-gradient-hollow-out {
  border-color: transparent;
  background-clip: border-box;
  background-origin: border-box;
  background-image: linear-gradient(
    97.1deg,
    rgba(195, 180, 255, 0.4) 3.8%,
    rgba(195, 180, 255, 0) 51.45%,
    rgba(195, 180, 255, 0.4) 99.1%
  );
  -webkit-mask:
    linear-gradient(#fff 0 100%) content-box,
    linear-gradient(#fff 0 100%);
  -webkit-mask-composite: xor;
}
.golden-text {
  background-image: linear-gradient(88.65deg, #ffe1a5 16.05%, #ffb697 72.93%);
  color: transparent;
  background-clip: text;
}
.hovering > .mask-content {
  visibility: hidden;
  opacity: 0;
  transition: 0.2s;
}

.hovering:hover > .mask-content {
  visibility: visible;
  opacity: 1;
  transition: 0.2s;
}

.divider {
  color: #95a0aa99;
}

.divider.default > .start,
.divider.default > .end {
  background: rgba(54, 61, 84, 0.4);
}

.divider.gradation > .start {
  background: linear-gradient(270deg, rgba(54, 61, 84, 0.4) 80%, rgba(54, 61, 84, 0) 100%);
}

.divider.gradation > .end {
  background: linear-gradient(90deg, rgba(54, 61, 84, 0.4) 80%, rgba(54, 61, 84, 0) 100%);
}

@layer base {
  :root {
    --radius: 0.5rem;
    --sidebar-background: 222 89% 5%; /* #030A18 基础背景色 */
    --sidebar-foreground: 0 0% 100%; /* #FFFFFF 文字颜色 */
    --sidebar-primary: 183 89% 52%; /* #1BE5EC primary色 */
    --sidebar-primary-foreground: 0 0% 100%; /* #FFFFFF */
    --sidebar-accent: 225 21% 23%; /* #273048 accent色 */
    --sidebar-accent-foreground: 183 89% 52%; /* #1BE5EC accent前景色 */
    --sidebar-border: 228 21% 18%; /* #242938 边框色 */
    --sidebar-ring: 183 89% 52%; /* #1BE5EC 聚焦环色 */
    --sidebar-glass-accent-foreground: 0 0% 100%; /* #FFFFFF glass状态下hover和highlight的文字颜色 */
    --sidebar-glass-background: 204 221 255 / 0.1; /* rgba(204, 221, 255, 0.10) glass状态下hover和highlight的背景色 */
    --sidebar-glass-foreground: 220 24% 79%; /* #B7C1DD glass状态下默认文字颜色 */
  }
  *:focus {
    outline: none !important;
  }
  *:focus-visible {
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.5) !important;
  }
}

/* 整个滚动条 */
::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
  height: 4px; /* 滚动条高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #2f3543; /* 轨道背景 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #424d65; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
}

/* 滚动条滑块悬停 */
::-webkit-scrollbar-thumb:hover {
  background: #424d65; /* 悬停时的颜色 */
}

.highlight {
  position: relative;
  z-index: 9999;
  color: #fff;
  transition: outline 0.3s ease;
}
.highlight2 {
  position: fixed !important;
  z-index: 9999 !important;
  color: #00e1ff;
  font-size: 16px;
  transition: outline 0.3s ease;
}
.user-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.container1 {
  border: 1px solid #efedfd1a;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}
.line {
  position: absolute;
  /* border-radius: 6px; */
}
.line:nth-child(1) {
  top: 0;
  left: 0;
  width: 50%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #fff, rgb(160, 245, 250));
  animation: animate1 8s linear infinite;
}
@keyframes animate1 {
  0% {
    left: -100%;
  }
  50%,
  100% {
    left: 100%;
  }
}
.line:nth-child(2) {
  top: -100%;
  right: 0;
  width: 2px;
  height: 50%;
  background: linear-gradient(180deg, transparent, #fff, rgb(160, 245, 250));
  animation: animate2 8s linear infinite;
  /* 注意要加上延时触发动画效果，这样线条才会依次触发 */
  animation-delay: 2s;
}
@keyframes animate2 {
  0% {
    top: -100%;
  }
  50%,
  100% {
    top: 100%;
  }
}
.line:nth-child(3) {
  bottom: 0;
  right: 0;
  width: 50%;
  background: linear-gradient(270deg, transparent, #fff, rgb(160, 245, 250));
  animation: animate3 8s linear infinite;
  animation-delay: 4s;
}
@keyframes animate3 {
  0% {
    right: -100%;
    height: 2px;
  }
  50%,
  100% {
    height: 2px;
    right: 100%;
  }
}
.line:nth-child(4) {
  bottom: -100%;
  left: 0;
  width: 2px;
  height: 50%;
  background: linear-gradient(360deg, transparent, #fff, rgb(160, 245, 250));
  animation: animate4 8s linear infinite;
  animation-delay: 6s;
}
@keyframes animate4 {
  0% {
    bottom: -100%;
  }
  50%,
  100% {
    bottom: 100%;
  }
}
@layer components {
  .custom-border {
    @apply border-b-2 border-transparent;
  }
  .custom-border-active {
    border-image: linear-gradient(to right, transparent 33%, #00e1ff 33%, #00e1ff 66%, transparent 66%) 1;
  }
}
.table {
  table-layout: fixed;
  width: 100%;
}

.markdown-content {
  /* 基础样式 */
  @apply prose prose-sm max-w-none;

  /* 自定义样式 */
  & h1 {
    @apply mb-4 text-2xl font-bold;
  }

  & p {
    @apply mb-4;
  }

  & code {
    @apply rounded bg-gray-100 px-1 py-0.5;
  }

  & pre {
    @apply overflow-x-auto rounded-lg bg-gray-100 p-4;
  }

  & ul {
    @apply mb-4 list-disc pl-5;
  }

  & ol {
    @apply mb-4 list-decimal pl-5;
  }
}

.markdown-content-compact {
  /* 基础样式 */
  @apply prose prose-sm prose-invert max-w-none;

  /* 标题样式 - 减小间距 */
  & h1 {
    @apply mb-2 text-xl font-bold text-white;
  }

  & h2 {
    @apply mb-2 mt-3 text-lg font-semibold text-white/90;
  }

  & h3 {
    @apply mb-1.5 mt-2 text-base font-medium text-white/80;
  }

  /* 段落样式 - 减小间距 */
  & p {
    @apply mb-2 leading-snug text-white/70;
  }

  /* 列表样式 - 更紧凑 */
  & ul,
  & ol {
    @apply mb-2 space-y-0.5 pl-4 text-white/70;
  }

  & li {
    @apply leading-snug;
  }

  & ul {
    @apply list-disc;
  }

  & ol {
    @apply list-decimal;
  }

  /* 代码样式 */
  & code {
    @apply rounded bg-gray-800 px-1 py-0.5 text-sm text-gray-200;
  }

  & pre {
    @apply mb-2 overflow-x-auto rounded-lg bg-gray-800 p-2;
  }

  /* 去除最后一个元素的底部边距 */
  & > *:last-child {
    @apply mb-0;
  }
}

.swiper-pagination-bullet {
  background: #ffffff !important;
  background-opacity: 9 !important;
}
.swiper-pagination-bullet-active {
  background: #ffffff !important;
  width: 16px !important;
  border-radius: 8px !important;
}

.analyze svg path.stick {
  transform: translate(0);
  animation: stick 2s ease infinite;
  /* Change wand color */
}

.analyze svg path.star-1 {
  fill: #ff4500; /* OrangeRed color */
  animation:
    sparkles 2s ease infinite,
    scaleStars 2s ease infinite,
    colorChange 2s ease infinite;
  animation-delay: 150ms;
}

.analyze svg path.star-2 {
  fill: #00ff00; /* Lime color */
  animation:
    sparkles 2s ease infinite,
    scaleStars 2s ease infinite,
    colorChange 2s ease infinite;
}

.board {
  animation: bounce 2s ease infinite;
}

@keyframes sparkles {
  0% {
    opacity: 1;
  }
  35% {
    opacity: 1;
  }
  55% {
    opacity: 0;
  }
  75% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
}

@keyframes stick {
  0% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
  25% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
  50% {
    transform: translate3d(3px, -2px, 0) rotate(8deg);
  }
  75% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
  100% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
}

@keyframes scaleStars {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(-1px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes colorChange {
  0% {
    fill: #54ffe0;
  }
  50% {
    fill: #00e1ff;
  }
  100% {
    fill: #9d81ff;
  }
}

/* 自定义滑块样式 */
.custom-range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00e1ff;
  cursor: pointer;
}

.custom-range-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00e1ff;
  cursor: pointer;
  border: none;
}

.custom-range-slider::-ms-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00e1ff;
  cursor: pointer;
}
@keyframes dotsAnimation {
  0% {
    content: '.';
  }
  33% {
    content: '..';
  }
  66% {
    content: '...';
  }
  100% {
    content: '...';
  }
}

.dots::after {
  content: '.';
  display: inline-block;
  animation: dotsAnimation 2s infinite;
}
