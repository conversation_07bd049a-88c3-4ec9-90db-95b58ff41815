'use client';
import { Details, Edit, Guide, None, Points } from '@/components/icon';
import { Button } from '@/components/ui/Button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/HoverCard';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { useCloudStorageSize, useWallet } from '@/hooks/useWallet';
import { useWalletChangeLogs } from '@/hooks/useWalletChangeLogs';
import { getMeInfo, updateMe, updatePassword } from '@/services/actions/me';
import { action } from '@/utils/server-action/action';
import { zodResolver } from '@hookform/resolvers/zod';
import Cookies from 'js-cookie';
import { ChevronRight, Eye, EyeOff, HardDrive } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import * as z from 'zod';
import { UserAvatar } from './UserAvatar';
import UserLevel from './UserLevel';
import { cn } from '@/utils/cn';
import { useSidebar } from '@/components/ui/Sidebar';
import { Progress } from './ui';

const formatDate = (dateString: string | Date) => {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    throw new Error('');
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedDate;
};

const changeReasonMap: Record<string, string> = {
  VIDEO: '视频生成扣款',
  SET_MENU_OUT: '套餐更换转出',
  SET_MENU_IN: '套餐更换转入',
  GIVE: '赠送',
  REFUND: '退款',
  CHARGE: '充值',
};

const passwordSchema = z
  .object({
    currentPassword: z.string().min(1, '请输入原密码'),
    newPassword: z
      .string()
      .min(6, '密码至少需要6个字符')
      .max(8, '密码最多需要8个字符')
      .regex(
        // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/,
        /^[a-zA-Z0-9]{6,8}$/,
        '密码必须包含大小写字母、数字',
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: '新密码和确认密码不匹配',
    path: ['confirmPassword'],
  });

type PasswordFormData = z.infer<typeof passwordSchema>;

export type VipData = {
  id: string;
  tmp_created_at: Date;
  tmp_updated_at: Date;
  tenant_id: string;
  quota: number;
  plan_name: string;
  start_time: Date;
  end_time: Date;
};

export default function ButtonAvatar() {
  const { walletList, page, hasMore, noMoreData, activeButton, loading, refreshWalletChangeLog, handleButtonClick } =
    useWalletChangeLogs();
  const { quota, plan, refresh: refreshWallet } = useWallet();
  const { storage, refresh: refreshStorage } = useCloudStorageSize();
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [open, setOpen] = useState(false);
  const [openTitle, setOpenTitle] = useState('个人资料修改');
  const [isNickname, setIsNickname] = useState(false);
  const [isPassWord, setIsPassWord] = useState(false);
  const [user, setUser] = useState<{
    nickname: string;
    password: string;
    username: string;
    company: string;
    enabledSubSystems: string[];
  }>({
    nickname: '',
    password: '********',
    username: '', //账号
    company: '', // 公司
    enabledSubSystems: [],
  });
  const [originalNickname, setOriginalNickname] = useState(user.nickname);
  const { state } = useSidebar();

  const collapsed = state === 'collapsed';
  const formatQuota = (num: number): string => {
    if (num >= 10000) {
      // 处理万级别的数字，使用 Math.floor 向下取整
      const wan = Math.floor(num / 1000) / 10;
      return `${wan}w`;
    } else if (num >= 1000) {
      // 处理千级别的数字
      // 使用 Math.floor 向下取整后再除以 10，确保不会出现四舍五入的情况
      const qian = Math.floor(num / 100) / 10;
      return `${qian}k`;
    }

    // 小于1000的数字直接返回
    return num.toString();
  };

  const openCard = async (title: string) => {
    setOpenTitle(title);
    setOpen(true);
    if (title === '点数使用明细') {
      refreshWalletChangeLog(1, 'all');
      handleButtonClick('all');
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore && !loading) {
          refreshWalletChangeLog(page + 1, activeButton);
        }
      },
      {
        rootMargin: '100px',
      },
    );

    const lastItem = document.querySelector('#last-item');
    if (lastItem) {
      observer.observe(lastItem);
    }

    return () => {
      if (lastItem) {
        observer.unobserve(lastItem);
      }
    };
  }, [hasMore, loading, page, activeButton, refreshWalletChangeLog]);

  const renderButton = (type: string, label: string) => (
    <Button
      onClick={() => handleButtonClick(type)}
      className={`hover: bg-secondary mr-3 h-8 w-16 border border-[#EFEDFD] border-opacity-10 bg-opacity-10 text-[13px] hover:bg-[##CCDDFF] hover:text-white ${activeButton === type ? 'bg-[#B8DDFF] bg-opacity-10 text-white' : 'text-[#9FA4B2]'}`}
    >
      {label}
    </Button>
  );

  const onClose = () => {
    setOpen(false);
    setIsNickname(false);
    reset();
    if (openTitle === '修改密码') {
      setOpenTitle('个人资料修改');
      setOpen(true);
    }
  };
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    mode: 'onBlur',
  });
  const PasswordInput = ({
    id,
    label,
    placeholder,
    register,
    error,
  }: {
    id: 'currentPassword' | 'newPassword' | 'confirmPassword';
    label: string;
    placeholder: string;
    register: any;
    error?: string;
  }) => {
    const [show, setShow] = React.useState(false);
    return (
      <div className="mb-2 mt-2 space-y-2 text-xs font-normal text-[#9FA4B2]">
        <Label htmlFor={id}>
          <span className="text-red-500">*</span>&nbsp;&nbsp;
          {label}
        </Label>
        <div className="relative">
          <Input
            type={show ? 'text' : 'password'}
            id={id}
            placeholder={placeholder}
            className="h-10 border border-[#292E3E] bg-[#151C29] pr-10 focus:border-[#00E1FF]"
            {...register(id)}
          />
          <button
            type="button"
            onClick={() => setShow(!show)}
            className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-gray-600"
            aria-label={show ? 'Hide password' : 'Show password'}
          >
            {show ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </button>
        </div>
        {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
      </div>
    );
  };
  const onSubmit = async (data: PasswordFormData) => {
    setIsPassWord(true);
    const res = await action(updatePassword, { newPassword: data.confirmPassword, oldPassword: data.currentPassword });
    if (res) {
      toast.success('密码修改成功,请重新登录');
      onLogout();
    }
    setIsPassWord(false);
  };
  const handleNicknameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUser((prevUser) => ({
      ...prevUser,
      nickname: e.target.value,
    }));
  };
  const handleBlur = () => {
    setUser((prevUser) => {
      return {
        ...prevUser,
        nickname: originalNickname,
      };
    });
    setIsNickname(false);
  };

  const nameSubmit = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      if (user.nickname.length >= 2 && user.nickname.length <= 10) {
        try {
          const res = await action(updateMe, { nickname: user.nickname });
          if (res) toast.success('修改成功');
          setOriginalNickname(user.nickname); // 更新原始昵称为新的昵称
          refreshMeInfo();
        } catch (error) {
          toast.error('修改失败');
        }
        setIsNickname(false);
      } else {
        toast.error('请控制字符再2~8位');
      }
    }
  };

  const refreshMeInfo = async () => {
    const me = await action(getMeInfo, undefined);
    if (!me) return;
    refreshWallet();
    refreshStorage();
    setUser({
      nickname: me.userBaseData.nickname!,
      password: '********',
      username: me.userBaseData.username!,
      company: me.userCompany,
      enabledSubSystems: me.enabledSubSystems,
    });
  };

  const onLogout = async () => {
    localStorage.removeItem('token');
    localStorage.removeItem('BWAI_APP_CACHE');
    localStorage.removeItem('BWAI_USER_CACHE');
    Cookies.remove('BWAI_ACCESS_TOKEN');
    Cookies.remove('Authorization');

    router.push('/login');
  };
  const handleEditClick = () => {
    setOriginalNickname(user.nickname); // 保存原始昵称
    setIsNickname(true);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  useEffect(() => {
    if (isNickname && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isNickname]);
  useEffect(() => {
    refreshMeInfo();
  }, []);

  return (
    <div className="relative">
      <div
        className={cn(
          collapsed ? 'w-full' : 'rainbow-border-avatar h-[96px] w-full rounded-xl border', // 只在非折叠状态下添加 rainbow-border
        )}
      ></div>
      <div
        className={cn(
          'w-full rounded-xl bg-[#D9D9D91A] text-xs',
          collapsed ? 'p-0' : 'absolute bottom-0 left-0 border-none px-3 py-2', // 折叠时移除边框和内边距
        )}
      >
        <div className="relative">
          <div
            className={cn(
              'w-full',
              collapsed ? 'mx-auto bg-[#070F1F]' : 'w-2/3', // 折叠时居中显示头像
            )}
          >
            <HoverCard openDelay={300} closeDelay={300}>
              <HoverCardTrigger asChild>
                <div
                  className={cn(
                    'flex cursor-pointer items-center',
                    collapsed ? 'justify-center' : 'justify-between', // 折叠时居中
                  )}
                >
                  <div className="flex flex-shrink-0 items-center justify-center overflow-hidden rounded-full">
                    <UserAvatar />
                  </div>
                  {!collapsed && (
                    <>
                      {user.nickname ? (
                        <div className="... text-primary ml-2 w-full truncate text-[13px] font-medium">
                          {user.nickname}
                        </div>
                      ) : (
                        <div className="ml-2 h-5 w-[40px] bg-gray-700/50"></div>
                      )}
                      <ChevronRight className="mt-[2px] h-6 w-6 text-[#B7C1DD]" />
                    </>
                  )}
                </div>
              </HoverCardTrigger>
              <HoverCardContent className="w-[236px] rounded-xl border border-[#fff] border-opacity-10 bg-[#262F40] p-0 pb-5 pl-4 pr-4 pt-5 backdrop-blur-3xl backdrop-filter">
                <div className="mb-7 flex cursor-pointer items-center" onClick={() => openCard('个人资料修改')}>
                  <div className="h-10 w-10">
                    <UserAvatar />
                  </div>
                  <div className="ml-3">
                    <div className="mb-1 flex items-center justify-between">
                      <span className="... text-primary w-32 truncate text-base font-normal">{user.nickname}</span>
                      <span className="flex-shrink-0">
                        <Edit />
                      </span>
                    </div>
                    <div className="... truncate text-sm text-[#9FA4B2]">ID : {user.username}</div>
                  </div>
                </div>
                {user.enabledSubSystems.includes('运营中台') && (
                  <>
                    <div
                      className="text-primary mb-6 flex items-center text-sm"
                      onClick={() => router.push('/distributions/goods')}
                    >
                      <Details />
                      <div className="ml-4 cursor-pointer">运营中台</div>
                    </div>
                    <div className="text-primary mb-6 flex items-center text-sm">
                      <Guide />
                      <Link
                        className="ml-4 cursor-pointer hover:text-white"
                        href="https://g-ldyi2063.coding.net/api/user/g-ldyi2063/project/dev/artifacts/repositories/24348666/packages/16605625/versions/47339674/file/download?fileName=boWongWebAssistant.zip"
                      >
                        浏览器插件下载
                      </Link>
                    </div>
                    <div className="text-primary mb-6 flex items-center text-sm">
                      <Guide />
                      <Link
                        className="ml-4 cursor-pointer hover:text-white"
                        href="https://g-ldyi2063-generic.pkg.coding.net/dev/test/bw-file-handler-setup.exe?version=latest"
                      >
                        文件资源管理器驱动下载
                      </Link>
                    </div>
                  </>
                )}
                <div className="text-primary mb-6 flex items-center text-sm">
                  <HardDrive className="h-[19px] w-[19px]" color="#81889D" />
                  <Link className="ml-4 cursor-pointer hover:text-white" href="/cloud">
                    我的云盘
                  </Link>
                </div>
                <div className="text-primary mb-6 flex items-center text-sm">
                  <Guide />
                  <a
                    className="ml-4 cursor-pointer hover:text-white"
                    href="https://ovz19pqydwa.feishu.cn/wiki/LaW5wVPPui0Plmkese5cCEninkf"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    使用指南
                  </a>
                </div>
                <div className="mb-5 w-52 border border-[#424A61] border-opacity-30 text-sm text-[#9FA4B2]"></div>
                <div className="flex cursor-pointer justify-center text-sm text-[#9FA4B2]" onClick={onLogout}>
                  退出登录
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
          {!collapsed && (
            <>
              <div className="absolute -right-3 -top-2 cursor-pointer rounded-bl-xl rounded-tr-xl bg-[#D9D9D91A]">
                <UserLevel userLevel={plan || undefined} formatDate={formatDate} />
              </div>
              <div className="flex items-center justify-between pb-[6px] pt-5">
                <div className="flex justify-between">
                  <div className="font-normal text-[#B2B6C3]">
                    {storage.used === undefined || storage.total === undefined ? (
                      <>
                        <div className="h-3 w-12 animate-pulse rounded bg-gray-700"></div>
                        <div className="mt-1 h-[6px] w-16 animate-pulse rounded bg-gray-700"></div>
                      </>
                    ) : (
                      <>
                        <div>
                          {(storage.used / 1024).toFixed(1)}GB/{Math.floor(storage.total / 1024).toFixed()}GB
                        </div>
                        <Progress
                          className="mt-1 bg-[#D9D9D9] bg-opacity-10"
                          value={storage.total ? (storage.used / storage.total) * 100 : 0}
                          color="#00e1ff"
                        />
                      </>
                    )}
                  </div>
                </div>
                <div className="h-3 border border-[#363D54]"></div>
                <div className="flex items-center justify-between" onClick={() => openCard('点数使用明细')}>
                  <Points />
                  <div className="text-primary mb-[2px] ml-2 cursor-pointer font-medium">{formatQuota(quota ?? 0)}</div>
                </div>
              </div>
            </>
          )}
        </div>
        <Dialog open={open} onOpenChange={() => onClose()}>
          <DialogContent
            className={`text-primary rounded-2xl border-none bg-[#151C29] ${openTitle === '点数使用明细' ? 'w-[720px] p-0 pb-7' : openTitle === '修改密码' ? 'w-96 pb-6 pl-8 pr-8 pt-6' : 'w-auto pb-6 pl-8 pr-8 pt-6'}`}
          >
            <DialogHeader>
              <DialogTitle
                className={`text-primary flex items-center justify-center text-base font-normal ${openTitle === '点数使用明细' ? 'mb-8 mt-6' : openTitle === '修改密码' ? 'mb-4' : 'mb-8'}`}
              >
                {openTitle}
              </DialogTitle>
              <DialogDescription>
                {openTitle === '个人资料修改' ? (
                  <div>
                    <div className="mb-5 flex h-10 items-center justify-between">
                      <span className="text-white">头像</span>
                      <span className="ml-4 w-64">
                        <UserAvatar />
                      </span>
                    </div>
                    <div className="mb-5 flex items-center justify-between">
                      <span className="text-white">昵称</span>
                      {isNickname ? (
                        <div className="h-10">
                          <Input
                            ref={inputRef}
                            value={user.nickname}
                            className="h-10 w-64 border border-[#292E3E] bg-[#151C29] focus:border-[#00E1FF]"
                            onChange={handleNicknameChange}
                            onKeyPress={nameSubmit}
                            onBlur={handleBlur}
                          />
                        </div>
                      ) : (
                        <span className="flex h-5 w-64 items-center justify-between leading-8 text-[#9FA4B2]">
                          <span>{user.nickname}</span>
                          <span className="cursor-pointer" onClick={handleEditClick}>
                            <Edit />
                          </span>
                        </span>
                      )}
                    </div>
                    <div className="mb-5 flex h-5 items-center justify-between">
                      <span className="text-white">密码</span>
                      <span className="flex h-8 w-64 justify-between leading-8 text-[#9FA4B2]">
                        <span>{user.password}</span>
                        <span className="cursor-pointer" onClick={() => openCard('修改密码')}>
                          <Edit />
                        </span>
                      </span>
                    </div>
                    <div className="mb-5 flex h-5 items-center justify-between">
                      <span className="text-white">账号id</span>
                      <span className="h-8 w-64 leading-8 text-[#9FA4B2]">{user.username}</span>
                    </div>
                    <div className="mb-4 flex h-5 items-center justify-between">
                      <span className="text-white">所属公司</span>
                      <span className="ml-6 h-8 w-64 leading-8 text-[#9FA4B2]">{user.company}</span>
                    </div>
                  </div>
                ) : openTitle === '修改密码' ? (
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 font-normal text-[#9FA4B2]">
                    <PasswordInput
                      id="currentPassword"
                      label="原密码"
                      placeholder="请输入原密码"
                      register={register}
                      error={errors.currentPassword?.message}
                    />
                    <PasswordInput
                      id="newPassword"
                      label="新密码"
                      placeholder="请输入新密码"
                      register={register}
                      error={errors.newPassword?.message}
                    />
                    <PasswordInput
                      id="confirmPassword"
                      label="确认新密码"
                      placeholder="请确认新密码"
                      register={register}
                      error={errors.confirmPassword?.message}
                    />
                    <div className="flex w-full justify-between">
                      <Button
                        type="button"
                        onClick={onClose}
                        className="hover:bg-secondary-hover mr-1 mt-6 w-1/2 rounded-lg bg-[#CCDDFF] bg-opacity-10 text-sm font-medium text-white"
                      >
                        取消
                      </Button>
                      <Button
                        type="submit"
                        className="ml-1 mt-6 w-1/2 rounded-lg text-sm font-medium text-[#111111]"
                        disabled={isPassWord}
                      >
                        确认修改
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div>
                    <div className="ml-8">
                      {renderButton('all', '全部')}
                      {renderButton('consume', '消耗')}
                      {renderButton('obtain', '获得')}
                    </div>
                    <div id="wallet-scroll-container" className="ml-8 h-[calc(100vh-70vh)] overflow-auto">
                      <ul>
                        {walletList.length === 0 ? (
                          <div className="flex h-[calc(100vh-70vh)] flex-col items-center justify-center text-sm">
                            <div className="mb-5">
                              <None />
                            </div>
                            <div>暂无使用明细～</div>
                          </div>
                        ) : (
                          walletList.map((item, index) => (
                            <li
                              key={`${item.id}-${index}`}
                              className="mt-6 pr-7 text-sm"
                              id={index === walletList.length - 1 ? 'last-item' : undefined}
                            >
                              <div className="mb-1 flex justify-between text-sm leading-5">
                                <div className="text-white">
                                  {item.change_reason ? item.change_reason : changeReasonMap[item.change_type]}
                                </div>
                                <div className={item.quota > 0 ? 'text-[#00E1FF]' : 'text-white'}>{item.quota}</div>
                              </div>
                              <div className="flex justify-between text-xs leading-4 text-[#95A0AA]">
                                <div>{formatDate(item.tmp_updated_at)}</div>
                                <div>{item.result_quota}</div>
                              </div>
                            </li>
                          ))
                        )}
                        {loading && <div className="text-center">加载中...</div>}
                        {noMoreData && !loading && (
                          <div className="text-center text-sm text-gray-500">没有更多数据了</div>
                        )}
                      </ul>
                    </div>
                  </div>
                )}
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
