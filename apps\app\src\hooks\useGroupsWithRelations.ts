import { useCurrentAdvertiser, useCurrentCampaign, useCurrentAdGroup, useCurrentView } from '@/store/ads/adStore';
import { useMemo } from 'react';

interface GroupRelation {
  groupId: string;
  groupName: string;
  campaignId: string;
  campaignName: string;
  advertiserId: string;
  advertiserName: string;
}

interface CampaignRelation {
  campaignId: string;
  campaignName: string;
  advertiserId: string;
  advertiserName: string;
}

interface AdvertiserRelation {
  advertiserId: string;
  advertiserName: string;
}

export const useGroupsWithRelations = () => {
  const currentView = useCurrentView();
  const currentAdvertiser = useCurrentAdvertiser();
  const currentCampaign = useCurrentCampaign();
  const currentAdGroup = useCurrentAdGroup();

  return useMemo(() => {
    if (!currentCampaign || !currentAdvertiser) {
      return {
        groupRelations: [],
        campaignRelations: [],
        advertiserRelations: [],
      };
    }

    // 组级别关系（包含组、系列、账户信息）
    const groupRelations: GroupRelation[] = currentAdGroup?.map((group) => {
      const campaign = currentCampaign.find((c) => c.campaignId === group.campaignId);
      const advertiser = currentAdvertiser.find((a) => a.advertiserId === group.advertiserId);

      return {
        groupId: group.groupId,
        groupName: group.groupName,
        campaignId: group.campaignId,
        campaignName: campaign?.campaignName || '',
        advertiserId: group.advertiserId,
        advertiserName: advertiser?.advertiserName || '',
      };
    }) ?? [];

    // 系列级别关系（包含系列、账户信息）
    const campaignRelations: CampaignRelation[] = Array.from(
      new Set(currentCampaign.map((campaign) => campaign.campaignId)),
    ).map((campaignId) => {
      const campaign = currentCampaign.find((c) => c.campaignId === campaignId);
      const advertiser = currentAdvertiser.find((a) => a.advertiserId === campaign?.advertiserId);

      return {
        campaignId: campaign?.campaignId || '',
        campaignName: campaign?.campaignName || '',
        advertiserId: campaign?.advertiserId || '',
        advertiserName: advertiser?.advertiserName || '',
      };
    });

    // 账户级别关系
    const advertiserRelations: AdvertiserRelation[] = Array.from(
      new Set(currentAdvertiser.map((advertiser) => advertiser.advertiserId)),
    ).map((advertiserId) => {
      const advertiser = currentAdvertiser.find((a) => a.advertiserId === advertiserId);

      return {
        advertiserId: advertiser?.advertiserId || '',
        advertiserName: advertiser?.advertiserName || '',
      };
    });

    return {
      groupRelations,
      campaignRelations,
      advertiserRelations,
    };
  }, [currentAdGroup, currentCampaign, currentAdvertiser, currentView]);
};
