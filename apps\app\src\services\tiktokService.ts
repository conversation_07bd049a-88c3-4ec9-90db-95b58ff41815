import type { PaginatedResponse } from '@/types/api';
import type {
  IProductListRequest,
  IUploadImageRequest,
  IUploadImageResponse,
  IUploadVideoRequest,
  IUploadVideoResponse,
} from '@/types/ads';
import apiClient from '@/utils/fetcher/apiClient';
import {
  CampaignListReq,
  AdGroupListRequest,
  SyncAdGroupListRequest,
  IBcAssetsListRequest,
  IProjectRequest,
  CustomAudienceListRequest,
  UpdateAdGroupRequest,
  CreateAdGroupRequest,
  UpdateAdGroupStatusRequest,
  GetInterestListRequest,
  GetTargetingRegionListsRequest,
  AdListRequest,
  ExportAdGroupListRequest,
  ExportAdListRequest,
  AdvertiserListReq,
  SyncCampaignListReq,
  CreateCampaignReq,
  UpdateAdStatusReq,
  SyncAdListRequest,
} from './interfaces/ads/req';
import {
  CampaignListItem,
  AdvertiserListResponse,
  AdGroupInfoResponse,
  AdInfoResponse,
  IAdGroupInfoResponse,
  AdGroupTemplateResponse,
  GetTkAccountResponse,
  ProductListResponse,
  AdInfoResponseCamel,
  CampaignListResponseCamel,
  ProductListResponseWrapper,
  ExcellentAdsResponse,
} from './interfaces/ads/res';
import { ToCamelCase } from '@/utils/camel';
export enum TiktokApi {
  getTkAccount = '/getTkAccount',
  getAdvertiserList = '/getTkAccountList',
  ProductList = '/getProductItemList',
  AdvertiserInfo = '/getAdInfo',
  AdGroup = '/listAdGroup',
  SyncyncAdGroup = '/sync/listAdGroup',
  CreateAdGroup = '/createAdGroup',
  UpdateBcAssetsList = '/updateAdvertiserStatus',
  UpdateAdGroupBudget = '/updateAdvertiserBudget',
  UpdateAdvertiser = '/updateAdGroup',
  GetCustomAudience = '/getCustomAudience/list',
  BcAssetsList = '/getBcAssetsList',
  ProjectList = '/getProjectList',
  Ad = '/adList',
  CreateAd = '/createAd',
  FastCreateAd = '/fast/createAd',
  UpdateAdStatus = '/updateAdStatus',
  UpdateAd = '/updateAd',
  getCampaignList = '/listCampaign',
  sycnCampaignList = '/syncCampaign',
  CreateCampaign = '/createCampaign',
  UpdateCampaign = '/updateCampaign',
  UpdateOperationStatus = '/updateStatus',
  UploadVideoToTikTokMaterialLibrary = '/fileUpload',
  UploadImageToTikTokMaterialLibrary = '/imageUpload',
  IdentityList = '/getIdentity',
  AdExportAd = '/export/adList',
  AdGroupExportAd = '/export/adGroupList',
  AdRealTimeData = '/sync/adList',
  GetInterestList = '/getTargetingSearch',
  GetTargetingRegionLists = '/tool/region',
  AdGroupTemplate = '/adGroup/template',
  CreateAdGroupTemplate = '/createAdGroup/template',
  ExcellentAds = '/recommend/getDeliveryAnalysis',
  startSyncRecommend = '/recommend/startSyncRecommend',
  RecommendedBudget = '/recommend/updateBudget',
}

const getTkAccount = (params: { authCode: string }) =>
  apiClient.get<GetTkAccountResponse>({
    url: TiktokApi.getTkAccount,
    params,
  });

const getAdvertiserList = (params: AdvertiserListReq) =>
  apiClient.get<AdvertiserListResponse>({
    url: TiktokApi.getAdvertiserList,
    params,
  });

// 广告组相关服务
const getAdGroupList = (params: AdGroupListRequest) => {
  const requestParams: Record<string, any> = {};

  if (params?.advertiser_ids) {
    requestParams.advertiser_ids = params?.advertiser_ids.join(',');
  }
  if (params?.campaign_ids && params?.campaign_ids?.length > 0) {
    // 修正campaign_id的处理方式
    requestParams.campaign_ids = params?.campaign_ids?.join(',');
  }
  if (params?.page) {
    requestParams.page = params.page;
  }
  if (params?.page_size) {
    requestParams.page_size = params.page_size;
  }
  if (params?.tag_names) {
    requestParams.tag_names = params.tag_names;
  }
  if (params?.start_time) {
    requestParams.start_time = params.start_time;
  }
  if (params?.end_time) {
    requestParams.end_time = params.end_time;
  }
  if (params?.operation_status) {
    requestParams.operation_status = params.operation_status;
  }
  return apiClient.get<ToCamelCase<PaginatedResponse<IAdGroupInfoResponse>>>({
    url: TiktokApi.AdGroup,
    params: requestParams,
  });
};
const getSyncAdGroupList = (params: Array<SyncAdGroupListRequest>) => {
  return apiClient.post({
    url: TiktokApi.SyncyncAdGroup,
    data: params,
  });
};
const getBcAssetsList = (params?: IBcAssetsListRequest) => apiClient.get({ url: TiktokApi.BcAssetsList, params });

const getProjectList = (params?: IProjectRequest) => apiClient.get({ url: TiktokApi.ProjectList, params });

//受众类别
const getCustomAudience = (params?: CustomAudienceListRequest) =>
  apiClient.get({
    url: TiktokApi.GetCustomAudience,
    params,
  });
const createAdGroup = (data: Partial<CreateAdGroupRequest[]>) => apiClient.post({ url: TiktokApi.CreateAdGroup, data });
// 更新广告组
const updateAdGroup = (data: Partial<UpdateAdGroupRequest>) =>
  apiClient.post<AdGroupInfoResponse>({ url: `${TiktokApi.UpdateAdvertiser}`, data });
//更新广告组状态
const updateAdGroupStatus = (data: UpdateAdGroupStatusRequest) =>
  apiClient.post<{ status: string; adgroup_ids: string[] }>({
    url: `${TiktokApi.UpdateBcAssetsList}`,
    data,
  });
//兴趣列表
const getInterestList = (params: GetInterestListRequest) =>
  apiClient.get({
    url: TiktokApi.GetInterestList,
    params,
  });
// 获取地域
const getTargetingRegionLists = (params: GetTargetingRegionListsRequest) => {
  const requestParams: Record<string, any> = {};
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'placements') {
          requestParams[key] = JSON.stringify(value);
        } else {
          requestParams[key] = value;
        }
      }
    });
  }
  return apiClient.get({
    url: TiktokApi.GetTargetingRegionLists,
    params: requestParams,
  });
};
// 广告组模板
const getAdGroupTemplate = (params: { advertiser_ids: string[] }) => {
  return apiClient.get<AdGroupTemplateResponse>({
    url: TiktokApi.AdGroupTemplate,
    params,
  });
};
// 模板创建广告组
const createAdGroupTemplate = (data: { campaign_ids: string[]; template_id: string }) => {
  return apiClient.post<AdGroupTemplateResponse>({
    url: TiktokApi.CreateAdGroupTemplate,
    data,
  });
};

const getAdList = (params: Partial<AdListRequest>) => {
  return apiClient.get<AdInfoResponseCamel>({ url: TiktokApi.Ad, params });
};
//同步广告
const getAdRealTimeData = (params?: SyncAdListRequest) => {
  return apiClient.get({
    url: TiktokApi.AdRealTimeData,
    params,
  });
};
// 导出广告
const getExportAdList = (params: ExportAdListRequest) => {
  const requestParams = new URLSearchParams();
  if (params?.advertiser_id) {
    requestParams.append('advertiser_id', params.advertiser_id);
  }
  if (params?.ad_name) {
    requestParams.append('ad_name', params.ad_name);
  }
  return apiClient.download({
    url: TiktokApi.AdExportAd,
    params: requestParams,
  });
};

//导出广告组列表
const getExportAdGroupList = (params: ExportAdGroupListRequest) => {
  const requestParams: Record<string, any> = {};

  if (params?.advertiser_ids) {
    requestParams.advertiser_ids = params.advertiser_ids.join(',');
  }
  if (params?.campaign_ids) {
    requestParams.campaign_ids = params.campaign_ids.join(',');
  }
  if (params?.adgroup_name) {
    requestParams.adgroup_name = params.adgroup_name;
  }
  if (params?.operation_status) {
    requestParams.operation_status = params.operation_status;
  }
  if (params?.adgroup_id) {
    requestParams.adgroup_id = params.adgroup_id;
  }
  if (params?.tag_names) {
    requestParams.tag_names = params.tag_names;
  }
  if (params?.start_time) {
    requestParams.start_time = params.start_time;
  }
  if (params?.end_time) {
    requestParams.end_time = params.end_time;
  }
  if (params?.page) {
    requestParams.page = params.page;
  }
  if (params?.page_size) {
    requestParams.page_size = params.page_size;
  }
  return apiClient.download({
    url: TiktokApi.AdGroupExportAd,
    params: requestParams,
  });
};

const createAd = (data: unknown) => apiClient.post({ url: TiktokApi.CreateAd, data });

const fastCreateAd = (data: unknown) => apiClient.post({ url: TiktokApi.FastCreateAd, data });

const updateAd = (data: any) =>
  apiClient.post<{
    ad_ids: string[];
    creatives: AdInfoResponse[];
  }>({ url: TiktokApi.UpdateAd, data });

const updateAdStatus = (data: UpdateAdStatusReq) => apiClient.post({ url: `${TiktokApi.UpdateAdStatus}`, data });

const getCampaignList = (params?: Partial<CampaignListReq>) => {
  return apiClient.get<CampaignListResponseCamel>({
    url: TiktokApi.getCampaignList,
    params,
  });
};

const getSyncCampaignList = (params: SyncCampaignListReq) => {
  return apiClient.post({
    url: TiktokApi.sycnCampaignList,
    data: params,
  });
};

// todo: 需要修改
const createCampaign = (data: Partial<CreateCampaignReq>) =>
  apiClient.post<CampaignListItem>({ url: TiktokApi.CreateCampaign, data });

// todo: 需要修改
const updateCampaign = (data: Partial<CreateCampaignReq>) =>
  apiClient.post<CampaignListItem>({ url: TiktokApi.UpdateCampaign, data });

const updateOperationStatus = (data: {
  ids: string[];
  advertiser_id: string;
  operation_status: 'ENABLE' | 'DISABLE' | 'DELETE';
}) => apiClient.post<CampaignListItem>({ url: TiktokApi.UpdateOperationStatus, data });

const getProductList = (params: IProductListRequest) =>
  apiClient.get<ToCamelCase<ProductListResponseWrapper>>({ url: TiktokApi.ProductList, params });

const uploadVideoToTikTokMaterialLibrary = (data: IUploadVideoRequest) =>
  apiClient.post<IUploadVideoResponse[]>({
    url: TiktokApi.UploadVideoToTikTokMaterialLibrary,
    data,
  });

const uploadImageToTikTokMaterialLibrary = (data: IUploadImageRequest) =>
  apiClient.post<IUploadImageResponse>({
    url: TiktokApi.UploadImageToTikTokMaterialLibrary,
    data,
  });

const getIdentityList = (params: {
  advertiser_id: string;
  identity_type?: string;
  identity_authorized_bc_id?: string;
}) => apiClient.get({ url: TiktokApi.IdentityList, params });
const getExcellentAds = () => apiClient.post<ExcellentAdsResponse>({ url: TiktokApi.ExcellentAds });
const startSyncRecommend = () => apiClient.post({ url: TiktokApi.startSyncRecommend });
const getRecommendedBudget = (params: { id: string }) => apiClient.get({ url: TiktokApi.RecommendedBudget, params });
const tiktokService = {
  // 账户
  getTkAccount,
  getAdvertiserList,
  getIdentityList,
  // 广告组
  getAdGroupList,
  getSyncAdGroupList,
  getBcAssetsList,
  getProjectList,
  createAdGroup,
  updateAdGroup,
  updateAdGroupStatus,
  getCustomAudience,
  getInterestList,
  getAdGroupTemplate,
  createAdGroupTemplate,
  // 广告
  getAdList,
  getAdRealTimeData,
  createAd,
  fastCreateAd,
  updateAd,
  updateAdStatus,
  // 广告系列
  getCampaignList,
  getSyncCampaignList,
  createCampaign,
  updateCampaign,
  updateOperationStatus,
  getTargetingRegionLists,
  // 产品
  getProductList,
  // 上传
  uploadVideoToTikTokMaterialLibrary,
  uploadImageToTikTokMaterialLibrary,
  //导出
  getExportAdList,
  getExportAdGroupList,
  //优秀广告组推荐
  getExcellentAds,
  //开启投放
  startSyncRecommend,
  //推荐预算
  getRecommendedBudget,
};
export default tiktokService;
