# 依赖目录
node_modules
**/node_modules
.pnpm-store
.turbo

# 构建输出
dist
build
.next
out

# 开发工具和配置
.git
.github
.vscode
.idea
.DS_Store
*.log
coverage
.coverage
.nyc_output

# 环境文件
.env*
!.env.example

# 测试文件
**/*.test.ts
**/*.spec.ts
**/__tests__
**/__mocks__

# 文档和其他资源
docs
*.md
LICENSE
README.md

# 调试文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 缓存目录
.cache
.eslintcache
.tsbuildinfo
tsconfig.tsbuildinfo

# 其他不需要的文件
*.local
*.bak
*.tmp
*.swp