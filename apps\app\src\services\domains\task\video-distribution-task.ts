import { Prisma } from '@roasmax/database';
import { ActionContext } from '@roasmax/serve';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';

/**
 * 获取指定分发任务的待分发视频
 * @param ctx
 * @returns
 */
export const listVideoDistributionSubTasksByTaskIds = async (ctx: ActionContext<{ taskIds?: string[] }>) => {
  const tasks = await ctx.db.video_distribution_tasks.findMany({
    where: { id: { in: ctx.data.taskIds }, goods: { ip: { not: null } } },
    include: { goods: true },
  });

  const sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
    where: {
      task_id: null,
      status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片,
      OR: tasks.map((t) => ({ ip: t.goods.ip!, goods_name: t.goods.name })),
    },
  });

  // 子任务关联的视频
  const materials = await ctx.db.materials.findMany({
    where: { id: { in: [...sub_tasks.map((st) => st.material_id!).filter(Boolean)] } },
  });

  return sub_tasks.map((st) => {
    const material = materials.find((m) => m.id === st.material_id);
    return { ...st, material };
  });
};

export const pageVideoDistributionSubTasks = async (
  ctx: ActionContext<{
    pagination: { page: number; pageSize: number };
    filters: {
      shouldPublish?: boolean;
      taskIds?: string[];
      statuses?: VIDEO_DISTRIBUTION_SUB_TASK_STATUS[];
      douyinAccountId?: string;
    };
  }>,
) => {
  const { page, pageSize } = ctx.data.pagination;

  const where: Prisma.video_distribution_sub_tasksWhereInput = {};
  if (ctx.data.filters.taskIds) {
    where.task_id = { in: ctx.data.filters.taskIds };
  }
  if (ctx.data.filters.statuses) {
    where.status = { in: ctx.data.filters.statuses.map((s) => s.toString()) };
  }
  if (ctx.data.filters.douyinAccountId) {
    const socialAccount = await ctx.db.social_accounts.findFirst({ where: { id: ctx.data.filters.douyinAccountId } });
    if (!socialAccount) {
      throw new Error(`未找到抖音账号 ${ctx.data.filters.douyinAccountId}`);
    }
    where.social_account_id = socialAccount.id;
  }
  if (ctx.data.filters.shouldPublish) {
    where.status = VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已发布;
    where.publish_plan_at = { lte: new Date() };
  }

  const [total, sub_tasks] = await ctx.db.$transaction([
    ctx.db.video_distribution_sub_tasks.count({ where }),
    ctx.db.video_distribution_sub_tasks.findMany({ where, skip: (page - 1) * pageSize, take: pageSize }),
  ]);

  return { pagination: { total, page, pageSize }, list: sub_tasks };
};
