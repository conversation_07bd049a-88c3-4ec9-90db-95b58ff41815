{"$schema": "https://turbo.build/schema.json", "ui": "stream", "globalEnv": ["TASK_POOL_MODE", "BULLMQ_ENDPOINT", "BULLMQ_AUTHORIZATION", "LANGFUSE_HOST", "LANGFUSE_RPC_EMAIL", "LANGFUSE_RPC_PASSWORD", "VOD_SECRETKEY", "VOD_SECRETID", "APPHOST", "APPID", "APPSECRET", "PRIVATE_ACCESSKEYID_ID", "PRIVATE_ACCESSKEYSECRET_ID", "FEISHU_NOTIFY_CALLBACK_URL", "VIDEO_DISTRIBUTION_HOST", "PUBLIC_SHARE_LINK_HOST", "COS_SECRET_ID", "COS_SECRET_KEY", "COS_REGION", "COS_BUCKET", "COS_APPID", "WEBHOOK_SECRET", "DIFY_WORKFLOW_KEY", "COS_PUSH_HOST", "SCF_PORT", "LANGFUSE_BASEURL", "VOD_SECRET_ID", "VOD_SECRET_KEY", "GENERATION_TASK_POOL_SIZE"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}}}