import { TaskStatus } from '@/types/task';
import { video_slice_tasks } from '@roasmax/database';
import { ActionContext } from '@roasmax/serve';

export const createVideoSliceTask = async (
  ctx: ActionContext<{ name: string; materialId: string; slice_duration: number }>,
) => {
  if (ctx.data.slice_duration !== 300) {
    throw new Error('仅支持 300s 的切片任务');
  }

  // 如果已经存在相同的任务，则直接返回
  const existTask = await ctx.db.video_slice_tasks.findMany({
    where: { origin_material_id: ctx.data.materialId },
    orderBy: { tmp_created_at: 'desc' },
  });
  if (existTask[0]) return existTask[0];

  return await ctx.db.video_slice_tasks.create({
    data: {
      tenant_id: ctx.tenant.id,
      user_id: ctx.user.id,
      name: ctx.data.name,
      origin_material_id: ctx.data.materialId,
      split_material_ids: [],
      status: TaskStatus.PENDING,
      slice_duration: ctx.data.slice_duration,
    },
  });
};

/**
 * 切分任务触发器
 * @param ctx
 * @returns
 */
export const emitVideoSliceTask = async (ctx: ActionContext<{ taskId: string }>): Promise<video_slice_tasks> => {
  const task = await ctx.db.video_slice_tasks.findUnique({
    where: { id: ctx.data.taskId },
  });
  if (!task) {
    await ctx.feishuRobot.error('未找到切分任务', ['触发切分任务', `任务ID: ${ctx.data.taskId}`]);
    throw new Error('任务不存在');
  }
  if (task.status !== TaskStatus.PENDING) {
    throw new Error('任务状态不是PENDING');
  }
  if (!task.origin_material_id) {
    await ctx.feishuRobot.error('切分任务没有原始素材ID', ['触发切分任务', `任务ID: ${ctx.data.taskId}`]);
    throw new Error('任务缺少原始素材');
  }

  const originMaterial = await ctx.db.materials.findUnique({
    where: { id: task.origin_material_id },
  });
  if (!originMaterial) {
    await ctx.feishuRobot.error('原始素材不存在', [
      '触发切分任务',
      `任务ID: ${ctx.data.taskId}`,
      `原始素材ID: ${task.origin_material_id}`,
    ]);
    throw new Error('原始素材不存在');
  }

  if (task.slice_duration !== 300) {
    // 仅支持 300s 的切片任务
    return await ctx.db.video_slice_tasks.update({
      where: { id: task.id },
      data: { status: TaskStatus.FAILED, status_desc: '仅支持 300s 的切片任务' },
    });
  }

  // 创建一个trace
  const trace = ctx.langfuse.trace({
    id: task.id,
    name: task?.name,
    userId: ctx.user.id,
    sessionId: task.id,
    input: {
      file_id: originMaterial.vod_file_id,
      file_name: `${task.id}_${originMaterial.vod_file_id}`,
    },
    metadata: {},
    tags: ['video_slice'],
  });

  await ctx.langfuse.flushAsync();

  // 准备任务的参数
  const region = ctx.tenant.config.slice_task_pool?.name === 'Bowong' ? 'HangZhou' : 'ShenZhen';
  const input = {
    file_id: originMaterial.vod_file_id,
    file_name: originMaterial.name,
    metadata: {
      tenant_id: ctx.tenant.id,
      trace_id: trace.id,
      x_region: region,
      x_langfuse_pk: ctx.tenant.config.langfuse_public_key,
      x_langfuse_sk: ctx.tenant.config.langfuse_secret_key,
      vod_sub_id: ctx.tenant.config.vod_sub_app_id,
      vod_fin_class_id: ctx.tenant.config.vod_c_screen_record_final,
      vod_slice_class_id: ctx.tenant.config.vod_c_screen_record_split,
      vod_merge_class_id: ctx.tenant.config.vod_c_screen_record_merge,
      slice_duration: task.slice_duration,
      video_duration: originMaterial.video_duration,
    },
  };
  // 推送到队列
  await ctx.mq.push({
    host: process.env.BULLMQ_ENDPOINT,
    topic: `/queues/slice/jobs/${region}`,
    name: 'video-slice',
    data: input,
    options: { priority: 5, attempts: 3, backoff: { type: 'exponential', delay: 1000 } },
  });

  // 更新任务状态为执行中
  return await ctx.db.video_slice_tasks.update({
    where: { id: task.id },
    data: { status: TaskStatus.PROCESSING, trace_id: trace.id },
  });
};
