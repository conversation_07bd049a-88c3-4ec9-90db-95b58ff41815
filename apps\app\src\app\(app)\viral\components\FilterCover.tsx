import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProImage } from '@/components/pro/pro-image';
import { decodeHtmlEntities } from './AnalysisPanel';
import { cn } from '@/utils/cn';
import { HeartOutline } from '@/components/icon/HeartOutline';
import { MessageOutline } from '@/components/icon/MessageOutline';
import { PlayCircleOutline } from '@/components/icon/PlayCircleOutline';
import { NotFoundCover } from '@/components/icon/NotFoundCover';

export default function FilterCover({
  cover,
  onAnimationComplete,
  isReplaying,
  videoIds,
}: {
  cover: any;
  onAnimationComplete?: () => void;
  isReplaying?: boolean;
  videoIds: string[];
}) {
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [selectedCovers, setSelectedCovers] = useState<any[]>([]);
  const [isFiltering, setIsFiltering] = useState(false);
  const [filterComplete, setFilterComplete] = useState(false);
  const [coverMap, setCoverMap] = useState<any[]>([]);
  const [isFilteringAnimation, setIsFilteringAnimation] = useState(false);

  useEffect(() => {
    // 重置索引
    setCurrentIndex(-1);
    setSelectedCovers([]);
    setIsFiltering(false);
    setFilterComplete(false);
    setCoverMap([]);
    setIsFilteringAnimation(false);
    // 设置定时器，逐个显示封面
    const interval = setInterval(() => {
      setCurrentIndex((prev) => {
        const newIndex = prev + 1;
        if (newIndex >= cover.length - 1) {
          clearInterval(interval);
          // 移除立即设置 isFiltering 的逻辑
          setTimeout(() => {
            setCoverMap((prevMap) => {
              const newMap = [...prevMap];
              newMap.push(cover[newIndex]);
              return newMap.length > 10 ? newMap.slice(-10) : newMap;
            });
          }, 500);
          return newIndex;
        }

        // 更新 coverMap
        if (newIndex >= 0) {
          setTimeout(() => {
            setCoverMap((prevMap) => {
              const newMap = [...prevMap];
              newMap.push(cover[newIndex]);
              return newMap.length > 10 ? newMap.slice(-10) : newMap;
            });
          }, 500); // 与动画时长匹配
        }
        return newIndex;
      });
    }, 400);

    return () => clearInterval(interval);
  }, [cover]);
  // 添加处理最后一张图片退出动画完成的逻辑
  const handleExitComplete = () => {
    if (currentIndex === cover.length - 1) {
      // 在最后一张图片退出后开始筛选
      setIsFilteringAnimation(true); // 开始筛选动画
      setTimeout(() => {
        setIsFiltering(true);
        const selected = cover.filter((item: any) => videoIds.includes(item['视频id']));
        setTimeout(() => {
          setSelectedCovers(selected);
          setFilterComplete(true);
          if (onAnimationComplete) {
            onAnimationComplete();
          }
        }, 1000);
      }, 1200);
    }
  };
  // 定义入场动画的变体
  const entryVariants = {
    initial: {
      x: '100%',
      opacity: 0,
    },
    animate: {
      x: 0,
      opacity: 1,
      scale: [1, 1.2, 1],
      transition: {
        x: { duration: 0.5 },
        scale: {
          duration: 0.8,
          times: [0, 0.5, 1],
        },
      },
    },
    exit: (custom: { x: number; y: number }) => ({
      x: custom.x,
      y: custom.y,
      scale: 0,
      opacity: 0,
      transition: {
        duration: 0.5,
        ease: 'easeInOut',
      },
    }),
  };

  // 筛选动画变体
  const filterVariants = {
    initial: { opacity: 1, scale: 1 },
    filtering: (i: number) => ({
      opacity: 0.3,
      scale: 0.8,
      x: (i % 5) * 10 - 20,
      y: Math.floor(i / 5) * 10 - 20,
      transition: { duration: 0.5, delay: i * 0.02 },
    }),
    selected: {
      opacity: 1,
      scale: 1.1,
      x: 0,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      },
    },
    unselected: {
      opacity: 0,
      scale: 0,
      transition: { duration: 0.3 },
    },
  };

  // 结果展示动画
  const resultVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8 },
    },
  };
  const getTargetPosition = (index: number) => {
    const targetElement = document.querySelector(`[data-cover-index="${index}"]`);
    if (!targetElement) return { x: -100, y: 0 };

    const rect = targetElement.getBoundingClientRect();
    const containerRect = document.querySelector('.cover-container')?.getBoundingClientRect();
    if (!containerRect) return { x: -100, y: 0 };

    return {
      x: rect.left - containerRect.left,
      y: rect.top - containerRect.top,
    };
  };

  return (
    <div className="w-full">
      <div className="mb-4 text-center font-medium">
        {!isFiltering && !filterComplete && !isFilteringAnimation && `已选择${cover.length}套模板`}
        {((isFilteringAnimation && !filterComplete) || (isFiltering && !filterComplete)) && '正在筛选最佳模板...'}
        {filterComplete && '已为您筛选出3套最佳模板'}
      </div>

      {!filterComplete ? (
        <div
          className={cn(
            'relative h-[460px] w-full overflow-hidden',
            isReplaying ? 'h-[calc(100vh-282px)]' : 'h-[calc(550px-312px)]',
          )}
        >
          <div
            className={cn(
              'absolute left-0 top-0 grid w-full gap-2',
              isReplaying ? 'grid-cols-3' : 'h-full grid-cols-5',
            )}
          >
            {coverMap.map((item: any, index: number) => (
              <motion.div
                key={index}
                className="relative h-full overflow-hidden rounded-[12px] text-sm font-normal"
                style={{ overflow: 'hidden' }}
                animate={
                  isFilteringAnimation
                    ? {
                        scale: [1, 1.1, 0.8, 0],
                        opacity: [1, 1, 0.5, 0],
                        transition: {
                          duration: 1.2,
                          times: [0, 0.3, 0.6, 1],
                          ease: 'easeInOut',
                        },
                      }
                    : {}
                }
              >
                <div
                  className={cn(
                    'flex h-full items-center overflow-hidden rounded-[12px] border-[1px]',
                    isReplaying &&
                      'border-[#363D54] bg-[linear-gradient(180deg,_rgba(204,221,255,0.1)_0%,_rgba(204,221,255,0)_100%)] backdrop-blur-[20px]',
                  )}
                >
                  <div
                    className="p-1 transition-opacity"
                    role="link"
                    tabIndex={0}
                    aria-label={`查看视频: ${decodeHtmlEntities(item.商品名称)}`}
                  >
                    <ProImage
                      width={70}
                      height={120}
                      src={`https://bwkj-cos-1324682537.cos.ap-shanghai.myqcloud.com/damai/covers/${item['视频id']}.jpg`}
                      className="h-[120px] w-[70px] min-w-[70px] rounded-[4px]"
                      alt={decodeHtmlEntities(item.商品名称)}
                      fallback={() => (
                        <div className="flex h-full w-full items-center justify-center bg-[#272D3E] text-[#9FA4B2]">
                          <NotFoundCover className="h-[35px] w-[48px]" />
                        </div>
                      )}
                    />
                  </div>
                  {isReplaying && (
                    <div className="flex h-full w-full flex-col justify-between overflow-hidden">
                      <div>
                        <div className="line-clamp-3" role="link" tabIndex={0}>
                          {decodeHtmlEntities(item.商品名称)}
                        </div>
                        <div className="line-clamp-1 text-xs" role="link" tabIndex={0}>
                          @{item.作者昵称}
                        </div>
                      </div>
                      <div className="flex h-[24px] justify-start">
                        <div className="flex items-center gap-1">
                          <div className="flex items-center gap-[1.5px] text-xs">
                            <PlayCircleOutline />
                            <div className="text-xs">
                              {Number(item.播放量) >= 10000
                                ? `${Math.floor(Number(item.播放量) / 10000)}w+`
                                : item.播放量}
                            </div>
                          </div>
                          <div className="flex items-center gap-[1.5px] text-xs">
                            <HeartOutline />
                            <div className="text-xs">
                              {Number(item.点赞数) >= 10000
                                ? `${Math.floor(Number(item.点赞数) / 10000)}w+`
                                : item.点赞数}
                            </div>
                          </div>
                          <div className="flex items-center gap-[1.5px] text-xs">
                            <MessageOutline />
                            <div className="text-xs">{item.评论数}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>

          {!isFilteringAnimation && (
            <AnimatePresence mode="popLayout" onExitComplete={handleExitComplete}>
              {cover.map((item: any, index: number) => {
                const shouldShow = index === currentIndex;
                return (
                  shouldShow && (
                    <motion.div
                      key={item['视频id']}
                      className="absolute left-0 top-0 h-full w-full"
                      variants={isFiltering ? filterVariants : entryVariants}
                      initial="initial"
                      animate={{
                        ...entryVariants.animate,
                        scale: [1, 1.2, 0.6], // 修改这里：添加缩小效果
                        transition: {
                          ...entryVariants.animate.transition,
                          scale: {
                            duration: 1,
                            times: [0, 0.3, 1],
                          },
                        },
                      }}
                      exit="exit"
                      custom={getTargetPosition(coverMap.length - 1)} // 确保目标位置正确
                    >
                      <ProImage
                        width={260}
                        height={260}
                        src={`https://bwkj-cos-1324682537.cos.ap-shanghai.myqcloud.com/damai/covers/${item['视频id']}.jpg`}
                        className="h-full w-full rounded-[4px] object-contain"
                        alt={decodeHtmlEntities(item.商品名称)}
                        fallback={() => (
                          <div className="flex h-full w-full items-center justify-center bg-[#272D3E] text-[#9FA4B2]">
                            <NotFoundCover className="h-[35px] w-[48px]" />
                          </div>
                        )}
                      />
                    </motion.div>
                  )
                );
              })}
            </AnimatePresence>
          )}
        </div>
      ) : (
        <motion.div
          className="mt-4 flex justify-center gap-3"
          variants={resultVariants}
          initial="initial"
          animate="animate"
        >
          {selectedCovers.map((item: any) => (
            <motion.div key={item['视频id']} variants={itemVariants} className="flex flex-col items-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="relative">
                <ProImage
                  width={100}
                  height={170}
                  src={`https://bwkj-cos-1324682537.cos.ap-shanghai.myqcloud.com/damai/covers/${item['视频id']}.jpg`}
                  className="h-[170px] w-[100px] min-w-[100px] rounded-[8px] shadow-lg"
                  alt={decodeHtmlEntities(item.商品名称)}
                  fallback={() => (
                    <div className="flex h-full w-full items-center justify-center bg-[#272D3E] text-[#9FA4B2]">
                      <NotFoundCover className="h-[35px] w-[48px]" />
                    </div>
                  )}
                />
                <motion.div
                  className="absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, type: 'spring' }}
                >
                  ✓
                </motion.div>
              </motion.div>
              <motion.p
                className="mt-2 w-[100px] truncate text-center text-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                {decodeHtmlEntities(item.商品名称)}
              </motion.p>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  );
}
