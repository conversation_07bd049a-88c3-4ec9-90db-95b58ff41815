import { ActionContextPlugins } from '@roasmax/serve';
import { FeishuRobot } from '@roasmax/utils/feishu';

interface BatchImportOptions<T extends Record<string, any>> {
  // 飞书机器人
  feishuRobot?: FeishuRobot;
  // 数据库实例
  db: ActionContextPlugins<T>['db'];
  // 要导入的数据
  importData: T[];
  // 模型名称
  modelName: keyof ActionContextPlugins<T>['db'];
  // 唯一标识字段
  identityField: keyof T;
  // 数据合并策略（可选）
  mergeStrategy?: (newData: T, existingData: any) => Record<string, any>;
  // 需要移动到 properties 中的字段列表
  propertiesFields?: (keyof T)[];
}

export async function batchImport<T extends Record<string, any>>({
  feishuRobot,
  db,
  importData,
  modelName,
  identityField,
  mergeStrategy,
  propertiesFields,
}: BatchImportOptions<T>) {
  // 获取所有待创建记录的唯一标识
  const identities = importData.map((item) => item[identityField]);

  // 查找已存在的记录
  const existingRecords = await (db[modelName] as any).findMany({
    where: {
      [identityField]: { in: identities },
      tmp_deleted_at: null,
    },
  });

  // 将数据分为需要更新和需要创建的两部分
  const toCreate = importData.filter((item) => {
    return !existingRecords.some((existing: any) => existing[identityField] === item[identityField]);
  });

  const toUpdate = importData.filter((item) => {
    return existingRecords.some((existing: any) => existing[identityField] === item[identityField]);
  });

  // 批量创建新记录
  let createResult = { count: 0 };
  try {
    if (toCreate.length > 0) {
      const uniqueToCreate = toCreate
        .filter((item, index, self) => index === self.findIndex((t) => t[identityField] === item[identityField]))
        .map((item) => {
          if (!propertiesFields || propertiesFields.length === 0) return item;

          // 将指定字段移动到 properties 中
          const properties: Record<string, any> = {};
          const newItem = { ...item };

          propertiesFields.forEach((field) => {
            if (field in newItem) {
              properties[field as string] = newItem[field];
              delete newItem[field];
            }
          });

          return {
            ...newItem,
            properties,
          };
        });

      createResult = await (db[modelName] as any).createMany({
        data: uniqueToCreate,
      });
    }
  } catch (error) {
    await feishuRobot?.error('批量导入失败', [
      `模型: ${String(modelName)}`,
      `唯一标识字段: ${String(identityField)}`,
      `导入数据: ${JSON.stringify(importData)}`,
    ]);
  }

  // 更新已存在的记录
  let updateCount = 0;
  for (const item of toUpdate) {
    const existing = existingRecords.find((r: any) => r[identityField] === item[identityField]);
    if (existing) {
      const mergedData = mergeStrategy
        ? mergeStrategy(item, existing)
        : Object.keys(item).reduce(
            (acc, key) => {
              const newValue = item[key];
              const oldValue = existing[key];
              acc[key] = newValue ?? oldValue;
              return acc;
            },
            {} as Record<string, any>,
          );

      // 处理 properties 字段
      if (propertiesFields?.length) {
        const properties = existing.properties ? { ...existing.properties } : {};
        propertiesFields.forEach((field) => {
          if (field in mergedData) {
            // @ts-ignore
            properties[field as string] = mergedData[field];
            // @ts-ignore
            delete mergedData[field];
          }
        });
        mergedData.properties = properties;
      }

      await (db[modelName] as any).update({
        where: { id: existing.id },
        data: mergedData,
      });
      updateCount++;
    }
  }

  return {
    created: createResult.count,
    updated: updateCount,
    total: createResult.count + updateCount,
  };
}
