import dayjs from 'dayjs';
import chalk from 'chalk';

type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

/**
 * 帮我写一个logger函数，接受 [时间戳] [日志级别] [服务名称] [请求来源] [请求唯一id] [消息内容] 并打印到控制台
 */
const logger = (level: string, ...message: string[]) => {
  const time = dayjs().toISOString();
  const str = `[${time}] [${level}]`;

  if (level === 'ERROR') {
    console.error(chalk.red(str), ...message);
    return;
  }
  if (level === 'WARN') {
    console.warn(chalk.yellow(str), ...message);
    return;
  }
  // eslint-disable-next-line no-console
  console.log(chalk.blue(str), ...message);
};

// 当前支持的日志级别
// DEBUG < INFO < WARN < ERROR
// 当环境变量配置为某个值时，只有比这个值高的日志级别才会被打印
const LOG_LEVEL_LIST = ['DEBUG', 'INFO', 'WARN', 'ERROR'] as LogLevel[];

export class Logger {
  service: string;
  source: string;
  requestId: string;

  private tails: string[] = [];

  private logLevel: LogLevel;

  constructor(service: string, source: string, requestId: string) {
    this.service = service;
    this.source = source;
    this.requestId = requestId;
    this.logLevel = 'DEBUG';
  }

  private prefix(x: number, type: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR') {
    // 保持相同的缩进
    const indent = ' '.repeat(x);
    const tail = this.tails.length ? ` ${chalk.cyan(this.tails.map((t) => `[${t}]`).join(' '))}` : '';
    const str = `${indent}[${this.requestId}] [${this.source}] [${this.service}]`;
    if (type === 'WARN') {
      return chalk.yellow(str) + tail;
    }
    if (type === 'ERROR') {
      return chalk.red(str) + tail;
    }
    return chalk.blue(str) + tail;
  }

  _push(...message: any[]) {
    this.tails.push(...message);
  }

  _pop() {
    return this.tails.pop();
  }

  _start(...message: any[]) {
    logger('LOG', chalk.bold(chalk.blue(`>>> [${this.requestId}] [${this.source}] [${this.service}]`)), ...message);
  }

  _end(...message: any[]) {
    logger('LOG', chalk.bold(chalk.blue(`<<< [${this.requestId}] [${this.source}] [${this.service}]`)), ...message);
  }

  info(...message: any[]) {
    if (!this.checkLogLevel('INFO')) return;
    logger('LOG', this.prefix(4, 'INFO'), ...message);
  }

  log(...message: any[]) {
    if (!this.checkLogLevel('INFO')) return;
    logger('LOG', this.prefix(4, 'INFO'), ...message);
  }

  debug(...message: any[]) {
    if (!this.checkLogLevel('DEBUG')) return;
    logger('DEBUG', this.prefix(2, 'DEBUG'), ...message);
  }

  error(...message: any[]) {
    if (!this.checkLogLevel('ERROR')) return;
    logger('ERROR', this.prefix(2, 'ERROR'), ...message);
  }

  warn(...message: any[]) {
    if (!this.checkLogLevel('WARN')) return;
    logger('WARN', this.prefix(3, 'WARN'), ...message);
  }

  clone() {
    return new Logger(this.service, this.source, this.requestId);
  }

  private checkLogLevel(level: LogLevel) {
    return LOG_LEVEL_LIST.indexOf(level) >= LOG_LEVEL_LIST.indexOf(this.logLevel);
  }
}
