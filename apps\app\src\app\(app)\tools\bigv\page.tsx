'use client';
import useMaterialStore from '@/store/materialStore';
import TaskCreator from './components/TaskCreator';
import MaterialDialog from '@/components/MaterialDialog';

export default function Page() {
  const { setMaterialDrawerOpen } = useMaterialStore();
  return (
    <div className="flex h-[100vh] w-full items-center justify-center rounded-lg py-4 pr-4">
      <div className="h-[100%] max-w-[960px] flex-1">
        <TaskCreator onClickMaterialImport={() => setMaterialDrawerOpen(true)} />
      </div>
      <MaterialDialog />
    </div>
  );
}
