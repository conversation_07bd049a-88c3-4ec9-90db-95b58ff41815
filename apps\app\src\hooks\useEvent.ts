import { useLayoutEffect, useCallback, useRef } from 'react';

type IHandler<R, P extends any[]> = (...args: P) => R;

/**
 * https://github.com/reactjs/rfcs/blob/useevent/text/0000-useevent.md
 * 只需传入函数即可，会保证全局唯一的引用，所以也不需要再考虑添加各种dependencies了
 * 升react18前的过度方案，也可以用ahooks的useMemoizedFn
 *
 * 20240118更新，此方法没通过 react 提案，除非你非常了解 hooks 运行原理和坑，否则不建议使用
 * 后续可能会出现 useEffectEvent 这个官方 api
 * @param handler 缓存的函数
 */
function useEvent<R, P extends any[], T extends IHandler<R, P>>(handler: T) {
  const handlerRef = useRef<IHandler<R, P>>(null);

  useLayoutEffect(() => {
    // @ts-ignore
    handlerRef.current = handler;
  });

  return useCallback((...args: P) => {
    const fn = handlerRef.current!;
    return fn(...args);
  }, []) as T;
}

export { useEvent };
