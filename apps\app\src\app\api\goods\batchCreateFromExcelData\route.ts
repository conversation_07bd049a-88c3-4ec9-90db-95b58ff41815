import { batchImport } from '@/utils/batch-import';
import { ActionContext, api } from '@roasmax/serve';

type BatchCreateContext = ActionContext<{
  data: Array<{
    product_url: string;
    [key: string]: any;
  }>;
}>;

export const POST = api(async (ctx: BatchCreateContext) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  return await ctx.trx(async (ctx) => {
    return await batchImport({
      db: ctx.db,
      importData: ctx.data.data,
      modelName: 'goods',
      identityField: 'product_url',
      mergeStrategy: (newData, existingData) => {
        return Object.keys(newData).reduce(
          (acc, key) => {
            const newValue = newData[key];
            const oldValue = existingData[key];

            if (key === 'platform') {
              acc[key] = newValue !== oldValue ? 'merged' : newValue;
            } else if (key === 'properties' && typeof oldValue === 'object') {
              acc[key] = {
                ...oldValue,
                ...newValue,
              };
            } else {
              acc[key] = newValue ?? oldValue;
            }

            return acc;
          },
          {} as Record<string, any>,
        );
      },
    });
  });
});
