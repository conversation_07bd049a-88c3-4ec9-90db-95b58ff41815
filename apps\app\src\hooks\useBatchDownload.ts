import { MaterialType } from '@/types/material';
import { saveAs } from 'file-saver';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { useCallback } from 'react';
import toast from 'react-hot-toast';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export enum DownloadingStatus {
  DOWNLOADING = 1,
  SUCCESS = 2,
  FAILED = 3,
}

export type DownloadingItemType = {
  id: string;
  name: string;
  total: number;
  received: number;
  coverUrl: string;
  progress: number;
  status: DownloadingStatus;
};

export const DOWNLOADING_STATUS_TEXT = {
  [DownloadingStatus.DOWNLOADING]: '下载中',
  [DownloadingStatus.SUCCESS]: '下载成功',
  [DownloadingStatus.FAILED]: '下载失败',
};

interface BatchDownloadStore {
  isDownloading: boolean;
  setDownloading: (isDownloading: boolean) => void;
  downloadingList: DownloadingItemType[];
  setDownloadingList: (action: (pre: DownloadingItemType[]) => DownloadingItemType[]) => void;
}

const useBatchDownloadStore = create<BatchDownloadStore>()(
  devtools((set, get) => ({
    downloadingList: [],
    isDownloading: false,
    setDownloading: (isDownloading) => set({ isDownloading }),
    setDownloadingList: (action) => {
      set({ downloadingList: action(get().downloadingList) });
    },
  })),
);

export const useBatchDownload = () => {
  const { isDownloading, downloadingList, setDownloading, setDownloadingList } = useBatchDownloadStore();

  const downloadAndZipVideos = useCallback(
    async (list: MaterialType[]) => {
      setDownloading(true);
      setDownloadingList(() =>
        list.map((item) => ({
          id: item.id,
          name: item.name,
          total: 0,
          received: 0,
          coverUrl: item.vod_cover_url,
          progress: 0,
          status: DownloadingStatus.DOWNLOADING,
        })),
      );

      try {
        const zip = new JSZip();
        const fileNameMap = new Map<string, number>();

        const downloadPromises = list.map(async (each) => {
          try {
            const response = await fetch(each.vod_media_url);
            const reader = response.body!.getReader();
            const contentLength = +(response.headers.get('Content-Length') || 0);
            let receivedLength = 0;
            const chunks: Uint8Array[] = [];

            while (true) {
              const { done, value } = await reader!.read();

              if (value) {
                chunks.push(value);
                receivedLength += value.length;
              }

              setDownloadingList((pre) => {
                return pre.map((item) => {
                  if (item.id === each.id) {
                    return {
                      ...item,
                      total: contentLength,
                      received: receivedLength,
                      progress: Math.floor((receivedLength / contentLength) * 100),
                    };
                  }
                  return item;
                });
              });

              if (done) {
                break;
              }
            }

            const blob = new Blob(chunks, { type: 'video/mp4' });

            // 处理文件名
            let fileName = `${each.name}.mp4`;
            if (fileNameMap.has(fileName)) {
              const count = fileNameMap.get(fileName)! + 1;
              fileNameMap.set(fileName, count);
              fileName = `${each.name}(${count}).mp4`;
            } else {
              fileNameMap.set(fileName, 0);
            }

            zip.file(fileName, blob);
          } catch (error) {
            console.error('下载单个视频时出错:', error);
          }
        });

        await Promise.all(downloadPromises);

        // 生成并保存 zip 文件
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        saveAs(zipBlob, `${'批量下载'}.zip`);
        toast.success('视频下载成功！');
      } catch (error) {
        console.error('下载视频时出错:', error);
        toast.error('视频下载失败！');
      } finally {
        setDownloading(false);
      }
    },
    [setDownloading, setDownloadingList],
  );

  return {
    isDownloading,
    downloadingList,
    downloadAndZipVideos,
  };
};
