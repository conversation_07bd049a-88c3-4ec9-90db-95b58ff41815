'use client';
import { CloudDriveManage } from '@/components/icon/cloudDrive';
import { Button } from '@/components/ui';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { confirm } from './ConfirmDialog';
import { fetchDistributionFileSourceReadWriteShareLink } from '@/services/actions/tenant';
import { action } from '@/utils/server-action/action';
import dayjs from 'dayjs';

export default function Distribution() {
  const pathname = usePathname();
  if (!pathname.startsWith('/distributions')) {
    return null;
  }

  return (
    <>
      <Link className="flex items-center justify-between" href="/distributions/generate">
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
        >
          <div className="flex justify-around px-4">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">生成中心</div>
          </div>
        </Button>
      </Link>
      <Link className="flex items-center justify-between" href="/distributions/tasks">
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
        >
          <div className="flex justify-around px-4">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">分发中心</div>
          </div>
        </Button>
      </Link>
      <Link className="flex items-center justify-between" href="/distributions/sales">
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
        >
          <div className="flex justify-around px-4">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">销售数据</div>
          </div>
        </Button>
      </Link>
      <Link className="flex items-center justify-between" href="/distributions/goods">
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
        >
          <div className="flex justify-around px-4">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">商品资产</div>
          </div>
        </Button>
      </Link>
      <Link className="flex items-center justify-between" href="/distributions/social-accounts">
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
        >
          <div className="flex justify-around px-4">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">账号资产</div>
          </div>
        </Button>
      </Link>
      <Button
        className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
        variant="secondary"
        onClick={async () => {
          const share = await action(fetchDistributionFileSourceReadWriteShareLink, {});
          if (!share) {
            return;
          }
          confirm({
            className: 'w-[500px] max-w-[70vw]',
            content: (
              <div className="w-[400px]">
                <div className="mb-2">
                  <div className="font-medium">分享链接</div>
                  <div className="text-wrap break-all">{share?.link}</div>
                </div>
                <div className="mb-2">
                  <div className="font-medium">提取码</div>
                  <div>{share?.extract}</div>
                </div>
                <div className="mb-2">
                  <div className="font-medium">过期时间</div>
                  <div>{dayjs(share.expire * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
                </div>
              </div>
            ),
            buttonText: {
              confirm: '打开链接',
            },
            onConfirm: async () => {
              // 复制提取码到剪贴板
              navigator.clipboard.writeText(share.extract);
              window.open(share.link, '_blank');
            },
          });
        }}
      >
        <div className="flex justify-around px-4">
          <CloudDriveManage />
          <div className="ml-2 cursor-pointer">素材资产</div>
        </div>
      </Button>
    </>
  );
}
