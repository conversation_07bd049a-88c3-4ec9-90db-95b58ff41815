import jwt from 'jsonwebtoken';
import * as jose from 'jose';

/**
 * 获取登录信息 本地解析Token
 * @param token
 * @returns
 */
export const getLoginSessionInfo = async (token: string) => {
  const cleanToken = token.replace('Bearer ', '');

  try {
    // 首先尝试使用 jsonwebtoken 验证
    const decoded = jwt.verify(cleanToken, process.env.APPSECRET!);
    if (typeof decoded === 'string' || !decoded.exp) {
      throw new Error('解析失败');
    }
    const expired = Date.now() / 1000 > decoded.exp;
    if (!expired) {
      return decoded as LoginSessionInfo;
    }
  } catch (e: any) {
    console.error('jwt验证失败，尝试使用jose解析', e.message);

    try {
      // 使用 jose.decodeJwt 作为备选方案
      const decoded = jose.decodeJwt(cleanToken);
      if (!decoded.exp) {
        throw new Error('Token缺少过期时间');
      }

      const expired = Date.now() / 1000 > decoded.exp;
      if (!expired) {
        return decoded as LoginSessionInfo;
      }
    } catch (joseError: any) {
      console.error('jose解析也失败', joseError.message, token);
      return;
    }
  }
};

type LoginSessionInfo = {
  updated_at: string;
  address: { country: string | null; postal_code: string | null; region: string | null; formatted: string | null };
  phone_number_verified: boolean;
  phone_number: string | null;
  locale: string | null;
  zoneinfo: string | null;
  birthdate: string | null;
  gender: string;
  email_verified: boolean;
  email: string;
  website: string | null;
  picture: string;
  profile: string | null;
  preferred_username: string | null;
  nickname: string;
  middle_name: string | null;
  family_name: string | null;
  given_name: string | null;
  name: string | null;
  sub: string;
  external_id: string | null;
  unionid: string | null;
  username: string;
  data: {
    type: 'user';
    userPoolId: string;
    appId: string;
    id: string;
    userId: string;
    _id: string;
    phone: string | null;
    email: string;
    username: string;
    unionid: string | null;
    openid: string | null;
    clientId: string;
  };
  userpool_id: string;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
};
