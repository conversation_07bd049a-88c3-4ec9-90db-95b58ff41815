export const LinkOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.625 2.375C16.1875 0.875 13.75 0.875 12.3125 2.375L10.125 4.625C9.875 4.875 9.875 5.3125 10.125 5.5625C10.375 5.8125 10.75 5.8125 11 5.5625L13.1875 3.3125C14.1875 2.3125 15.75 2.3125 16.75 3.3125C17.75 4.3125 17.75 5.9375 16.75 6.9375L13.1875 10.5625C12.1875 11.5625 10.625 11.5625 9.625 10.5625C9.375 10.3125 9 10.3125 8.75 10.5625C8.5 10.8125 8.5 11.25 8.75 11.5C10.1875 13 12.625 13 14.0625 11.5L17.625 7.875C19.125 6.3125 19.125 3.875 17.625 2.375Z"
        fill="white"
      />
      <path
        d="M9.00012 14.4375L6.81262 16.6875C5.81262 17.6875 4.25012 17.6875 3.25012 16.6875C2.25012 15.6875 2.25012 14.0625 3.25012 13.0625L6.81262 9.4375C7.81262 8.4375 9.37512 8.4375 10.3751 9.4375C10.6251 9.6875 11.0001 9.6875 11.2501 9.4375C11.5001 9.1875 11.5001 8.75 11.2501 8.5C9.81262 7 7.37512 7 5.93762 8.5L2.37512 12.125C0.875125 13.625 0.937625 16.0625 2.37512 17.5625C3.81262 19.0625 6.25012 19.0625 7.68762 17.5625L9.87512 15.3125C10.1251 15.0625 10.1251 14.625 9.87512 14.375C9.62512 14.1875 9.25012 14.1875 9.00012 14.4375Z"
        fill="white"
      />
    </svg>
  );
};
