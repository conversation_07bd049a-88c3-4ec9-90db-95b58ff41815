/**
 * 本地登录 API 路由
 * 替换原有的 Authing 登录接口
 */

import { NextResponse } from 'next/server';
import { LocalAuthenticationClient } from '@roasmax/serve/src/adapters/LocalAuthenticationClient';
import { JWTManager } from '@roasmax/serve/src/utils/jwt';
import * as jose from 'jose';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { account: email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ message: '邮箱和密码不能为空' }, { status: 400 });
    }

    // 创建本地认证客户端
    const authenticationClient = new LocalAuthenticationClient({
      appId: process.env.APPID,
      appSecret: process.env.APPSECRET,
      appHost: process.env.APPHOST,
    });

    // 获取客户端信息
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 执行登录
    const result = await authenticationClient.signInByEmailPassword({
      email,
      password,
      clientIp,
      customData: { userAgent }
    });

    if (result.statusCode !== 200) {
      return NextResponse.json({ message: result.message }, { status: 400 });
    }

    // 检查是否需要密码重置
    if (result.message === 'PASSWORD_RESET_REQUIRED') {
      return NextResponse.json({
        message: 'PASSWORD_RESET_REQUIRED',
        resetToken: result.data.access_token,
        user: result.data.user
      }, { status: 200 });
    }

    // 重新签名 ID Token（保持与原有逻辑兼容）
    const secret = new TextEncoder().encode(process.env.APPSECRET);
    const payload = {
      userId: result.data.user.userId,
      email: result.data.user.email,
      nickname: result.data.user.nickname,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24小时
      iss: 'roasmax-local-auth',
      aud: 'roasmax-app'
    };

    const idToken = await new jose.SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('24h')
      .sign(secret);

    // 创建响应
    const response = NextResponse.json({
      message: '登录成功',
      data: {
        ...result.data,
        id_token: idToken
      }
    });

    // 设置 Cookie
    response.cookies.set('token', idToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Local login error:', error);
    
    // 返回通用错误信息，避免泄露敏感信息
    const errorMessage = error instanceof Error ? error.message : '登录失败，请重试';
    
    return NextResponse.json({ 
      message: errorMessage 
    }, { status: 400 });
  }
}

// 处理密码重置请求
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { resetToken, newPassword } = body;

    if (!resetToken || !newPassword) {
      return NextResponse.json({ message: '重置令牌和新密码不能为空' }, { status: 400 });
    }

    // 验证重置令牌
    const { userId } = JWTManager.verifyPasswordResetToken(resetToken);

    // 创建本地认证客户端
    const authenticationClient = new LocalAuthenticationClient();
    authenticationClient.setAccessToken(resetToken);

    // 更新密码
    const result = await authenticationClient.updatePassword({
      newPassword,
      // 密码重置时不需要提供旧密码
    });

    if (result.statusCode !== 200) {
      return NextResponse.json({ message: result.message }, { status: 400 });
    }

    return NextResponse.json({
      message: '密码重置成功，请重新登录',
      data: { success: true }
    });

  } catch (error) {
    console.error('Password reset error:', error);
    
    const errorMessage = error instanceof Error ? error.message : '密码重置失败，请重试';
    
    return NextResponse.json({ 
      message: errorMessage 
    }, { status: 400 });
  }
}
