export const Look4_3 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <path
        d="M21.1802 13.0783L21.1798 13.079L21.2307 13.1299C21.8325 13.7338 22.2919 14.4644 22.5754 15.2684C22.8589 16.0724 22.9593 16.9296 22.8693 17.7774C22.7793 18.6251 22.5012 19.4421 22.0552 20.1687C21.6092 20.8953 21.0067 21.5132 20.2915 21.9772C19.5763 22.4413 18.7666 22.7398 17.9213 22.851C17.0761 22.9623 16.2167 22.8834 15.4058 22.6202C14.5949 22.357 13.853 21.916 13.2343 21.3295C12.6155 20.7431 12.1355 20.0259 11.8292 19.2302L11.8133 19.1888L11.7718 19.1728C10.8811 18.8304 10.0901 18.2708 9.47066 17.5449C8.85122 16.819 8.42297 15.9498 8.22482 15.0164C8.02668 14.0829 8.06492 13.1147 8.33607 12.1998C8.60722 11.2849 9.10269 10.4522 9.77745 9.77745C10.4522 9.10269 11.2849 8.60722 12.1998 8.33607C13.1147 8.06492 14.0829 8.02668 15.0164 8.22482C15.9498 8.42297 16.819 8.85122 17.5449 9.47066C18.2708 10.0901 18.8304 10.8811 19.1728 11.7718L19.1888 11.8134L19.2304 11.8293C19.9592 12.108 20.6227 12.5332 21.1802 13.0783ZM17.0161 10.5687H17.014C16.5693 10.1334 16.0401 9.79379 15.4589 9.57077C14.8646 9.34269 14.229 9.24166 13.5932 9.27417C12.9574 9.30669 12.3355 9.47203 11.7675 9.75955C11.1995 10.0471 10.698 10.4504 10.2954 10.9435C9.89274 11.4366 9.59787 12.0087 9.42977 12.6227C9.26167 13.2367 9.22407 13.8792 9.31938 14.5086C9.41469 15.1381 9.64081 15.7406 9.98316 16.2773C10.3255 16.814 10.7765 17.2731 11.3071 17.6249L11.4748 17.7361L11.4621 17.5353C11.4111 16.7246 11.5334 15.9124 11.8209 15.1527C12.1084 14.393 12.5545 13.7033 13.1294 13.1296L13.1296 13.1294C13.703 12.5545 14.3924 12.1084 15.1518 11.8209C15.9113 11.5334 16.7232 11.4111 17.5336 11.4621L17.7337 11.4747L17.6235 11.3073C17.4553 11.0519 17.2616 10.8142 17.0454 10.598L17.0161 10.5687ZM20.2334 13.7851V13.7959C20.0645 13.6432 19.8844 13.5032 19.6945 13.3772L19.5267 13.2657L19.5394 13.4668C19.5905 14.2774 19.4683 15.0896 19.1809 15.8493C18.8935 16.609 18.4475 17.2987 17.8726 17.8725L17.8725 17.8726C17.2989 18.4475 16.6094 18.8935 15.8499 19.1809C15.0904 19.4683 14.2783 19.5905 13.4679 19.5394L13.2667 19.5267L13.3783 19.6945C13.7305 20.2242 14.1897 20.6743 14.7263 21.0159C15.2629 21.3575 15.8651 21.5829 16.4941 21.6777C17.1231 21.7725 17.765 21.7346 18.3785 21.5664C18.9919 21.3982 19.5634 21.1035 20.0561 20.7011C20.5488 20.2988 20.9518 19.7978 21.2392 19.2303C21.5266 18.6628 21.692 18.0414 21.7248 17.4062C21.7576 16.7709 21.657 16.1358 21.4296 15.5418C21.2022 14.9477 20.8529 14.4079 20.4043 13.9569L20.2334 13.7851ZM18.2399 12.7616L18.2187 12.7405L18.1894 12.7344L18.0296 12.7014L18.0296 12.7014L18.028 12.701C17.2985 12.5627 16.5462 12.6048 15.8366 12.8238C15.1271 13.0427 14.482 13.4318 13.9573 13.9572C13.412 14.502 13.0138 15.1763 12.8 15.9169C12.5862 16.6575 12.5638 17.4403 12.7348 18.1919L12.7488 18.2532L12.8101 18.2672C13.5619 18.4385 14.3448 18.4162 15.0856 18.2025C15.8264 17.9888 16.5009 17.5907 17.0459 17.0453C17.5912 16.5007 17.9893 15.8267 18.2031 15.0863C18.4169 14.3458 18.4394 13.5633 18.2683 12.8118L18.2619 12.7837L18.2415 12.7633L18.2399 12.7616Z"
        fill="#9FA4B2"
        stroke="#1B2435"
        strokeWidth="0.2"
      />
    </svg>
  );
};
