import { TaskStatus, UploadManager, type UploadTask } from '@/lib/uploader';
import { useEffect, useMemo, useState } from 'react';

// 创建全局管理器实例
const manager = new UploadManager();

/**
 * 文件上传Hook - 简化版本
 * @returns
 *
 * @example
 * const uploader = useUpload();
 * uploader.push([file], {
 *   onSuccess: (info) => {
 *     console.log(info.url);
 *   },
 * });
 */
export const useUpload = () => {
  const [tasks, setTasks] = useState<UploadTask[]>([]);

  // 启动管理器
  useEffect(() => {
    manager.process();

    // 定期更新任务状态
    const interval = setInterval(() => {
      setTasks([...manager.tasks]);
    }, 500);

    return () => {
      clearInterval(interval);
    };
  }, []);

  // 分组任务
  const groupedTasks = useMemo(() => {
    return tasks.reduce(
      (acc, task) => {
        if (!acc[task.status]) {
          acc[task.status] = [];
        }
        acc[task.status].push(task);
        return acc;
      },
      {
        [TaskStatus.PENDING]: [],
        [TaskStatus.RUNNING]: [],
        [TaskStatus.SUCCESS]: [],
        [TaskStatus.FAILED]: [],
        [TaskStatus.CANCELED]: [],
      } as Record<TaskStatus, UploadTask[]>
    );
  }, [tasks]);

  return useMemo(
    () => ({
      tasks,
      groupedTasks,
      push: (
        files: File[],
        options?: {
          onSuccess?: (info: {
            task: UploadTask;
            url: string;
            vodUrl?: string;
            vodStatus?: 'processing' | 'ready' | 'failed';
          }) => void;
        }
      ) => {
        return manager.push(files, options);
      },
      cancel: (id: string) => {
        manager.cancel(id);
      },
      remove: (id: string) => {
        manager.remove(id);
      },
    }),
    [tasks, groupedTasks]
  );
};
