import {
  getSuccessfulTaskCount,
  getPendingTaskCount,
  getSuccessVideoCount,
} from '@/services/actions/video-generation-task';
import { useAction } from '@/utils/server-action/action';

export const useDashboardStats = () => {
  const { data: successTaskCount = 0, loading: successLoading } = useAction(getSuccessfulTaskCount, {});
  const { data: pendingTaskCount = 0, loading: pendingLoading } = useAction(getPendingTaskCount, {});
  const { data: successVideoCount = 0, loading: successVideoLoading } = useAction(getSuccessVideoCount, {});

  return {
    successVideoCount,
    successTaskCount,
    pendingTaskCount,
    loading: successLoading || pendingLoading || successVideoLoading,
  };
};
