import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
  Tabs,
  <PERSON>bsContent,
  <PERSON>bsTrigger,
  TabsList,
  Textarea,
  Badge,
  Panel,
} from '../ui';
import ProTable from '../pro/pro-table';
import { ProSelect } from '../pro/pro-select';
import { listDataImportTemplates } from '@/services/actions/config';
import { action, ActionResult } from '@/utils/server-action/action';
import { DataImportTemplateDialog, DataImportTemplateDialogRef } from '../DataImportTemplateDialog';
import { confirm } from '../ConfirmDialog';
import { parseDataFromRawString } from './parser';

export interface DataImportDialogProps {
  onRefreshNeeded?: () => void;
}

interface DataImportDialogConfig {
  templateResourceName: string;
  request: (parsedData: any[], schema: PrismaJson.DataImportTemplateContent) => Promise<any[]>;
  onSubmit: (data: { newData: any[]; updateData: any[]; noChangeData: any[] }) => Promise<boolean>;
}

export interface DataImportDialogRef {
  show: (config?: DataImportDialogConfig) => void;
}

const TOTAL_STEPS = 3; // 假设总共有3个步骤
const STEP_INPUT = 0;
const STEP_PREVIEW = 1;
const STEP_CONFIRM = 2;

export const DataImportDialog = React.forwardRef<DataImportDialogRef, DataImportDialogProps>((props, ref) => {
  const dataImportTemplateDialogRef = useRef<DataImportTemplateDialogRef>(null);

  const [open, setOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ActionResult<typeof listDataImportTemplates>[number]>();
  const [templates, setTemplates] = useState<ActionResult<typeof listDataImportTemplates>>([]);
  const [config, setConfig] = useState<DataImportDialogConfig>();
  // 步骤状态
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  // 第一步填入的原始数据
  const [rawData, setRawData] = useState<string>('');
  // 第一步提交时解析出的需要新增和需要更新的数据
  const [newData, setNewData] = useState<any[]>([]);
  const [updateData, setUpdateData] = useState<any[]>([]);
  const [noChangeData, setNoChangeData] = useState<any[]>([]);
  // 执行完成
  const [executed, setExecuted] = useState(false);

  useEffect(() => {
    if (!config?.templateResourceName) return;
    action(listDataImportTemplates, { resource_name: config.templateResourceName }).then((res) =>
      setTemplates(res || []),
    );
  }, [config?.templateResourceName]);

  // 添加步骤控制函数
  const handleNext = async () => {
    if (currentStep === STEP_INPUT) {
      setLoading(true);
      if (!selectedTemplate?.content || !config?.request) return;

      const data = await parseDataFromRawString(rawData, selectedTemplate.content, config.request);

      setNewData(data.new || []);
      setUpdateData(data.update || []);
      setNoChangeData(data.noChange || []);
      setLoading(false);
    }
    if (currentStep === STEP_PREVIEW) {
      // 提交数据
      confirm({
        content: '确认提交数据吗？',
        onConfirm: async () => {
          await config?.onSubmit?.({ newData, updateData, noChangeData });
          setExecuted(true);
        },
      });
    }

    if (currentStep < TOTAL_STEPS - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  useImperativeHandle(ref, () => ({
    show: (config) => {
      setConfig(config);
      setOpen(true);
    },
  }));

  // 输入数据
  const renderStepContentInput = () => {
    return (
      <Panel className="m-1 p-4">
        <div className="my-2">选择导入模板</div>
        <div className="flex items-center gap-2">
          <ProSelect
            options={templates.map((item) => ({ label: item.name, value: item.id }))}
            value={selectedTemplate?.id}
            onValueChange={(value) => setSelectedTemplate(templates.find((item) => item.id === value))}
          />
          <Button
            variant="outline"
            onClick={() => {
              if (!config?.templateResourceName) return;
              dataImportTemplateDialogRef.current?.show({ templates, resource_name: config.templateResourceName });
            }}
          >
            查看模板
          </Button>
        </div>
        <div className="my-2">输入数据</div>
        <Textarea className="h-[calc(80vh-260px)]" value={rawData} onChange={(e) => setRawData(e.target.value)} />
      </Panel>
    );
  };

  // 预览数据
  const renderStepContentPreview = () => {
    return (
      <div>
        <Tabs defaultValue="new">
          <TabsList>
            <TabsTrigger value="new">新增数据({newData.length})</TabsTrigger>
            <TabsTrigger value="update">更新数据({updateData.length})</TabsTrigger>
            <TabsTrigger value="noChange">无需变更数据({noChangeData.length})</TabsTrigger>
          </TabsList>
          <TabsContent value="new">
            <ProTable
              className="h-[calc(80vh-100px)]"
              columns={(selectedTemplate?.content.columns || []) as any}
              dataSource={newData}
            />
          </TabsContent>
          <TabsContent value="update">
            <ProTable
              className="h-[calc(80vh-100px)]"
              columns={[
                {
                  title: '变更字段',
                  dataIndex: '__diff__',
                  width: 200,
                  fixed: 'left',
                  render: (_, record) => {
                    // 需要把对应的key转化为title
                    return (
                      <div className="flex flex-wrap gap-2">
                        {record.__diff__
                          .map((key: string) => selectedTemplate?.content.columns?.find((c) => c.key === key)?.title)
                          .map((title: string) => (
                            <Badge key={title} variant="outline">
                              {title}
                            </Badge>
                          ))}
                      </div>
                    );
                  },
                },
                ...((selectedTemplate?.content.columns || []) as any),
              ]}
              dataSource={updateData}
            />
          </TabsContent>
          <TabsContent value="noChange">
            <ProTable
              className="h-[calc(80vh-100px)]"
              columns={(selectedTemplate?.content.columns || []) as any}
              dataSource={noChangeData}
            />
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  // 步骤内容渲染
  const renderStepContent = () => {
    if (!open) return null;
    switch (currentStep) {
      case STEP_INPUT:
        return renderStepContentInput();
      case STEP_PREVIEW:
        return renderStepContentPreview();
      case STEP_CONFIRM:
        return executed ? <div>执行完成</div> : <div>执行中...</div>;
      default:
        return null;
    }
  };
  const stepFooterNextContent = useMemo(() => {
    if (loading) return '处理中...';
    if (currentStep === STEP_PREVIEW) return '提交';
    if (currentStep === STEP_CONFIRM) return '完成';
    return '下一步';
  }, [loading, currentStep]);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          setConfig(undefined);
          setRawData('');
          setNewData([]);
          setUpdateData([]);
          setNoChangeData([]);
          setCurrentStep(0);
        }
        setOpen(open);
      }}
    >
      <DialogContent className="max-w-[80vw]">
        <DialogHeader>
          <DialogTitle>数据导入</DialogTitle>
        </DialogHeader>
        <div className="max-h-[80vh] overflow-auto">{renderStepContent()}</div>
        <DialogFooter>
          <div className="mt-4 flex justify-end gap-4">
            <Button variant="outline" onClick={handlePrev} disabled={currentStep === 0} className="h-[32px] w-[92px]">
              上一步
            </Button>
            <Button
              onClick={handleNext}
              disabled={currentStep === TOTAL_STEPS - 1 || loading}
              className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]"
            >
              {stepFooterNextContent}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
      <DataImportTemplateDialog
        ref={dataImportTemplateDialogRef}
        onRefreshNeeded={() => {
          if (!config) return;
          action(listDataImportTemplates, { resource_name: config.templateResourceName }).then((res) =>
            setTemplates(res || []),
          );
        }}
      />
    </Dialog>
  );
});
DataImportDialog.displayName = 'DataImportDialog';
