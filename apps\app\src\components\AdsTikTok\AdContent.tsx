import { AdSheet } from '@/components/AdsTikTok/Sheet/AdSheet/AdSheet';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import {
  Button,
  Checkbox,
  DialogTrigger,
  DialogTitle,
  DialogHeader,
  Dialog,
  DialogContent,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  MultiSelect,
  Switch,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui';
import Pagination from '@/components/ui/PaginationAcc';
import { useAdList } from '@/hooks/useAdList';
import { useCheckboxLogic } from '@/hooks/useCheckboxLogic';
import { useGroupList } from '@/hooks/useGroupList';
import { usePagination } from '@/hooks/usePagination';
import { useTableLogic } from '@/hooks/useTableLogic';
import { useAdActions, useCurrentAd, useCurrentAdGroup, useCurrentAdvertiser } from '@/store/ads/adStore';
import debounce from 'lodash/debounce';
import { Plus, RefreshCw, XIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import SelectStatus from './selectTable/selectAccount/Selectstu';
import { getAdSecondaryStatusText } from '@/types/enum';
import { cn } from '@/utils/cn';
import { toast } from 'react-hot-toast';
import { confirm } from '@/components/ConfirmDialog';
import { useGroupsWithRelations } from '@/hooks/useGroupsWithRelations';
import { AdItem } from '@/types/ads';

export default function AdContent() {
  const currentAdvertiser = useCurrentAdvertiser();
  const currentAdGroup = useCurrentAdGroup();
  const currentAd = useCurrentAd();
  const { setAdRightDrawer, setCurrentAd, setCurrentAdGroup, updateAdStatus } = useAdActions();
  const { adData, isLoading, refresh, mutate } = useAdList();
  const { selectedRows, sortState, sortedData, handleSelectAll, handleSelect, handleSort } = useTableLogic(
    adData?.list ?? [],
    'id',
    currentAd?.map((item) => item.id) || [],
  );
  const [operationStatus, setOperationStatus] = useState<string>('All');
  const [searchText, setSearchText] = useState<string>('');
  const [updatingRows, setUpdatingRows] = useState<string[]>([]);
  const { currentPage, pageSize, handlePaginationChange, updatePagination } = usePagination({
    totalItems: adData?.total || 0,
    initialPageSize: adData?.pageSize || 10,
    initialPage: adData?.page || 1,
    onPaginationChange: (newPage, newPageSize) => {
      mutate({
        newPage,
        newPageSize,
        total: adData?.total || 0,
      });
    },
  });
  const { groupRelations } = useGroupsWithRelations();

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      mutate({
        newPage: 1,
        newPageSize: pageSize,
        total: adData?.total || 0,
        newAdName: value,
        operationStatus,
      });
    }, 1000),
    [operationStatus, pageSize, adData?.total],
  );

  const onSearch = (value: string) => {
    setSearchText(value);
    debouncedSearch(value);
  };

  const handleCreateAd = (createType: 'normal' | 'quick') => {
    setAdRightDrawer({
      show: true,
      type: 'create',
      createType,
      formValue: {},
      title: createType === 'normal' ? '常规创建' : '快速创建',
    });
  };

  // const handleEdit = (record: AdItem) => {
  //   setAdRightDrawer({
  //     show: true,
  //     type: 'edit',
  //     formValue: record,
  //     title: '编辑广告',
  //   });
  // };

  const isAllSelected = sortedData.length > 0 && sortedData.every((item) => selectedRows.includes(item.id));

  const { handleCheckboxChange, handleAllCheckboxChange } = useCheckboxLogic({
    selectedRows,
    sortedData,
    currentItems: currentAd || [],
    setCurrentItems: setCurrentAd,
    itemKey: 'id',
    handleSelect,
    handleSelectAll,
  });

  const columns = [
    {
      key: 'checkbox',
      title: (
        <Checkbox
          className="h-[14px] w-[14px] border-[#9FA4B2]"
          checked={isAllSelected}
          onCheckedChange={(checked) => handleAllCheckboxChange(!!checked)}
        />
      ),
      width: 50,
    },
    { key: 'switch', title: '开关', width: 60, fixed: 'left' as 'left' },
    {
      key: 'adName',
      title: '广告',
      width: 400,
      fixed: 'left' as 'left',
      tooltip: {
        enable: true,
        placement: 'right' as const,
        contentKey: 'adName',
      },
    },
    {
      key: 'accountName',
      title: '广告账户',
      width: 180,
      tooltip: {
        enable: true,
        placement: 'right' as const,
        contentKey: 'advertiserName',
      },
    },
    {
      key: 'campaignName',
      title: '广告系列',
      width: 180,
      tooltip: {
        enable: true,
        placement: 'right' as const,
        contentKey: 'campaignName',
      },
    },
    {
      key: 'groupName',
      title: '广告组',
      width: 180,
      tooltip: {
        enable: true,
        contentKey: 'groupName',
        placement: 'right' as const,
      },
    },
    { key: 'actions', title: '操作', width: 150, fixed: 'left' as 'left' },
    { key: 'pubStatus', title: '创建状态', width: 150 },
    { key: 'operationStatus', title: '投放状态', width: 120, fixed: 'left' as 'left' },
    { key: 'spend', title: '消耗', width: 80, sortable: true },
    { key: 'cpc', title: '平均点击成本(CPC)', width: 120, sortable: true },
    { key: 'cpm', title: '千次展示成本(CPM)', width: 120, sortable: true },
    { key: 'impressions', title: '展示量', width: 85, sortable: true },
    { key: 'clicks', title: '点击量', width: 80, sortable: true },
    { key: 'purchases', title: '付费数', width: 80, sortable: true },
    { key: 'onsiteShoppingRate', title: '点击转化率', width: 80, sortable: true },
    { key: 'onsiteShoppingRoas', title: 'ROAS', width: 80, sortable: true },
    { key: 'totalOnsiteShoppingValue', title: '总收入', width: 90, sortable: true },
    { key: 'onsiteOnWebCart', title: '加入购物车次数', width: 90, sortable: true },
    // { key: 'ctr', title: '点击率(CTR)', width: 120, sortable: true },
    { key: 'orderValue', title: '平均订单价值', width: 120, sortable: true },
  ];

  const renderCell = (key: string, record: AdItem) => {
    const isUpdating = updatingRows.includes(record.id);

    switch (key) {
      case 'checkbox':
        return (
          <Checkbox
            className="h-[14px] w-[14px] border-[#9FA4B2]"
            checked={selectedRows.includes(record.id)}
            disabled={isUpdating || record.pubStatus !== 'SUCCESS'}
            onCheckedChange={(checked) => handleCheckboxChange(record, !!checked)}
          />
        );
      case 'switch':
        return (
          <div className="w-[60px]">
            <Switch
              className="h-[16px] w-[28px]"
              thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
              checked={record?.operationStatus === 'ENABLE'}
              disabled={isUpdating || record.pubStatus !== 'SUCCESS'}
              onCheckedChange={async (checked) => {
                try {
                  setUpdatingRows((prev) => [...prev, record.id]);
                  await updateAdStatus({
                    advertiserId: record.advertiserId,
                    ids: [record.id],
                    operationStatus: checked ? 'ENABLE' : 'DISABLE',
                  });
                } catch (error) {
                  console.error('更新广告状态失败:', error);
                } finally {
                  setUpdatingRows((prev) => prev.filter((id) => id !== record.id));
                }
              }}
            />
          </div>
        );
      case 'adName':
        return (
          <div className="flex items-center gap-3">
            {record.vodUrl && (
              <Dialog>
                <DialogTrigger asChild>
                  <div className="h-12 min-w-12 flex-shrink-0 cursor-pointer overflow-hidden rounded-md">
                    <video src={record.vodUrl} className="h-full w-full object-cover" />
                  </div>
                </DialogTrigger>
                <DialogContent className="max-h-[50vh] max-w-[400px] overflow-hidden bg-[#151C29]">
                  <DialogHeader>
                    <DialogTitle>广告视频预览</DialogTitle>
                  </DialogHeader>
                  <div className="h-full w-full overflow-hidden">
                    <video src={record.vodUrl} controls className="h-full w-full rounded-lg object-contain" />
                  </div>
                </DialogContent>
              </Dialog>
            )}
            <div>
              {record?.adName}
              <div className="mt-[2px] text-xs text-gray-500">{record.id}</div>
            </div>
          </div>
        );
      case 'actions':
        return (
          <div className="flex items-center gap-6">
            <Button
              variant="link"
              size="sm"
              disabled={isUpdating || record.pubStatus === 'CREATING'}
              className="px-0 text-sm text-[#00E1FF] hover:no-underline disabled:opacity-50"
              onClick={() => {
                confirm({
                  content: (
                    <div className="space-y-2">
                      <div className="text-base font-medium">确认删除该广告?</div>
                      <div className="text-sm text-gray-400">删除后将无法恢复，请谨慎操作</div>
                    </div>
                  ),
                  onConfirm: async () => {
                    try {
                      setUpdatingRows((prev) => [...prev, record.id]);
                      await updateAdStatus({
                        advertiserId: record.advertiserId,
                        ids: [record.id],
                        operationStatus: 'DELETE',
                      });
                    } catch (error) {
                      console.error('删除广告失败:', error);
                    } finally {
                      setUpdatingRows((prev) => prev.filter((id) => id !== record.id));
                    }
                  },
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      case 'groupName':
        const groupRelation = groupRelations.find((relation) => relation.groupId === record.groupId);
        return (
          <div>
            <div>{groupRelation?.groupName ?? record?.groupName ?? record?.jsonDate?.adgroupName}</div>
            <div className="mt-[2px] text-xs text-gray-500">{record.groupId}</div>
          </div>
        );
      case 'campaignName':
        const campaignRelation = groupRelations.find((relation) => relation.groupId === record.groupId);
        return (
          <div>
            <div className="truncate">{campaignRelation?.campaignName || record.campaignName}</div>
            <div className="mt-[2px] text-xs text-gray-500">{record?.campaignId}</div>
          </div>
        );
      case 'accountName':
        const advertiserRelation = groupRelations.find((relation) => relation.groupId === record.groupId);
        return (
          <div>
            <div className="truncate">{advertiserRelation?.advertiserName || record?.advertiserName}</div>
            <div className="mt-[2px] text-xs text-gray-500">{record?.advertiserId}</div>
          </div>
        );
      case 'pubStatus':
        return (
          <div className="flex w-full items-center">
            <span
              className={`mr-2 h-2 w-2 flex-shrink-0 rounded-full ${
                record.pubStatus === 'SUCCESS'
                  ? 'bg-green-500'
                  : record.pubStatus === 'FAIL'
                    ? 'bg-red-500'
                    : record.pubStatus === 'BUILDING'
                      ? 'bg-orange-500'
                      : record.pubStatus === 'CREATING'
                        ? 'bg-blue-500'
                        : 'bg-gray-500'
              }`}
            />
            <div className="flex flex-col flex-nowrap items-start overflow-hidden whitespace-nowrap">
              {record.pubStatus === 'SUCCESS'
                ? '成功'
                : record.pubStatus === 'FAIL'
                  ? '失败'
                  : record.pubStatus === 'BUILDING'
                    ? '生成中'
                    : record.pubStatus === 'CREATING'
                      ? '创建中'
                      : '未知状态'}
              {record?.message && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="mt-[2px] block truncate text-xs text-gray-500">{record?.message}</span>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{record?.message}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        );
      case 'operationStatus':
        // @ts-ignore
        const secondaryStatus = record?.secondaryStatus || '';
        const isEnabled = record?.operationStatus === 'ENABLE';
        const DISABLE = record?.operationStatus === 'DISABLE';
        const FROZEN = record?.operationStatus === 'FROZEN';
        return (
          <div>
            <div className={`text-sm ${isEnabled ? 'text-green-500' : DISABLE ? 'text-[#F3A93C]' : 'text-gray-500'}`}>
              {isEnabled ? '开启' : DISABLE ? '已暂停' : FROZEN ? '已冻结' : '未知状态'}
            </div>
            {secondaryStatus && (
              <div className="mt-[2px]">
                <div className={`text-xs text-[#9FA4B2]`}>{getAdSecondaryStatusText(secondaryStatus)}</div>
              </div>
            )}
          </div>
        );
      case 'spend':
        return record?.metricsResult?.spend;
      case 'cpc':
        return record?.metricsResult?.cpc;
      case 'cpm':
        return record?.metricsResult?.cpm;
      case 'impressions':
        return record?.metricsResult?.impressions;
      case 'clicks':
        return record?.metricsResult?.clicks;
      case 'purchases':
        return record?.metricsResult?.initiateCheckout;
      case 'onsiteShoppingRate':
        return record?.metricsResult?.onsiteShoppingRate;
      case 'onsiteShoppingRoas':
        return record?.metricsResult?.onsiteShoppingRoas;
      case 'totalOnsiteShoppingValue':
        return record?.metricsResult?.totalOnsiteShoppingValue;
      case 'onsiteOnWebCart':
        return record?.metricsResult?.onsiteOnWebCart;
      case 'ctr':
        return record?.metricsResult?.ctr;
      case 'orderValue':
        return record?.metricsResult?.valuePerOnsiteShopping;
      default:
        return '';
    }
  };

  const availableGroupCount = useMemo(() => {
    return currentAdGroup?.filter((group) => group.pubStatus === 'SUCCESS').length;
  }, [currentAdGroup]);

  const { groups, isLoading: groupLoading } = useGroupList(1, 999);

  const handleGroupsChange = (value: string[]) => {
    const newGroups = groups?.list?.filter((item) => value.includes(item.groupId));
    setCurrentAdGroup(newGroups || []);
    mutate({
      newPage: 1,
      newPageSize: pageSize,
      total: groups?.total || 0,
    });
  };
  const onOperationStatusChange = (value: string) => {
    setOperationStatus(value);
    // 仅用于更新分页的状态
    updatePagination({
      newPage: 1,
      newPageSize: pageSize,
      total: adData?.total || 0,
    });
    // 实际调用接口的数据
    mutate({
      newPage: 1,
      newPageSize: pageSize,
      total: adData?.total || 0,
      newAdName: searchText,
      operationStatus: value,
    });
  };
  return (
    <div className="mt-6">
      <div className="flex items-center">
        {groups?.list?.length ? (
          <MultiSelect
            options={
              groups?.list
                ?.filter((group) => group.pubStatus === 'SUCCESS')
                .map((group) => ({
                  label: group.groupName,
                  value: group.groupId,
                  disabled: group.pubStatus !== 'SUCCESS',
                })) || []
            }
            onValueChange={handleGroupsChange}
            defaultValue={currentAdGroup?.map((group) => group.groupId) || []}
            placeholder={`选择广告组`}
            placeholderTitle={`广告组`}
            test={true}
            variant="inverted"
            animation={2}
            maxCount={1}
          />
        ) : (
          groupLoading && <div className="mr-4 h-8 w-[200px] animate-pulse rounded bg-gray-700/50" />
        )}
        <SelectStatus onChange={(value) => onOperationStatusChange(value)} />
        <div className="flex h-8 w-[240px] items-center rounded border border-[#1C2A3F] px-3 text-[12px] text-white">
          <div className="w-12 border-r border-gray-700 text-white">广告</div>
          <Input
            placeholder="请输入名称"
            value={searchText}
            className="h-full border-0 bg-transparent placeholder:text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
            onChange={(e) => onSearch?.(e.target.value)}
          />
          {searchText && (
            <XIcon
              className="mx-2 h-8 cursor-pointer text-[#9FA4B2]"
              onClick={(event) => {
                event.stopPropagation();
                setSearchText('');
                debouncedSearch('');
              }}
            />
          )}
        </div>
      </div>
      <div className="mt-6 flex items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="h-8 rounded px-4 text-[12px] text-xs font-medium text-[#050A1C]"
              disabled={!currentAdvertiser || !currentAdGroup || availableGroupCount === 0}
            >
              <Plus className="mr-1.5 h-4 w-4 flex-shrink-0" />
              新建广告
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className={cn('min-w-24 px-0 py-2')}>
            <DropdownMenuItem
              onClick={() => handleCreateAd('quick')}
              className="h-8 w-24 flex-shrink-0 cursor-pointer items-center justify-center hover:bg-[#354054]"
            >
              快速创建
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleCreateAd('normal')}
              className="h-8 w-24 flex-shrink-0 cursor-pointer items-center justify-center hover:bg-[#354054]"
            >
              常规创建
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {/* <Button variant="outline" className="ml-4 h-8 border-gray-700 text-[12px]">
          批量操作
        </Button>
        */}
        {/* <Button variant="outline" className="ml-4 h-8 border-gray-700 text-[12px]">
          导出列表
        </Button> */}
        <Button
          variant="outline"
          className="ml-4 h-8 rounded border-[#363D54] bg-transparent text-[12px]"
          onClick={refresh}
        >
          <RefreshCw className="mr-1.5 h-4 w-4" />
          获取实时数据
        </Button>
        {/* <Button
          variant="outline"
          disabled={syncLoading}
          className="ml-4 h-8 border-[#363D54] bg-transparent text-[12px]"
          onClick={async () => {
            setSyncLoading(true);
            try {
              await syncRealTimeData();
              toast.success('同步成功');
            } finally {
              setSyncLoading(false);
            }
          }}
        >
          <SyncIcon className="mr-1.5 h-4 w-4" />
          {syncLoading ? '获取中...' : '同步'}
        </Button> */}
      </div>
      <CommonTable
        columns={columns}
        dataSource={sortedData}
        selectedRows={selectedRows}
        sortState={sortState}
        rowKey="id"
        onSelectAll={handleSelectAll}
        onSelect={handleSelect}
        onSort={handleSort}
        renderCell={renderCell}
        loading={isLoading}
      />
      <div className="flex justify-end">
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={adData?.total || 0}
          handlePaginationChange={handlePaginationChange}
        />
      </div>
      <AdSheet
        onOpenChange={(status: boolean) => {
          if (!status) {
            setAdRightDrawer({
              show: false,
              type: 'create',
              formValue: {},
            });
          }
        }}
      />
    </div>
  );
}
