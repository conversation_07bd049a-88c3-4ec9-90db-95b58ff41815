'use client';
import { CloudDriveManage } from '@/components/icon/cloudDrive';
import { Button } from '@/components/ui';
import { Progress } from '@/components/ui/Progress';
import { useCloudStorageSize } from '@/hooks/useWallet';
import { guide } from '@/store/guide';
import useMaterialStore from '@/store/materialStore';
import Link from 'next/link';
import { useEffect } from 'react';

export default function CloudDrive() {
  const { storage, refresh: refreshStorage } = useCloudStorageSize();
  const { setMaterialDrawerOpen } = useMaterialStore();
  const { currentStep, handleHighlight } = guide();

  const used = storage.used ?? 0;
  const total = storage.total ?? 0;
  const progressPercentage = total ? Math.floor((used / total) * 100) : 0;
  const isLoading = !storage.used && !storage.total;

  useEffect(() => {
    handleHighlight('next5', 3, currentStep);
    refreshStorage();
  }, [handleHighlight, currentStep, refreshStorage]);

  return (
    <div id="next5">
      <Link className="flex w-72 items-center justify-between" href="/cloud" prefetch={true}>
        <Button
          className="mr-3 flex h-8 w-full items-center rounded-lg border border-[#EFEDFD] border-opacity-10 bg-[#EFEDFD] bg-opacity-10 p-0 text-xs hover:bg-[#EFEDFD] hover:bg-opacity-10"
          variant="secondary"
          onClick={() => {
            setTimeout(() => {
              setMaterialDrawerOpen(false);
            }, 1000);
          }}
        >
          <div className="flex justify-around pl-4 hover:cursor-pointer hover:bg-opacity-10 hover:text-[#00E1FF]">
            <CloudDriveManage />
            <div className="ml-2 cursor-pointer">云盘中心</div>
          </div>
          <div className="ml-3 mr-3 h-3 border border-[#363D54]"></div>
          <div className="mr-4 flex-1">
            <div className="flex justify-between">
              {isLoading ? (
                <div className="h-4 w-24 animate-pulse rounded bg-[#363D54]" />
              ) : (
                <div className="font-normal text-[#B2B6C3]">
                  {(used / 1024).toFixed(1)}GB / {Math.floor(total / 1024).toFixed()}GB
                </div>
              )}
              <div className="ml-6 text-[#00E1FF]">管理</div>
            </div>
            {isLoading ? (
              <div className="mt-1 h-1 w-full animate-pulse rounded bg-[#363D54]" />
            ) : (
              <Progress className="mt-1 bg-[#D9D9D9] bg-opacity-10" value={progressPercentage} color="#00e1ff" />
            )}
          </div>
        </Button>
      </Link>
    </div>
  );
}
