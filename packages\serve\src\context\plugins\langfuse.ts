import Langfuse from 'langfuse';
import { ActionContextPluginLoader } from '../../types';

type FetchLangfusePlugin = Langfuse;

const fetchLangfusePlugin: ActionContextPluginLoader<'langfuse', FetchLangfusePlugin> = (context) => {
  return {
    name: 'langfuse',
    plugin: new Langfuse({
      baseUrl: process.env.LANGFUSE_BASEURL,
      publicKey: context.tenant.config.langfuse_public_key,
      secretKey: context.tenant.config.langfuse_secret_key,
    }),
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * langfuse 实例
     */
    langfuse: FetchLangfusePlugin;
  }
}

export default fetchLangfusePlugin;
