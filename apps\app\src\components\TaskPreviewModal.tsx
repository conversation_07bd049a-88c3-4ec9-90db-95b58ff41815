import { getVideoGenerationTaskById } from '@/services/actions/video-generation-task';
import { action, ActionResult } from '@/utils/server-action/action';
import React, { useEffect, useImperativeHandle } from 'react';
import { Loader } from './icon/loader';
import TaskItem from './TaskItem';
import { Dialog, DialogContent, DialogHeader, Panel } from './ui';

export type TaskType = ActionResult<typeof getVideoGenerationTaskById>;

interface TaskPreviewModalProps {}

export interface TaskPreviewModalRef {
  show: (taskIds: string[]) => void;
}

export const TaskPreviewModal = React.forwardRef<TaskPreviewModalRef, TaskPreviewModalProps>((_props, ref) => {
  const [open, setOpen] = React.useState(false);
  const [taskIds, setTaskIds] = React.useState<string[]>([]);
  const [tasks, setTasks] = React.useState<TaskType[]>([]);

  useImperativeHandle(ref, () => ({
    show(_taskIds) {
      setOpen(true);
      setTaskIds(_taskIds);
    },
  }));

  useEffect(() => {
    if (!open || !taskIds) {
      setTasks([]);
      return;
    }

    const promises = taskIds.map((taskId) => action(getVideoGenerationTaskById, { taskId }));
    Promise.all(promises).then((res) => {
      setTasks(res as TaskType[]);
    });
  }, [open, taskIds]);

  return (
    <Dialog open={open} onOpenChange={(o) => setOpen(o)}>
      <DialogContent className="w-[1024px] max-w-[1024px]">
        <DialogHeader>任务详情</DialogHeader>
        <Panel className="h-[80vh] w-full overflow-auto p-4">
          {tasks.length ? (
            tasks.map((task) => <TaskItem key={task.id} data={task} />)
          ) : (
            <div className="flex h-full items-center justify-center">
              <Loader />
            </div>
          )}
        </Panel>
      </DialogContent>
    </Dialog>
  );
});

TaskPreviewModal.displayName = 'TaskPreviewModal';
