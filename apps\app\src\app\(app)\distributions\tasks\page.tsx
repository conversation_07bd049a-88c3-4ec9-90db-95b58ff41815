'use client';

import ProTable, { useTableRowSelect } from '@/components/pro/pro-table';
import { Button, Panel } from '@/components/ui';
import { VideoDistributionDialog, VideoDistributionDialogRef } from '@/components/VideoDistributionDialog';
import { VideoDistributionTaskPreviewModal } from '@/components/VideoDistributionTaskPreviewModal';
import {
  pageVideoDistributionTasks,
  updateVideoDistributionTaskPlanCount,
} from '@/services/actions/video-distribution-task';
import { action, ActionResult, useAction } from '@/utils/server-action/action';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { useDistributionTaskColumns } from './config';
import { QuickDataPicker } from './quick-data-picker';
import toast from 'react-hot-toast';

export type DistributionTaskType = ActionResult<typeof pageVideoDistributionTasks>['list'][0];

export default function Distributions() {
  const { columns, taskPreviewModalRef } = useDistributionTaskColumns();
  const videoDistributionDialog = useRef<VideoDistributionDialogRef>(null);

  const [date, setDate] = useState<Date | undefined>(new Date());

  const {
    data: dataSource,
    loading,
    run: refresh,
  } = useAction(pageVideoDistributionTasks, {
    pagination: { page: 1, pageSize: 1000 },
    filters: { distribution_batch_no: dayjs(date).format('YYYY-MM-DD') },
    sorters: [{ field: 'tmp_created_at', order: 'desc' }],
  });

  const { selectedRowKeys, onSelect, onSelectAll } = useTableRowSelect({ dataSource: dataSource?.list, rowKey: 'id' });

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <div className="mb-4 flex w-full justify-between px-4">
          <div className="flex items-center gap-4">
            <div className="text-base font-bold">分发排期</div>
            <QuickDataPicker date={date} setDate={setDate} />
          </div>
          <div className="flex items-center gap-4">
            <Button
              className="h-[32px] w-[128px] text-[#050A1C] hover:text-[#050A1C]"
              disabled={selectedRowKeys.size === 0}
              onClick={() => videoDistributionDialog.current?.open({ taskIds: Array.from(selectedRowKeys) })}
            >
              <div>视频分发</div>
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable
            columns={columns}
            loading={loading}
            dataSource={dataSource?.list || []}
            selection={{ selectedRowKeys, onSelect, onSelectAll }}
            onEdit={async (value, record, col) => {
              if (col.key === 'plan_count') {
                await action(updateVideoDistributionTaskPlanCount, { taskId: record.id, planCount: value });
                toast.success('更新成功');
                await refresh();
                return;
              }
              toast.error('不支持的编辑');
              return;
            }}
          />
        </div>
      </Panel>
      <VideoDistributionTaskPreviewModal ref={taskPreviewModalRef} />
      <VideoDistributionDialog ref={videoDistributionDialog} />
    </div>
  );
}
