import { DOWNLOADING_STATUS_TEXT, DownloadingStatus, useBatchDownload } from '@/hooks/useBatchDownload';
import { DownloadingItem } from './DownloadingItem';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui';
import { cn } from '@/utils/cn';

const DownloadingList = () => {
  const { downloadingList } = useBatchDownload();
  if (!downloadingList?.length) return null;

  return (
    <Table>
      <TableHeader>
        <TableRow className="hover:bg-[#0D1320] hover:bg-opacity-10">
          <TableHead className="h-[18px] pb-4 whitespace-nowrap text-[#81889D] text-xs pl-0 ">文件名称</TableHead>
          <TableHead className="h-[18px] pb-4 whitespace-nowrap text-[#81889D] text-xs text-center">上传状态</TableHead>
          <TableHead className="h-[18px] pb-4 whitespace-nowrap text-[#81889D] text-xs pl-0 text-left">操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {downloadingList?.map((item, idx: number) => (
          <TableRow key={item.id} className={cn('text-xs')}>
            <TableCell className={cn('flex items-center justify-start text-white pl-0', idx === 0 ? 'mt-2' : 'mt-0')}>
              <DownloadingItem {...item} />
            </TableCell>
            <TableCell
              className={cn(
                item.status === DownloadingStatus.DOWNLOADING && 'text-white',
                item.status === DownloadingStatus.SUCCESS && 'text-[#60D2A7]',
                item.status === DownloadingStatus.FAILED && 'text-[#ff6161]',
                'pb-0 px-0 text-center',
                idx === 0 ? 'mt-2' : 'mt-0',
              )}
            >
              {DOWNLOADING_STATUS_TEXT[item.status]}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export { DownloadingList };
