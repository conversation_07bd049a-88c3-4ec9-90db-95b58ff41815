import { AdItem, StoreProductItemType } from '@/types/ads';
import { AdFormValues } from './AdSheet/adFormSchema';

export interface AdSheetProps {
  onOpenChange: (open: boolean) => void;
  onSubmit?: (values: AdFormValues) => void;
}

export interface VideoFieldProps {
  form: any;
  isCreating: boolean;
  uploadRef: React.RefObject<HTMLInputElement>;
  uploadList: any[];
  onLocalUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  selectedVideoItem: any;
}

export interface AdTextFieldProps {
  form: any;
  isGenerating: boolean;
  type: 'edit' | 'create';
  onGenerate: () => void;
  onCancelGeneration: () => void;
}

export interface GoodsFieldProps {
  form: any;
  type: 'edit' | 'create';
  formValue?: AdItem;
  selectedRowKeys: StoreProductItemType[];
  setSelectedRowKeys: (goods: StoreProductItemType[]) => void;
  setIsModalOpen: (open: boolean) => void;
}

export interface IdentityFieldProps {
  form: any;
  adsIdentity: any[];
}
