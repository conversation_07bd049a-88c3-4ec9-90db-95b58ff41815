export const Magic = ({ className }: { className?: string }) => {
  return (
    <svg viewBox="0 0 26 32" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <circle cx="13.215" cy="16.1955" r="7.80488" fill="#D9D9D9" />
      <g filter="url(#filter0_f_5016_292)">
        <ellipse
          cx="1.81747"
          cy="3.50292"
          rx="1.81747"
          ry="3.50292"
          transform="matrix(-0.829186 -0.558972 -0.558972 0.829186 11.9297 12.4766)"
          fill="#B7EAC1"
        />
      </g>
      <g filter="url(#filter1_f_5016_292)">
        <ellipse
          cx="17.3258"
          cy="17.3633"
          rx="2.83708"
          ry="4.29088"
          transform="rotate(17.0225 17.3258 17.3633)"
          fill="#09B3FF"
        />
      </g>
      <g filter="url(#filter2_f_5016_292)">
        <ellipse
          cx="14.0198"
          cy="10.216"
          rx="1.92413"
          ry="2.12634"
          transform="rotate(-98.4492 14.0198 10.216)"
          fill="#54FFE0"
        />
      </g>
      <g filter="url(#filter3_f_5016_292)">
        <ellipse
          cx="12.9109"
          cy="21.7898"
          rx="1.33327"
          ry="4.06532"
          transform="rotate(-81.8222 12.9109 21.7898)"
          fill="#5434FF"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_5016_292"
          x="0.994141"
          y="6.28726"
          width="14.9414"
          height="16.1559"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="2.5" result="effect1_foregroundBlur_5016_292" />
        </filter>
        <filter
          id="filter1_f_5016_292"
          x="9.33594"
          y="8.17639"
          width="15.9805"
          height="18.3739"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="2.5" result="effect1_foregroundBlur_5016_292" />
        </filter>
        <filter
          id="filter2_f_5016_292"
          x="3.89648"
          y="0.286957"
          width="20.2461"
          height="19.858"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_5016_292" />
        </filter>
        <filter
          id="filter3_f_5016_292"
          x="0.882812"
          y="12.3486"
          width="24.0566"
          height="18.8824"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_5016_292" />
        </filter>
      </defs>
    </svg>
  );
};
