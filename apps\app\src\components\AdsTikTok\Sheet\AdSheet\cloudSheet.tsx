'use client';

import LibraryContainer from '@/components/LibraryContainer';
import { DialogTitle, VisuallyHidden } from '@/components/ui';
import { Sheet, SheetClose, SheetContent } from '@/components/ui/Sheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { useAdActions } from '@/store/ads/adStore';
import useMaterialStore from '@/store/materialStore';
import { MaterialItemType } from '@/hooks/useMaterial';
import { useEffect } from 'react';

export default function CloudSheet({
  open,
  onOpenChange,
  onConfirmSelect,
  isQuickCreate = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirmSelect: (list: MaterialItemType[]) => void;
  isQuickCreate?: boolean;
}) {
  const { setCloudSheetOpen } = useAdActions();
  const { setCloudTab, cloudTab } = useMaterialStore();

  useEffect(() => {
    if (!isQuickCreate) {
      setCloudTab(2);
    }
  }, [isQuickCreate]);

  const handleClose = () => {
    onOpenChange(false);
    setCloudSheetOpen(false);
    setCloudTab(1);
  };

  const handleConfirmSelect = (list: MaterialItemType[]) => {
    onConfirmSelect(list);
    onOpenChange(false);
    setCloudSheetOpen(false);
    setCloudTab(1);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="min-w-[800px] rounded-l-none rounded-r-2xl border border-l-0 border-[#EFEDFD] border-opacity-10 bg-[#0D1320] px-8 py-6 pr-0"
      >
        <VisuallyHidden>
          <DialogTitle className="hidden">Only for visually hidden</DialogTitle>
        </VisuallyHidden>

        <Tabs value={String(cloudTab)} onValueChange={(value) => setCloudTab(Number(value))}>
          <TabsList className="mb-4 w-[240px] bg-[#1A2333] p-1">
            {isQuickCreate ? (
              <TabsTrigger value="1" className="flex-1 data-[state=active]:bg-[#2D3B52] data-[state=active]:text-white">
                素材库
              </TabsTrigger>
            ) : (
              <TabsTrigger
                value="2"
                className={`${isQuickCreate ? 'flex-1' : 'w-full'} data-[state=active]:bg-[#2D3B52] data-[state=active]:text-white`}
              >
                生成库
              </TabsTrigger>
            )}
          </TabsList>
          {isQuickCreate && (
            <TabsContent value="1">
              <LibraryContainer
                handleConfirmSelect={handleConfirmSelect}
                config={{ select: true, upload: false, batch: false, search: false, createDir: false }}
              />
            </TabsContent>
          )}
          <TabsContent value="2">
            <LibraryContainer
              handleConfirmSelect={handleConfirmSelect}
              config={{ select: true, upload: false, batch: false, search: false, createDir: false }}
            />
          </TabsContent>
        </Tabs>

        <SheetClose className="absolute right-4 top-6 cursor-pointer" onClick={handleClose} />
      </SheetContent>
    </Sheet>
  );
}
