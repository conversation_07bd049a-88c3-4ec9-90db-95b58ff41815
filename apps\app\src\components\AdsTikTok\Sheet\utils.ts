import { MaterialItemType } from '@/hooks/useMaterial';
import { AdGroupListRequestCamel } from '@/services/interfaces/ads/res';
import { AdItem } from '@/types/ads';
import { calcGenerateCount, calcCostQuota } from '@roasmax/utils';

export function getDefaultFormValues(type: string, formValue?: AdItem, currentAdGroup?: AdGroupListRequestCamel[]) {
  if (type === 'edit') {
    return {
      sheetType: 'edit' as const,
      adName: formValue?.adName || '',
      darkPostStatus: formValue?.jsonDate?.darkPostStatus === 'ON',
      operationStatus: formValue?.jsonDate?.operationStatus === 'ENABLE',
      selectedVideoItem: [],
      itemGroupIds: formValue?.jsonDate?.itemGroupIds || [],
      identityId: '',
      identityType: '',
      identityAuthorizedBcId: '',
      adgroupIds: [formValue?.jsonDate?.groupId || formValue?.groupId],
      adText: formValue?.jsonDate?.adText ?? formValue?.jsonDate?.adText,
      adId: formValue?.adId || '',
      id: formValue?.id || '',
    };
  }

  return {
    sheetType: 'normal' as const,
    adName: '',
    darkPostStatus: false,
    operationStatus: false,
    adText: '',
    selectedVideoItem: [],
    transitionMode: true,
    subtitle: true,
    itemGroupIds: [],
    adgroupIds: currentAdGroup?.filter((each) => !!each?.groupId)?.map((item) => item.groupId) || [],
  };
}

export const calculateExpectVideoCount = (selectedItems: MaterialItemType[], form: any, currentAdGroup: any[]) => {
  if (!form.getValues('sheetType') || form.getValues('sheetType') !== 'quick') return;

  const p = {
    method: 'normal' as 'normal' | 'gc_imitate',
    sliceDuration: 300,
    materialDurations: selectedItems?.map((m) => m?.video_duration) || [],
    generateRound: Number(form?.getValues('generateRound') ?? 1),
    prompts: form.getValues('prompts') as string[],
  };

  const expectVideoCount = Math.floor(calcGenerateCount(p) / (currentAdGroup?.length || 1));
  form.setValue('expectVideoCount', expectVideoCount);
  const costQuota: number = calcCostQuota(p);
  form.setValue('costQuota', costQuota);
};

interface FieldError {
  message?: string;
}

export const validateForm = (form: any, quota: number, isQuickCreate: boolean) => {
  const errorMessageMap: Record<string, string> = {
    adName: '广告名称',
    adText: '广告文案',
    selectedVideoItem: '视频',
    adgroupIds: '广告组',
    industry: '行业选择',
    generateRound: '生成轮次',
    subtitle: '字幕',
    speed: '视频加速',
    language: '原视频语言',
    prompts: '提示词',
    identityId: 'TikTok账号',
    identityType: 'TikTok账号',
    identityAuthorizedBcId: 'TikTok账号',
    itemGroupIds: '商品详情',
    darkPostStatus: '是否开启暗投',
    operationStatus: '是否开启投放',
  };

  const errors = form.formState.errors;

  // 遍历错误对象，遇到第一个错误就返回
  for (const [field, error] of Object.entries(errors) as [string, FieldError][]) {
    if (error?.message) {
      const fieldName = errorMessageMap[field] || field;
      return {
        valid: false,
        message: `请检查以下必填项: ${fieldName}`,
      };
    }
  }

  // 检查快速创建时的点数是否足够
  if (isQuickCreate) {
    if (Number(quota) && form?.getValues('costQuota') && Number(quota) - Number(form?.getValues('costQuota')) < 0) {
      return {
        valid: false,
        message: '点数不足, 无法创建',
      };
    }
  }

  return { valid: true };
};
