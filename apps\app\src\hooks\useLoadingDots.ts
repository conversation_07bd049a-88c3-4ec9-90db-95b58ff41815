import { useState, useEffect } from 'react';

/**
 * 一个用于生成加载动画点的 Hook
 * @param isCurrentStep - 是否是当前正在执行的步骤
 * @param interval - 点的变化间隔时间（毫秒），默认为 500ms
 * @returns 返回当前应该显示的加载点字符串
 */
export const useLoadingDots = (isCurrentStep: boolean, interval: number = 500): string => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (!isCurrentStep) {
      setDots('');
      return;
    }

    // 创建定时器，循环更新点的数量
    const timer = setInterval(() => {
      setDots((prevDots) => {
        switch (prevDots) {
          case '':
            return '.';
          case '.':
            return '..';
          case '..':
            return '...';
          default:
            return '';
        }
      });
    }, interval);

    // 清理定时器
    return () => {
      clearInterval(timer);
    };
  }, [isCurrentStep, interval]);

  // 如果不是当前步骤，返回空字符串
  return isCurrentStep ? dots : '';
};
