import { ActionContextPluginLoader } from '../../types';

type FetchTenantPlugin = () => Promise<{ id: string; name: string }>;

const fetchTenantPlugin: ActionContextPluginLoader<'fetchTenant', FetchTenantPlugin> = (context) => {
  return {
    name: 'fetchTenant',
    plugin: async () => {
      return { id: context.tenant.id, name: context.tenant.name };
    },
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 获取当前租户信息 自动缓存
     * @returns 当前租户信息
     *
     * @deprecated 租户基础信息已经没有必要再通过异步请求了，可以直接取 ctx.tenant 来获取当前请求所属租户
     */
    fetchTenant: FetchTenantPlugin;
  }
}

export default fetchTenantPlugin;
