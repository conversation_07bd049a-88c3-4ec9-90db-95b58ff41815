interface SSEHandlerOptions<T> {
  onLine?: (line: T) => void;
  onError?: (error: Error) => void;
  onFinish?: (line: T) => void;
}

// 辅助函数：尝试解析 JSON
const tryParseJSON = (data: string, buffer: string) => {
  let newBuffer = buffer;
  try {
    // 尝试解析当前数据
    const jsonData = JSON.parse(data);
    return { success: true, data: jsonData, newBuffer: '' };
  } catch (e) {
    // 如果解析失败，将当前数据添加到缓冲区
    newBuffer += data;
    try {
      // 尝试解析累积的缓冲区数据
      const jsonData = JSON.parse(newBuffer);
      return { success: true, data: jsonData, newBuffer: '' };
    } catch (e) {
      // 如果仍然解析失败，保留缓冲区数据等待下一次处理
      return { success: false, data: null, newBuffer };
    }
  }
};

export const handleSSEResponse = async <T>(response: Response, options: SSEHandlerOptions<T>): Promise<void> => {
  const { onLine, onError, onFinish } = options;

  if (!response.ok) {
    throw new Error('请求失败');
  }

  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('无法读取响应流');
  }

  let buffer = '';

  while (true) {
    const { done, value } = await reader.read();
    if (done) {
      onError?.(new Error('网络出错了，请检查您的网络'));
      break;
    }

    const chunk = new TextDecoder().decode(value);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.includes('workflow_finished')) {
        onFinish?.(line as unknown as T);
        return;
      }
      if (!line) continue;

      const data = line.replace('data:', '').trim();
      if (!data) continue;

      try {
        // 使用抽象的解析函数
        const { success, data: jsonData, newBuffer } = tryParseJSON(data, buffer);
        buffer = newBuffer;

        // 如果解析失败，继续下一行
        if (!success) continue;
        // 处理成功解析的JSON数据
        if (jsonData.event === 'workflow_finished' || data.includes('workflow_finished')) {
          if (jsonData.data.status === 'failed') {
            onError?.(new Error('Ai睡着啦，请再次分析唤醒它'));
          }
          onFinish?.(jsonData as T);
        } else if (jsonData.event === 'node_finished' && jsonData.data?.node_type === 'end') {
          onLine?.(jsonData as T);
        } else {
          continue;
        }
      } catch (error) {
        onError?.(error as Error);
        continue;
      }
    }
  }
};
