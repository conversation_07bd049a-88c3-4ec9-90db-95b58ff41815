import { UpdateWalletQuotaInput, WalletChangeType } from '@/types/wallet';
import { ActionContext } from '@roasmax/serve';

export const updateWalletQuota = async (ctx: ActionContext<UpdateWalletQuotaInput>) => {
  // 数据库
  const { db } = ctx;
  const { quota, changeType = WalletChangeType.CHARGE, changeReason, toTenantId, toUserId } = ctx.data;

  // 获取用户id
  const userId = ctx.user.id;
  // 获取当前用户对应的租户信息
  const tenantInfo = await ctx.fetchTenant();
  const { id: tenantId } = tenantInfo;

  // 1. 获取当前用户钱包
  const wallet = await db.member_wallets.findUnique({
    where: {
      tenant_id_user_id: {
        tenant_id: tenantId,
        user_id: userId,
      },
    },
  });

  if (!wallet) {
    throw new Error('找不到用户钱包');
  }

  // 2. 计算新的额度
  const newQuota = wallet.quota + quota;

  // 验证新的额度不能为负数
  if (newQuota < 0) {
    throw new Error('您账户上的余额不足，请联系管理员进行充值');
  }

  // 如果是转账操作，验证转入和转出的信息
  if (changeType === 'SET_MENU_IN' || changeType === 'SET_MENU_OUT') {
    if (!tenantId || !userId || !toTenantId || !toUserId) {
      throw new Error('转账操作需要提供完整的转入和转出信息');
    }
  }

  // 3. 更新钱包额度
  await db.member_wallets.update({
    where: {
      tenant_id_user_id: {
        tenant_id: tenantId,
        user_id: userId,
      },
    },
    data: {
      quota: newQuota,
    },
  });

  // 更新创建变更记录的部分
  await db.member_wallet_changelogs.create({
    data: {
      tenant_id: tenantId,
      user_id: userId,
      change_type: changeType,
      change_reason: changeReason,
      quota: quota,
      result_quota: newQuota,
      wallet_id: wallet.id,
      ...(changeType === 'SET_MENU_IN' || changeType === 'SET_MENU_OUT'
        ? {
            from_tenant_id: tenantId,
            from_user_id: userId,
            to_tenant_id: toTenantId,
            to_user_id: toUserId,
          }
        : {}),
    },
  });

  return { success: true, newQuota };
};
