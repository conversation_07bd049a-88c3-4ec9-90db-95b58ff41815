import { prisma } from '@/utils/prisma';
import { Logger } from '@roasmax/utils';
import { NextRequest, NextResponse } from 'next/server';

// 通过分享ID获取产品分析记录服务函数
const getProductAnalysisByShareId = async (shareId: string) => {
  const record = await prisma.product_analysis_records.findFirst({
    where: {
      share_id: shareId,
      tmp_deleted_at: null,
      share_expired_at: {
        gt: new Date(), // 确保分享链接未过期
      },
    },
  });

  return record;
};

export const GET = async (request: NextRequest, { params }: { params: { shareId: string } }) => {
  const logger = new Logger('product-analysis-share', 'api', request.headers.get('x-request-id') || 'unknown');
  const shareId = params.shareId;

  try {
    logger._start(`开始处理获取分享ID为 ${shareId} 的产品分析记录请求`);

    logger.info('开始查询产品分析记录');
    const record = await getProductAnalysisByShareId(shareId);

    if (!record) {
      logger.warn('未找到产品分析记录或分享链接已过期');
      return NextResponse.json({ success: false, message: '分享链接不存在或已过期' }, { status: 404 });
    }

    logger.info('获取产品分析记录成功', { id: record.id });
    logger._end('获取产品分析记录请求处理完成');

    return NextResponse.json({
      success: true,
      record,
    });
  } catch (error) {
    logger.error('获取产品分析记录失败', (error as Error).message);
    logger._end('获取产品分析记录请求处理失败');

    return NextResponse.json({ success: false, message: (error as Error).message }, { status: 500 });
  }
};
