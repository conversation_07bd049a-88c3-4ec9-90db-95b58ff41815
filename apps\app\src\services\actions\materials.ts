'use server';

import { createVideoGenerationSubTasks, emitVideoGenerationTask } from '@/services/domains/task/video-generation-task';
import { onFileUploaded, refreshStoreSize, userTotalStore } from '@/services/domains/vod';
import { TaskStatus } from '@/types/task';
import { ActionContext, server } from '@roasmax/serve';

/**
 * 查询素材库并返回带使用状态的素材列表
 */
export async function getMaterialList(
  ctx: ActionContext<{ page: number; pageSize: number; directoryId?: string; ids?: string[]; name?: string }>,
) {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }
  const where: NonNullable<NonNullable<Parameters<typeof ctx.db.materials.findMany>['0']>['where']> = {};
  if (ctx.data.directoryId) {
    where.directory_id = ctx.data.directoryId;
  }
  if (ctx.data.ids) {
    where.id = { in: ctx.data.ids };
  }
  if (ctx.data.name) {
    where.name = { contains: ctx.data.name };
  }
  where.tmp_deleted_at = null;

  const [materials = [], total = 0] = await Promise.all([
    ctx.db.materials.findMany({
      where,
      skip: (ctx.data.page - 1) * ctx.data.pageSize,
      take: ctx.data.pageSize,
      orderBy: [{ tmp_created_at: 'desc' }, { id: 'asc' }],
    }),
    ctx.db.materials.count({ where }),
  ]);

  const materialIds = materials.map((material) => material.id);
  const referencedMaterials =
    (await ctx.db.video_generation_sub_tasks.findMany({
      where: {
        origin_material_id: {
          in: materialIds,
        },
        status: { in: [TaskStatus.PENDING, TaskStatus.PROCESSING] },
      },
      select: {
        origin_material_id: true,
        status: true,
      },
    })) ?? [];

  const referencedMaterialIds = new Set(referencedMaterials?.map((m) => m.origin_material_id));
  const materialsWithUsage = materials.map((material) => ({
    ...material,
    usage: referencedMaterialIds.has(material.id),
  }));

  return {
    list: materialsWithUsage,
    total,
    page: ctx.data.page,
    pageSize: ctx.data.pageSize,
  };
}

/**
 * 分页查询素材库
 * 只获取当前目录下的素材，目录不进行递归查询
 */
export const pageMaterials = server(
  '分页查询素材库',
  async (
    ctx: ActionContext<{ page: number; pageSize: number; directoryId?: string; ids?: string[]; name?: string }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      return await getMaterialList(ctx);
    });
  },
);

/**
 * 获取单个视频的信息
 */
export const getMaterialInfo = server('获取单个视频的信息', async (ctx: ActionContext<{ materialId: string }>) => {
  const material = await ctx.db.materials.findUnique({
    where: { id: ctx.data.materialId },
  });
  if (!material) {
    throw new Error('未找到对应素材');
  }

  const res = await ctx.vod.DescribeMediaInfos({
    SubAppId: Number(ctx.tenant.config.vod_sub_app_id),
    FileIds: [material.vod_file_id],
  });
  if (!res.MediaInfoSet?.length) {
    throw new Error('未找到对应媒体信息');
  }
  return {
    ...material,
    vod: res.MediaInfoSet[0],
  };
});

/**
 * 添加素材
 * 上传到VOD后，调用此方法以更新或添加素材库记录信息
 */
export const addMaterial = server(
  '登记素材',
  async (
    ctx: ActionContext<{
      name?: string;
      vodFileId: string;
      directoryId: string;
      autoGenByTaskId?: string;
      isAudio?: boolean;
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      // 验证directoryId是否存在
      const directory = await ctx.db.material_directories.findUnique({ where: { id: ctx.data.directoryId } });
      if (!directory) {
        throw new Error('未找到目标目录');
      }
      console.log('开始登记素材开始登记素材');
      // 登记素材
      const [material] = await ctx.execute(onFileUploaded, {
        forceUpdate: false,
        info: {
          list: [{ name: ctx.data.name, vodFileId: ctx.data.vodFileId }],
          directoryId: ctx.data.directoryId,
          isAudio: ctx.data.isAudio,
        },
      });

      if (!material) {
        throw new Error('素材上传失败');
      }

      if (ctx.data.autoGenByTaskId) {
        // 自动创建并执行生成任务
        const generationSubTask = await ctx.execute(createVideoGenerationSubTasks, {
          taskId: ctx.data.autoGenByTaskId,
          materialIds: [material.id],
        });
        await ctx.execute(emitVideoGenerationTask, { taskId: generationSubTask.id });
      }
      return material;
    });
  },
);

/**
 * 移动素材
 */
export const moveMaterials = server(
  '移动素材',
  async (ctx: ActionContext<{ materialIds: string[]; directoryId: string }>) => {
    return ctx.trx(async (ctx) => {
      const directory = await ctx.db.material_directories.findUnique({ where: { id: ctx.data.directoryId } });
      if (!directory) {
        throw new Error('未找到目标目录');
      }
      await ctx.db.materials.updateMany({
        where: { id: { in: ctx.data.materialIds } },
        data: { directory_id: ctx.data.directoryId },
      });
      return;
    });
  },
);

/**
 * 删除素材
 * 逻辑删除, 仅标记删除时间, vod 中素材的清理由定时清理服务完成
 * 如果素材被 video_generation_sub_tasks 引用,则不允许删除
 */
export const removeMaterials = server('删除素材', async (ctx: ActionContext<{ materialIds: string[] }>) => {
  const res = await ctx.trx(async (ctx) => {
    const referencedMaterials = await ctx.db.video_generation_sub_tasks.findMany({
      where: {
        origin_material_id: {
          in: ctx.data.materialIds,
        },
        status: { in: [TaskStatus.PENDING, TaskStatus.PROCESSING] },
      },
      select: {
        origin_material_id: true,
        status: true,
      },
    });

    if (referencedMaterials.length > 0) {
      const referencedIds = referencedMaterials.map((m) => m.origin_material_id);
      throw new Error(`以下素材ID正在被任务引用,无法删除: ${referencedIds.join(', ')}`);
    }

    // 如果没有被引用,则进行逻辑删除
    await ctx.db.materials.updateMany({
      where: { id: { in: ctx.data.materialIds } },
      data: { tmp_deleted_at: new Date() },
    });

    return true;
  });
  await ctx.execute(refreshStoreSize, {});
  return res;
});

/**
 * 创建素材目录
 */
export const makeMaterialDirectory = server(
  '创建素材目录',
  async (ctx: ActionContext<{ name: string; parentId: string | undefined }>) => {
    if (!ctx.data.name) {
      throw new Error('目录名不能为空');
    }

    const tenant = await ctx.fetchTenant();

    return await ctx.trx(async (ctx) => {
      // 如果有父目录,检查父目录是否存在
      if (ctx.data.parentId) {
        const parent = await ctx.db.material_directories.findUnique({
          where: { id: ctx.data.parentId },
        });
        if (!parent) {
          throw new Error('未找到父级目录');
        }

        // 计算当前目录深度
        const depth = await getDirectoryDepth(ctx, parent.id);
        if (depth > 4) {
          throw new Error('目录层级最多支持五级');
        }
      }

      const directory = await ctx.db.material_directories.create({
        data: {
          name: ctx.data.name,
          tenant_id: tenant.id,
          parent_id: ctx.data.parentId,
        },
      });
      return directory;
    });
  },
);
/**
 * 修改文件夹名称
 */
export const updateDirName = server(
  '修改文件夹名称',
  async (ctx: ActionContext<{ name: string; id: string | undefined }>) => {
    if (!ctx.data.name) {
      throw new Error('目录名不能为空');
    }
    return await ctx.trx(async (ctx) => {
      if (ctx.data.id) {
        const parent = await ctx.db.material_directories.findUnique({
          where: { id: ctx.data.id },
        });
        if (!parent) {
          throw new Error('未找到素材');
        }
      }

      const directory = await ctx.db.material_directories.update({
        where: {
          id: ctx.data.id,
        },
        data: {
          name: ctx.data.name,
        },
      });
      return directory;
    });
  },
);
/**
 * 修改素材名称
 */
export const updateMaterialsName = server(
  '修改素材名称',
  async (ctx: ActionContext<{ name: string; id: string | undefined }>) => {
    if (!ctx.data.name) {
      throw new Error('目录名不能为空');
    }
    return await ctx.trx(async (ctx) => {
      if (ctx.data.id) {
        const parent = await ctx.db.materials.findUnique({
          where: { id: ctx.data.id },
        });
        if (!parent) {
          throw new Error('未找到素材');
        }
      }

      const directory = await ctx.db.materials.update({
        where: {
          id: ctx.data.id,
        },
        data: {
          name: ctx.data.name,
        },
      });
      return directory;
    });
  },
);
// 新增获取目录深度的辅助函数
async function getDirectoryDepth(ctx: ActionContext<any>, directoryId: string): Promise<number> {
  let currentDepth = 1;
  let currentDirectory = await ctx.db.material_directories.findUnique({
    where: { id: directoryId, tmp_deleted_at: null },
    select: { parent_id: true, tmp_created_at: true },
  });

  // 递归向上查找父目录，直到找到根目录
  while (currentDirectory?.parent_id) {
    currentDepth++;
    currentDirectory = await ctx.db.material_directories.findUnique({
      where: { id: currentDirectory.parent_id, tmp_deleted_at: null },
      select: { parent_id: true, tmp_created_at: true },
    });
  }

  return currentDepth;
}

/**
 * 递归获取指定目录及其所有子目录的ID
 */
async function getRecursiveDirectoryIds(ctx: ActionContext<any>, directoryId: string, tenantId: string) {
  const allSubDirectories: { id: string }[] | undefined = await ctx.db.$queryRaw`
    WITH RECURSIVE subdirectories AS (
      SELECT id
      FROM material_directories
      WHERE id = ${directoryId} AND tenant_id = ${tenantId} AND tmp_deleted_at IS NULL

      UNION ALL

      SELECT d.id
      FROM material_directories d
      INNER JOIN subdirectories sd ON d.parent_id = sd.id
      WHERE d.tenant_id = ${tenantId} AND d.tmp_deleted_at IS NULL
    )
    SELECT id FROM subdirectories;
  `;

  return allSubDirectories?.map((dir) => dir.id) || [];
}

/**
 * 获取用户使用总存储空间
 */
export const getUserTotalStore = server('获取用户使用总存储空间', async (ctx) => {
  const res = await ctx.execute(userTotalStore, {});
  return res;
});

export const getDirectoryList = async (ctx: ActionContext<{ parentId?: string }>) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  const targetId = ctx.data.parentId;
  const directories = await ctx.db.material_directories.findMany({
    where: {
      tenant_id: tenant.id,
      tmp_deleted_at: null,
      ...(targetId ? { parent_id: targetId } : {}),
    },
    orderBy: {
      tmp_created_at: 'desc',
    },
  });
  return directories;
};

/**
 * 获取素材目录列表
 */
export const getMaterialDirectories = server('获取素材目录列表', async (ctx: ActionContext<{ parentId?: string }>) => {
  return await getDirectoryList(ctx);
});

export const getRootDirectories = server('获取根目录', async (ctx: ActionContext<any>) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }
  return await ctx.db.material_directories.findMany({
    where: {
      tenant_id: tenant.id,
      tmp_deleted_at: null,
      parent_id: '',
      name: { in: ['原始素材', '生成素材'] },
    },
  });
});

/**
 * 递归获取指定目录及其所有子目录下的所有素材
 */
export const getAllMaterialsInDirectory = server(
  '递归获取目录下所有素材',
  async (ctx: ActionContext<{ directoryId: string }>) => {
    const tenant = await ctx.fetchTenant();
    if (!tenant) {
      throw new Error('未找到租户信息');
    }

    const directoryIds = await getRecursiveDirectoryIds(ctx, ctx.data.directoryId, tenant.id);

    // 获取素材
    const materials = await ctx.db.materials.findMany({
      where: {
        tenant_id: tenant.id,
        directory_id: { in: directoryIds },
        tmp_deleted_at: null,
      },
      orderBy: {
        tmp_created_at: 'desc',
      },
    });

    const totalCount = await ctx.db.materials.count({
      where: {
        tenant_id: tenant.id,
        directory_id: { in: directoryIds },
        tmp_deleted_at: null,
      },
    });

    return {
      list: materials,
      total: totalCount,
    };
  },
);

// 修改检查是否为子目录的辅助函数
async function isDescendant(ctx: ActionContext<any>, ancestorId: string, descendantId: string): Promise<boolean> {
  // 递归查找所有父目录
  async function findAllAncestors(currentId: string, ancestors: Set<string> = new Set()): Promise<Set<string>> {
    const directory = await ctx.db.material_directories.findUnique({
      where: { id: currentId, tmp_deleted_at: null },
      select: { parent_id: true, tmp_created_at: true },
    });

    if (!directory || !directory.parent_id) {
      return ancestors;
    }

    ancestors.add(directory.parent_id);
    return findAllAncestors(directory.parent_id, ancestors);
  }

  // 获取所有祖先目录ID
  const ancestors = await findAllAncestors(descendantId);

  // 检查目标ancestorId是否在祖先集合中
  return ancestors.has(ancestorId);
}

// 新增获取最大子目录深度的辅助函数
async function getMaxChildDepth(ctx: ActionContext<any>, directoryId: string): Promise<number> {
  // 递归获取子目录深度
  async function getDepth(currentId: string, depth: number = 1): Promise<number> {
    const children = await ctx.db.material_directories.findMany({
      where: { parent_id: currentId, tmp_deleted_at: null },
      select: { id: true, tmp_created_at: true },
    });

    if (children.length === 0) {
      return depth - 1;
    }

    const childDepths = await Promise.all(children.map((child) => getDepth(child.id, depth + 1)));

    return Math.max(...childDepths);
  }

  return getDepth(directoryId, 1);
}

/**
 * 批量删除素材和目录
 */
export const batchRemoveMaterialsAndDirs = server(
  '批量删除素材和目录',
  async (ctx: ActionContext<{ materialIds: string[]; directoryIds: string[] }>) => {
    const tenant = await ctx.fetchTenant();
    if (!tenant) {
      throw new Error('未找到租户信息');
    }

    const res = await ctx.trx(async (ctx) => {
      // 1. 检查目录是否存在
      if (ctx.data.directoryIds.length > 0) {
        const directories = await ctx.db.material_directories.findMany({
          where: {
            id: { in: ctx.data.directoryIds },
          },
        });
        if (directories.length !== ctx.data.directoryIds.length) {
          throw new Error('部分目录不存在');
        }

        // 2. 递归获取所有子目录下的素材，检查是否有被引用的素材
        for (const directoryId of ctx.data.directoryIds) {
          const directoryIds = await getRecursiveDirectoryIds(ctx, directoryId, tenant.id);
          const materialIds = await ctx.db.materials
            .findMany({
              where: {
                directory_id: { in: directoryIds },
                tmp_deleted_at: null,
              },
              select: { id: true },
            })
            .then((materials) => materials.map((m) => m.id));

          const referencedMaterial = await ctx.db.video_generation_sub_tasks.findFirst({
            where: {
              origin_material_id: { in: materialIds },
              status: { in: [TaskStatus.PENDING, TaskStatus.PROCESSING] },
            },
          });

          if (referencedMaterial) {
            throw new Error('目录下存在正在被任务引用的素材，无法删除');
          }
        }
      }

      // 3. 检查单独选择的素材是否被引用
      if (ctx.data.materialIds.length > 0) {
        const referencedMaterials = await ctx.db.video_generation_sub_tasks.findMany({
          where: {
            origin_material_id: { in: ctx.data.materialIds },
            status: { in: [TaskStatus.PENDING, TaskStatus.PROCESSING] },
          },
          select: {
            origin_material_id: true,
          },
        });

        if (referencedMaterials.length > 0) {
          const referencedIds = referencedMaterials.map((m) => m.origin_material_id);
          throw new Error(`以下素材ID正在被任务引用,无法删除: ${referencedIds.join(', ')}`);
        }
      }

      const deleteAt = new Date();

      // 4. 删除目录下的所有素材
      if (ctx.data.directoryIds.length > 0) {
        for (const directoryId of ctx.data.directoryIds) {
          const directoryIds = await getRecursiveDirectoryIds(ctx, directoryId, tenant.id);
          await ctx.db.materials.updateMany({
            where: {
              directory_id: { in: directoryIds },
              tmp_deleted_at: null,
            },
            data: { tmp_deleted_at: deleteAt },
          });
        }

        // 5. 删除目录
        await ctx.db.material_directories.updateMany({
          where: { id: { in: ctx.data.directoryIds } },
          data: { tmp_deleted_at: deleteAt },
        });
      }

      // 6. 删除单独选择的素材
      if (ctx.data.materialIds.length > 0) {
        await ctx.db.materials.updateMany({
          where: { id: { in: ctx.data.materialIds } },
          data: { tmp_deleted_at: deleteAt },
        });
      }

      return true;
    });
    // 7. 刷新存储空间
    await ctx.execute(refreshStoreSize, {});
    return res;
  },
);

/**
 * 批量移动素材和目录
 */
export const batchMoveMaterialsAndDirs = server(
  '批量移动素材和目录',
  async (ctx: ActionContext<{ materialIds: string[]; directoryIds: string[]; targetDirectoryId: string }>) => {
    return await ctx.trx(async (ctx) => {
      // 验证目标目录是否存在
      const targetDirectory = await ctx.db.material_directories.findUnique({
        where: { id: ctx.data.targetDirectoryId },
      });
      if (!targetDirectory) {
        throw new Error('未找到目标目录');
      }

      // 1. 移动目录
      if (ctx.data.directoryIds.length > 0) {
        // 计算目标目录的深度
        const targetDepth = await getDirectoryDepth(ctx, targetDirectory.id);
        if (targetDepth >= 5) {
          throw new Error('目标目录层级超过5层限制');
        }

        // 获取所有要移动的目录
        const directories = await ctx.db.material_directories.findMany({
          where: { id: { in: ctx.data.directoryIds } },
        });
        if (directories.length !== ctx.data.directoryIds.length) {
          throw new Error('部分目录不存在');
        }

        // 对每个要移动的目录进行处理
        for (const directory of directories) {
          // 检查是否在移动到自己的子目录
          const isChildDir = await isDescendant(ctx, directory.id, ctx.data.targetDirectoryId);
          if (directory.id === ctx.data.targetDirectoryId || isChildDir) {
            throw new Error('不能将目录移动到其自身或其子目录中');
          }

          // 计算移动后的总深度
          const childMaxDepth = await getMaxChildDepth(ctx, directory.id);
          if (targetDepth + (childMaxDepth + 1) > 5) {
            throw new Error(`目录 "${directory.name}" 移动后总层级将超过5层限制`);
          }

          // 更新目录的父级
          await ctx.db.material_directories.update({
            where: { id: directory.id },
            data: { parent_id: targetDirectory.id },
          });
        }
      }

      // 2. 移动素材
      if (ctx.data.materialIds.length > 0) {
        await ctx.db.materials.updateMany({
          where: { id: { in: ctx.data.materialIds } },
          data: { directory_id: ctx.data.targetDirectoryId },
        });
      }

      return true;
    });
  },
);

export const getDirectoryListWithPagination = async (
  ctx: ActionContext<{
    parentId?: string;
    page: number;
    pageSize: number;
    keyword?: string;
  }>,
) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  const where = {
    tenant_id: tenant.id,
    tmp_deleted_at: null,
    ...(ctx.data.parentId ? { parent_id: ctx.data.parentId } : {}),
    ...(ctx.data.keyword
      ? {
          name: {
            contains: ctx.data.keyword,
          },
        }
      : {}),
  };

  const [directories, total] = await Promise.all([
    ctx.db.material_directories.findMany({
      where,
      skip: (ctx.data.page - 1) * ctx.data.pageSize,
      take: ctx.data.pageSize,
      orderBy: [{ tmp_created_at: 'desc' }, { id: 'asc' }],
    }),
    ctx.db.material_directories.count({ where }),
  ]);

  return {
    list: directories,
    total,
    page: ctx.data.page,
    pageSize: ctx.data.pageSize,
  };
};
