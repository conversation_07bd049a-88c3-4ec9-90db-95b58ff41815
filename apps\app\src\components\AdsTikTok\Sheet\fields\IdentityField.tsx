import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from '@/components/ui';
import { IdentityFieldProps } from '../types';

export const IdentityField = ({ form, adsIdentity }: IdentityFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="identityId"
      render={({ field }) => {
        return (
          <FormItem className="mt-6 flex h-12 items-center">
            <FormLabel className="w-1/5 text-sm text-white">
              TikTok账号 <span className="ml-2 text-red-500">*</span>
            </FormLabel>
            <FormControl className="w-4/5">
              <Select
                onValueChange={(value) => {
                  const selectedIdentity = adsIdentity.find((identity) => identity.identityId === value);
                  if (selectedIdentity) {
                    field.onChange(value);
                    form.setValue('identityType', selectedIdentity.identityType);
                    form.setValue('identityAuthorizedBcId', selectedIdentity.identityAuthorizedBcId);
                  }
                }}
                value={field?.value ?? ''}
                defaultValue={''}
              >
                <SelectTrigger className="h-12 w-[360px] bg-transparent text-xs">
                  <SelectValue placeholder="请选择TikTok账号" />
                </SelectTrigger>
                <SelectContent>
                  {adsIdentity.map((identity) => (
                    <SelectItem key={identity.identityId} value={identity.identityId} className="text-xs">
                      <div className="flex items-center gap-2">
                        <div className="h-10 w-10 rounded-full">
                          <img src={identity.profileImage} alt="" className="h-10 w-10 rounded-md" />
                        </div>
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2 text-sm font-medium tracking-widest text-white">
                            {identity.displayName}
                          </div>
                          <div className="text-sm text-[#9FA4B2]">{identity.identityId}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
          </FormItem>
        );
      }}
    />
  );
};
