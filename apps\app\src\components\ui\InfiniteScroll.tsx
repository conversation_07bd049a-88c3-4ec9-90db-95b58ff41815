'use client';

import * as React from 'react';
import { cva } from 'class-variance-authority';

import { cn } from '@/utils/cn';

const labelVariants = cva('text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70');

type InfiniteScrollRef = void;

type InfiniteScrollProps = {
  className?: string;
  children?: React.ReactNode;
  debounceInterval?: number;
  throttleInterval?: number;
  bottomOffsetTrigger?: number;
  onBottomOffset?: () => void | Promise<void>;
};

function debounce<T extends (...args: any[]) => any>(func: T, wait: number) {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function throttle<T extends (...args: any[]) => any>(func: T, limit: number) {
  let inThrottle = false;
  return (...args: any[]) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

const InfiniteScroll = React.forwardRef<InfiniteScrollRef, InfiniteScrollProps>(
  (
    { className, throttleInterval = 100, debounceInterval = 100, bottomOffsetTrigger, onBottomOffset, ...props },
    ref,
  ) => {
    const scrollContainerRef = React.useRef<HTMLDivElement>(null);

    React.useImperativeHandle(ref, () => ({}), []);

    const handleScroll = React.useCallback(async () => {
      const sc = scrollContainerRef.current;
      if (!sc) return;
      const bottom = Math.ceil(sc.scrollTop + sc.clientHeight) >= sc.scrollHeight - (bottomOffsetTrigger || 0);
      if (bottom) {
        await onBottomOffset?.();
      }
    }, [bottomOffsetTrigger, onBottomOffset]);

    const debouncedHandleScroll = React.useMemo(
      () => debounce(handleScroll, debounceInterval),
      [handleScroll, debounceInterval],
    );
    const throttledHandleScroll = React.useMemo(
      () => throttle(debouncedHandleScroll, throttleInterval),
      [debouncedHandleScroll, throttleInterval],
    );

    React.useEffect(() => {
      const scrollContainer = scrollContainerRef.current;
      if (!scrollContainer) return;
      scrollContainer.addEventListener('scroll', throttledHandleScroll);
      return () => scrollContainer.removeEventListener('scroll', throttledHandleScroll);
    }, [throttledHandleScroll]);

    return (
      <div ref={scrollContainerRef} className={cn('px-2', labelVariants(), className)}>
        <div {...props} />
      </div>
    );
  },
);
InfiniteScroll.displayName = 'InfiniteScroll';

export { InfiniteScroll };
