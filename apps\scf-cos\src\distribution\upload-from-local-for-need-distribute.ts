'use strict';
import { sleep, snowflake } from '@roasmax/utils';
import { RequestRoasmax } from '@roasmax/utils/net';
import { uploadFileToVod } from '@roasmax/utils/tencentcloud';
import fs from 'fs';
import { uniq } from 'lodash';
import { EventPayload, Handler } from '..';
import { cos } from '../utils/cos';
import { prisma } from '../utils/prisma';

/**
 * 当原始素材被上传到需要【自动生成并分发】的空间时，触发该回调
 * 处理单个的cos文件
 * @param record
 */
export class UploadFromLocalForNeedDistributeHandler implements Handler {
  async handle(record: EventPayload['Records'][0]) {
    console.log('开始处理文件【自动分发-原始素材】', record.cos.cosObject.key);
    console.log('----------------------------');

    // 获取文件相对于bucket下的key
    const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

    const keyArr = key.split('/');
    const [, tenantId, , , ipName, productName, liveRoom, liveSession, ...fileName] = keyArr;

    if (!tenantId) {
      throw new Error(`文件名格式不正确，没有租户id: ${key}`);
    }
    if (!ipName) {
      throw new Error(`文件名格式不正确，没有IP名: ${key}`);
    }
    if (!productName) {
      throw new Error(`文件名格式不正确，没有商品名: ${key}`);
    }

    // 处理商品相关逻辑
    await this.handleProduct(tenantId, productName, ipName, liveRoom);

    if (!liveRoom) {
      console.log(`文件名格式不正确，没有直播间: ${key}`);
      return;
    }
    if (!liveSession) {
      console.log(`文件名格式不正确，没有直播场次: ${key}`);
      return;
    }

    if (!fileName.length) {
      console.log(`文件名格式不正确，不处理 ${key}`);
      return;
    }

    // 处理视频上传相关逻辑
    await this.handleVideoUpload(record, tenantId, ipName, productName, liveRoom, liveSession, fileName, key);
  }

  private async handleProduct(tenantId: string, productName: string, ipName: string, liveRoom?: string) {
    const product = await prisma.goods.findFirst({ where: { tenant_id: tenantId, name: productName } });

    if (!product) {
      if (productName !== '新建文件夹') {
        console.log(`商品不存在，新建 ${productName}`);
        await prisma.goods.create({
          data: {
            tenant_id: tenantId,
            name: productName,
            images: [],
            tags: ['自动创建'],
            properties: {},
            live_rooms: liveRoom ? [liveRoom] : [],
            ip: ipName,
            platform: '',
            product_identity: '',
            product_url: '',
            hot_product: 0,
            shop_name: '',
            commission_rate: 0,
            remark: '',
          },
        });
      }
    } else if (liveRoom && liveRoom !== '新建文件夹' && !product.live_rooms?.includes(liveRoom)) {
      await prisma.goods.update({
        where: { id: product.id },
        data: { live_rooms: uniq([...(product.live_rooms || []), liveRoom]) },
      });
    }
  }

  private async handleVideoUpload(
    record: EventPayload['Records'][0],
    tenantId: string,
    ipName: string,
    productName: string,
    liveRoom: string,
    liveSession: string,
    fileName: string[],
    key: string,
  ) {
    const mediaName = `${fileName.join('-')}`;
    const uniqueIdentifier = `${ipName}-${productName}-${liveSession}-${liveRoom}-${fileName.join('-')}`;
    // 检查重复素材
    const existMedia = await prisma.materials.findFirst({
      where: { unique_identifier: uniqueIdentifier, tenant_id: tenantId },
    });
    if (existMedia && new Date().getTime() - new Date(existMedia.tmp_created_at).getTime() < 10 * 60 * 1000) {
      console.log('同名素材已存在，跳过', mediaName);
      return;
    }

    await sleep(5000);

    // 处理文件下载
    const objectLocalPath = await this.downloadFile(record, key);

    // 获取配置信息
    const source_config = await prisma.source_configs.findFirst({ where: { tenant_id: tenantId } });
    if (!source_config) {
      throw new Error(`租户配置不存在 ${tenantId}`);
    }

    const rootDir = await prisma.material_directories.findFirst({
      where: { tenant_id: tenantId, name: '原始素材' },
    });
    if (!rootDir) {
      throw new Error(`未找到原始素材根目录 ${tenantId}`);
    }

    // 上传到VOD
    const uploadRes = await this.uploadToVod(objectLocalPath, source_config);

    // 创建生成任务
    await this.createGenerateTask(tenantId, mediaName, uploadRes.FileId, ipName, productName, liveRoom, liveSession);
  }

  private async downloadFile(record: EventPayload['Records'][0], key: string) {
    const bucket = `${record.cos.cosBucket.name}-${process.env.COS_APPID}`;
    const objectLocalPath = `/mnt/tmp/${snowflake.nextId()}.mp4`;

    await cos.downloadFile({
      Bucket: bucket,
      Region: process.env.COS_REGION!,
      Key: key,
      FilePath: objectLocalPath,
      ChunkSize: 1024 * 1024 * 10,
      ParallelLimit: 5,
      RetryTimes: 3,
      onProgress: (progress) => {
        console.log(`文件下载中...${progress.percent}`);
      },
    });

    return objectLocalPath;
  }

  private async uploadToVod(objectLocalPath: string, source_config: any) {
    const vodFileName = snowflake.nextId();
    const uploadRes = await uploadFileToVod({
      secretId: process.env.VOD_SECRET_ID!,
      secretKey: process.env.VOD_SECRET_KEY!,
      MediaName: vodFileName,
      MediaFilePath: objectLocalPath,
      SubAppId: source_config.vod_sub_app_id,
      ClassId: source_config.vod_c_screen_record_original,
      region: 'ap-shanghai',
    });

    fs.unlinkSync(objectLocalPath);
    await sleep(5000);

    return uploadRes;
  }

  private async createGenerateTask(
    tenantId: string,
    mediaName: string,
    fileId: string,
    ipName: string,
    productName: string,
    liveRoom: string,
    liveSession: string,
  ) {
    if (!process.env.ROASMAX_API || !process.env.ROASMAX_API_SECRET) {
      throw new Error('ROASMAX_API 或 ROASMAX_API_SECRET 未设置');
    }

    const roasmax = new RequestRoasmax({
      host: process.env.ROASMAX_API,
      secret: process.env.ROASMAX_API_SECRET,
      tenantId: tenantId,
    });

    const res = await roasmax.post('/api/webhooks/generate-video', {
      name: mediaName,
      vodFileId: fileId,
      properties: { distribute: 'auto', ipName, productName, liveRoom, liveSession },
    });

    if (res.status !== 200) {
      throw new Error(`视频生成任务创建失败 ${res.status} ${JSON.stringify(res.data)}`);
    }
  }
}
