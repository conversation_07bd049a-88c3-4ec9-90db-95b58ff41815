'use client';

import { cn } from '@/utils/cn';
import React, { useRef, useState, useEffect } from 'react';

type HighlighterProps = {
  children: React.ReactNode;
  className?: string;
  refresh?: boolean;
};

interface MousePosition {
  x: number;
  y: number;
}

function useMousePosition(): MousePosition {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      setMousePosition({ x: event.clientX, y: event.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return mousePosition;
}

export default function Highlighter({ children, className = '', refresh = false }: HighlighterProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mousePosition = useMousePosition();
  const mouse = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const containerSize = useRef<{ w: number; h: number }>({ w: 0, h: 0 });
  const [boxes, setBoxes] = useState<Array<HTMLElement>>([]);

  useEffect(() => {
    containerRef.current && setBoxes(Array.from(containerRef.current.children).map((el) => el as HTMLElement));
  }, []);

  useEffect(() => {
    initContainer();
    window.addEventListener('resize', initContainer);

    return () => {
      window.removeEventListener('resize', initContainer);
    };
  }, [setBoxes]);

  useEffect(() => {
    onMouseMove();
  }, [mousePosition]);

  useEffect(() => {
    initContainer();
  }, [refresh]);

  const initContainer = () => {
    if (containerRef.current) {
      containerSize.current.w = containerRef.current.offsetWidth;
      containerSize.current.h = containerRef.current.offsetHeight;
    }
  };

  const onMouseMove = () => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const { w, h } = containerSize.current;
      const x = mousePosition.x - rect.left;
      const y = mousePosition.y - rect.top;
      const inside = x < w && x > 0 && y < h && y > 0;
      if (inside) {
        mouse.current.x = x;
        mouse.current.y = y;
        boxes.forEach((box) => {
          const boxX = -(box.getBoundingClientRect().left - rect.left) + mouse.current.x;
          const boxY = -(box.getBoundingClientRect().top - rect.top) + mouse.current.y;
          box.style.setProperty('--mouse-x', `${boxX}px`);
          box.style.setProperty('--mouse-y', `${boxY}px`);
        });
      }
    }
  };

  return (
    <div className={className} ref={containerRef}>
      {children}
    </div>
  );
}

type HighlighterItemProps = {
  children: React.ReactNode;
  className?: string;
};

export function HighlighterItem({ children, className = '' }: HighlighterItemProps) {
  return (
    <div
      className={`before:bg-primary relative h-full overflow-hidden p-px before:absolute before:-left-9 before:-top-9 before:z-30 before:h-12 before:w-full before:translate-x-[var(--mouse-x)] before:translate-y-[var(--mouse-y)] before:rounded-full before:opacity-0 before:blur-[20px] before:transition-opacity before:duration-500 after:absolute after:inset-0 after:z-10 after:rounded-[inherit] after:opacity-0 after:transition-opacity after:duration-500 after:[background:_radial-gradient(35px_circle_at_var(--mouse-x)_var(--mouse-y),theme(colors.primary.400/50),transparent)] before:hover:opacity-20 after:group-hover:opacity-100 ${className}`}
    >
      {children}
    </div>
  );
}
export function HighlighterItem2({ children, className = '' }: HighlighterItemProps) {
  return (
    <div
      className={cn(
        'relative h-full overflow-hidden rounded-[24px] p-px',
        'before:pointer-events-none before:absolute before:-left-24 before:-top-24 before:z-30 before:h-48 before:w-48',
        'before:translate-x-[var(--mouse-x)] before:translate-y-[var(--mouse-y)] before:rounded-full before:bg-[#00E1FF]',
        'before:opacity-0 before:blur-[50px] before:transition-opacity before:duration-500',
        'after:absolute after:inset-0 after:z-10 after:rounded-[inherit] after:opacity-0 after:transition-opacity after:duration-500',
        'after:[background:_radial-gradient(150px_circle_at_var(--mouse-x)_var(--mouse-y),theme(colors.slate.400),transparent)]',
        'before:hover:opacity-20 after:group-hover:opacity-100',
        className,
      )}
    >
      {children}
    </div>
  );
}
