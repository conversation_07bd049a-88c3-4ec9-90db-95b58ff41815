export const Selected = () => {
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.992676" y="1.08789" width="15" height="15" rx="3.5" fill="black" fillOpacity="0.3" stroke="#95A0AA" />
      <path
        d="M11.8294 5.6992L7.63443 10.2254L5.77756 8.18857C5.7268 8.13142 5.66469 8.08547 5.5952 8.05363C5.52571 8.0218 5.45035 8.00478 5.37392 8.00366C5.29749 8.00255 5.22167 8.01735 5.15128 8.04714C5.08088 8.07693 5.01746 8.12105 4.96506 8.1767C4.85503 8.29365 4.79279 8.44758 4.79059 8.60814C4.78839 8.7687 4.8464 8.92427 4.95318 9.0442L7.21943 11.5304C7.22058 11.532 7.22208 11.5333 7.22381 11.5342C7.22356 11.5359 7.22356 11.5375 7.22381 11.5392C7.26108 11.575 7.30315 11.6055 7.34881 11.6298C7.36881 11.6448 7.38968 11.6586 7.41131 11.6711C7.47989 11.701 7.55397 11.7164 7.62881 11.7161C7.70269 11.7161 7.77581 11.7012 7.84381 11.6723C7.86542 11.6598 7.88629 11.646 7.90631 11.6311C7.95215 11.6081 7.99428 11.5784 8.03131 11.5429C8.03239 11.5415 8.03365 11.5403 8.03506 11.5392C8.03606 11.5372 8.03757 11.5355 8.03943 11.5342L12.6488 6.56295C12.7563 6.44364 12.8154 6.2885 12.8144 6.12791C12.8135 5.96733 12.7527 5.81288 12.6438 5.69482C12.5919 5.63832 12.5288 5.59329 12.4586 5.56262C12.3883 5.53195 12.3123 5.51632 12.2357 5.51673C12.159 5.51714 12.0832 5.53359 12.0132 5.56501C11.9433 5.59644 11.8807 5.64214 11.8294 5.6992Z"
        fill="white"
      />
      <rect x="0.492676" y="0.587891" width="16" height="16" rx="4" fill="white" />
      <path
        d="M11.8294 5.6992L7.63443 10.2254L5.77756 8.18857C5.7268 8.13142 5.66469 8.08547 5.5952 8.05363C5.52571 8.0218 5.45035 8.00478 5.37392 8.00366C5.29749 8.00255 5.22167 8.01735 5.15128 8.04714C5.08088 8.07693 5.01746 8.12105 4.96506 8.1767C4.85503 8.29365 4.79279 8.44758 4.79059 8.60814C4.78839 8.7687 4.8464 8.92427 4.95318 9.0442L7.21943 11.5304C7.22058 11.532 7.22208 11.5333 7.22381 11.5342C7.22356 11.5359 7.22356 11.5375 7.22381 11.5392C7.26108 11.575 7.30315 11.6055 7.34881 11.6298C7.36881 11.6448 7.38968 11.6586 7.41131 11.6711C7.47989 11.701 7.55397 11.7164 7.62881 11.7161C7.70269 11.7161 7.77581 11.7012 7.84381 11.6723C7.86542 11.6598 7.88629 11.646 7.90631 11.6311C7.95215 11.6081 7.99428 11.5784 8.03131 11.5429C8.03239 11.5415 8.03365 11.5403 8.03506 11.5392C8.03606 11.5372 8.03757 11.5355 8.03943 11.5342L12.6488 6.56295C12.7563 6.44364 12.8154 6.2885 12.8144 6.12791C12.8135 5.96733 12.7527 5.81288 12.6438 5.69482C12.5919 5.63832 12.5288 5.59329 12.4586 5.56262C12.3883 5.53195 12.3123 5.51632 12.2357 5.51673C12.159 5.51714 12.0832 5.53359 12.0132 5.56501C11.9433 5.59644 11.8807 5.64214 11.8294 5.6992Z"
        fill="#050A1C"
      />
    </svg>
  );
};
