# Roasmax - 部署与上线

## 📋 任务概述

**任务ID**: jtZMenPc7mjaB4EGSEPBoc  
**任务名称**: 部署与上线  
**优先级**: 高  
**预估工时**: 1-2天  
**前置依赖**: 系统测试与验证  

## 🎯 任务目标

安全地部署新认证系统到生产环境，完成 Authing 依赖的移除，确保系统平稳过渡和正常运行。

## 📊 详细任务分解

### 1. 部署前准备

#### 1.1 环境变量配置
```bash
# .env.production
# 移除 Authing 相关配置
# APPHOST=
# APPID=
# APPSECRET=
# PRIVATE_ACCESSKEYID_ID=
# PRIVATE_ACCESSKEYSECRET_ID=

# 新增本地认证配置
JWT_SECRET=your-super-secure-jwt-secret-key-here
PASSWORD_SALT_ROUNDS=12
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_DURATION=3600

# 数据库配置
DATABASE_URL=mysql://username:password@localhost:3306/roasmax_prod

# 其他现有配置保持不变
BULLMQ_ENDPOINT=your-bullmq-endpoint
LANGFUSE_BASEURL=your-langfuse-url
VOD_SECRETKEY=your-vod-secret
VOD_SECRETID=your-vod-id
```

#### 1.2 数据库迁移脚本
```sql
-- production-migration.sql
-- 生产环境数据库迁移脚本

-- 1. 备份现有数据
CREATE TABLE members_backup AS SELECT * FROM members;

-- 2. 添加新字段
ALTER TABLE members 
ADD COLUMN password_hash VARCHAR(255) NULL COMMENT '密码哈希值',
ADD COLUMN salt VARCHAR(255) NULL COMMENT '密码盐值',
ADD COLUMN is_migrated BOOLEAN DEFAULT FALSE COMMENT '是否已迁移',
ADD COLUMN password_reset_required BOOLEAN DEFAULT FALSE COMMENT '是否需要重置密码',
ADD COLUMN migration_date DATETIME NULL COMMENT '迁移时间',
ADD COLUMN authing_user_id VARCHAR(255) NULL COMMENT '原 Authing 用户ID',
ADD COLUMN last_login_at DATETIME NULL COMMENT '最后登录时间',
ADD COLUMN login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
ADD COLUMN locked_until DATETIME NULL COMMENT '锁定到期时间',
ADD COLUMN email_verified BOOLEAN DEFAULT TRUE COMMENT '邮箱是否已验证';

-- 3. 创建新表
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY COMMENT '角色ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  code VARCHAR(255) NOT NULL COMMENT '角色代码',
  name VARCHAR(255) NOT NULL COMMENT '角色名称',
  description TEXT COMMENT '角色描述',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted_at DATETIME NULL COMMENT '删除时间',
  UNIQUE KEY unique_tenant_code (tenant_id, code),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_code (code)
) COMMENT='角色定义表';

CREATE TABLE user_roles (
  id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
  role_code VARCHAR(255) NOT NULL COMMENT '角色代码',
  assigned_by VARCHAR(255) NULL COMMENT '分配者ID',
  assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  expires_at DATETIME NULL COMMENT '过期时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY unique_user_role (tenant_id, user_id, role_code),
  INDEX idx_tenant_user (tenant_id, user_id),
  INDEX idx_role_code (role_code)
) COMMENT='用户角色关联表';

CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY COMMENT '权限ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  role_code VARCHAR(255) NOT NULL COMMENT '角色代码',
  resource_type ENUM('MENU', 'API', 'DATA') NOT NULL COMMENT '资源类型',
  resource_code VARCHAR(255) NOT NULL COMMENT '资源代码',
  actions JSON COMMENT '允许的操作',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_tenant_role (tenant_id, role_code),
  INDEX idx_resource (resource_type, resource_code)
) COMMENT='权限资源表';

CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY COMMENT '会话ID',
  user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
  tenant_id VARCHAR(255) NOT NULL COMMENT '租户ID',
  token_hash VARCHAR(255) NOT NULL COMMENT 'Token哈希值',
  device_info JSON COMMENT '设备信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  last_active_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  expires_at DATETIME NOT NULL COMMENT '过期时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
  INDEX idx_user_id (user_id),
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires_at (expires_at)
) COMMENT='用户会话表';

-- 4. 创建索引
CREATE INDEX idx_members_email_tenant ON members(email, tenant_id);
CREATE INDEX idx_members_authing_user_id ON members(authing_user_id);
CREATE INDEX idx_members_migration_status ON members(is_migrated, password_reset_required);
CREATE INDEX idx_members_login_status ON members(last_login_at, login_attempts);
```

#### 1.3 部署脚本
```bash
#!/bin/bash
# deploy.sh - 生产环境部署脚本

set -e

echo "=== Roasmax 认证系统部署开始 ==="

# 1. 检查环境
echo "1. 检查部署环境..."
if [ ! -f ".env.production" ]; then
    echo "错误: .env.production 文件不存在"
    exit 1
fi

if [ ! -f "production-migration.sql" ]; then
    echo "错误: production-migration.sql 文件不存在"
    exit 1
fi

# 2. 备份数据库
echo "2. 备份数据库..."
BACKUP_FILE="roasmax_backup_$(date +%Y%m%d_%H%M%S).sql"
mysqldump --single-transaction --routines --triggers roasmax_prod > "backups/$BACKUP_FILE"
echo "数据库备份完成: $BACKUP_FILE"

# 3. 停止应用服务
echo "3. 停止应用服务..."
pm2 stop roasmax-app || true
pm2 stop roasmax-api || true

# 4. 更新代码
echo "4. 更新应用代码..."
git fetch origin
git checkout main
git pull origin main

# 5. 安装依赖
echo "5. 安装依赖..."
pnpm install --frozen-lockfile

# 6. 执行数据库迁移
echo "6. 执行数据库迁移..."
mysql roasmax_prod < production-migration.sql

# 7. 执行数据迁移
echo "7. 执行用户数据迁移..."
node scripts/migration/run-production-migration.js

# 8. 构建应用
echo "8. 构建应用..."
pnpm build

# 9. 更新环境变量
echo "9. 更新环境变量..."
cp .env.production .env

# 10. 启动应用服务
echo "10. 启动应用服务..."
pm2 start ecosystem.config.js --env production

# 11. 健康检查
echo "11. 执行健康检查..."
sleep 10
curl -f http://localhost:3000/api/health || {
    echo "健康检查失败，回滚部署..."
    pm2 stop all
    mysql roasmax_prod < "backups/$BACKUP_FILE"
    pm2 start ecosystem.config.js --env production
    exit 1
}

echo "=== 部署完成 ==="
echo "备份文件: backups/$BACKUP_FILE"
echo "应用状态: $(pm2 status)"
```

### 2. 生产环境数据迁移

#### 2.1 生产数据迁移脚本
```typescript
// scripts/migration/run-production-migration.js
const { PrismaClient } = require('@prisma/client');
const { ManagementClient } = require('authing-node-sdk');
const bcrypt = require('bcryptjs');
const fs = require('fs').promises;

class ProductionMigration {
  constructor() {
    this.prisma = new PrismaClient();
    this.authingClient = new ManagementClient({
      accessKeyId: process.env.PRIVATE_ACCESSKEYID_ID,
      accessKeySecret: process.env.PRIVATE_ACCESSKEYSECRET_ID,
    });
  }

  async run() {
    console.log('开始生产环境数据迁移...');
    
    try {
      // 1. 导出 Authing 数据
      console.log('1. 导出 Authing 用户数据...');
      const authingUsers = await this.exportAuthingUsers();
      
      // 2. 保存导出数据
      await fs.writeFile(
        `migration-data/production-export-${Date.now()}.json`,
        JSON.stringify(authingUsers, null, 2)
      );
      
      // 3. 迁移用户数据
      console.log('2. 迁移用户数据到本地数据库...');
      await this.migrateUsers(authingUsers);
      
      // 4. 迁移角色数据
      console.log('3. 迁移角色和权限数据...');
      await this.migrateRolesAndPermissions(authingUsers);
      
      // 5. 验证迁移结果
      console.log('4. 验证迁移结果...');
      await this.verifyMigration();
      
      console.log('生产环境数据迁移完成！');
      
    } catch (error) {
      console.error('迁移失败:', error);
      throw error;
    }
  }

  async exportAuthingUsers() {
    const users = [];
    let page = 1;
    const limit = 100;

    while (true) {
      const result = await this.authingClient.listUsers({ page, limit });
      
      if (!result.data?.list?.length) break;

      for (const user of result.data.list) {
        // 获取用户角色
        const roles = await this.authingClient.getUserRoles({
          userId: user.userId,
          userIdType: 'user_id'
        });

        users.push({
          userId: user.userId,
          email: user.email,
          nickname: user.nickname,
          username: user.username,
          phone: user.phone,
          tenantId: user.tenantId || 'default',
          status: user.status,
          roles: roles.data?.list || []
        });

        // 避免 API 限流
        await this.delay(100);
      }

      page++;
    }

    return users;
  }

  async migrateUsers(authingUsers) {
    for (const authingUser of authingUsers) {
      try {
        // 检查用户是否已存在
        const existingUser = await this.prisma.members.findFirst({
          where: {
            email: authingUser.email,
            tenant_id: authingUser.tenantId
          }
        });

        if (existingUser) {
          // 更新现有用户
          await this.prisma.members.update({
            where: { id: existingUser.id },
            data: {
              authing_user_id: authingUser.userId,
              nickname: authingUser.nickname,
              account: authingUser.username || authingUser.email,
              password_reset_required: true,
              is_migrated: false,
              migration_date: new Date(),
              email_verified: true
            }
          });
        } else {
          // 创建新用户
          await this.prisma.members.create({
            data: {
              id: this.generateUUID(),
              tenant_id: authingUser.tenantId,
              user_id: this.generateLocalUserId(),
              email: authingUser.email,
              nickname: authingUser.nickname || authingUser.email.split('@')[0],
              account: authingUser.username || authingUser.email,
              user_status: this.mapUserStatus(authingUser.status),
              admin: 0,
              password: '', // 临时空密码
              authing_user_id: authingUser.userId,
              password_reset_required: true,
              is_migrated: false,
              migration_date: new Date(),
              email_verified: true
            }
          });
        }

        console.log(`迁移用户: ${authingUser.email}`);
        
      } catch (error) {
        console.error(`迁移用户 ${authingUser.email} 失败:`, error);
      }
    }
  }

  async migrateRolesAndPermissions(authingUsers) {
    const processedRoles = new Set();

    for (const authingUser of authingUsers) {
      const localUser = await this.prisma.members.findFirst({
        where: {
          email: authingUser.email,
          tenant_id: authingUser.tenantId
        }
      });

      if (!localUser) continue;

      for (const role of authingUser.roles) {
        const roleKey = `${authingUser.tenantId}_${role.code}`;

        // 创建角色（去重）
        if (!processedRoles.has(roleKey)) {
          await this.prisma.roles.upsert({
            where: {
              tenant_id_code: {
                tenant_id: authingUser.tenantId,
                code: role.code
              }
            },
            create: {
              id: this.generateUUID(),
              tenant_id: authingUser.tenantId,
              code: role.code,
              name: role.name,
              description: role.description || `从 Authing 迁移: ${role.name}`,
              is_system: false
            },
            update: {}
          });

          processedRoles.add(roleKey);
        }

        // 分配用户角色
        await this.prisma.user_roles.upsert({
          where: {
            tenant_user_role: {
              tenant_id: authingUser.tenantId,
              user_id: localUser.user_id,
              role_code: role.code
            }
          },
          create: {
            id: this.generateUUID(),
            tenant_id: authingUser.tenantId,
            user_id: localUser.user_id,
            role_code: role.code
          },
          update: {}
        });
      }
    }
  }

  async verifyMigration() {
    const stats = {
      totalUsers: await this.prisma.members.count({
        where: { authing_user_id: { not: null } }
      }),
      totalRoles: await this.prisma.roles.count(),
      totalUserRoles: await this.prisma.user_roles.count(),
      usersNeedingPasswordReset: await this.prisma.members.count({
        where: { password_reset_required: true }
      })
    };

    console.log('迁移统计:');
    console.log(`- 迁移用户数: ${stats.totalUsers}`);
    console.log(`- 角色数: ${stats.totalRoles}`);
    console.log(`- 用户角色关系数: ${stats.totalUserRoles}`);
    console.log(`- 需要重置密码的用户数: ${stats.usersNeedingPasswordReset}`);

    return stats;
  }

  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  generateLocalUserId() {
    return 'usr_' + Math.random().toString(36).substr(2, 10);
  }

  mapUserStatus(authingStatus) {
    const statusMap = {
      'Activated': 'active',
      'Suspended': 'suspended',
      'Deactivated': 'inactive'
    };
    return statusMap[authingStatus] || 'active';
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 执行迁移
async function main() {
  const migration = new ProductionMigration();
  await migration.run();
  process.exit(0);
}

if (require.main === module) {
  main().catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
}
```

### 3. 健康检查和监控

#### 3.1 健康检查接口
```typescript
// apps/app/src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { JWTManager } from '@/utils/jwt';

export async function GET() {
  try {
    const healthChecks = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {
        database: 'unknown',
        jwt: 'unknown',
        auth: 'unknown'
      }
    };

    // 检查数据库连接
    try {
      await prisma.$queryRaw`SELECT 1`;
      healthChecks.checks.database = 'healthy';
    } catch (error) {
      healthChecks.checks.database = 'unhealthy';
      healthChecks.status = 'unhealthy';
    }

    // 检查 JWT 功能
    try {
      const testToken = JWTManager.generateToken({
        user_id: 'health-check',
        email: '<EMAIL>',
        nickname: 'Health Check',
        tenant_id: 'health-tenant'
      });
      
      const verified = await JWTManager.verifyToken(testToken);
      if (verified) {
        healthChecks.checks.jwt = 'healthy';
      } else {
        healthChecks.checks.jwt = 'unhealthy';
        healthChecks.status = 'unhealthy';
      }
    } catch (error) {
      healthChecks.checks.jwt = 'unhealthy';
      healthChecks.status = 'unhealthy';
    }

    // 检查认证系统
    try {
      const userCount = await prisma.members.count({
        where: { tmp_deleted_at: null }
      });
      
      if (userCount >= 0) {
        healthChecks.checks.auth = 'healthy';
      }
    } catch (error) {
      healthChecks.checks.auth = 'unhealthy';
      healthChecks.status = 'unhealthy';
    }

    const statusCode = healthChecks.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(healthChecks, { status: statusCode });
    
  } catch (error) {
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      error: error.message
    }, { status: 503 });
  }
}
```

#### 3.2 监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

LOG_FILE="/var/log/roasmax/monitor.log"
HEALTH_URL="http://localhost:3000/api/health"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

check_health() {
    local response=$(curl -s -w "%{http_code}" $HEALTH_URL)
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log "健康检查通过"
        return 0
    else
        log "健康检查失败 - HTTP $http_code: $body"
        return 1
    fi
}

check_processes() {
    local pm2_status=$(pm2 status | grep -c "online")
    if [ "$pm2_status" -gt 0 ]; then
        log "PM2 进程正常运行"
        return 0
    else
        log "PM2 进程异常"
        return 1
    fi
}

main() {
    log "开始系统监控检查"
    
    local health_ok=0
    local process_ok=0
    
    if check_health; then
        health_ok=1
    fi
    
    if check_processes; then
        process_ok=1
    fi
    
    if [ $health_ok -eq 1 ] && [ $process_ok -eq 1 ]; then
        log "系统状态正常"
    else
        log "系统状态异常，发送告警"
        # 这里可以添加告警通知逻辑
        # send_alert "Roasmax 系统状态异常"
    fi
}

# 每5分钟执行一次
while true; do
    main
    sleep 300
done
```

### 4. 回滚计划

#### 4.1 回滚脚本
```bash
#!/bin/bash
# rollback.sh - 回滚脚本

set -e

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    echo "可用备份文件:"
    ls -la backups/
    exit 1
fi

echo "=== 开始回滚到 Authing 系统 ==="

# 1. 停止应用
echo "1. 停止应用服务..."
pm2 stop all

# 2. 恢复数据库
echo "2. 恢复数据库..."
mysql roasmax_prod < "backups/$BACKUP_FILE"

# 3. 恢复代码
echo "3. 恢复代码版本..."
git checkout authing-version  # 切换到使用 Authing 的版本

# 4. 恢复环境变量
echo "4. 恢复环境变量..."
cp .env.authing .env

# 5. 重新安装依赖
echo "5. 重新安装依赖..."
pnpm install

# 6. 重新构建
echo "6. 重新构建应用..."
pnpm build

# 7. 启动应用
echo "7. 启动应用..."
pm2 start ecosystem.config.js --env production

# 8. 验证回滚
echo "8. 验证回滚结果..."
sleep 10
curl -f http://localhost:3000/api/health

echo "=== 回滚完成 ==="
```

## ✅ 验收标准

1. **部署成功**
   - [ ] 应用成功部署到生产环境
   - [ ] 数据库迁移执行成功
   - [ ] 用户数据完整迁移
   - [ ] 健康检查通过

2. **功能正常**
   - [ ] 用户可以正常登录
   - [ ] 权限验证正确
   - [ ] 业务功能不受影响
   - [ ] 性能满足要求

3. **监控有效**
   - [ ] 健康检查接口正常
   - [ ] 监控脚本运行正常
   - [ ] 日志记录完整
   - [ ] 告警机制有效

4. **安全保障**
   - [ ] 敏感数据已清理
   - [ ] 访问权限正确配置
   - [ ] 备份机制完善
   - [ ] 回滚方案可用

## 🔧 Augment Code 提示词

```
请帮我完成 Roasmax 项目新认证系统的生产环境部署：

1. 准备生产环境配置和部署脚本
2. 执行数据库迁移和用户数据迁移
3. 部署新认证系统到生产环境
4. 配置健康检查和监控
5. 移除 Authing 相关依赖和配置
6. 准备回滚方案和应急预案
7. 验证部署结果和系统稳定性

要求：
- 确保部署过程的安全性
- 提供完整的备份和回滚机制
- 包含详细的监控和日志
- 支持零停机或最小停机部署
- 提供完整的部署文档
- 确保数据迁移的完整性

请提供完整的部署脚本和配置文件。
```

## 📅 时间安排

- **第1天**: 部署准备和数据迁移
- **第2天**: 系统部署和验证

## 🚨 风险提示

1. **数据丢失风险**: 部署过程中可能出现数据丢失
2. **服务中断风险**: 部署期间可能影响用户使用
3. **回滚风险**: 回滚过程可能不够及时或完整

## 📞 协作要求

- 需要与运维团队协调部署时间
- 需要与测试团队验证部署结果
- 需要与产品团队确认用户通知方案
