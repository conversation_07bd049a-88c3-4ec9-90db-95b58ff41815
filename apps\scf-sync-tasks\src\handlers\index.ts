import { prisma } from '@/utils/prisma';
import { sleep } from '@roasmax/utils';
import generationTask from './generation-task';
import sliceTask from './slice-task';
import { fetchTenants, TaskStatus } from './utils';

type ITraceType = 'slice' | 'generation' | 'emit-generation';

// 按照租户进行同步 可以选择仅同步某种类型的任务
type ISyncByTenantHandlerParams = {
  type: 'tenant';
  traceType: ITraceType[];
  all?: boolean;
  tenantIds?: string[];
  // 忽略某些租户 仅在 all 为 true 时有效
  ignoreTenantIds?: string[];
};

// 按照trace进行同步
type ISyncByTraceHandlerParams = {
  type: 'trace';
  traceType: ITraceType;
  traceIds: string[];
};

export type ISyncHandlerParams<T extends 'tenant' | 'trace' = 'tenant' | 'trace'> = (T extends 'trace'
  ? ISyncByTraceHandlerParams
  : ISyncByTenantHandlerParams) & {
  // 有一些请求需要延迟执行
  delay?: number;
};

/**
 * 处理租户级别的同步
 * @param params
 */
const syncByTenant = async (params: ISyncHandlerParams<'tenant'>) => {
  console.log('开始租户级别任务处理...');
  console.log(`任务类型: ${params.traceType.join(',')}`);
  // 获取所有的租户
  const tenants = await fetchTenants({
    all: params.all,
    tenantIds: params.tenantIds,
    ignoreTenantIds: params.ignoreTenantIds,
  });
  console.log(`获取租户完成 共${tenants.length}个租户`);

  /**
   * 处理进行中的切分任务
   */
  if (params.traceType.includes('slice')) {
    // 获取所有进行中的任务
    const sliceTasks = await prisma.video_slice_tasks.findMany({
      where: { tenant_id: { in: tenants.map((t) => t.tenant_id) }, status: TaskStatus.PROCESSING },
    });
    console.log(`[获取任务] [slice] [进行中] 共${sliceTasks.length}个任务`);
    const originMaterials = await prisma.materials.findMany({
      where: { id: { in: sliceTasks.map((t) => t.origin_material_id!).filter(Boolean) } },
    });
    const sourceConfigs = await prisma.source_configs.findMany({
      where: { tenant_id: { in: sliceTasks.map((t) => t.tenant_id) } },
    });

    for (let i = 0; i < sliceTasks.length; i++) {
      console.log('同步任务', sliceTasks[i]!.id, `(${i + 1} / ${sliceTasks.length})`);
      const task = sliceTasks[i]!;
      const originMaterial = originMaterials.find((m) => m.id === task.origin_material_id);
      if (!originMaterial) {
        console.log('原始素材不存在', task.id, task.origin_material_id);
        continue;
      }
      const sourceConfig = sourceConfigs.find((c) => c.tenant_id === task.tenant_id);
      if (!sourceConfig) {
        console.log('租户配置不存在', task.id, task.tenant_id);
        continue;
      }
      await sliceTask.handleProcessing({ task, originMaterial, sourceConfig });
    }
  }

  /**
   * 处理进行中的生成子任务
   */
  if (params.traceType.includes('generation')) {
    const subTasks = await prisma.video_generation_sub_tasks.findMany({
      where: { tenant_id: { in: tenants.map((t) => t.tenant_id) }, status: TaskStatus.PROCESSING },
    });
    console.log(`[获取任务] [generation] [进行中] 共${subTasks.length}个任务`);
    const tasks = await prisma.video_generation_tasks.findMany({
      where: { id: { in: subTasks.map((t) => t.task_id) } },
    });
    const originMaterials = await prisma.materials.findMany({
      where: { id: { in: subTasks.map((t) => t.origin_material_id!).filter(Boolean) } },
    });
    const sourceConfigs = await prisma.source_configs.findMany({
      where: { tenant_id: { in: subTasks.map((t) => t.tenant_id) } },
    });

    for (let i = 0; i < subTasks.length; i++) {
      const subTask = subTasks[i]!;
      const task = tasks.find((t) => t.id === subTask.task_id);
      if (!task) {
        console.log('任务不存在', subTask.id, subTask.task_id);
        continue;
      }
      const originMaterial = originMaterials.find((m) => m.id === subTask.origin_material_id);
      if (!originMaterial) {
        console.log('原始素材不存在', subTask.id, subTask.origin_material_id);
        continue;
      }
      const sourceConfig = sourceConfigs.find((c) => c.tenant_id === subTask.tenant_id);
      if (!sourceConfig) {
        console.log('租户配置不存在', subTask.id, subTask.tenant_id);
        continue;
      }
      await generationTask.generation.handleProcessing({ subTask, task, originMaterial, sourceConfig });
    }
  }

  /**
   * 处理待触发的生成子任务
   */
  if (params.traceType.includes('emit-generation')) {
    const subTasks = await prisma.video_generation_sub_tasks.findMany({
      where: {
        tenant_id: { in: tenants.map((t) => t.tenant_id) },
        sub_task_type: 'generation',
        status: TaskStatus.PENDING,
      },
    });
    console.log(`[获取任务] [emit-generation] [待触发] 共${subTasks.length}个任务`);

    for (let i = 0; i < subTasks.length; i++) {
      const subTask = subTasks[i]!;
      await generationTask.generation.handlePending({ subTaskId: subTask.id });
    }
    return;
  }
};

/**
 * 处理trace级别的同步
 * @param params
 */
const syncByTrace = async (params: ISyncHandlerParams<'trace'>) => {
  /**
   * 处理进行中的切分任务
   */
  if (params.traceType === 'slice') {
    const tasks = await prisma.video_slice_tasks.findMany({
      where: { id: { in: params.traceIds }, status: TaskStatus.PROCESSING },
    });
    if (!tasks.length) {
      console.log('没有需要同步的任务');
      return;
    }
    const originMaterials = await prisma.materials.findMany({
      where: { id: { in: tasks.map((t) => t.origin_material_id!).filter(Boolean) } },
    });
    const sourceConfigs = await prisma.source_configs.findMany({
      where: { tenant_id: { in: tasks.map((t) => t.tenant_id) } },
    });
    console.log('开始同步任务', tasks.length);
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i]!;
      const originMaterial = originMaterials.find((m) => m.id === task.origin_material_id);
      if (!originMaterial) {
        console.log('原始素材不存在', task.id, task.origin_material_id);
        continue;
      }
      const sourceConfig = sourceConfigs.find((c) => c.tenant_id === task.tenant_id);
      if (!sourceConfig) {
        console.log('租户配置不存在', task.id, task.tenant_id);
        continue;
      }
      console.log('开始同步任务', task.id, `(${i + 1} / ${tasks.length})`);
      await sliceTask.handleProcessing({ task, originMaterial, sourceConfig });
    }
    return;
  }

  /**
   * 处理进行中的生成任务
   */
  if (params.traceType === 'generation') {
    const subTasks = await prisma.video_generation_sub_tasks.findMany({
      where: { id: { in: params.traceIds }, status: TaskStatus.PROCESSING },
    });
    if (!subTasks.length) {
      console.log('没有需要同步的任务');
      return;
    }
    const tasks = await prisma.video_generation_tasks.findMany({
      where: { id: { in: subTasks.map((t) => t.task_id) } },
    });
    const originMaterials = await prisma.materials.findMany({
      where: { id: { in: subTasks.map((t) => t.origin_material_id!).filter(Boolean) } },
    });
    const sourceConfigs = await prisma.source_configs.findMany({
      where: { tenant_id: { in: subTasks.map((t) => t.tenant_id) } },
    });
    console.log('开始同步任务', subTasks.length);
    for (let i = 0; i < subTasks.length; i++) {
      const subTask = subTasks[i]!;
      const task = tasks.find((t) => t.id === subTask.task_id);
      if (!task) {
        console.log('任务不存在', subTask.id, subTask.task_id);
        continue;
      }
      const originMaterial = originMaterials.find((m) => m.id === subTask.origin_material_id);
      if (!originMaterial) {
        console.log('原始素材不存在', subTask.id, subTask.origin_material_id);
        continue;
      }
      const sourceConfig = sourceConfigs.find((c) => c.tenant_id === subTask.tenant_id);
      if (!sourceConfig) {
        console.log('租户配置不存在', subTask.id, subTask.tenant_id);
        continue;
      }
      console.log('开始同步任务', subTask.id, `(${i + 1} / ${subTasks.length})`);
      await generationTask.generation.handleProcessing({ subTask, task, originMaterial, sourceConfig });
    }
    return;
  }

  /**
   * 处理待触发的生成子任务
   */
  if (params.traceType === 'emit-generation') {
    const subTasks = await prisma.video_generation_sub_tasks.findMany({
      where: { id: { in: params.traceIds }, sub_task_type: 'generation', status: TaskStatus.PENDING },
    });
    if (!subTasks.length) {
      console.log('没有需要同步的任务');
      return;
    }

    console.log('开始同步任务', subTasks.length);
    for (let i = 0; i < subTasks.length; i++) {
      const subTask = subTasks[i]!;
      console.log('开始同步任务', subTask.id, `(${i + 1} / ${subTasks.length})`);
      await generationTask.generation.handlePending({ subTaskId: subTask.id });
    }
    return;
  }
};

export default async (params: ISyncHandlerParams) => {
  await sleep(typeof params.delay === 'number' ? params.delay : 3000);

  /**
   * 处理租户级别的任务
   */
  if (params.type === 'tenant') {
    await syncByTenant(params);
  }

  /**
   * 处理trace级别的任务
   */
  if (params.type === 'trace') {
    await syncByTrace(params);
  }
  console.log('同步任务完成');
};
