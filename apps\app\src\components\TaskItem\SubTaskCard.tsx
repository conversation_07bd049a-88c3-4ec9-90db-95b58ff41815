'use client';

import { EyeFullfil } from '@/components/icon/EyeFullfil';
import { NotFoundVideoFullfil } from '@/components/icon/NotFoundVideoFullfil';
import { Card } from '@/components/ui/Card';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { TaskStatus } from '@/types/task';
import { ActionResult } from '@/utils/server-action/action';
import dayjs from 'dayjs';
import React from 'react';
import GradientCircleProgressBar from './RainbowCircleProgress';
import { Checkbox } from '@/components/ui';
import { cn } from '@/utils/cn';
import { NetError } from '@/components/icon/NetError';
import { ContentViolation } from '@/components/icon/ContentViolation';
import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { ProImage } from '@/components/pro/pro-image';
import { ProCheckbox } from '../pro/pro-checkbox';

/**
 * 子任务卡片
 * @param props
 * @returns
 */
const SubTaskCard: React.FC<{
  data: ArrayItemType<ArrayItemType<ActionResult<typeof pageVideoGenerationTasks>['list']>['subTasks']>;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  onClick?: () => void;
  className?: string;
}> = (props) => {
  const { data, checked, onCheckedChange, onClick, className, ...restProps } = props;

  return (
    <div className="relative pt-3">
      <div className="absolute left-4 top-[0px] z-[-1] h-[64px] w-[128px] rounded-lg bg-[#81889D] bg-opacity-15 backdrop-blur-[4px]"></div>
      <div className="absolute left-2 top-[6px] z-[-1] h-[64px] w-[144px] rounded-lg bg-[#81889D] bg-opacity-25 backdrop-blur-[4px]"></div>
      <Card
        onClick={onClick}
        {...restProps}
        className={cn(
          'h-[216px] w-[160px] cursor-pointer overflow-hidden rounded-xl border-none bg-[#1F2434] text-xs font-normal',
          className,
        )}
      >
        <SubTaskCardContent data={data} checked={checked} onCheckedChange={onCheckedChange} />
      </Card>
    </div>
  );
};

const SubTaskCardHoverContent = (props: { checked?: boolean; onCheckedChange?: (v: boolean) => void }) => {
  return (
    <div
      className={cn(
        'left-0 top-0 h-full w-full',
        'hidden group-hover:absolute group-hover:bg-[#00000066]',
        'flex items-center justify-center',
      )}
    >
      <div className="flex items-center justify-start gap-[6px]">
        <EyeFullfil />
        查看视频
      </div>
      <ProCheckbox
        checked={props.checked ? true : false}
        onClick={(e) => e.stopPropagation()}
        onCheckedChange={props.onCheckedChange}
        className={cn(
          'absolute right-3 top-3 bg-[#000714] bg-opacity-40',
          props.checked ? '' : 'hidden group-hover:block',
        )}
      />
    </div>
  );
};

const SubTaskCardContent = (props: {
  data: ArrayItemType<ArrayItemType<ActionResult<typeof pageVideoGenerationTasks>['list']>['subTasks']>;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}) => {
  const { data } = props;

  const notEmpty = !!props.data.origin_material || !!props.data.generated_materials?.length;
  const hoverContent = notEmpty && (
    <SubTaskCardHoverContent checked={props.checked} onCheckedChange={props.onCheckedChange} />
  );

  // 任务失败 显示失败
  if (data.status === TaskStatus.FAILED) {
    const judgeErrorType = (
      data: ArrayItemType<ActionResult<typeof pageVideoGenerationTasks>['list']>['subTasks'][0],
    ) => {
      if (data.status_desc?.includes('The response was filtered due to the prompt triggering Azure OpenAI')) {
        return '内容违规';
      }
      return '网络错误';
    };

    const errorType = judgeErrorType(data);

    return (
      <div className="flex h-full w-full flex-col items-center justify-center gap-[16px] bg-[#272D3E]">
        {errorType === '内容违规' ? (
          <ContentViolation className="h-[56px] w-[56px]" />
        ) : (
          <NetError className="h-[56px] w-[56px]" />
        )}
        <div>{errorType}</div>
      </div>
    );
  }

  if (data.status === TaskStatus.PENDING) {
    return (
      <div>
        <div className="group relative h-[160px] w-[160px] overflow-hidden">
          <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center bg-[#272D3E]">
            <GradientCircleProgressBar progress={0.3} className="mb-4 h-[32px] w-[32px]" />
            <label className="text-[#72798E]">正在排队中...</label>
          </div>
          {hoverContent}
        </div>
        <div className="mx-[10px] my-[10px]">
          <div className="mb-1 overflow-hidden overflow-ellipsis text-nowrap">{data.origin_material?.name}</div>
          <div className="text-[#72798E]">{dayjs(data.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
        </div>
      </div>
    );
  }

  // 任务进行中 显示进度
  if (data.status !== TaskStatus.SUCCESS) {
    return (
      <div>
        <div className="group relative h-[160px] w-[160px] overflow-hidden">
          <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center bg-[#272D3E]">
            <GradientCircleProgressBar progress={0.3} className="mb-4 h-[32px] w-[32px]" />
            <label className="text-[#72798E]">
              正在生成
              {<span className="text-[#fff]">{` ${data.progress.processing} / ${data.progress.total} `}</span>}
              视频...
            </label>
          </div>
          {hoverContent}
        </div>
        <div className="mx-[10px] my-[10px]">
          <div className="mb-1 overflow-hidden overflow-ellipsis text-nowrap">{data.origin_material?.name}</div>
          <div className="text-[#72798E]">{dayjs(data.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
        </div>
      </div>
    );
  }

  // 任务成功 但原始素材丢失 显示视频
  if (!data.origin_material?.vod_cover_url) {
    return (
      <div>
        <div className="group relative h-[160px] w-[160px] overflow-hidden" onClick={() => {}}>
          <div className="flex h-[160px] w-[160px] items-center justify-center gap-2 bg-[#272D3E]">
            <NotFoundVideoFullfil />
          </div>
          <div className="absolute bottom-1 left-1 flex h-6 items-center rounded-lg bg-[#272D3E66] px-2 text-xs">
            {data.generated_materials.length || 0} / {data.progress.total} 条
          </div>
          {hoverContent}
        </div>
        <div className="mx-[10px] my-[10px]">
          <div className="overflow-hidden overflow-ellipsis text-nowrap text-center">
            {data.origin_material?.name || '原视频素材已删除'}
          </div>
        </div>
      </div>
    );
  }

  // 任务成功 显示视频
  return (
    <div>
      <div className="group relative h-[160px] w-[160px] overflow-hidden">
        <ProImage
          width={160}
          height={160}
          style={{ height: '160px', width: '160px', objectFit: 'cover', objectPosition: 'center' }}
          src={data.origin_material?.vod_cover_url || ''}
          alt={data.origin_material?.name || ''}
          fallback={() => (
            <div className="flex h-full w-full flex-col items-center justify-center gap-[16px] bg-[#272D3E] text-[#9FA4B2]">
              <NotFoundCover className="h-[35px] w-[48px]" />
              <div>封面丢失</div>
            </div>
          )}
        />
        <div className="absolute bottom-1 left-1 flex h-6 items-center rounded bg-[#272D3E66] px-2 text-xs">
          {data.generated_materials.length || 0} / {data.progress.total} 条
        </div>
        {hoverContent}
      </div>
      <div className="mx-[10px] my-[10px]">
        <div className="mb-1 overflow-hidden overflow-ellipsis text-nowrap">{data.origin_material.name}</div>
        <div className="text-[#72798E]">{dayjs(data.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
      </div>
    </div>
  );
};

export default SubTaskCard;
