'use client';
import { <PERSON>, CardContent, Skeleton, Button } from '@/components/ui';
import { Plus } from 'lucide-react';
import { CategoryDiaLog, CATEGORY_LIST } from './CategoryDiaLog';
import { HomePageTags, HomePageTagsSize, Suggestion } from '@/components/icon';
import React, { useState, useEffect } from 'react';
import { getMemberSubscribedCategories } from '@/services/actions/category';
import { action } from '@/utils/server-action/action';
import useUserId, { useSelectedCategory, useUserActions } from '@/store/userStore';
import { toast } from 'react-hot-toast';
import { ProImage } from '@/components/pro/pro-image';
import ReactMarkdown from 'react-markdown';
import { TrendAnalysis, trendsData as initialTrendsData } from '@/types/trendsData';

export default function WeeklyHotTopics({ className }: { className: string }) {
  const [showDialog, setShowDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [subscribedCategories, setSubscribedCategories] = useState<any[]>([]);
  const [trendsData, setTrendsData] = useState<TrendAnalysis[]>(initialTrendsData);
  const [categoryVideos, setCategoryVideos] = useState<any[]>((trendsData as any[])[0]?.videoData || []);
  const selectedCategory = useSelectedCategory();
  const { setSelectedCategory } = useUserActions();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const userInfo = useUserId();
  const { userId } = userInfo;
  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  const handleOpenTikTok = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const fetchData = async () => {
    if (!userId) return;
    try {
      setLoading(true);
      const subscribed = await action(getMemberSubscribedCategories, { userId });

      if (!subscribed || !subscribed.list) {
        setShowDialog(true);
        setSubscribedCategories([]);
        return;
      }
      setSubscribedCategories(subscribed.list);

      // 如果用户没有订阅分类，则显示对话框
      if (subscribed.list.length === 0) {
        setShowDialog(true);
        return;
      } // 获取分析数据
      const categoryNames = subscribed.list
        .map((item) => CATEGORY_LIST.find((cat) => cat.id.toString() === item.category_id)?.category_name)
        .filter(Boolean) as string[];

      // 更新选中的分类列表
      setSelectedCategories(categoryNames);
      if (categoryNames.length > 0 && (!selectedCategory || !categoryNames.includes(selectedCategory))) {
        setSelectedCategory(categoryNames[0] as string);
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
      toast.error('数据加载失败，请稍后重试');
      setTrendsData([]);
      setCategoryVideos([]);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    // 如果有趋势数据但没有选择分类，默认选择第一个
    if (trendsData && trendsData.length > 0 && !selectedCategory) {
      const firstCategory = trendsData[0]?.category;
      if (firstCategory) {
        setSelectedCategory(firstCategory);
      }
    }
  }, [trendsData, selectedCategory, setSelectedCategory]);
  const handleRefresh = () => {
    fetchData();
  };
  useEffect(() => {
    if (selectedCategory && trendsData.length > 0) {
      const selectedCategoryData = trendsData.find((item) => item.category === selectedCategory);
      if (selectedCategoryData && selectedCategoryData.videoData) {
        setCategoryVideos(selectedCategoryData.videoData);
      }
    }
  }, [selectedCategory, trendsData]);
  useEffect(() => {
    if (userId) {
      fetchData();
    }
  }, [userId]);

  return (
    <div className={className}>
      <div className="mb-5 flex items-center justify-between">
        <div className="flex items-end">
          <div className="text-xl font-semibold">本周热点趋势分析 </div>
          <div className="ml-4 text-xs text-[#9FA4B2]">
            <span>更新时间：</span>
            <span>2025-03-11 11:20:00</span>
          </div>
        </div>
      </div>
      <div className="mb-4 flex items-center gap-2 rounded-lg text-xs text-[#9FA4B2]">
        {loading ? (
          <>
            <Skeleton className="h-8 w-20 bg-[#363D54]" />
            <Skeleton className="h-8 w-20 bg-[#363D54]" />
            <Skeleton className="h-8 w-20 bg-[#363D54]" />
            <Skeleton className="h-8 w-8 bg-[#363D54]" />
          </>
        ) : (
          <>
            {subscribedCategories.map((item, index) => {
              const categoryName = CATEGORY_LIST.find((cat) => {
                return cat.id.toString() === item?.category_id;
              })?.category_name;
              // console.log('selectedCategory', selectedCategory);
              if (!categoryName) return null;
              return (
                <Button
                  key={index}
                  className={`h-8 px-3 hover:bg-[#CCDDFF1A] hover:text-white ${
                    selectedCategory === categoryName ? 'border-[#00E1FF] bg-[#CCDDFF1A] text-white' : 'border-none'
                  }`}
                  variant="outline"
                  onClick={() => {
                    setLoading(false);
                    setSelectedCategory(categoryName);
                  }}
                >
                  {categoryName}
                </Button>
              );
            })}
            <Button
              className="h-8 p-[10px] text-white hover:bg-[#CCDDFF1A]"
              variant="outline"
              onClick={() => {
                setShowDialog(true);
              }}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </>
        )}
      </div>
      <Card className="h-[calc(100vh-392px)] flex-1 rounded-2xl border-none bg-[#1C1F2D] pl-6 pr-5">
        <CardContent className="flex h-full p-0">
          {loading ? (
            <>
              <div className="h-[calc(100vh-392px)] w-2/5 border-r border-[#FFFFFF1A] py-5 pr-6">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="h-[calc(100vh-392px)] w-3/5 py-5 pl-5">
                <Skeleton className="h-full w-full" />
              </div>
            </>
          ) : (
            <>
              <div className="h-[calc(100vh-392px)] w-2/5 border-r border-[#FFFFFF1A] py-5 pr-6">
                <div className="mb-4 font-medium">视频模版</div>
                <div className="flex h-[calc(100vh-478px)] w-full flex-col gap-4 overflow-auto">
                  {subscribedCategories?.length === 0 ? (
                    <div className="p-4 text-center text-[#9FA4B2]">{'未找到相关趋势分析数据'}</div>
                  ) : (
                    <div className="h-[calc(100vh-478px)] flex-col gap-4 overflow-auto">
                      {categoryVideos?.map((item, index) => (
                        <div
                          key={index}
                          className="relative mb-4 h-[120px] gap-2 overflow-hidden rounded-[12px] text-sm font-normal sm:h-[130px] md:h-[140px]"
                        >
                          <div className="flex h-full items-center rounded-[8px] border-[1px] border-none bg-[#CCDDFF0D] p-1 backdrop-blur-[20px] sm:p-2">
                            <div
                              onClick={() =>
                                handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item.视频id}`)
                              }
                              className="bg my-2 h-full w-[25%] cursor-pointer rounded-[8px] transition-opacity hover:opacity-80 sm:my-3 sm:w-[22%] md:w-[20%]"
                              role="link"
                              tabIndex={0}
                              aria-label={`查看视频: ${decodeHtmlEntities(item.商品名称)}`}
                              onKeyDown={(e) =>
                                e.key === 'Enter' &&
                                handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item.视频id}`)
                              }
                            >
                              <ProImage
                                width={70}
                                height={100}
                                src={`https://bwkj-cos-1324682537.cos.ap-shanghai.myqcloud.com/damai/covers/${item.视频id}.jpg`}
                                className="h-full w-full min-w-[60px] flex-shrink-0 rounded-[8px] object-cover object-center sm:min-w-[65px] md:min-w-[70px]"
                                alt={decodeHtmlEntities(item.商品名称)}
                              />
                            </div>
                            <div className="flex h-full w-full flex-col justify-center overflow-hidden pl-2 sm:pl-3">
                              <div>
                                <div
                                  onClick={() =>
                                    handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item.视频id}`)
                                  }
                                  className="line-clamp-2 cursor-pointer text-xs hover:text-blue-400 sm:line-clamp-2 sm:text-sm"
                                  role="link"
                                  tabIndex={0}
                                  onKeyDown={(e) =>
                                    e.key === 'Enter' &&
                                    handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item.视频id}`)
                                  }
                                >
                                  {decodeHtmlEntities(item.商品名称)}
                                </div>
                              </div>
                              <div className="mt-4 flex h-[20px] pr-2 sm:h-[24px] sm:pr-4">
                                <div className="flex items-center gap-1 text-[#FFFFFFCC] sm:gap-2">
                                  <div className="line-clamp-1"> {decodeHtmlEntities(item.视频描述)}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="h-[calc(100vh-392px)] w-3/5 py-5 pl-5">
                <div className="mb-8 flex items-center gap-3">
                  <Suggestion />
                  <div className="font-medium">AI精准分析报告</div>
                </div>
                <div className="h-[calc(100vh-480px)] w-full overflow-auto">
                  {subscribedCategories?.length === 0 ? (
                    <div className="mt-10 text-center text-[#9FA4B2]">
                      未找到相关趋势分析数据，请稍后再试或选择其他分类
                    </div>
                  ) : selectedCategory ? (
                    <div className="space-y-5">
                      {(() => {
                        const categoryTrends = trendsData?.find(
                          (item) => item.category === selectedCategory,
                        )?.trends_analysis;
                        if (!categoryTrends) {
                          return <div className="mt-10 text-center text-[#9FA4B2]">暂无该分类的趋势分析数据</div>;
                        }

                        return categoryTrends.split('\n').map((line, index) => (
                          <div
                            key={index}
                            className={`${
                              line.startsWith('一、') || line.startsWith('二、') || line.startsWith('三、')
                                ? 'mb-6 text-lg font-medium text-white'
                                : 'text-sm text-[#FFFFFFCC]'
                            } flex`}
                          >
                            {line.startsWith('一、') || line.startsWith('二、') || line.startsWith('三、') ? (
                              <div className="mr-4 mt-1">
                                <HomePageTags />
                                <div className="ml-[9px] h-[calc(100%+12px)] w-[1px] border border-[#FFFFFF1A]"></div>
                              </div>
                            ) : (
                              // line.startsWith(': ') ?
                              <div className="mr-4 mt-1">
                                <div className="ml-[6px]">
                                  <HomePageTagsSize />
                                </div>
                                <div className="ml-[9px] h-[calc(100%+24px)] w-[1px] border border-[#FFFFFF1A]"></div>
                              </div>
                            )}
                            <ReactMarkdown>{line}</ReactMarkdown>
                          </div>
                        ));
                      })()}
                    </div>
                  ) : (
                    <div className="mt-10 text-center text-[#9FA4B2]">请选择左侧分类查看详细分析</div>
                  )}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
      <CategoryDiaLog
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        selectedCategories={selectedCategories}
        setSelectedCategories={setSelectedCategories}
        handleRefresh={handleRefresh}
      />
    </div>
  );
}
