import { Prisma, PrismaClient } from '@roasmax/database';

export const prisma = new PrismaClient({
  // https://www.prisma.io/docs/orm/prisma-client/queries/transactions#transaction-options
  transactionOptions: {
    isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
    maxWait: 5000,
    timeout: 100000,
  },
  log: [
    { emit: 'event', level: 'query' },
    { emit: 'event', level: 'info' },
    { emit: 'event', level: 'warn' },
    { emit: 'event', level: 'error' },
  ],
});
