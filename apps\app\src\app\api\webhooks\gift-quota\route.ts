import { prisma } from '@/utils/prisma';
import { NextResponse } from 'next/server';
/**
 * 赠送
 */
export const POST = async (request: Request) => {
  try {
    const body = await request.json();
    // 确保 data 存在
    if (!body) {
      return NextResponse.json({ success: false, message: '请求数据缺失', code: '400' }, { status: 400 });
    }

    const wallets = await prisma.members.findMany({
      where: { email: body.email },
    });
    if (wallets.length === 0 || !wallets[0]?.tenant_id) {
      return NextResponse.json({ success: false, message: '获取租户失败', code: '400' }, { status: 400 });
    }
    if (wallets.length > 1) {
      throw new Error('用户存在多个租户');
    }
    const tenant_id = wallets[0].tenant_id;

    // 获取当前时间
    const now = new Date();
    const endTime = new Date(2099, 12 - 1, 31, 23, 59, 59, 999);

    // 查找所有的套餐
    const allPlans = await prisma.pricing_plans.findMany({
      where: {
        tenant_id: tenant_id,
        end_time: {
          gt: now,
        },
      },
    });
    console.log('所有的套餐',allPlans);
    
    const totalQuota1 = allPlans.reduce((sum, plan) => sum + plan.quota, 0);
    console.log('所有的套餐的额度',totalQuota1);

    const plans = await prisma.pricing_plans.findMany({
      where: {
        tenant_id: tenant_id,
        plan_name: '普通用户',
        end_time: {
          gt: now,
        },
      },
    });
    console.log("普通用户套餐",plans);
    const totalQuota = plans.reduce((sum, plan) => sum + plan.quota, 0);
    console.log('普通用户套餐的额度',totalQuota);
    
    const allQuota = totalQuota + body.quota;
    console.log('普通用户套餐的额度+赠送的',allQuota);
    // 计算本次赠送后所有套餐的总剩余额度
    const newTotalQuota = totalQuota1 - totalQuota + allQuota;
    if (plans.length > 0 && plans[0]) {
      console.log('plans');
      const plan = plans[0];

      await prisma.pricing_plans.update({
        where: {
          id: plan.id,
          tenant_id: tenant_id,
          plan_name: '普通用户',
        },
        data: {
          quota: plan.quota + body.quota,
          end_time: endTime,
        },
      });
      await prisma.quota_changelogs.create({
        data: {
          pricing_plan_id: plan.id,
          tenant_id: tenant_id,
          quota: body.quota,
          result_quota: newTotalQuota,
          change_type: 'GIVE',
          change_reason: '赠送金额',
        },
      });
      return NextResponse.json({ success: true }, { status: 200 });
    } else {
      console.log('plans>0');
      
      const res = await prisma.pricing_plans.create({
        data: {
          tenant_id: tenant_id,
          quota: body.quota,
          plan_name: '普通用户',
          start_time: now,
          end_time: endTime,
        },
      });

      await prisma.quota_changelogs.create({
        data: {
          pricing_plan_id: res.id,
          tenant_id: tenant_id,
          quota: body.quota,
          result_quota: newTotalQuota,
          change_type: 'GIVE',
          change_reason: '赠送金额',
        },
      });
      return NextResponse.json({ success: true }, { status: 200 });
    }
  } catch (error) {
    console.error('充值时发生错误:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '服务器内部错误',
        code: '500',
        data: null,
      },
      { status: 500 },
    );
  }
};
