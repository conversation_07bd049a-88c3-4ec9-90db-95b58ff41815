import { Avatar, AvatarImage, AvatarFallback, Progress } from '@/components/ui';
import { CompletedIcon } from '@/components/icon/completed';
import { UploadStatus } from '@/types/upload';
import { cn } from '@/utils/cn';
export type UploadingItemType = {
  name: string;
  coverUrl?: string;
  progress: number;
  status: UploadStatus;
};

const UploadingItem: React.FC<UploadingItemType> = ({ name, coverUrl, progress, status }) => {
  return (
    <div className="flex items-center justify-start gap-3">
      <Avatar className="h-14 w-14 rounded-lg object-cover">
        <AvatarImage src={coverUrl || ''} alt={name} />
        <AvatarFallback>{name}</AvatarFallback>
      </Avatar>
      <div className="flex flex-col items-start justify-between gap-3">
        <div className="max-w-[300px] truncate text-xs text-[#81889D]">{name}</div>
        <div className="flex h-4 items-center justify-between gap-2">
          <Progress
            value={progress}
            className={cn('h-1.5 min-w-[320px] bg-[#1F2434]')}
            color={`${status === UploadStatus.SUCCESS && progress === 100 ? '#60D2A7' : '#FFFFFF'}`}
          />
          {status === UploadStatus.SUCCESS && progress === 100 && <CompletedIcon />}
          {status === UploadStatus.UPLOADING && progress < 100 && (
            <div className="w-8 text-xs text-[#81889D]">
              {progress === 100 ? 100 : progress.toFixed(1).replace(/\.0$/, '')}%
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { UploadingItem };
