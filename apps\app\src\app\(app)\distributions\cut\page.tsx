'use client';

import { ProFilter } from '@/components/pro/pro-filter';
import { ProPagination } from '@/components/pro/pro-pagination';
import ProTable from '@/components/pro/pro-table';
import { Button, Panel } from '@/components/ui';
import { pageVideoCutTasks } from '@/services/actions/video-cut-task';
import { ActionParams, useAction } from '@/utils/server-action/action';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { CreateTaskDialog, CreateTaskDialogRef } from './components/CreateCutTaskDialog';
import { TaskPreviewModal, TaskPreviewModalRef } from './components/CutTaskPreviewDialog';
import { filterColumns, useColumns } from './config';

export default function Distributions() {
  const router = useRouter();
  const taskPreviewModal = useRef<TaskPreviewModalRef>(null);

  const [filters, setFilters] = useState<ActionParams<typeof pageVideoCutTasks>['filters']>({});
  const [pagination, setPagination] = useState<{ page: number; pageSize: number }>({ page: 1, pageSize: 100 });

  const {
    data: dataSource,
    loading,
    run,
  } = useAction(pageVideoCutTasks, {
    pagination: { page: pagination.page, pageSize: pagination.pageSize },
    filters: filters,
  });

  const createTaskDialog = useRef<CreateTaskDialogRef>(null);

  const columns = useColumns({
    onShowTaskPreview: (id) => taskPreviewModal.current?.show(id),
  });

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter
          value={filters}
          onSubmit={(f) => {
            setPagination({ page: 1, pageSize: 100 });
            setFilters(f);
          }}
          columns={filterColumns}
          className="mb-2"
        />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="text-base font-bold">生成任务</div>
          <div className="flex items-center justify-end gap-2">
            <Button variant="link" className="h-[32px]" onClick={() => router.push('/distributions/generate')}>
              生成任务
            </Button>
            <Button
              className="h-[32px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={() => createTaskDialog.current?.show()}
            >
              创建切片任务
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable columns={columns} loading={loading} dataSource={dataSource?.list || []} />
        </div>
        <div className="mt-1">
          <ProPagination
            pagination={{ ...pagination, total: dataSource?.pagination.total }}
            onPaginationChange={setPagination}
          />
        </div>
      </Panel>
      <TaskPreviewModal
        ref={taskPreviewModal}
        onCommitted={() => {
          setPagination({ page: 1, pageSize: 100 });
          run();
        }}
      />
      <CreateTaskDialog
        ref={createTaskDialog}
        onCommitted={() => {
          setPagination({ page: 1, pageSize: 100 });
          run();
        }}
      />
    </div>
  );
}
