/**
 * 本地认证工具函数
 * 替换原有的 Authing 工具函数
 */

import jwt from 'jsonwebtoken';
import * as jose from 'jose';

export interface UserInfo {
  userId: string;
  email: string;
  nickname: string;
  username?: string;
  phone?: string;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  loginsCount?: number;
  lastLogin?: string;
  signedUp?: string;
  blocked?: boolean;
  isDeleted?: boolean;
  tenantId?: string;
  roles?: string[];
  permissions?: string[];
}

export interface LoginSessionInfo {
  user: UserInfo;
  token: string;
  isValid: boolean;
  expiresAt?: Date;
}

/**
 * 获取登录会话信息
 * 兼容原有的 getLoginSessionInfo 函数
 */
export async function getLoginSessionInfo(token: string): Promise<LoginSessionInfo | null> {
  try {
    if (!token) {
      return null;
    }

    // 首先尝试使用 jsonwebtoken 解析
    let decoded: any;
    try {
      decoded = await verifyTokenWithJsonWebToken(token);
    } catch (error) {
      // 如果失败，尝试使用 jose 解析
      try {
        decoded = await verifyTokenWithJose(token);
      } catch (joseError) {
        console.error('Token verification failed with both methods:', error, joseError);
        return null;
      }
    }

    if (!decoded) {
      return null;
    }

    // 构造用户信息
    const user: UserInfo = {
      userId: decoded.userId || decoded.sub,
      email: decoded.email,
      nickname: decoded.nickname || decoded.name,
      username: decoded.username || decoded.preferred_username,
      phone: decoded.phone,
      emailVerified: decoded.email_verified || true,
      phoneVerified: decoded.phone_verified || false,
      loginsCount: decoded.logins_count || 0,
      lastLogin: decoded.last_login,
      signedUp: decoded.signed_up,
      blocked: decoded.blocked || false,
      isDeleted: decoded.is_deleted || false,
      tenantId: decoded.tenantId,
      roles: decoded.roles || [],
      permissions: decoded.permissions || [],
    };

    return {
      user,
      token,
      isValid: true,
      expiresAt: decoded.exp ? new Date(decoded.exp * 1000) : undefined,
    };

  } catch (error) {
    console.error('Get login session info error:', error);
    return null;
  }
}

/**
 * 使用 jsonwebtoken 验证令牌
 */
async function verifyTokenWithJsonWebToken(token: string): Promise<any> {
  const secret = process.env.APPSECRET;
  if (!secret) {
    throw new Error('APPSECRET 环境变量未设置');
  }

  return new Promise((resolve, reject) => {
    jwt.verify(token, secret, { algorithms: ['HS256'] }, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
}

/**
 * 使用 jose 验证令牌
 */
async function verifyTokenWithJose(token: string): Promise<any> {
  const secret = process.env.APPSECRET;
  if (!secret) {
    throw new Error('APPSECRET 环境变量未设置');
  }

  const secretKey = new TextEncoder().encode(secret);
  
  const { payload } = await jose.jwtVerify(token, secretKey, {
    algorithms: ['HS256'],
  });

  return payload;
}

/**
 * 检查令牌是否有效
 */
export function isTokenValid(token: string): boolean {
  try {
    if (!token) {
      return false;
    }

    // 解码令牌（不验证签名）
    const decoded = jwt.decode(token) as any;
    
    if (!decoded || !decoded.exp) {
      return false;
    }

    // 检查是否过期
    const now = Math.floor(Date.now() / 1000);
    return decoded.exp > now;

  } catch (error) {
    return false;
  }
}

/**
 * 检查令牌是否即将过期
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
  try {
    if (!token) {
      return true;
    }

    const decoded = jwt.decode(token) as any;
    
    if (!decoded || !decoded.exp) {
      return true;
    }

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (decoded.exp - now) < threshold;

  } catch (error) {
    return true;
  }
}

/**
 * 从令牌中提取用户ID
 */
export function getUserIdFromToken(token: string): string | null {
  try {
    if (!token) {
      return null;
    }

    const decoded = jwt.decode(token) as any;
    return decoded?.userId || decoded?.sub || null;

  } catch (error) {
    return null;
  }
}

/**
 * 从令牌中提取租户ID
 */
export function getTenantIdFromToken(token: string): string | null {
  try {
    if (!token) {
      return null;
    }

    const decoded = jwt.decode(token) as any;
    return decoded?.tenantId || null;

  } catch (error) {
    return null;
  }
}

/**
 * 生成新的访问令牌
 */
export async function generateAccessToken(payload: any): Promise<string> {
  const secret = process.env.APPSECRET;
  if (!secret) {
    throw new Error('APPSECRET 环境变量未设置');
  }

  const secretKey = new TextEncoder().encode(secret);
  
  const token = await new jose.SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .setIssuer('roasmax-local-auth')
    .setAudience('roasmax-app')
    .sign(secretKey);

  return token;
}

/**
 * 刷新访问令牌
 */
export async function refreshAccessToken(refreshToken: string): Promise<string | null> {
  try {
    // 验证刷新令牌
    const decoded = await verifyTokenWithJose(refreshToken);
    
    if (decoded.type !== 'refresh') {
      throw new Error('无效的刷新令牌');
    }

    // 这里需要从数据库获取最新的用户信息
    // 暂时使用令牌中的信息
    const newPayload = {
      userId: decoded.userId,
      email: decoded.email,
      nickname: decoded.nickname,
      tenantId: decoded.tenantId,
      iat: Math.floor(Date.now() / 1000),
    };

    return await generateAccessToken(newPayload);

  } catch (error) {
    console.error('Refresh token error:', error);
    return null;
  }
}

/**
 * 登出（撤销令牌）
 */
export async function logout(token: string): Promise<boolean> {
  try {
    // 这里可以调用本地认证客户端的 revokeToken 方法
    // 或者将令牌加入黑名单
    
    // 暂时返回 true，实际实现需要调用适配器
    return true;

  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
}

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): { isValid: boolean; message: string } {
  if (!password) {
    return { isValid: false, message: '密码不能为空' };
  }

  if (password.length < 8) {
    return { isValid: false, message: '密码长度不能少于8位' };
  }

  if (password.length > 128) {
    return { isValid: false, message: '密码长度不能超过128位' };
  }

  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);

  if (!hasLetter || !hasNumber) {
    return { isValid: false, message: '密码必须包含字母和数字' };
  }

  return { isValid: true, message: '密码强度符合要求' };
}
