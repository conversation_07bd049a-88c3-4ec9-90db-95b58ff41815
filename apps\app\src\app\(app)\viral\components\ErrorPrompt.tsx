import React from 'react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/ui';
import { ProImage } from '@/components/pro/pro-image';

interface ErrorPromptProps {
  imgComponent: string; // 图片组件类型
  errorMessage: string; // 错误信息类型
  handleCancelAnalyze: () => void; // 取消分析处理函数类型
  setShowTitle: React.Dispatch<React.SetStateAction<boolean>>; // 设置标题显示状态的函数类型
  handleAnalyzeWithAnimation: (link: string) => void; // 重新分析处理函数类型
  link: string; // 链接类型
}
const ErrorPrompt: React.FC<ErrorPromptProps> = ({
  imgComponent,
  errorMessage,
  handleCancelAnalyze,
  setShowTitle,
  handleAnalyzeWithAnimation,
  link,
}) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center lg:h-[calc(100vh-290px)] xl:h-[calc(100vh-314px)] 2xl:h-[calc(100vh-344px)]',
      )}
    >
      <div className="flex flex-col items-center justify-center lg:h-[calc(100vh-294px)] xl:h-[calc(100vh-414px)] 2xl:h-[calc(100vh-444px)]">
        <ProImage width={72} height={72} alt="error" src={imgComponent} className="h-[72px] w-[72px]" />
        <div className="mt-3">{errorMessage}</div>
      </div>
      <div>
        <Button
          variant="outline"
          className="mr-3 w-[120px] bg-[#ccddff]/10"
          onClick={() => {
            handleCancelAnalyze();
            setShowTitle(true);
          }}
        >
          取消
        </Button>
        <Button
          className="w-[120px] bg-[linear-gradient(90.59deg,_#54FFE0_4.36%,_#00E1FF_22.69%,_#9D81FF_96%)] px-7 py-4 text-black transition duration-150 ease-in-out hover:text-black"
          onClick={() => handleAnalyzeWithAnimation(link)}
        >
          重新分析
        </Button>
      </div>
    </div>
  );
};

export default ErrorPrompt;
