import React, { useState, useCallback } from 'react';
import { Input, Label } from '@/components/ui';
import { UploadIcon } from '@/components/icon/upload';
import { UPLOAD_DRAGGING_NOTICE, UPLOAD_NOTICE } from '@/common/statics/zh_cn';
import { cn } from '@/utils/cn';
import { usePathname } from 'next/navigation';

export const Uploader = ({
  handleFileChange,
  uploadTarDirId,
  disabled,
}: {
  handleFileChange: (
    e: React.ChangeEvent<HTMLInputElement>,
    options?: { uploadTarDirId: string; openModal?: boolean; shouldUnder40Second?: boolean },
  ) => void;
  uploadTarDirId: string;
  disabled?: boolean;
}) => {
  const pathname = usePathname();
  const [isDragging, setIsDragging] = useState(false);

  const onDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const onDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);
  const isUnderKocOrViralPath = pathname.includes('/koc') || pathname.includes('/viral');

  const onDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);
      const files = e.dataTransfer.files;
      if (files.length) {
        const options = {
          uploadTarDirId: uploadTarDirId,
          shouldUnder40Second: isUnderKocOrViralPath,
        };
        handleFileChange({ target: { files } } as React.ChangeEvent<HTMLInputElement>, options);
      }
    },
    [handleFileChange],
  );

  return (
    <div className="h-full w-full">
      <Label
        htmlFor="bwai-local-upload"
        className={cn(
          'flex h-[140px] w-full flex-col items-center justify-center gap-3 rounded-2xl bg-[#CCDDFF] bg-opacity-10 text-[#81889D]',
          isDragging && 'border-2 border-[#00e1ff]',
          disabled ? 'cursor-not-allowed' : 'cursor-pointer',
          'hover:border-2 hover:border-[#00e1ff] hover:text-white',
        )}
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        onDrop={onDrop}
      >
        <div className="mb-2 h-8 w-8">
          <UploadIcon />
        </div>
        <div className="text-3.5 font-400 text-center">{isDragging ? UPLOAD_DRAGGING_NOTICE : UPLOAD_NOTICE}</div>
      </Label>
      <Input
        className="hidden"
        id="bwai-local-upload"
        multiple
        type="file"
        accept=".mp4"
        onChange={(e) =>
          handleFileChange(e, { uploadTarDirId: uploadTarDirId, shouldUnder40Second: isUnderKocOrViralPath })
        }
        disabled={disabled}
      />
    </div>
  );
};
