'use client';

import React, { memo, useCallback, useEffect, useRef } from 'react';

interface IPreviewItem {
  url: string;
  cover: string;
  id: string;
  width: number;
  height: number;
  onMouseOut?: () => void;
}

const PreviewVideo: React.FC<IPreviewItem> = ({ url, cover, id, width, height }) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const ratio = width / height;

  const getBoxStyleByRatio = useCallback((ratio: number) => {
    let width, height;

    if (ratio <= 9 / 16) {
      width = 200;
      height = 355;
    } else if (ratio <= 3 / 4) {
      width = 200;
      height = 266;
    } else if (ratio <= 1) {
      width = 200;
      height = 200;
    } else if (ratio <= 16 / 9) {
      width = 365;
      height = 200;
    } else {
      width = 240;
      height = 240;
    }
    return {
      width: `${width}px`,
      height: `${height}px`,
    };
  }, []);

  useEffect(() => {
    if (!videoRef.current) return () => {};

    const ele = videoRef.current;

    function loadedmetadata() {
      const ele = videoRef.current;
      if (!ele) return;
    }

    function muteFn() {
      const ele = videoRef.current;
      if (!ele) return;
      sessionStorage.setItem('BWAI_MATERIAL_MUTED', String(ele.muted));
    }

    ele.addEventListener('loadedmetadata', loadedmetadata);
    ele.addEventListener('volumechange', muteFn);

    return () => {
      if (ele) {
        ele.removeEventListener('loadedmetadata', loadedmetadata);
        ele.removeEventListener('volumechange', muteFn);
      }
    };
  }, []);

  useEffect(() => {
    if (!videoRef.current) return () => {};
    const ele = videoRef.current;

    function recordVideoLastTime() {
      const ele = videoRef.current;
      if (!ele) return;
    }

    function playFn() {
      const ele = videoRef.current;
      if (!ele) return;
    }

    function pauseFn() {
      const ele = videoRef.current;
      if (!ele) return;

      if (ele.currentTime === ele.duration) {
        ele?.play().then(() => {});
      } else {
        recordVideoLastTime();
      }
    }

    ele.addEventListener('play', playFn);
    ele.addEventListener('pause', pauseFn);

    return () => {
      if (ele) {
        ele.removeEventListener('play', playFn);
        ele.removeEventListener('pause', pauseFn);
      }
    };
  }, [id, videoRef.current]);

  useEffect(() => {
    return () => {
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.src = '';
      }
    };
  }, []);

  return (
    <div
      style={getBoxStyleByRatio(ratio)}
      className="h-full w-full overflow-hidden rounded-lg border-none"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <video
        ref={videoRef}
        className="h-full w-full border-none"
        controls
        controlsList="nodownload"
        src={url}
        poster={cover}
        autoPlay
        muted
        loop
      />
    </div>
  );
};

export default memo(PreviewVideo);
