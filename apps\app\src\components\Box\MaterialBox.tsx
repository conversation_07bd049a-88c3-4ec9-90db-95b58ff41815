'use client';

import { MaterialItemType } from '@/hooks/useMaterial';
import { HoverCard, HoverCardContent, HoverCardTrigger, Skeleton, Input } from '@/components/ui';
import PreviewVideo from '@/components/PopupVideo';
import { useImageLoad } from '@/hooks/useImageLoad';
import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { formatDuration, getVodCoverUrl } from '@/utils/common';
import { useState, useEffect, useRef } from 'react';
import { cn } from '@/utils/cn';
import { Edit } from '@/components/icon';
import toast from 'react-hot-toast';

const ERROR_COVER_STYLE = {
  height: 'calc(100% - 50px)',
};

const MaterialBox = ({
  data,
  batchMode = false,
  selectMode = false,
  onSelect,
  width,
  height,
  updateMaterials,
}: {
  width: number;
  height: number;
  data: MaterialItemType;
  batchMode: boolean;
  selectMode: boolean;
  onSelect: (item: MaterialItemType) => void;
  updateMaterials: (data: { name: string; id: string }) => void;
}) => {
  const {
    vod_media_url,
    vod_cover_url,
    name = '',
    tmp_created_at = '',
    checked = false,
    locked = false,
    usage = false,
    video_duration,
    id,
  } = data;
  const [inputValue, setInputValue] = useState(''); // 新增状态
  const [isName, setIsName] = useState(false);
  const [coverImgMeta, coverLoading] = useImageLoad(getVodCoverUrl(vod_cover_url));
  const [loadImgError, setLoadImgError] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isHovering, setIsHovering] = useState(false);

  const handleDoubleClick = () => {
    setIsName(!isName);
    setInputValue(name); // 双击时将当前名称设置为输入框的值
  };
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace('*', '').slice(0, 36);
    setInputValue(newValue);
    if (newValue.length < 36) return;
    toast.error('名称最多只能输入36个字符');
  };
  const nameSubmit = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (inputValue === '') {
        return toast.error('名称不能为空');
      }
      updateMaterials({ name: inputValue, id: id });
      setIsName(false);
    }
  };
  const handleBlur = () => {
    setIsName(false);
    setInputValue(name); //取消修改
  };
  useEffect(() => {
    if (isName && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isName]);

  const isUsing = !coverLoading && usage;

  return (
    <HoverCard openDelay={500} closeDelay={150}>
      <HoverCardTrigger asChild>
        <div
          className={cn(
            'space-between relative h-full w-full cursor-pointer',
            locked ? 'cursor-not-allowed opacity-60' : 'cursor-pointer',
          )}
          onClick={() => {
            if (locked) return;
            if (batchMode || selectMode) {
              onSelect(data);
            }
          }}
        >
          {(batchMode || selectMode) && (
            <div className="relative">
              <input
                onChange={() => {}}
                type="checkbox"
                checked={checked || locked}
                className={cn(
                  'peer absolute right-2 top-2 z-10 h-4 w-4 appearance-none rounded border border-[#95A0AA] bg-[#000714] bg-opacity-40 checked:border-[#00E1FF] checked:bg-[#00E1FF]',
                  locked ? 'cursor-not-allowed' : 'cursor-pointer',
                )}
              />
              <span className="pointer-events-none absolute right-2 top-2 z-20 h-4 w-4 after:absolute after:left-[5px] after:top-[1px] after:h-[10px] after:w-[6px] after:rotate-45 after:border-b-2 after:border-r-2 after:border-black after:opacity-0 after:content-[''] peer-checked:after:opacity-100"></span>
            </div>
          )}
          <>
            {isUsing && (
              <div className="absolute left-[5px] top-[5px] z-10 h-5 rounded-3xl bg-[#000714] bg-opacity-40 px-2 py-0.5 text-[11px] text-white">
                文件使用中
              </div>
            )}
            {data?.properties?.map_platform === 'TIKTOK' && (
              <div
                className={cn(
                  'absolute left-1 z-10 rounded bg-black/40 px-1.5 py-0.5 text-xs text-white',
                  isUsing ? 'top-[28px]' : 'top-[5px]',
                )}
              >
                TK
              </div>
            )}
          </>
          {coverLoading ? (
            <Skeleton className="mb-3 aspect-square w-full rounded-lg bg-slate-700 object-cover" />
          ) : coverImgMeta?.src && !loadImgError && !loadImgError ? (
            <div className="relative">
              {/* eslint-disable-next-line */}
              <img
                width={width}
                height={height}
                src={coverImgMeta.src}
                alt="cover"
                onError={() => {
                  setLoadImgError(true);
                }}
                className="mb-3 aspect-square w-full rounded-lg object-cover"
              />
              <div className="absolute bottom-0 left-0 h-8 w-full rounded-b-lg bg-gradient-to-t from-[#171C28]/80 to-transparent pl-2 pt-1.5 text-[13px]">
                {formatDuration(video_duration)}
              </div>
            </div>
          ) : (
            <NotFoundCover className="mb-3 aspect-square w-full rounded-lg" style={ERROR_COVER_STYLE} />
          )}
          <div
            className="flex h-[38px] flex-col gap-[3px]"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            {isName ? (
              <Input
                ref={inputRef}
                value={inputValue}
                className="h-5 w-full border border-[#292E3E] bg-[#151C29] pl-1 focus:border-[#00E1FF]"
                onChange={handleNameChange}
                onKeyPress={nameSubmit}
                onBlur={handleBlur}
                maxLength={36}
              />
            ) : (
              <span className="flex h-5 w-full items-center justify-between truncate whitespace-nowrap text-[13px] text-[#ebfbff]">
                <span className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{name}</span>
                <span style={{ display: isHovering && !isName ? 'inline' : 'none' }} onClick={handleDoubleClick}>
                  <Edit />
                </span>
              </span>
            )}
            <span className="truncate whitespace-nowrap text-xs font-medium text-[#808080]">
              {new Date(tmp_created_at ?? '').toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
            </span>
          </div>
        </div>
      </HoverCardTrigger>
      <HoverCardContent
        side="right"
        align="center"
        sideOffset={0}
        alignOffset={0}
        className="z-50 w-auto border-none p-0"
      >
        <PreviewVideo
          url={vod_media_url}
          cover={vod_cover_url}
          id={data.id}
          width={coverImgMeta?.width ?? 182}
          height={coverImgMeta?.height ?? 308}
          onMouseOut={() => {}}
        />
      </HoverCardContent>
    </HoverCard>
  );
};

export default MaterialBox;
