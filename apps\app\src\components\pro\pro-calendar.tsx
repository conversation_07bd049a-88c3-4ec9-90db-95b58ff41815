import { Calendar } from '@/components/ui'; // 确保导入正确的 Calendar 组件
import { Button, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { CalendarIcon } from 'lucide-react';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import emitter from '@/utils/mitt';
import { cn } from '@/utils/cn';

type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

interface ProCalendarProps {
  key?: string;
  title: string;
  className?: string;
  onDateChange: (range: DateRange) => void;
  onClear: () => void;
}

const ProCalendar: React.FC<ProCalendarProps> = ({ title, className, onDateChange, onClear }) => {
  const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined });
  const [open, setOpen] = useState(false);

  useEffect(() => {
    function clear() {
      onClear();
      setDateRange({ from: undefined, to: undefined });
      setOpen(false);
    }

    emitter.on('CLEAR_PRO_CALENDAR', clear);

    return () => {
      emitter.off('CLEAR_PRO_CALENDAR', clear);
    };
  }, [onClear]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'w-[280px] justify-start text-left font-normal',
            !dateRange.from ? 'text-muted-foreground' : '',
            className,
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {dateRange.from && dateRange.to
            ? `${title} ${dayjs(dateRange.from).format('YYYY-MM-DD')} - ${dayjs(dateRange.to).format('YYYY-MM-DD')}`
            : title}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto bg-[#262F40] p-0">
        <Calendar
          mode="range"
          selected={dateRange}
          onSelect={(range) => {
            setDateRange((prev) => ({
              ...prev,
              from: range?.from,
              to: range?.to,
            }));
            onDateChange(range ? { from: range.from, to: range.to } : { from: undefined, to: undefined });
          }}
        />
        <Button
          onClick={() => {
            onClear();
            setDateRange({ from: undefined, to: undefined });
            setOpen(false);
          }}
          className="mt-2 text-red-500"
        >
          清除
        </Button>
      </PopoverContent>
    </Popover>
  );
};

export default ProCalendar;
