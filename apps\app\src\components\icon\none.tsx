export const None = () => {
  return (
    <svg width="60" height="60" viewBox="0 0 64 65" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.3971 39.0366C12.3971 37.8094 13.392 36.8145 14.6193 36.8145H27.8041C29.0314 36.8145 30.0263 37.8094 30.0263 39.0366C30.0263 40.2639 29.0314 41.2588 27.8041 41.2588H14.6193C13.392 41.2588 12.3971 40.2639 12.3971 39.0366Z"
        fill="#D9D9D9"
      />
      <path
        d="M12.3971 27.4819C12.3971 26.2547 13.392 25.2598 14.6193 25.2598H41.0384C42.2656 25.2598 43.2605 26.2547 43.2605 27.4819C43.2605 28.7092 42.2656 29.7041 41.0384 29.7041H14.6193C13.392 29.7041 12.3971 28.7092 12.3971 27.4819Z"
        fill="#D9D9D9"
      />
      <path
        d="M61.8614 62.4166L58.3799 58.9783C59.6424 57.3586 60.3999 55.3361 60.3999 53.1395C60.3999 47.8424 56.0359 43.5352 50.6749 43.5352C45.3138 43.5352 40.9498 47.8448 40.9498 53.1421C40.9498 58.4392 45.3113 62.7464 50.6749 62.7464C52.9244 62.7464 54.9929 61.9808 56.6429 60.7087L60.1166 64.1394C60.3565 64.3762 60.6727 64.4971 60.989 64.4971C61.3052 64.4971 61.6215 64.3787 61.8614 64.1394C62.3434 63.6635 62.3434 62.8926 61.8614 62.4166ZM43.416 53.1421C43.416 49.19 46.6733 45.9734 50.6749 45.9734C54.6765 45.9734 57.9335 49.19 57.9335 53.1421C57.9335 57.0942 54.6765 60.3108 50.6749 60.3108C46.6733 60.3108 43.416 57.0941 43.416 53.1421Z"
        fill="#00E1FF"
      />
      <rect
        x="20.7955"
        y="1"
        width="21.3639"
        height="6.34917"
        rx="3.17458"
        fill="white"
        fillOpacity="0.1"
        stroke="url(#paint0_linear_518_101)"
      />
      <g filter="url(#filter0_b_518_101)">
        <mask id="path-5-inside-1_518_101" fill="white">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.77991 4.12891C5.36163 4.12891 1.77991 7.71063 1.77991 12.1289V52.4988C1.77991 59.1262 7.15249 64.4988 13.7799 64.4988H31.4709H43.3705C39.6785 62.1709 37.2311 58.0946 37.2311 53.455C37.2311 46.2192 43.1837 40.3535 50.5267 40.3535C54.8752 40.3535 58.736 42.4106 61.1618 45.591V34.3138V12.1289C61.1618 7.71063 57.5801 4.12891 53.1619 4.12891H49.9843C48.9558 4.12891 48.1221 4.96265 48.1221 5.99112C48.1221 9.07652 45.6209 11.5777 42.5354 11.5777H20.6578C17.5724 11.5777 15.0712 9.07652 15.0712 5.99111C15.0712 4.96265 14.2374 4.12891 13.209 4.12891H9.77991Z"
          />
        </mask>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.77991 4.12891C5.36163 4.12891 1.77991 7.71063 1.77991 12.1289V52.4988C1.77991 59.1262 7.15249 64.4988 13.7799 64.4988H31.4709H43.3705C39.6785 62.1709 37.2311 58.0946 37.2311 53.455C37.2311 46.2192 43.1837 40.3535 50.5267 40.3535C54.8752 40.3535 58.736 42.4106 61.1618 45.591V34.3138V12.1289C61.1618 7.71063 57.5801 4.12891 53.1619 4.12891H49.9843C48.9558 4.12891 48.1221 4.96265 48.1221 5.99112C48.1221 9.07652 45.6209 11.5777 42.5354 11.5777H20.6578C17.5724 11.5777 15.0712 9.07652 15.0712 5.99111C15.0712 4.96265 14.2374 4.12891 13.209 4.12891H9.77991Z"
          fill="white"
          fillOpacity="0.1"
        />
        <path
          d="M43.3705 64.4988V65.4988H46.8315L43.9039 63.6529L43.3705 64.4988ZM61.1618 45.591L60.3667 46.1975L62.1618 48.551V45.591H61.1618ZM2.77991 12.1289C2.77991 8.26291 5.91392 5.12891 9.77991 5.12891V3.12891C4.80935 3.12891 0.779907 7.15834 0.779907 12.1289H2.77991ZM2.77991 52.4988V12.1289H0.779907V52.4988H2.77991ZM13.7799 63.4988C7.70477 63.4988 2.77991 58.5739 2.77991 52.4988H0.779907C0.779907 59.6785 6.6002 65.4988 13.7799 65.4988V63.4988ZM31.4709 63.4988H13.7799V65.4988H31.4709V63.4988ZM43.3705 63.4988H31.4709V65.4988H43.3705V63.4988ZM43.9039 63.6529C40.4862 61.498 38.2311 57.7326 38.2311 53.455H36.2311C36.2311 58.4566 38.8708 62.8438 42.8372 65.3447L43.9039 63.6529ZM38.2311 53.455C38.2311 46.7854 43.7221 41.3535 50.5267 41.3535V39.3535C42.6454 39.3535 36.2311 45.6531 36.2311 53.455H38.2311ZM50.5267 41.3535C54.5535 41.3535 58.1241 43.2571 60.3667 46.1975L61.957 44.9846C59.348 41.564 55.1968 39.3535 50.5267 39.3535V41.3535ZM60.1618 34.3138V45.591H62.1618V34.3138H60.1618ZM60.1618 12.1289V34.3138H62.1618V12.1289H60.1618ZM53.1619 5.12891C57.0279 5.12891 60.1618 8.26291 60.1618 12.1289H62.1618C62.1618 7.15834 58.1324 3.12891 53.1619 3.12891V5.12891ZM49.9843 5.12891H53.1619V3.12891H49.9843V5.12891ZM49.1221 5.99112C49.1221 5.51493 49.5081 5.12891 49.9843 5.12891V3.12891C48.4035 3.12891 47.1221 4.41036 47.1221 5.99112H49.1221ZM42.5354 12.5777C46.1731 12.5777 49.1221 9.62881 49.1221 5.99112H47.1221C47.1221 8.52424 45.0686 10.5777 42.5354 10.5777V12.5777ZM20.6578 12.5777H42.5354V10.5777H20.6578V12.5777ZM14.0712 5.99111C14.0712 9.62881 17.0201 12.5777 20.6578 12.5777V10.5777C18.1247 10.5777 16.0712 8.52424 16.0712 5.99111H14.0712ZM13.209 5.12891C13.6852 5.12891 14.0712 5.51493 14.0712 5.99111H16.0712C16.0712 4.41036 14.7897 3.12891 13.209 3.12891V5.12891ZM9.77991 5.12891H13.209V3.12891H9.77991V5.12891Z"
          fill="url(#paint1_linear_518_101)"
          mask="url(#path-5-inside-1_518_101)"
        />
      </g>
      <defs>
        <filter
          id="filter0_b_518_101"
          x="-2.22009"
          y="0.128906"
          width="67.382"
          height="68.3699"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_518_101" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_518_101" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_518_101"
          x1="31.4775"
          y1="0.5"
          x2="31.4775"
          y2="7.84917"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EFEDFD" stopOpacity="0.1" />
          <stop offset="1" stopColor="#EFEDFD" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_518_101"
          x1="31.4709"
          y1="4.12891"
          x2="31.4709"
          y2="64.4988"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EFEDFD" stopOpacity="0.1" />
          <stop offset="1" stopColor="#EFEDFD" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
