'use client';

import { <PERSON>, Button, TooltipProvider, Too<PERSON><PERSON>, TooltipTrigger, TooltipContent } from '@/components/ui';
import { ArrowRight, ChevronRight, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css'; // Swiper 样式
import { useSidebar } from '@/components/ui';
import 'swiper/css/pagination'; // 如果需要分页器
import { Pagination, Navigation } from 'swiper/modules';
import { cn } from '@/utils/cn';
import { useRouter } from 'next/navigation';
import { BidBudget, CarouselTitle1, CarouselTitle2, Recommend } from '@/components/icon';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import { useTableLogic } from '@/hooks/useTableLogic';
import { useCurrentAdGroup } from '@/store/ads/adStore';
import { useEffect, useRef, useState } from 'react';
import tiktokService from '@/services/tiktokService';
import dayjs from 'dayjs';
import { toast } from 'react-hot-toast';
import { ProMask } from '@/components/pro/pro-mask';
import { to } from '@roasmax/utils';
import { ExcellentAdsResponse } from '@/services/interfaces/ads/res';

const Dashboard = () => {
  const currentAdGroup = useCurrentAdGroup();
  const { state } = useSidebar();
  const collapsed = state === 'collapsed';
  const { successVideoCount, pendingTaskCount, loading } = useDashboardStats();
  const router = useRouter();
  const [excellentAds, setExcellentAds] = useState<ExcellentAdsResponse['adGroupList']>([]);
  const [excellentIds, setExcellentIds] = useState('');
  const [excellentStatus, setExcellentStatus] = useState('');
  const [updateTme, setUpdateTime] = useState('');
  const [isLoadingCard, setIsLoadingCard] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  /**
   * 获取优质广告最小值的异步函数
   * @param mode 更新模式：'auto' | 'manual'
   * @returns 无返回值
   */
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const getExcellentAdsMin = async (mode: 'auto' | 'manual' = 'auto') => {
    setIsLoading(true);
    try {
      const [err, res] = await to(tiktokService.getExcellentAds());
      if (err) {
        toast.error('获取状态失败');
      }
      if (!res) {
        setExcellentAds([]);
        setExcellentIds('');
        // 比较特殊，因为第一次什么数据都没有的时候，需要调用startSyncRecommend
        await startSyncRecommend();
        return;
      }
      setExcellentAds(res.adGroupList);
      setExcellentIds(res.adGroupDeliveryAnalysisId);
      setExcellentStatus(res.status);
      const formattedTime = dayjs(res.updateTime).format('YYYY-MM-DD HH:mm:ss');
      setUpdateTime(formattedTime);

      // 只有在手动模式下，才在状态为 FAIL、SUCCESS、CANCEL 或 EDIT 时调用 startSyncRecommend
      if (mode === 'manual' && ['FAIL', 'SUCCESS', 'EDIT', 'CANCEL', 'CANCELED'].includes(res.status)) {
        await startSyncRecommend();
      }
    } catch (error) {
      console.error('获取优质广告最小值失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startSyncRecommend = async () => {
    setIsLoadingCard(true);
    try {
      const [err, res] = await to(tiktokService.startSyncRecommend());
      if (err || !res) {
        toast.error(err?.message || '获取推荐列表失败');
        return;
      }

      getExcellentAdsMin('auto');
    } catch (error) {
      console.error('获取推荐列表失败:', error);
    } finally {
      setIsLoadingCard(false);
    }
  };

  // 添加更新按钮的处理函数
  const handleUpdateData = () => {
    getExcellentAdsMin('manual');
  };

  useEffect(() => {
    getExcellentAdsMin('auto');
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  const { sortedData } = useTableLogic(excellentAds, 'id', currentAdGroup?.map((item) => item.id) || []);
  const slides = [
    {
      id: 1,
      title: <CarouselTitle1 />,
      description: 'AI学习行业最热卖视频风格和模式，智能分析商品热点信息',
      link: '/viral',
      linkName: '前往大卖',
    },
    {
      id: 2,
      title: <CarouselTitle2 />,
      description: '智能分析平台规则，精准匹配视颜内容',
      link: '/originality',
      linkName: '前往创作',
    },
  ];

  const columns = [
    { key: 'groupName', title: '广告组', width: 200 },
    { key: 'onsiteShoppingRate', title: '点击转化率', width: 80, sortable: true },
    { key: 'onsiteShoppingRoas', title: 'ROAS', width: 80, sortable: true },
    { key: 'onsiteShoppingValue', title: '总收入', width: 80, sortable: true },
    { key: 'budget', title: '预算', width: 80, sortable: true },
    { key: 'operationStatus', title: '建议预算', width: 80, sortable: true },
  ];
  const renderCell = (key: string, record: any) => {
    switch (key) {
      case 'groupName':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className={`cursor-pointer truncate`}>
                  {record.adgroupName}
                  <div className="mt-[2px] text-xs text-gray-500">{record.groupId}</div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{record.adgroupName}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'onsiteShoppingRate':
        return record?.onsiteShoppingRate;
      case 'onsiteShoppingRoas':
        return record?.onsiteShoppingRoas;
      case 'onsiteShoppingValue':
        return record?.totalOnsiteShoppingValue;
      case 'budget':
        return record?.oldBudget;
      case 'operationStatus':
        return (
          <div className="flex items-center gap-1 text-[#54FFE0]">
            <Recommend />
            <span>{record.newBudget}</span>
          </div>
        );
    }
  };
  const editBudget = async () => {
    try {
      const [err, res] = await to(
        tiktokService.getRecommendedBudget({
          id: excellentIds,
        }),
      );
      if (err || !res) {
        toast.error(err?.message || '获取推荐预算失败');
      }
      getExcellentAdsMin('auto');
      toast.success('预算更新成功');
    } catch (error) {
      console.log(error);
      toast.error('预算更新失败');
    }
  };

  return (
    <div className={cn('py-6 pr-6', collapsed ? 'w-[calc(100vw-48px)]' : 'w-[calc(100vw-222px)]')}>
      <div className="mb-[26px] flex h-[233px] gap-6">
        <Swiper
          modules={[Pagination, Navigation, Autoplay]} // 启用分页器和导航按钮
          spaceBetween={30} // 幻灯片之间的间距
          slidesPerView={1} // 每次显示的幻灯片数量
          pagination={{ clickable: true }} // 分页器配置
          navigation // 启用导航按钮
          loop // 循环播放
          autoplay={{ delay: 3000 }} // 自动播放配置
          className="m-0 h-full flex-1 rounded-2xl p-0"
        >
          {slides.map((slide) => (
            <SwiperSlide key={slide.id}>
              <Card className="h-full w-full overflow-hidden rounded-2xl border-none bg-[#CCDDFF1A] shadow-lg">
                <div
                  className="relative h-full w-full px-6 py-8"
                  style={{
                    backgroundImage: 'url(/background/gallaryDashboardBg.png)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                >
                  <div
                    className="absolute left-0 top-0 h-full w-full rounded-2xl backdrop-blur-sm"
                    style={{
                      maskImage: 'linear-gradient(90deg, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 80%)',
                      WebkitMaskImage: 'linear-gradient(90deg, rgba(0,0,0,1) 0%, rgba(0,0,0,1) 40%, rgba(0,0,0,0) 80%)',
                    }}
                  />
                  <div className="relative z-10">
                    <h2 className="mb-2 mt-2">{slide.title}</h2>
                    <p className="mb-14 text-sm text-[#9FA4B2]">{slide.description}</p>
                    <div className="relative">
                      <div className={cn('rounded-gradient-hollow-out h-10 w-[110px] rounded-lg border')}></div>
                      <div
                        onClick={() => {
                          router.push(slide.link);
                        }}
                        className={cn(
                          'absolute left-0 top-0 flex h-10 w-[110px] cursor-pointer items-center justify-center rounded-lg text-sm font-semibold text-white',
                        )}
                      >
                        {slide.linkName}
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </SwiperSlide>
          ))}
        </Swiper>
        <Card
          className="w-[40%] cursor-pointer rounded-2xl p-6 transition-shadow hover:shadow-lg"
          style={{
            backgroundImage: 'url(/background/gradientDashboardBg.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <div className="font-[PingFang SC] mb-8 flex items-center gap-3 text-base font-medium text-white">
            我的生成
          </div>
          <div className="mb-10 flex justify-between">
            <div className="flex flex-col gap-2">
              <div className="font-[PingFang SC] flex items-center justify-between text-right text-[13px] font-normal text-[rgba(255,255,255,0.45)]">
                累计生成视频 <ChevronRight className="inline-block h-4 w-4 text-[rgba(255,255,255,0.45)]" />
              </div>
              {loading ? (
                <div className="h-[24px] w-[12.57px] animate-pulse rounded bg-gray-700" />
              ) : (
                <div className="font-[PingFang SC] flex h-[24px] w-[12.57px] flex-shrink-0 flex-col justify-center text-[22px] font-medium text-white">
                  {successVideoCount}
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2">
              <div className="font-[PingFang SC] flex items-center justify-between text-right text-[13px] font-normal text-[rgba(255,255,255,0.45)]">
                累计投放广告视频 <ChevronRight className="inline-block h-4 w-4 text-[rgba(255,255,255,0.45)]" />
              </div>
              {loading ? (
                <div className="h-[24px] w-[12.57px] animate-pulse rounded bg-gray-700" />
              ) : (
                <div className="font-[PingFang SC] flex h-[24px] w-[12.57px] flex-shrink-0 flex-col justify-center text-[22px] font-medium text-white">
                  {/*TODO: 假的 */}
                  {/* {pendingTaskCount} */}0
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2">
              <div className="font-[PingFang SC] flex items-center justify-between text-right text-[13px] font-normal text-[rgba(255,255,255,0.45)]">
                视频生成中 <ChevronRight className="inline-block h-4 w-4 text-[rgba(255,255,255,0.45)]" />
              </div>
              {loading ? (
                <div className="h-[24px] w-[12.57px] animate-pulse rounded bg-gray-700" />
              ) : (
                <div className="font-[PingFang SC] flex h-[24px] w-[12.57px] flex-shrink-0 flex-col justify-center text-[22px] font-medium text-white">
                  {pendingTaskCount}
                </div>
              )}
            </div>
          </div>
          <Link
            href="/video-generation-tasks"
            prefetch
            className="mx-auto flex h-[40px] w-[90%] flex-shrink-0 items-center justify-center rounded-lg bg-black/20 backdrop-blur-sm"
          >
            管理生成视频
          </Link>
        </Card>
      </div>
      <div>
        <div className="mb-[18px] flex items-center justify-between">
          <h2 className="text-xl font-semibold text-white">投流分析，视频复刻</h2>
          <div className="flex items-center">
            <div className="text-xs text-[#9FA4B2]">更新时间：{updateTme}</div>
            <Button
              variant="outline"
              onClick={handleUpdateData}
              disabled={isLoadingCard}
              className="ml-2 flex h-8 items-center rounded-md border-none bg-[#CCDDFF1A] text-xs"
            >
              <RefreshCw className={`mr-1 h-3 w-3 ${isLoadingCard ? 'animate-spin' : ''}`} />
              <span className="mb-[2px]">更新数据</span>
            </Button>
          </div>
        </div>

        <Card className="w-full rounded-2xl border-none bg-[#CCDDFF1A] p-4 pb-1">
          <div className="relative">
            <ProMask loading={isLoadingCard} />
            {isLoading ? (
              <div className="flex min-h-[calc(100vh-390px)] items-center justify-center">
                <div className="flex flex-col items-center">
                  <RefreshCw className="mb-2 h-5 w-5 animate-spin" />
                  <span>加载中...</span>
                </div>
              </div>
            ) : !isLoadingCard && (excellentStatus === 'CREATING' || excellentStatus === 'RECOMMEND') ? (
              <div className="flex min-h-[calc(100vh-390px)] items-center justify-center">
                <div className="flex flex-col items-center">
                  <span>暂无数据...</span>
                </div>
              </div>
            ) : !isLoadingCard && excellentStatus === 'FAIL' ? (
              <div className="flex min-h-[calc(100vh-390px)] items-center justify-center">
                <div className="flex flex-col items-center">
                  <span className="text-red-500">获取数据失败</span>
                </div>
              </div>
            ) : !isLoadingCard && excellentAds?.length !== 0 ? (
              <div className="">
                <Card className="rainbow-border1 mb-[27px] rounded-xl">
                  <div className="flex w-full items-center justify-between rounded-xl bg-[#121B2B] px-4 py-5">
                    <div className="w-10/12 space-y-4">
                      <div className="flex items-center">
                        <BidBudget />
                        <span className="ml-3 text-sm font-medium text-[#00E1FF]">出价和预算：</span>
                        <span className="flex-1 truncate text-sm">
                          已控据{excellentAds?.length}个优秀的广告组，建议预算加投
                        </span>
                      </div>
                    </div>
                    <div className="ml-4 flex w-2/12 justify-end"></div>
                  </div>
                </Card>
                <div className="mb-[18px] flex items-center justify-between">
                  <div className="text-sm font-medium">优秀广告推荐</div>
                  {excellentAds?.length > 0 && excellentStatus === 'EDIT' && (
                    <Button
                      variant="outline"
                      onClick={editBudget}
                      className="h-8 rounded-md border-none bg-[#CCDDFF1A] text-xs hover:bg-[#CCDDFF1A] hover:text-white"
                    >
                      修改预算
                    </Button>
                  )}
                </div>
                <CommonTable
                  columns={columns}
                  dataSource={sortedData}
                  rowKey="adgroup_id"
                  renderCell={renderCell}
                  loading={isLoading}
                  test={true}
                />
              </div>
            ) : (
              <div className="flex min-h-[calc(100vh-390px)] items-center justify-center">没有找到任何数据...</div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
