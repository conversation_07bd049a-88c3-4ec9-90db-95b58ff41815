import { NextRequest, NextResponse } from 'next/server';

const isDev = process.env.NODE_ENV === 'development';

async function proxyRequest(request: NextRequest) {
  const url = new URL(request.url);
  const base = isDev ? 'http://*************:8081/api' : 'https://ads-sv.bowong.cc/api';
  const targetUrl = base + url.pathname.replace('/api/ads', '') + url.search;

  // 复制原始请求的headers
  const headers = new Headers(request.headers);

  // // 添加CORS相关的headers
  headers.set('Origin', isDev ? 'https://ads.roasmax.cn' : 'https://video-dev.bowongai.com');
  headers.set('Referer', isDev ? 'https://ads.roasmax.cn' : 'https://video-dev.bowongai.com');
  // // 添加 Host header
  headers.set('Host', isDev ? 'https://ads.roasmax.cn' : 'video-dev.bowongai.com');
  // 如果有认证信息，确保包含
  headers.set('credentials', 'include');

  let requestBody = null;
  if (request.body) {
    requestBody = await request.text();
  }

  const response = await fetch(targetUrl, {
    method: request.method,
    headers: headers,
    body: requestBody,
    cache: 'no-store',
  });

  // 先获取响应文本
  const responseText = await response.text();

  let responseData;
  try {
    // 尝试解析JSON
    responseData = JSON.parse(responseText);
  } catch (e) {
    // 如果解析失败，返回原始文本
    responseData = { error: responseText };
    console.error('响应JSON解析失败:', e);
  }

  // 设置响应头
  const responseHeaders = new Headers(response.headers);
  responseHeaders.set('Access-Control-Allow-Origin', '*');
  responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  return new NextResponse(JSON.stringify(responseData), {
    status: response.status,
    statusText: response.statusText,
    headers: responseHeaders,
  });
}

export async function GET(request: NextRequest) {
  return proxyRequest(request);
}

export async function POST(request: NextRequest) {
  return proxyRequest(request);
}
