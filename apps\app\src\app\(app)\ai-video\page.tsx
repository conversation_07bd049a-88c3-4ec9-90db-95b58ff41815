'use client';

import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import { cn } from '@/utils/cn';
import type React from 'react';
import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { confirm } from '@/components/ConfirmDialog';
import CustomSelect from './components/CustomSelect';
import { GenerationDialog } from './components/GenerationDialog';
import { VideoPreviewDialog } from './components/VideoPreviewDialog';
import { VideoWaterfall } from './components/VideoWaterfall';
import { useUpload } from './hooks/useUpload';
import { useVideoGeneration, useHistoryTaskPolling } from './hooks/useVideoGeneration';
import {
  getVideoTypeOptions,
  setApiDataCache,
  getApiDataCache,
  getPresetPrompts,
  getVideoTitle,
  getVideoDescription,
} from './data';
import type { VideoItem, VideoType, UploadResult } from './types';
import { listAiVideoGenerationTasks } from '@/services/actions/ai-video-generation-task';
import { action } from '@/utils/server-action/action';

const MAX_IMAGES = 10;

// API返回数据的接口定义
interface ApiVideoData {
  prompt: string;
  cover_url: string;
  video_url: string;
  description: string;
  detailDescription: string;
  title_zh: string;
  description_zh: string;
  aspect_ratio: string;
  engine: string;
  presetPrompts: string[];
}

interface ApiResponse {
  status: boolean;
  msg: string;
  data: Record<string, ApiVideoData>;
}

// Tab组件
const TabButton = ({
  active,
  onClick,
  children,
}: {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        'relative px-4 py-2 text-sm font-medium transition-all duration-200',
        'border-b-2 border-transparent',
        active ? 'border-[#00E1FF] text-[#00E1FF]' : 'text-white/70 hover:border-white/30 hover:text-white',
      )}
    >
      {children}
    </button>
  );
};

// 历史记录组件
const HistoryTab = ({ onVideoClick }: { onVideoClick: (video: VideoItem) => void }) => {
  const [historyTasks, setHistoryTasks] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const { toast } = useToast();

  // 使用历史记录轮询hook
  const { isPolling, startPollingTasks, stopAllPolling, getPollingTaskCount, completedTaskCount, cleanup } =
    useHistoryTaskPolling();

  // 获取历史记录
  const fetchHistoryTasks = async () => {
    try {
      setIsLoadingHistory(true);
      const result = await action(listAiVideoGenerationTasks, {
        pagination: { page: 1, limit: 999 },
      });

      if (result?.list) {
        setHistoryTasks(result.list);

        console.log('result?.list', result?.list);

        // 检查是否有运行中的任务需要轮询
        const runningTasks = result.list.filter(
          (task: any) => task.status === 'running' && task.external_task_id && task.external_task_id.trim() !== '',
        );

        if (runningTasks.length > 0) {
          console.log(
            `发现 ${runningTasks.length} 个运行中的任务，开始轮询`,
            runningTasks.map((t) => ({
              id: t.id,
              external_id: t.external_task_id,
              name: t.name,
            })),
          );
          startPollingTasks(runningTasks);
        } else {
          console.log('没有发现需要轮询的运行中任务');
        }
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
      toast({
        variant: 'destructive',
        title: '获取历史记录失败',
        description: error instanceof Error ? error.message : '未知错误',
      });
    } finally {
      setIsLoadingHistory(false);
    }
  };

  useEffect(() => {
    fetchHistoryTasks();
  }, []);

  // 当有任务完成时自动刷新历史记录
  useEffect(() => {
    if (completedTaskCount > 0) {
      const timer = setTimeout(() => {
        fetchHistoryTasks();
      }, 1000); // 延迟1秒后刷新，避免频繁请求

      return () => clearTimeout(timer);
    }
  }, [completedTaskCount]);

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // 将历史任务数据转换为VideoItem格式
  const transformHistoryToVideos = (tasks: any[]): VideoItem[] => {
    return tasks.map((task) => ({
      id: task.id,
      title: task.name || `${task.video_type || 'unknown'} Video`,
      prompt: task.prompt || '',
      thumbnailUrl: task.thumbnail_url || '',
      videoUrl: task.video_url || undefined, // 失败的任务可能没有视频URL
      type: (task.video_type as VideoType) || 'tea',
      createdAt: task.tmp_created_at,
      status: task.status,
      progress: task.progress,
      error_message: task.error_message,
    }));
  };

  const historyVideos = transformHistoryToVideos(historyTasks);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h2 className="text-lg font-medium text-white">历史生成记录</h2>
          {isPolling && (
            <div className="flex items-center gap-2 text-xs text-[#00E1FF]">
              <div className="h-3 w-3 animate-spin rounded-full border border-[#00E1FF] border-t-transparent" />
              <span>轮询中 ({getPollingTaskCount()} 个任务)</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isPolling && (
            <Button
              onClick={stopAllPolling}
              variant="outline"
              size="sm"
              className="border-orange-500/30 text-orange-400 hover:bg-orange-500/10"
            >
              停止轮询
            </Button>
          )}
          <Button
            onClick={fetchHistoryTasks}
            variant="outline"
            size="sm"
            className="border-[#00E1FF]/30 text-[#00E1FF] hover:bg-[#00E1FF]/10"
          >
            刷新
          </Button>
        </div>
      </div>

      {isLoadingHistory ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-white/70">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white/20 border-t-white" />
            加载历史记录中...
          </div>
        </div>
      ) : historyVideos.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center text-white/50">
            <div className="mb-2 text-lg">📹</div>
            <div>暂无历史记录</div>
          </div>
        </div>
      ) : (
        <VideoWaterfall videos={historyVideos} onVideoClick={onVideoClick} isLoading={isLoadingHistory} />
      )}
    </div>
  );
};

// 自定义文件上传组件
const FileUploadButton = ({
  onChange,
  disabled,
  uploadCount,
}: {
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled: boolean;
  uploadCount: number;
}) => {
  return (
    <div className="relative">
      <input
        type="file"
        accept="image/*"
        multiple
        onChange={onChange}
        disabled={disabled}
        className="absolute inset-0 h-full w-full cursor-pointer opacity-0 disabled:cursor-not-allowed"
        id="file-upload"
      />
      <label
        htmlFor="file-upload"
        className={cn(
          'flex h-10 items-center justify-center gap-2 rounded-md border px-4 transition-all duration-200',
          'cursor-pointer border-[#00E1FF]/30 bg-[#1F2434] text-sm font-medium text-white',
          'hover:border-[#00E1FF]/50 hover:bg-[#273048]',
          'focus-within:border-[#00E1FF] focus-within:ring-2 focus-within:ring-[#00E1FF]/20',
          disabled && 'cursor-not-allowed opacity-50 hover:border-[#242938] hover:bg-[#1F2434]',
        )}
      >
        <svg className="h-4 w-4 text-[#00E1FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
        <span className="text-white/90">{uploadCount > 0 ? `已选择 ${uploadCount} 张图片` : '上传图片'}</span>
      </label>
    </div>
  );
};

const TopSellingVideos = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<'generate' | 'history'>('generate');
  const [prompt, setPrompt] = useState('');
  const [selectedType, setSelectedType] = useState<VideoType>('tea');
  const [selectedVideo, setSelectedVideo] = useState<VideoItem | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showGenerationDialog, setShowGenerationDialog] = useState(false);
  const [open, setOpen] = useState(false);
  const [videos, setVideos] = useState<VideoItem[]>([]);
  const [isLoadingVideos, setIsLoadingVideos] = useState(true);
  const [videoTypeOptions, setVideoTypeOptions] = useState<
    Array<{ value: VideoType; label: string; description: string }>
  >([]);
  const uploader = useUpload();
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);

  // 添加滚动容器的引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 检查是否有正在上传的任务
  const isUploading = uploader.groupedTasks.running.length > 0;

  // 使用 useVideoGeneration hook
  const { isGenerating, currentTask, error, generateVideo, stopGeneration, resetTask } = useVideoGeneration();

  // 将API数据转换为VideoItem[]格式 - 支持VIDEO_TYPE_DATA和API数据
  const transformApiDataToVideos = (data: Record<string, any>): VideoItem[] => {
    return Object.entries(data).map(([key, value], index) => ({
      id: `${key}-${index}`,
      title: value.title_zh || getVideoTitle(key as VideoType),
      prompt: value.description_zh || getVideoDescription(key as VideoType),
      thumbnailUrl: value.cover_url,
      videoUrl: value.video_url,
      type: key as VideoType,
      createdAt: new Date().toISOString(),
      // 新增字段用于模板功能（如果存在）
      aspectRatio: value.aspect_ratio,
      engine: value.engine,
    }));
  };

  // 获取示例提示词的函数
  const getSamplePrompt = async () => {
    try {
      setIsLoadingVideos(true);

      // 创建一个Promise用于API调用
      const apiPromise = fetch('https://bowongai--text2video-api-dev-fastapi-app.modal.run/api/prompt/default')
        .then((response) => {
          if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          console.log('API 返回数据:', data);

          // API返回格式: {status: boolean, msg: string, data: {...}}
          // 我们需要提取data字段
          if (data && data.status && data.data && typeof data.data === 'object') {
            return data.data; // 返回实际的视频数据
          } else {
            throw new Error(`API 返回数据格式不正确: ${data?.msg || '未知错误'}`);
          }
        });

      // 创建一个超时Promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('API 调用超时'));
        }, 5000); // 5秒超时
      });

      // 使用Promise.race来竞争
      let apiData;
      try {
        apiData = await Promise.race([apiPromise, timeoutPromise]);
        console.log('使用 API 数据:', apiData);

        // 设置API数据缓存
        setApiDataCache(apiData);

        // 更新视频类型选项
        setVideoTypeOptions(getVideoTypeOptions());

        const videoItems = transformApiDataToVideos(apiData);
        setVideos(videoItems);
        console.log('转换后的视频数据(API):', videoItems);

        return { status: true, data: apiData };
      } catch (apiError) {
        console.error('API 调用失败或超时:', apiError);

        // 如果API失败，显示错误提示
        toast({
          variant: 'destructive',
          title: 'API调用失败',
          description: '无法获取视频模板数据，请稍后重试',
        });

        setVideos([]);
        return { status: false, data: null };
      }
    } catch (error) {
      console.error('获取示例数据失败:', error);

      // 显示错误提示
      toast({
        variant: 'destructive',
        title: '数据加载失败',
        description: '无法加载视频数据，请刷新页面重试',
      });

      setVideos([]);
      return { status: false, data: null };
    } finally {
      setIsLoadingVideos(false);
    }
  };

  // 页面加载时调用接口
  useEffect(() => {
    if (activeTab === 'generate') {
      getSamplePrompt();
    }
  }, [activeTab]);

  // 带确认对话框的生成视频函数
  const handleGenerateWithConfirm = async () => {
    if (!prompt.trim()) {
      toast({
        variant: 'destructive',
        title: '请输入提示词',
        description: '请先输入视频生成的提示词',
      });
      return;
    }

    confirm({
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#00E1FF]/10">
              <svg className="h-6 w-6 text-[#00E1FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">确认生成视频</h3>
              <p className="text-sm text-white/70">即将消耗积分来生成您的视频</p>
            </div>
          </div>
          <div className="rounded-lg border border-[#00E1FF]/20 bg-[#00E1FF]/5 p-4">
            <div className="mb-3 flex items-center justify-between">
              <span className="text-sm font-medium text-white/90">本次生成将消耗:</span>
              <span className="text-lg font-bold text-[#00E1FF]">5 积分</span>
            </div>
            <div className="text-xs text-white/60">
              <div className="mb-1">
                • 视频类型: {videoTypeOptions.find((opt) => opt.value === selectedType)?.label || selectedType}
              </div>
              <div className="mb-1">• 生成时间: 预计 3-5 分钟</div>
              <div>• 限时特惠: 原价 10 积分，现仅需 5 积分</div>
            </div>
          </div>
          <div className="text-sm text-white/70">确认后将开始生成视频，请耐心等待生成完成。</div>
        </div>
      ),
      buttonText: {
        cancel: '取消',
        confirm: '确认生成',
      },
      onConfirm: async () => {
        await handleGenerate();
      },
    });
  };

  const handleGenerate = async () => {
    try {
      setShowGenerationDialog(true);
      await generateVideo({
        prompt: prompt.trim(),
        type: selectedType,
        files: uploadResults,
      });
    } catch (error) {
      console.error('生成视频失败:', error);
      toast({
        variant: 'destructive',
        title: '生成视频失败',
        description: error instanceof Error ? error.message : '未知错误',
      });
    }
  };

  const handleVideoClick = (video: VideoItem) => {
    setSelectedVideo(video);
    setShowPreviewDialog(true);
  };

  const handleApplyTemplate = (video: VideoItem) => {
    const presets = getPresetPrompts(video.type);
    const newPrompt = presets.length > 0 ? presets[Math.floor(Math.random() * presets.length)] : '请描述一个场景';
    setSelectedType(video.type);
    setPrompt(newPrompt || '请描述一个场景');
    setActiveTab('generate'); // 切换到生成tab

    // 滚动到页面顶部
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
      }
    }, 100); // 稍微延迟以确保tab切换完成
  };

  const handleCloseGenerationDialog = () => {
    setShowGenerationDialog(false);
    resetTask();
  };

  const handleCancelGeneration = () => {
    stopGeneration();
    setShowGenerationDialog(false);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    const fileArray = Array.from(files).slice(0, MAX_IMAGES);
    if (fileArray.length > MAX_IMAGES) {
      toast({
        variant: 'destructive',
        title: `最多只能上传 ${MAX_IMAGES} 张图片`,
      });
      return;
    }
    setUploadResults([]);
    uploader.push(fileArray, {
      onSuccess: (info) => {
        const result: UploadResult = {
          url: info.url,
          taskName: info.task.name,
          taskId: info.task.id,
        };
        setUploadResults((prev) => [...prev, result]);
      },
    });
  };

  // 换一换按钮逻辑
  const shufflePrompt = () => {
    const presets = getPresetPrompts(selectedType);
    if (presets.length === 0) {
      setPrompt('请描述一个场景');
      return;
    }
    const candidates = presets.filter((p: string) => p !== prompt);
    const newPrompt = (candidates.length > 0 ? candidates : presets)[
      Math.floor(Math.random() * (candidates.length > 0 ? candidates.length : presets.length))
    ];
    setPrompt(newPrompt || '请描述一个场景');
  };

  return (
    <Panel className="flex h-full w-full flex-col overflow-hidden px-6 py-4">
      {/* Tab导航 */}
      <div className="mb-6 flex border-b border-white/10">
        <TabButton active={activeTab === 'generate'} onClick={() => setActiveTab('generate')}>
          视频生成
        </TabButton>
        <TabButton active={activeTab === 'history'} onClick={() => setActiveTab('history')}>
          历史记录
        </TabButton>
      </div>

      {/* Tab内容 */}
      <div ref={scrollContainerRef} className="min-h-0 flex-1 overflow-y-scroll" id="scroll-container">
        {activeTab === 'generate' ? (
          <>
            {/* 顶部控制区域 */}
            <div className="mb-6 flex flex-shrink-0 gap-4">
              {/* 左侧提示词输入区域 */}
              <div className="flex-1">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-white/90">视频生成提示词</label>
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="描述你想要生成的视频内容，比如：一个美丽的日落场景，海边有棕榈树摇摆..."
                    className={cn(
                      'h-[180px] w-full resize-none rounded-lg border transition-all duration-200',
                      'border-[#00E1FF]/30 bg-[#1F2434] text-sm leading-relaxed text-white',
                      'px-4 py-3 placeholder:text-[#7E8495]',
                      'focus:border-[#00E1FF] focus:outline-none focus:ring-2 focus:ring-[#00E1FF]/20',
                      'hover:border-[#00E1FF]/50',
                    )}
                  />
                </div>
              </div>

              {/* 右侧控制区域 */}
              <div className="flex w-[200px] flex-col gap-3">
                {/* 文件上传 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-white/90">参考图片 (可选)</label>
                  <FileUploadButton
                    onChange={handleFileSelect}
                    disabled={isUploading || isGenerating}
                    uploadCount={uploadResults.length}
                  />
                  {uploadResults.length > 0 && (
                    <div className="text-xs text-[#7E8495]">
                      已上传 {uploadResults.length}/{MAX_IMAGES} 张图片
                    </div>
                  )}
                </div>

                {/* 视频类型选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-white/90">视频类型</label>
                  <CustomSelect
                    className="w-full"
                    value={selectedType}
                    options={videoTypeOptions}
                    placeholder="选择视频类型"
                    open={open}
                    onOpenChange={setOpen}
                    onValueChange={(value) => {
                      const type = value as VideoType;
                      setSelectedType(type);
                      const presets = getPresetPrompts(type);
                      const newPrompt = presets.length
                        ? presets[Math.floor(Math.random() * presets.length)]
                        : '请描述一个场景';
                      setPrompt(newPrompt || '请描述一个场景');
                    }}
                  />
                </div>

                {/* 生成 & 换一换 按钮 */}
                <div className="flex flex-col gap-2">
                  <div className="flex gap-2">
                    <Button
                      onClick={handleGenerateWithConfirm}
                      disabled={!prompt.trim() || isUploading || isGenerating}
                      className={cn(
                        'h-12 flex-1 rounded-lg text-sm font-medium transition-all duration-200',
                        'bg-gradient-to-r from-[#00E1FF] to-[#9D81FF] text-white',
                        'hover:from-[#00E1FF]/90 hover:to-[#9D81FF]/90',
                        'disabled:from-[#242938] disabled:to-[#242938] disabled:text-[#7E8495]',
                        'shadow-lg hover:shadow-xl disabled:shadow-none',
                        'relative',
                      )}
                    >
                      {isGenerating ? (
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-white/20 border-t-white" />
                          生成中...
                        </div>
                      ) : (
                        <>
                          <div className="flex flex-col items-center">
                            <span>生成视频</span>
                            <span className="text-xs text-white/80">每次扣 5 点</span>
                          </div>
                          <span className="absolute -right-2 -top-2 rounded-full bg-orange-500 px-2 py-0.5 text-xs font-bold text-white">
                            限时特惠
                          </span>
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={shufflePrompt}
                      disabled={isUploading || isGenerating}
                      variant="outline"
                      className="h-12 flex-1 rounded-lg border-[#00E1FF]/50 text-sm font-medium text-[#00E1FF] hover:bg-[#00E1FF]/10"
                    >
                      换一换
                    </Button>
                  </div>

                  {/* 积分说明 */}
                  <div className="rounded-md border border-[#00E1FF]/20 bg-[#00E1FF]/5 p-2 text-xs text-white/70">
                    <div className="flex items-center gap-1">
                      <svg className="h-3 w-3 text-[#00E1FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="font-medium text-[#00E1FF]">限时特惠</span>
                    </div>
                    <div className="mt-1">原价 10 积分，现仅需 5 积分</div>
                  </div>
                </div>

                {/* 状态提示 */}
                {isUploading && (
                  <div className="flex items-center gap-1 text-xs text-[#00E1FF]">
                    <div className="h-3 w-3 animate-spin rounded-full border border-[#00E1FF] border-t-transparent" />
                    图片上传中...
                  </div>
                )}
              </div>
            </div>

            {/* 视频展示区域 */}
            <div className="min-h-0 flex-1">
              {isLoadingVideos ? (
                <div className="flex h-64 items-center justify-center">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-[#00E1FF]"></div>
                    <p className="text-white/70">加载中...</p>
                  </div>
                </div>
              ) : videos.length === 0 ? (
                <div className="flex h-64 items-center justify-center text-white/50">
                  <div className="text-center">
                    <svg
                      className="mx-auto mb-4 h-16 w-16 text-white/30"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    <p className="text-lg">暂无视频内容</p>
                    <p className="mt-1 text-sm">生成新的视频开始探索</p>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="mb-4 text-lg font-medium text-white">视频模板</div>
                  <VideoWaterfall videos={videos} onVideoClick={handleVideoClick} isLoading={isLoadingVideos} />
                </div>
              )}
            </div>
          </>
        ) : (
          <HistoryTab onVideoClick={handleVideoClick} />
        )}
      </div>

      {/* 弹窗组件 */}
      <VideoPreviewDialog
        video={selectedVideo}
        isOpen={showPreviewDialog}
        onClose={() => setShowPreviewDialog(false)}
        onApply={handleApplyTemplate}
      />

      <GenerationDialog
        isOpen={showGenerationDialog}
        task={currentTask}
        isGenerating={isGenerating}
        error={error}
        onClose={handleCloseGenerationDialog}
        onCancel={handleCancelGeneration}
      />
    </Panel>
  );
};

export default TopSellingVideos;
