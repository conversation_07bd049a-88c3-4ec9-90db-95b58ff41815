declare namespace NodeJS {
  interface ProcessEnv {
    // 任务调度模式 static: 静态调度，固定值； dynamic: 动态调度，根据租户配置项动态调度
    TASK_POOL_MODE?: 'static' | 'dynamic';

    // Bull MQ Endpoint
    BULLMQ_ENDPOINT: string;
    BULLMQ_AUTHORIZATION: string;

    // Langfuse 服务配置
    LANGFUSE_BASEURL: string;

    // Langfuse RPC 服务配置
    LANGFUSE_RPC_EMAIL: string;
    LANGFUSE_RPC_PASSWORD: string;

    // 腾讯 VOD 服务配置
    VOD_SECRETKEY: string;
    VOD_SECRETID: string;

    // Authing AuthenticationClient 包含注册登录、重置手机号邮箱、修改账号信息等方法
    APPHOST: string;
    APPID: string;
    APPSECRET: string;

    // Authing ManagementClient 包含用户管理、角色管理、权限管理等方法
    PRIVATE_ACCESSKEYID_ID: string;
    PRIVATE_ACCESSKEYSECRET_ID: string;

    // 飞书提醒机器人Callback Url
    FEISHU_NOTIFY_CALLBACK_URL: string;

    // 分发分配请求的HOST
    VIDEO_DISTRIBUTION_HOST: string;

    // 对公分享链接的HOST
    PUBLIC_SHARE_LINK_HOST: string;

    // Dify Workflow Key
    DIFY_WORKFLOW_KEY: string;
    // Dify Workflow Key For GC
    DIFY_WORKFLOW_GC_KEY: string;
    // Dify Workflow Key For GC 的配置获取host
    DIFY_WORKFLOW_GC_CONFIG_HOST: string;

    // COS
    COS_SECRET_ID: string;
    COS_SECRET_KEY: string;
    COS_REGION: string;
    COS_BUCKET: string;
    COS_APPID: string;

    // SCF调用
    SCF_CUT_URL: string;

    // Webhook Secret Key
    WEBHOOK_SECRET: string;
  }
}

declare module 'mp4box' {
  interface MP4MediaTrack {
    id: number;
    created: Date;
    modified: Date;
    movie_duration: number;
    layer: number;
    alternate_group: number;
    volume: number;
    track_width: number;
    track_height: number;
    timescale: number;
    duration: number;
    bitrate: number;
    codec: string;
    language: string;
    nb_samples: number;
  }

  interface MP4VideoData {
    width: number;
    height: number;
  }

  interface MP4VideoTrack extends MP4MediaTrack {
    video: MP4VideoData;
  }

  interface MP4AudioData {
    sample_rate: number;
    channel_count: number;
    sample_size: number;
  }

  interface MP4AudioTrack extends MP4MediaTrack {
    audio: MP4AudioData;
  }

  type MP4Track = MP4VideoTrack | MP4AudioTrack;

  interface MP4Info {
    duration: number;
    timescale: number;
    fragment_duration: number;
    isFragmented: boolean;
    isProgressive: boolean;
    hasIOD: boolean;
    brands: string[];
    created: Date;
    modified: Date;
    tracks: MP4Track[];
    audioTracks: MP4AudioTrack[];
    videoTracks: MP4VideoTrack[];
  }

  export type MP4ArrayBuffer = ArrayBuffer & { fileStart: number };

  export interface MP4File {
    onMoovStart?: () => void;
    onReady?: (info: MP4Info) => void;
    onError?: (e: string) => void;

    appendBuffer(data: MP4ArrayBuffer): number;
    start(): void;
    stop(): void;
    flush(): void;
  }

  export function createFile(): MP4File;

  export {};
}
