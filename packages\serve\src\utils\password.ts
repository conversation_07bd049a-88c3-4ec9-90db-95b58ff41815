/**
 * 密码加密和验证工具
 * 提供安全的密码哈希和验证功能
 */

import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export class PasswordManager {
  private static readonly SALT_ROUNDS = 12;
  private static readonly MIN_PASSWORD_LENGTH = 8;
  private static readonly MAX_PASSWORD_LENGTH = 128;

  /**
   * 生成随机盐值
   */
  static generateSalt(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 哈希密码
   * @param password 明文密码
   * @param salt 盐值（可选，不提供则自动生成）
   */
  static async hashPassword(password: string, salt?: string): Promise<{ hash: string; salt: string }> {
    this.validatePassword(password);
    
    const finalSalt = salt || this.generateSalt();
    const saltedPassword = password + finalSalt;
    const hash = await bcrypt.hash(saltedPassword, this.SALT_ROUNDS);
    
    return { hash, salt: finalSalt };
  }

  /**
   * 验证密码
   * @param password 明文密码
   * @param hash 存储的哈希值
   * @param salt 存储的盐值
   */
  static async verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
    try {
      const saltedPassword = password + salt;
      return await bcrypt.compare(saltedPassword, hash);
    } catch (error) {
      console.error('Password verification error:', error);
      return false;
    }
  }

  /**
   * 验证密码强度
   * @param password 密码
   */
  static validatePassword(password: string): void {
    if (!password) {
      throw new Error('密码不能为空');
    }

    if (password.length < this.MIN_PASSWORD_LENGTH) {
      throw new Error(`密码长度不能少于 ${this.MIN_PASSWORD_LENGTH} 位`);
    }

    if (password.length > this.MAX_PASSWORD_LENGTH) {
      throw new Error(`密码长度不能超过 ${this.MAX_PASSWORD_LENGTH} 位`);
    }

    // 检查密码复杂度：至少包含字母和数字
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);

    if (!hasLetter || !hasNumber) {
      throw new Error('密码必须包含字母和数字');
    }
  }

  /**
   * 生成随机密码
   * @param length 密码长度（默认12位）
   */
  static generateRandomPassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    // 确保至少包含一个字母和一个数字
    password += 'A'; // 大写字母
    password += 'a'; // 小写字母
    password += '1'; // 数字
    
    // 填充剩余长度
    for (let i = 3; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * 检查密码是否需要重置
   * @param lastPasswordChange 上次密码修改时间
   * @param maxDays 密码有效期（天数，默认90天）
   */
  static isPasswordExpired(lastPasswordChange: Date, maxDays: number = 90): boolean {
    const now = new Date();
    const diffTime = now.getTime() - lastPasswordChange.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > maxDays;
  }

  /**
   * 生成密码重置令牌
   */
  static generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 验证密码重置令牌格式
   */
  static validateResetToken(token: string): boolean {
    return /^[a-f0-9]{64}$/.test(token);
  }
}
