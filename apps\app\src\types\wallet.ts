export enum WalletChangeType {
  CHARGE = 'CHARGE',
  REFUND = 'REFUND',
  SET_MENU_IN = 'SET_MENU_IN',
  SET_MENU_OUT = 'SET_MENU_OUT',
  VIDEO = 'VIDEO',
  GIVE = 'GIVE',
}

export interface UpdateWalletQuotaInput {
  quota: number;
  changeType: WalletChangeType;
  changeReason: string;
  fromTenantId?: string;
  fromUserId?: string;
  toTenantId?: string;
  toUserId?: string;
}
export interface UpdateQuotaInput {
  quota: number;
  changeType: WalletChangeType;
  changeReason: string;
  result_quota?: number;
  pricing_plan_id?: string;
  tenant_id?: string;
}
export interface PricingPlan {
  id: string;
  tmp_created_at: Date;
  tmp_updated_at: Date;
  tenant_id: string;
  plan_name: string;
  quota: number;
  start_time: Date;
  end_time: Date;
}
export interface QuotaResult {
  quota: number;
  grade: PricingPlan;
}
export interface CreateQuotaInput {
  email: string;
  quota: number;
  plan_name: string;
}
export interface GiftQuotaInput {
  email: string;
  quota: number;
}
