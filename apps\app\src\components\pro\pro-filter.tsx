import { Button } from '@/components/ui';
import { cn } from '@/utils/cn';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { ProColumnType } from './pro-column';
import { ProEditField } from './pro-field';

export interface ProFilterColumnType<T extends Record<string, any>>
  extends Omit<ProColumnType<T>, 'editable' | 'render'> {
  key: string;
  title: string;
  config?: {
    switch?: { checked: boolean; value: any; label: string }[];
    options?: { value: string; label: string }[];
  };
  render?: (value: T[keyof T], onChange: (value: T[keyof T]) => void, dom: ReactNode) => ReactNode;
}

export interface ProFilterProps<T extends Record<string, any>> {
  value?: T;
  defaultValue?: T;
  onChange?: (value: T) => void;
  onSubmit?: (value: T) => void;
  onReset?: (defaultValue: T) => void;
  columns: ProFilterColumnType<T>[];
  className?: string;
}

export function ProFilter<T extends Record<string, any>>(props: ProFilterProps<T>) {
  const { value, defaultValue = {} as T, onSubmit, onReset, columns, className } = props;

  const [innerFilters, setInnerFilters] = useState<T>(value || defaultValue);

  const [placeholdersCount, setPlaceholdersCount] = useState(0);
  const gridRef = useRef<HTMLDivElement>(null);

  const handleSubmit = () => {
    onSubmit?.(innerFilters);
  };

  const resetAll = () => {
    setInnerFilters(defaultValue);
    onSubmit?.(defaultValue);
    onReset?.(defaultValue);
  };

  useEffect(() => {
    const calculatePlaceholders = () => {
      if (gridRef.current) {
        // 容器宽度
        const containerWidth = gridRef.current.clientWidth;
        // 筛选项宽度
        const itemWidth = gridRef.current.children.item(0)?.clientWidth || 200;
        // 每行筛选项数量
        const columnsPerRow = Math.floor(containerWidth / itemWidth);
        // 筛选项总数
        const totalItems = columns.length;
        // 最后一行筛选项数量
        const lastRowItems = totalItems % columnsPerRow;

        // 只有当最后一行有足够空间时才添加占位符
        if (lastRowItems > 0 && lastRowItems < columnsPerRow) {
          setPlaceholdersCount(columnsPerRow - lastRowItems - 1);
        } else {
          setPlaceholdersCount(columnsPerRow - 1); // 如果按钮需要单独一行，填充整行减一个位置
        }
      }
    };

    calculatePlaceholders();
    const resizeObserver = new ResizeObserver(calculatePlaceholders);
    if (gridRef.current) {
      resizeObserver.observe(gridRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [columns.length]);

  return (
    <div className={cn('w-full', className)}>
      <div
        ref={gridRef}
        className="grid gap-2"
        style={{
          gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
          gridAutoRows: 'min-content',
        }}
      >
        {/* 筛选项 */}
        {columns.map((column) => {
          const columnKey = column.key;
          return (
            <div key={columnKey}>
              <ProEditField
                className="w-full"
                value={innerFilters[columnKey]}
                type={column.type}
                placeholder={column.title}
                config={column.config}
                onValueChange={(e) => {
                  setInnerFilters({ ...innerFilters, [columnKey]: e });
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSubmit();
                  }
                }}
                initialValue={innerFilters[columnKey]}
                render={column.render}
              />
            </div>
          );
        })}
        {/* 占位符 */}
        {placeholdersCount > 0 &&
          [...Array(placeholdersCount)].map((_, index) => (
            <div key={`placeholder-${index}`} className="min-w-[200px]" />
          ))}
        {/* 按钮组 */}
        <div className="col-end-[-1] flex min-w-[200px] justify-end gap-2">
          <Button onClick={handleSubmit} className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]">
            查询
          </Button>
          <Button onClick={resetAll} className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]">
            重置
          </Button>
        </div>
      </div>
    </div>
  );
}
