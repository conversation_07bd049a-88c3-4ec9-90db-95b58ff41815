'use strict';
import { sleep, VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';
import { EventPayload, Handler } from '..';
import { cos } from '../utils/cos';
import { prisma } from '../utils/prisma';
import { snowflake } from '../utils/snowflake';

export class UploadFromCosForDistributedHandler implements Handler {
  async handle(record: EventPayload['Records'][0]) {
    console.log('开始处理文件【自动分发-分发素材】', record.cos.cosObject.key);
    console.log('----------------------------');

    // 获取文件相对于bucket下的key
    const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

    const keyArr = key.split('/');
    if (keyArr.length < 5) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }

    const [, tenantId, , , ...distributed] = keyArr;
    if (!tenantId || !distributed.length) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }
    if (!distributed[distributed.length - 1]?.endsWith('.mp4')) {
      console.log('不是一个.mp4文件，不处理', key);
      return;
    }

    console.log('接收到待分发的视频', JSON.stringify({ tenantId, fileName: distributed }));
    await this.handleDistributedFile(record, tenantId, distributed);
  }

  private async handleDistributedFile(record: EventPayload['Records'][0], tenantId: string, distributed: string[]) {
    await sleep(5000);

    const distributedPath = `roasmax/${tenantId}/自动分发/分发素材/${distributed.join('/')}`;
    const exist = await prisma.video_distribution_sub_tasks.findFirst({
      where: { distributed_cos_path: distributedPath },
    });

    if (!exist) {
      console.log('不存在该待分发子任务，自动创建');
      // 最后两个分别是日期和文件名
      const socialAccount = await prisma.social_accounts.findFirst({
        where: { cloud_terminal: { cloud_host_cos_path: distributed.slice(0, -2).join('/') } },
      });
      console.log(`该视频分发给了账号${socialAccount?.nickname}(${socialAccount?.id})`);

      await prisma.video_distribution_sub_tasks.create({
        data: {
          tenant_id: tenantId,
          status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已分发,
          status_desc: '已成功分发',
          batch_no: snowflake.nextId(),
          distributed_cos_path: distributedPath,
          social_account_id: socialAccount?.id,
        },
      });
      return;
    }

    console.log('成功分发');
    await prisma.video_distribution_sub_tasks.update({
      where: { id: exist.id },
      data: { status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已分发, status_desc: '已成功分发' },
    });

    // 删除分发成功的文件
    if (exist.wait_cos_path) {
      await cos.deleteObject({
        Bucket: `${record.cos.cosBucket.name}-${record.cos.cosBucket.appid}`,
        Region: record.cos.cosBucket.s3Region,
        Key: exist.wait_cos_path,
      });
    }
  }
}
