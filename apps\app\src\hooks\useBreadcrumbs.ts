import { useState } from 'react';
import { DirItemType } from '@/types/material';

const DEFAULT_BREADCRUMB_ITEMS = [{ name: '云盘', id: 'root' }];

export const useBreadcrumbs = () => {
  const [breadcrumbItems, setBreadcrumbItems] = useState<DirItemType[]>(DEFAULT_BREADCRUMB_ITEMS);

  // 添加处理面包屑点击的函数
  const handleBreadcrumbClick = (index: number) => {
    if (index < breadcrumbItems.length - 1) {
      setBreadcrumbItems(breadcrumbItems.slice(0, index + 1));
    }
  };

  const handleAddBreadcrumb = (item: DirItemType) => {
    setBreadcrumbItems((prev) => {
      return [...prev, item];
    });
  };

  const resetBreadcrumb = () => {
    setBreadcrumbItems(DEFAULT_BREADCRUMB_ITEMS);
  };

  return {
    breadcrumbItems,
    setBreadcrumbItems,
    handleBreadcrumbClick,
    handleAddBreadcrumb,
    resetBreadcrumb,
  };
};
