import { CreateCampaignReq, SyncCampaignListReq } from '@/services/interfaces/ads/req';
import { CampaignItem } from '@/types/ads';
import tiktokService from '@/services/tiktokService';
import { recursiveCamelToUnderscore, ToCamelCase } from '@/utils/camel';
import { to } from '@roasmax/utils';
import { toast } from 'react-hot-toast';
import type { StoreApi } from 'zustand';
import type { AdStore } from '../storeTypes';
import type { StoreSlice } from './types';
import emitter from '@/utils/mitt';

export interface CampaignSlice
  extends StoreSlice<
    | 'setCurrentCampaign'
    | 'setCampaignModal'
    | 'resetCampaignModal'
    | 'createCampaign'
    | 'updateCampaign'
    | 'updateCampaignStatus'
    | 'syncCampaigns'
  > {}

// 创建一个高阶函数来处理 loading 状态
const withLoading = (set: StoreApi<AdStore>['setState']) => {
  return <T extends (...args: any[]) => Promise<any>>(
    statusKey: keyof Pick<AdStore, 'campaignStatus' | 'advertiserStatus' | 'adGroupStatus' | 'adStatus'>,
    fn: T,
  ): T => {
    return (async (...args: Parameters<T>) => {
      set({
        [statusKey]: { loading: true, error: false },
      });
      try {
        const result = await fn(...args);
        set({
          [statusKey]: { loading: false, error: false },
        });
        return result;
      } catch (error) {
        set({
          [statusKey]: { loading: false, error: true },
        });
        throw error;
      }
    }) as T;
  };
};

export const createCampaignSlice = (set: StoreApi<AdStore>['setState'], get: () => AdStore): CampaignSlice => {
  const handleLoading = withLoading(set);

  return {
    actions: {
      setCurrentCampaign: (campaign) => {
        set({ currentCampaign: campaign, currentAd: null, currentAdGroup: null });
      },
      syncCampaigns: handleLoading('campaignStatus', async (params?: SyncCampaignListReq) => {
        const currentAdvertiser = get().currentAdvertiser;
        // const currentView = get().currentView;

        if (!currentAdvertiser || !currentAdvertiser.length) {
          toast.error('未选择广告主');
          return;
        }
        if (!params?.start_date || !params?.end_date) {
          toast.error('请选择日期范围');
          return;
        }
        const requestParams: SyncCampaignListReq = {
          advertiser_ids: currentAdvertiser?.map((advertiser) => advertiser?.advertiserId),
          start_date: params?.start_date,
          end_date: params?.end_date,
        };
        try {
          await tiktokService.getSyncCampaignList(requestParams);
          toast.success('同步成功');
          // if (currentView === 'group') {
          //   // 刷新广告组列表
          //   await get().actions.fetchAdGroups({
          //     campaign_ids: get().currentCampaign?.map((c) => String(c.campaignId)),
          //     page: 1,
          //     page_size: 10,
          //     start_time: params?.start_date,
          //     end_time: params?.end_date,
          //   });
          // } else if (currentView === 'ad') {
          //   // 触发广告列表刷新事件
          //   emitter.emit('REFRESH_AD_LIST', {
          //     page: 1,
          //     pageSize: 10,
          //     advertiserIds: currentAdvertiser?.map((advertiser) => advertiser?.advertiserId).join(','),
          //     groupIds: get()
          //       .currentAdGroup?.map((adGroup) => adGroup.groupId)
          //       .join(','),
          //   });
          // }

          emitter.emit('REFRESH_CAMPAIGN_LIST', {
            // 传递需要的参数
          });
        } catch (error) {
          console.error('同步广告系列失败:', error);
          toast.error('同步失败');
          throw error;
        }
      }),
      setCampaignModal: (modal) => {
        set((state) => ({
          campaignModal: {
            ...state.campaignModal,
            ...modal,
          },
        }));
      },

      resetCampaignModal: () => {
        set({
          campaignModal: {
            type: 'create',
            show: false,
            title: '',
            formValue: null,
          },
        });
      },
      updateCampaign: handleLoading('campaignStatus', async (campaignData: Partial<CampaignItem>) => {
        try {
          const currentAdvertiser = get().currentAdvertiser;
          if (!currentAdvertiser || !currentAdvertiser.length) {
            throw new Error('未选择广告主');
          }

          const { jsonDate, ...restData } = campaignData;

          const campaignUpdateData = {
            ...restData,
            id: campaignData.id,
            advertiserId: campaignData.advertiserId,
            campaignId: campaignData.campaignId,
            campaignName: campaignData.campaignName,
          };
          const underScoreData = recursiveCamelToUnderscore(campaignUpdateData);

          const [err, res] = await to(tiktokService.updateCampaign(underScoreData));
          if (err || !res) {
            return;
          }

          toast.success('操作成功');
          emitter.emit('REFRESH_CAMPAIGN_LIST', {
            // 传递需要的参数
          });

          set({
            campaignModal: {
              type: 'create',
              show: false,
              title: '',
              formValue: null,
            },
          });
        } catch (error) {
          toast.error('操作失败');
          throw error;
        }
      }),
      createCampaign: handleLoading('campaignStatus', async (campaignData: ToCamelCase<CreateCampaignReq>) => {
        try {
          const currentAdvertiser = get().currentAdvertiser;
          if (!currentAdvertiser || !currentAdvertiser.length) {
            throw new Error('未选择广告主');
          }

          const fullCampaignData = currentAdvertiser.map((advertiser) => ({
            ...campaignData,
            advertiser_id: advertiser.advertiserId,
          }));
          const underScoreData = recursiveCamelToUnderscore(fullCampaignData);

          const [err, res] = await to(tiktokService.createCampaign(underScoreData));
          if (err || !res) {
            return;
          }

          toast.success('操作成功');
          emitter.emit('REFRESH_CAMPAIGN_LIST', {
            // 传递需要的参数
          });

          set({
            campaignModal: {
              type: 'create',
              show: false,
              title: '',
              formValue: null,
            },
          });
        } catch (error) {
          toast.error('操作失败');
          throw error;
        }
      }),

      updateCampaignStatus: async (params) => {
        try {
          const underScoreData = recursiveCamelToUnderscore(params);
          await tiktokService.updateOperationStatus(underScoreData);

          emitter.emit('REFRESH_CAMPAIGN_LIST', {
            // 传递需要的参数
          });

          if (params.operationStatus === 'DELETE') {
            toast.success('删除成功');
          } else {
            toast.success('状态更新成功');
          }
        } catch (error) {
          console.error('更新广告系列状态失败:', error);
          toast.error('状态更新失败');
          throw error;
        }
      },
    },
  };
};
