import { VideoIcon } from '@/components/icon';
import { cn } from '@/utils/cn';
import { Link as LinkIcon, Loader2, XIcon } from 'lucide-react';

interface LinkInputProps {
  value: string;
  disabled?: boolean;
  loading?: boolean;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  className?: string;
}
export const LinkInput = ({ value, onChange, onSubmit, loading, disabled, className }: LinkInputProps) => {
  return (
    <div className={className}>
      <div className={cn('flex w-full flex-col')}>
        <div className="relative">
          <LinkIcon className="absolute left-6 top-1/2 h-8 w-8 flex-shrink-0 -translate-y-1/2 text-white" />
          <div className="rainbow-border overflow-hidden rounded-2xl border">
            <div className="rainbow-border-content bg">
              <input
                type="text"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && isValidLink(value) && !disabled) {
                    onSubmit(value);
                  }
                }}
                placeholder="仅支持 TikTok 美区商品详情链接"
                className={`h-[64px] w-full shrink-0 px-4 py-2 pl-[70px] pr-[172px] font-[PingFang-SC] text-base font-normal leading-normal text-white placeholder:text-[#9FA4B2] focus:outline-none`}
                style={{
                  background:
                    'linear-gradient(70deg, rgba(84,255,224,0.04) 17.35%, rgba(0,225,255,0.04) 33%, rgba(157,129,255,0.04) 75.26%)',
                }}
              />
              {value && (
                <XIcon
                  className="absolute right-36 top-[25%] mx-2 h-8 cursor-pointer text-[#9FA4B2]"
                  onClick={(event) => {
                    event.stopPropagation();
                    onChange('');
                  }}
                />
              )}
            </div>
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2">
            <button
              onClick={() => onSubmit(value)}
              // disabled={!isValidLink || (!!analysisResult?.template_info && !!analysisResult?.suggestion)}
              disabled={!isValidLink(value) || loading || disabled}
              className="flex h-[48px] w-[130px] flex-shrink-0 items-center justify-center gap-2 rounded-xl bg-[linear-gradient(90.59deg,_#9BF7FE_0%,_#DADAFF_57.5%,_#F6F6F9_96.5%)] font-[PingFang-SC] text-base font-medium text-[#050A1C] transition-colors hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="font-[PingFang-SC] text-sm font-medium">分析中</span>
                </>
              ) : (
                <>
                  <VideoIcon />
                  <span className="font-[PingFang-SC] text-sm font-medium">分析内容</span>
                </>
              )}
            </button>
          </div>
        </div>
        {value && !isValidLink(value) && (
          <p className="text-sm text-red-500">请输入以 http:// 或 https:// 开头的有效链接</p>
        )}
      </div>
    </div>
  );
};

const isValidLink = (link: string) => {
  if (!link.trim()) return false;
  // 验证是否以 http:// 或 https:// 开头
  const urlPattern = /^https?:\/\/.+/i;
  return urlPattern.test(link.trim());
};
