'use strict';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';
import { EventPayload, Handler } from '..';
import { cos } from '../utils/cos';
import { prisma } from '../utils/prisma';

/**
 * 1. 当生成的素材被 node-scf-sync-task 接收到后，若需要自动分发的，会调用VOD转推COS的服务。当COS接收到文件后，触发该回调
 * 2. 剪辑同学手动的添加新的文件，也需要自动创建待分发任务
 * 处理单个的cos文件
 * @param record
 */
export class UploadFromVodForWaitDistributeHandler implements Handler {
  async handle(record: EventPayload['Records'][0]) {
    console.log('开始处理文件【自动分发-生成素材】', record.cos.cosObject.key);
    console.log('----------------------------');

    // 获取文件相对于bucket下的key
    const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

    const keyArr = key.split('/');
    if (keyArr.length !== 9) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }

    const [, tenantId, , , ipName, productName, liveRoom, liveSession, fileName] = keyArr;
    if (!tenantId || !ipName || !productName || !liveSession || !liveRoom || !fileName) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }

    console.log(
      '接收到待分发的视频',
      JSON.stringify({ tenantId, ipName, productName, liveSession, liveRoom, fileName }),
    );

    await this.handleDistributionTask(
      `${record.cos.cosBucket.name}-${record.cos.cosBucket.appid}`,
      tenantId,
      ipName,
      productName,
      liveRoom,
      liveSession,
      fileName,
    );
  }

  private async handleDistributionTask(
    bucketName: string,
    tenantId: string,
    ipName: string,
    productName: string,
    liveRoom: string,
    liveSession: string,
    fileName: string,
  ) {
    if (!fileName.startsWith('[成片]') && !fileName.startsWith('[出片]')) {
      console.log('文件名格式不正确，文件名需要以 [成片] 或 [出片] 开头，不处理', fileName);
      return;
    }

    const cos_prefix = `roasmax/${tenantId}/自动分发/生成素材/${ipName}/${productName}/${liveRoom}/${liveSession}/`;
    // 当前推送的文件路径
    const path = `${cos_prefix}${fileName}`;
    // 它可能对应的原始文件路径(如果推送的文件是成片的话)
    const origin_path = `${cos_prefix}${fileName.replace('[成片]', '[出片]')}`;

    const sub_tasks = await prisma.video_distribution_sub_tasks.findMany({
      where: { wait_cos_path: { in: [path, origin_path] }, tmp_deleted_at: null },
    });

    const status = fileName.startsWith('[成片]')
      ? VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已成片
      : VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已出片;

    if (sub_tasks.length !== 0) {
      console.log(`已存在待分发的子任务，更新状态为【${status}】`);
      await prisma.video_distribution_sub_tasks.updateMany({
        where: { id: { in: sub_tasks.map((item) => item.id) } },
        data: { status, wait_cos_path: path },
      });
      if (path !== origin_path) {
        // 只有当wait_cos_path和origin_file_cos_path不一致时，说明出现了成片替代出片的情况，才删除origin_file_cos_path
        // 更新完数据库后，删除origin_file_cos_path，确保原任务不会被处理
        // 正常情况下，它会触发删除事件，然后删除对应的子任务。但是由于子任务已经更新了wait_cos_path，所以不会匹配到
        await cos.deleteObject({
          Bucket: bucketName,
          Region: process.env.COS_REGION!,
          Key: origin_path,
        });
      }
    } else {
      console.log('不存在待分发的子任务，属于手动上传，自动创建任务');
      await prisma.video_distribution_sub_tasks.create({
        data: {
          tenant_id: tenantId,
          wait_cos_path: path,
          status,
          ip: ipName,
          goods_name: productName,
          live_room: liveRoom,
          live_session: liveSession,
        },
      });
    }
  }
}
