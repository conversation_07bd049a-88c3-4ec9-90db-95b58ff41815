'use client';

import React, { useImperativeHandle, useMemo, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Pencil } from 'lucide-react';
import ProTable, { ProTableColumnType } from '@/components/pro/pro-table';
import { action, ActionResult } from '@/utils/server-action/action';
import { createDataImportTemplate, listDataImportTemplates, updateDataImportTemplate } from '@/services/actions/config';
import { ProFormField } from '@/components/pro/pro-form';
import { Form, Input, Textarea } from '@/components/ui';
import { useForm } from 'react-hook-form';

export interface DataImportTemplateDialogRef {
  show: (params: { templates: ActionResult<typeof listDataImportTemplates>; resource_name: string }) => void;
}

interface DataImportTemplateDialogProps {
  onRefreshNeeded?: () => void;
}

export const DataImportTemplateDialog = React.forwardRef<DataImportTemplateDialogRef, DataImportTemplateDialogProps>(
  (props, ref) => {
    const [open, setOpen] = useState(false);
    const [resourceName, setResourceName] = useState('');
    const [templates, setTemplates] = useState<ActionResult<typeof listDataImportTemplates>>([]);
    const [loading, setLoading] = useState(false);

    const [formDialogType, setFormDialogType] = useState<'add' | 'edit' | undefined>();
    const form = useForm();

    const columns = useMemo<ProTableColumnType[]>(() => {
      return [
        { title: '模板名称', dataIndex: 'name' },
        { title: '描述', dataIndex: 'description' },
        { title: '创建时间', dataIndex: 'tmp_created_at', type: 'datetime' },
        {
          title: '操作',
          dataIndex: 'action',
          render: (_, record) => (
            <Button
              variant="link"
              onClick={() => {
                setFormDialogType('edit');
                form.reset({ ...record, content: JSON.stringify(record.content, null, 2) });
              }}
              className="gap-1"
            >
              <Pencil className="h-4 w-4" />
              编辑
            </Button>
          ),
        },
      ];
    }, [form]);

    const onSubmit = async (data: any) => {
      setLoading(true);
      if (formDialogType === 'add') {
        const content = JSON.parse(data.content);
        const res = await action(createDataImportTemplate, { ...data, content, resource_name: resourceName });
        if (res) {
          setFormDialogType(undefined);
          props.onRefreshNeeded?.();
        }
      } else if (formDialogType === 'edit') {
        const content = JSON.parse(data.content);
        const res = await action(updateDataImportTemplate, { ...data, content });
        if (res) {
          setFormDialogType(undefined);
          props.onRefreshNeeded?.();
        }
      }
      setLoading(false);
    };

    useImperativeHandle(ref, () => ({
      show: (params) => {
        setTemplates(params.templates);
        setResourceName(params.resource_name);
        setOpen(true);
      },
    }));

    return (
      <Dialog
        open={open}
        modal
        onOpenChange={(open) => {
          setOpen(open);
          if (!open) {
            form.reset({ name: '', description: '', content: '' });
            setFormDialogType(undefined);
            setLoading(false);
            setResourceName('');
          }
        }}
      >
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>导入模板</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-between">
            <div></div>
            <Button
              onClick={() => setFormDialogType('add')}
              className="h-[32px] w-[92px] gap-1 text-[#050A1C] hover:text-[#050A1C]"
            >
              新增模板
            </Button>
          </div>
          <ProTable columns={columns} dataSource={templates} className="min-h-[200px]" />
        </DialogContent>
        <Dialog
          open={!!formDialogType}
          onOpenChange={(open) => {
            setFormDialogType(open ? formDialogType : undefined);
            if (!open) {
              form.reset({ name: '', description: '', content: '' });
            }
          }}
        >
          <DialogContent className="max-w-[900px]">
            <DialogHeader>
              <DialogTitle>{formDialogType === 'add' ? '新增模板' : '编辑模板'}</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <ProFormField
                  form={form}
                  name="name"
                  label="模板名称"
                  renderFormControl={(field) => (
                    <Input value={field.value} onChange={(e) => field.onChange(e.target.value)} />
                  )}
                />
                <ProFormField
                  form={form}
                  name="description"
                  label="描述"
                  renderFormControl={(field) => (
                    <Input value={field.value} onChange={(e) => field.onChange(e.target.value)} />
                  )}
                />
                <ProFormField
                  form={form}
                  name="content"
                  label="模板内容"
                  renderFormControl={(field) => (
                    <Textarea
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                      className="h-[400px] font-mono"
                    />
                  )}
                />
                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="h-[32px] w-[92px] gap-1 text-[#050A1C] hover:text-[#050A1C]"
                  >
                    {loading ? '保存中...' : '保存'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </Dialog>
    );
  },
);

DataImportTemplateDialog.displayName = 'DataImportTemplateDialog';
