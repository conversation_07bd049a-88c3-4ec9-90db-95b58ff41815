import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui';
import { ArrowFullfil } from '@/components/icon/ArrowFullfil';
import { cn } from '@/utils/cn';
import { useState } from 'react';
import { Close } from '../icon/close';
import { SelectProps } from '@radix-ui/react-select';

export function ProSelect({
  clearable,
  placeholder,
  options,
  className,
  onOpenChange,
  ...props
}: Omit<Parameters<typeof Select>[0], 'children'> & {
  clearable?: boolean;
  placeholder?: React.ReactNode;
  options: { value: string; label: React.ReactNode; hidden?: boolean }[];
  className?: string;
} & SelectProps) {
  const [open, setOpen] = useState(false);
  return (
    <Select
      {...props}
      onOpenChange={(e) => {
        setOpen(e);
        onOpenChange?.(e);
      }}
    >
      <SelectTrigger
        className={cn(
          'rounded-[8px] font-normal shadow-none outline-none focus:ring-offset-0',
          'hover:bg-[#CCDDFF33]',
          className,
        )}
        arrow={
          <div className="group flex h-[20px] w-[20px] items-center justify-center">
            <ArrowFullfil
              className={cn(
                'text-[#7E8495]',
                clearable && props.value ? 'group-hover:hidden' : '',
                open ? 'rotate-180' : '',
              )}
            />
            {clearable && props.value && (
              <div
                className="hidden group-hover:block"
                onClick={(e) => {
                  e.stopPropagation();
                  props.onValueChange?.('');
                }}
              >
                <Close className="h-[20px] w-[20px] text-[#7E8495]" />
              </div>
            )}
          </div>
        }
      >
        <SelectValue placeholder={<span className="text-[#7E8495]">{placeholder}</span>} />
      </SelectTrigger>
      <SelectContent className="max-h-[40vh]">
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value} hidden={option.hidden}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
