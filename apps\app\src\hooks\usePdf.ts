import { useState } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import toast from 'react-hot-toast';

interface ExportToPDFOptions {
  elementId: string;
  fileName?: string;
  logoPath?: string;
  scale?: number;
  backgroundColor?: string;
  padding?: string;
  includeLink?: boolean; // 新增参数，用于控制是否包含链接输入框
  link?: string; // 新增参数，用于传递链接值
}

export const useExportToPDF = () => {
  const [pdfLoading, setPdfLoading] = useState(false);

  const exportToPDF = async ({
    elementId,
    fileName = 'RoasMax.pdf',
    logoPath = '/logo.png',
    scale = 2,
    backgroundColor = '#070F1F',
    padding = '30px',
    includeLink = false, // 默认不包含链接
    link = '', // 默认链接为空字符串
  }: ExportToPDFOptions) => {
    try {
      setPdfLoading(true);
      const element = document.getElementById(elementId);
      if (!element) {
        toast.error('未找到要分享的内容');
        return;
      }

      // 加载 logo
      const logoImg = new Image();
      logoImg.src = logoPath;
      await new Promise((resolve) => {
        logoImg.onload = resolve;
      });

      const canvas = await html2canvas(element, {
        scale: scale,
        useCORS: true,
        logging: true,
        backgroundColor: backgroundColor,
        onclone: (documentClone) => {
          const clonedElement = documentClone.getElementById(elementId);
          if (clonedElement) {
            // 确保克隆元素有足够的内边距
            (clonedElement as HTMLElement).style.padding = `0 ${padding} ${padding} ${padding}`;
            // 处理背景色
            const elementsWithBg = clonedElement.querySelectorAll('[class*="bg-"]');
            elementsWithBg.forEach((el) => {
              const computedStyle = window.getComputedStyle(el);
              (el as HTMLElement).style.backgroundColor = computedStyle.backgroundColor;
              (el as HTMLElement).style.backgroundImage = computedStyle.backgroundImage;
            });

            // 修复模板名称截断问题
            const templateElements = clonedElement.querySelectorAll('.template-item');
            templateElements.forEach((el) => {
              // 确保文本元素完全显示
              const textElements = el.querySelectorAll('p, span, div');
              textElements.forEach((textEl) => {
                (textEl as HTMLElement).style.overflow = 'visible';
                (textEl as HTMLElement).style.whiteSpace = 'normal';
                (textEl as HTMLElement).style.lineHeight = '1.5';
                (textEl as HTMLElement).style.textOverflow = 'initial';
                (textEl as HTMLElement).style.maxWidth = 'none';
                (textEl as HTMLElement).style.width = 'auto';
              });

              // 增加模板卡片的高度以确保内容完全显示
              (el as HTMLElement).style.height = 'auto';
              (el as HTMLElement).style.minHeight = '100%';
              (el as HTMLElement).style.display = 'flex';
              (el as HTMLElement).style.flexDirection = 'column';
              (el as HTMLElement).style.padding = '8px';
              (el as HTMLElement).style.boxSizing = 'border-box';
            });

            // 特别处理商品名称和作者昵称，防止重叠
            const productNames = clonedElement.querySelectorAll('.product-name');
            const authorNames = clonedElement.querySelectorAll('.author-name');
            const authoricon1 = clonedElement.querySelectorAll('.icon1');

            // 处理商品名称
            if (productNames.length > 0) {
              productNames.forEach((nameEl) => {
                (nameEl as HTMLElement).style.margin = '8px 0 4px 2px'; // 增加间距以防止重叠
                (nameEl as HTMLElement).style.padding = '0';
                (nameEl as HTMLElement).style.overflow = 'visible';
                (nameEl as HTMLElement).style.whiteSpace = 'normal';
                (nameEl as HTMLElement).style.textOverflow = 'initial';
                (nameEl as HTMLElement).style.display = 'block';
                (nameEl as HTMLElement).style.position = 'relative';
              });
            }

            // 处理作者昵称
            if (authorNames.length > 0) {
              authorNames.forEach((nameEl) => {
                (nameEl as HTMLElement).style.color = '#cccccc';
                (nameEl as HTMLElement).style.margin = '4px 0 8px 2px';
                (nameEl as HTMLElement).style.padding = '0';
                (nameEl as HTMLElement).style.overflow = 'visible';
                (nameEl as HTMLElement).style.whiteSpace = 'normal';
                (nameEl as HTMLElement).style.textOverflow = 'initial';
                (nameEl as HTMLElement).style.display = 'block';
                (nameEl as HTMLElement).style.position = 'relative';
              });
            }

            if (authoricon1.length > 0) {
              authoricon1.forEach((nameEl) => {
                (nameEl as HTMLElement).style.marginBottom = '14px';
              });
            }
            const analysisContainersanalysis1 = clonedElement.querySelectorAll('.analysis1');
            analysisContainersanalysis1.forEach((container) => {
              (container as HTMLElement).style.marginTop = '8px';
              (container as HTMLElement).style.marginRight = '6px';
            });

            // 特别处理分析报告部分，确保内容完全显示
            const analysisContainers = clonedElement.querySelectorAll('.product-analysis, .content-analysis');
            analysisContainers.forEach((container) => {
              (container as HTMLElement).style.height = 'auto';
              (container as HTMLElement).style.overflow = 'visible';
              (container as HTMLElement).style.pageBreakInside = 'avoid';

              // 处理内部的markdown内容
              const markdownElements = container.querySelectorAll('.markdown');
              markdownElements.forEach((mdEl) => {
                (mdEl as HTMLElement).style.overflow = 'visible';
                (mdEl as HTMLElement).style.maxHeight = 'none';
                (mdEl as HTMLElement).style.height = 'auto';
                (mdEl as HTMLElement).style.whiteSpace = 'normal';
                (mdEl as HTMLElement).style.wordBreak = 'break-word';

                // 处理markdown内部的所有文本元素
                const mdTextElements = mdEl.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre, code');
                mdTextElements.forEach((textEl) => {
                  (textEl as HTMLElement).style.overflow = 'visible';
                  (textEl as HTMLElement).style.textOverflow = 'initial';
                  (textEl as HTMLElement).style.whiteSpace = 'normal';
                  (textEl as HTMLElement).style.wordBreak = 'break-word';
                  (textEl as HTMLElement).style.pageBreakInside = 'avoid';
                  (textEl as HTMLElement).style.marginBottom = '10px';
                });
                const lastElement = mdTextElements[mdTextElements.length - 1];
                if (lastElement) {
                  (lastElement as HTMLElement).style.marginBottom = '30px';
                }
              });
            });
          }
        },
      });

      // 调整 PDF 尺寸计算
      const pdfWidth = 210; // A4 宽度（mm）
      const contentRatio = canvas.height / canvas.width;
      const pdfHeight = Math.max(297, pdfWidth * contentRatio + 15); // 确保至少是 A4 高度
      // 创建 PDF，使用计算后的尺寸
      const pdf = new jsPDF('p', 'mm', [pdfWidth, pdfHeight]);

      // 设置背景色
      pdf.setFillColor(7, 15, 31);
      pdf.rect(0, 0, pdfWidth, pdfHeight, 'F');

      // 计算内容的缩放和位置，确保完整显示
      const contentWidth = pdfWidth;
      const bottomPadding = 20 * 0.264583; // 将像素转换为毫米（1px ≈ 0.264583mm）
      const contentHeight = (canvas.height * contentWidth) / canvas.width;
      let contentStartY = 8;

      // 创建一个临时的 div 来包含链接信息
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '100%'; // 设置为A4纸张宽度，确保内容完整显示，并确保内容完整显示在A4纸张上
      tempDiv.style.padding = '15px';
      tempDiv.style.backgroundColor = backgroundColor;
      tempDiv.style.color = '#ffffff';
      tempDiv.style.fontFamily = 'Arial, sans-serif';
      // 创建更美观的链接显示结构
      tempDiv.innerHTML = `
        <div style="padding: 4px; display: flex; align-items: center;">
          <div style="color: #9FA4B2; margin-right: 10px; margin-bottom: 6px; font-weight: bold; font-size:  24px;">商品链接:</div>
          <div style="color: white; word-break: break-all; flex: 1; font-size: 18px; letter-spacing: 1px;">${link}</div>
        </div>
      `;

      // 将临时 div 添加到 body 中
      document.body.appendChild(tempDiv);
      // 确保链接存在
      const linkInputCanvas = await html2canvas(tempDiv, {
        scale: scale,
        useCORS: true,
        logging: true,
        backgroundColor: backgroundColor,
      });
      const linkCanvasRatio = linkInputCanvas.height / linkInputCanvas.width;
      const linkWidth = contentWidth - 6; // 宽度
      // 调整链接输入框的位置和大小
      const linkHeight = linkWidth * linkCanvasRatio;
      pdf.addImage(
        linkInputCanvas.toDataURL('image/png'),
        'PNG',
        3, // x位置
        contentStartY, // y位置
        contentWidth - 6, // 宽度
        linkHeight, // 高度
      );
      contentStartY += linkHeight + 2; // 增加间距
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, contentStartY, contentWidth, contentHeight - bottomPadding);
      pdf.addImage(logoImg, 'PNG', 3, 2, 30, 6);
      // 添加水印
      const watermarkText = 'RoasMax';
      const watermarkFontSize = 60;
      const watermarkAngle = -45; // 水印旋转角度

      pdf.saveGraphicsState();
      pdf.setGState(new (pdf.GState as any)({ opacity: 0.2 }));
      // 设置水印文字样式
      pdf.setFontSize(watermarkFontSize);
      pdf.setTextColor(150, 150, 150); // 灰色水印
      // 计算页面中心位置
      const centerX = pdfWidth / 2;

      // 上半部分水印位置
      const topCenterY = pdfHeight / 4;
      pdf.text(watermarkText, centerX, topCenterY, {
        align: 'center',
        angle: watermarkAngle,
      });

      // 下半部分水印位置
      const bottomCenterY = (pdfHeight * 3) / 4 - bottomPadding / 2;
      pdf.text(watermarkText, centerX, bottomCenterY, {
        align: 'center',
        angle: watermarkAngle,
      });
      pdf.restoreGraphicsState();
      pdf.save(fileName);
      toast.success('分享文件已生成');
      setPdfLoading(false);
    } catch (error) {
      toast.error('分享文件生成失败');
      setPdfLoading(false);
    } finally {
      // 移除临时 div
      const tempDiv = document.querySelector('.link-input-container');
      if (tempDiv) {
        tempDiv.remove();
      }
    }
  };

  return { exportToPDF, pdfLoading };
};
