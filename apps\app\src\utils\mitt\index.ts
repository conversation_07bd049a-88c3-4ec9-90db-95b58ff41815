import mitt, { EventType } from './mitt';

export interface Events extends Record<EventType, unknown> {
  CLEAR_PRO_SEARCH: void;
  CLEAR_PRO_CALENDAR: void;
  REFRESH_CAMPAIGN_LIST: {
    page?: number;
    pageSize?: number;
    total?: number;
    advertiserId?: string;
    campaignName?: string;
    operationStatus?: string;
  };
  REFRESH_AD_LIST: {
    page?: number;
    pageSize?: number;
    total?: number;
    advertiserIds?: string;
    groupIds?: string;
  };
  REFRESH_PRODUCT_LIST: {
    page?: number;
    pageSize?: number;
  };
}

const emitter = mitt<Events>();

export default emitter;
