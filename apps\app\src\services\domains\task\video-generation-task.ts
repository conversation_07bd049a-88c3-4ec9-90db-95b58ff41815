import { TaskStatus } from '@/types/task';
import { WalletChangeType } from '@/types/wallet';
import { ActionContext } from '@roasmax/serve';
import { calcCostQuota } from '@roasmax/utils';
import { updateQuota } from '../pricing_plans';
import { userTotalStore } from '../vod';
import { createVideoSliceTask, emitVideoSliceTask } from './video-slice-task';

/**
 * 创建并触发视频生成任务
 * @param ctx
 * @returns
 */
export const createAndEmitVideoGenerationTask = async (
  ctx: ActionContext<Parameters<typeof createVideoGenerationTask>[0]['data']>,
) => {
  return await ctx.trx(async (ctx) => {
    const task = await ctx.execute(createVideoGenerationTask, ctx.data);
    await ctx.execute(emitVideoGenerationTask, { taskId: task.id });
    const result = await ctx.db.video_generation_tasks.findUnique({ where: { id: task.id } });
    return result;
  });
};

/**
 * 创建视频生成任务
 */
export const createVideoGenerationTask = async (
  ctx: ActionContext<{
    name: string;
    sliceType: '300' | '1800';
    language?: string;
    generateCount: number;
    accelerate: string;
    subtitles: boolean;
    transition_mode: string;
    materialIds: string[];
    method: 'normal' | 'gc_imitate';
    industry?: string;
    industryId?: string;
    // 仅 method === 'normal' 时有效
    prompts?: string[];
    // 仅 method === 'normal' 时有效
    dify?: boolean;
    // 仅 method === 'gc_imitate' 时有效
    kolStyle?: string;
    // 仅 method === 'gc_imitate' 时有效
    productUrl?: string;
    // 仅 method === 'gc_imitate' 时有效
    productAnalysis?: string;
    // 仅 method === 'gc_imitate' 时有效
    productTitle?: string;
    // 仅 method === 'gc_imitate' 时有效
    productMinPrice?: number;
    // 仅 method === 'gc_imitate' 时有效
    productMaxPrice?: number;
    // 仅 method === 'gc_imitate' 时有效
    productCurrency?: string;
    // 仅 method === 'gc_imitate' 时有效
    generationType?: '大V严选' | 'KOL转移' | '大卖推荐';
    // 仅 method === 'gc_imitate' 时有效
    templateVideoVodId?: string;
    // 仅 method === 'gc_imitate' 时有效
    templateVideoTiktokIds?: string[];
    // 仅 method === 'gc_imitate' 时有效
    templateVideoLanguage?: string;
    // 仅 method === 'gc_imitate' 时有效
    targetLanguage?: string;
    // 仅 method === 'gc_imitate' 时有效
    customPrompt?: string;
    // 仅 method === 'gc_imitate' 时有效
    targetVoiceCloneType?: 'default' | 'template';
    // 仅 method === 'gc_imitate' & targetVoiceCloneType === 'template' 时有效
    targetVoiceTemplateId?: string;
    // 仅 method === 'gc_imitate' & targetVoiceCloneType === 'default' 时有效
    targetVoice?: string;
    // 仅 method === 'gc_imitate' 时有效
    targetVideoDuration?: number;
    // 仅 method === 'gc_imitate' 时有效
    sceneImplantation?: boolean;
    // 仅 method === 'gc_imitate' 时有效
    festiveAtmosphere?: boolean;
    // 生成来源 roasmax ADS 等
    generationSource?: string;
    // 生成来源的 ADS 批量创建批次ID, 广告id等
    generationSourceInfo?: string;
  }>,
) => {
  return await ctx.trx(async (ctx) => {
    const dify_workflow_key =
      ctx.tenant.config.enabled_sub_systems?.includes('dify') && ctx.data.method === 'normal' && ctx.data.dify
        ? process.env.DIFY_WORKFLOW_KEY
        : ctx.tenant.config.enabled_sub_systems?.includes('gc_imitate') &&
            ctx.data.method === 'gc_imitate' &&
            ctx.data.generationType !== '大卖推荐'
          ? process.env.DIFY_WORKFLOW_GC_KEY
          : ctx.tenant.config.enabled_sub_systems?.includes('gc_imitate') &&
              ctx.data.method === 'gc_imitate' &&
              ctx.data.generationType === '大卖推荐'
            ? 'app-112Yh66GZwe537VCK7wu3tMx'
            : null;
    // 创建任务
    const task = await ctx.db.video_generation_tasks.create({
      data: {
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        name: ctx.data.name,
        slice_duration: '300',
        video_language: ctx.data.language,
        method: ctx.data.method || 'normal',
        industry: ctx.data.industry,
        industry_id: ctx.data.industryId,
        kol_style: ctx.data.kolStyle,
        product_url: ctx.data.productUrl,
        prompts: ctx.data.prompts || [],
        generate_round: ctx.data.generateCount,
        video_speed: ctx.data.accelerate,
        subtitle: ctx.data.subtitles ? 1 : 0,
        transition_mode: ctx.data.transition_mode,
        dify_workflow_key,
        generation_type: ctx.data.generationType,
        template_video_vod_id: ctx.data.templateVideoVodId,
        product_analysis: ctx.data.productAnalysis,
        template_video_tiktok_ids: ctx.data.templateVideoTiktokIds,
        template_video_language: ctx.data.templateVideoLanguage,
        target_language: ctx.data.targetLanguage,
        custom_prompt: ctx.data.customPrompt,
        target_voice_clone_type: ctx.data.targetVoiceCloneType,
        target_voice: ctx.data.targetVoice,
        target_video_duration: ctx.data.targetVideoDuration,
        scene_implantation: ctx.data.sceneImplantation,
        festive_atmosphere: ctx.data.festiveAtmosphere,
        ...(ctx?.data?.generationSource && {
          generation_source: ctx?.data?.generationSource,
          generation_source_info: ctx?.data?.generationSourceInfo,
        }),
      },
    });
    await ctx.execute(createVideoGenerationSubTasks, { taskId: task.id, materialIds: ctx.data.materialIds });
    return task;
  });
};

/**
 * 创建视频生成子任务
 * @returns
 */
export const createVideoGenerationSubTasks = async (ctx: ActionContext<{ taskId: string; materialIds: string[] }>) => {
  const task = await ctx.db.video_generation_tasks.findUnique({
    where: { id: ctx.data.taskId },
  });
  if (!task) {
    throw new Error('任务不存在');
  }

  // 获取素材信息
  const materials = await ctx.db.materials.findMany({
    where: { id: { in: ctx.data.materialIds } },
  });

  // 确保素材存在
  const unFoundFileIds = ctx.data.materialIds.filter((mId) => !materials.find((m) => m.id === mId));
  if (unFoundFileIds.length) {
    throw new Error('素材已被删除');
  }

  // 检测存储空间
  const stores = await ctx.execute(userTotalStore, undefined);
  if (stores.total <= stores.used) {
    throw new Error('当前云盘存储空间已满');
  }

  let costQuota = 0;
  // 计算成本并扣点
  costQuota = calcCostQuota({
    method: (task.method || 'normal') as 'normal' | 'gc_imitate',
    sliceDuration: task.slice_duration === '300' ? 300 : 1800,
    materialDurations: materials.map((m) => m.video_duration),
    generateRound: task.generate_round,
    prompts: task.prompts,
    ...(task.method === 'gc_imitate' &&
      task.generation_type === '大卖推荐' && {
        generationType: task.generation_type,
        templateCount: task?.template_video_tiktok_ids?.length ?? 0,
      }),
  });
  await ctx.execute(updateQuota, {
    quota: -costQuota,
    changeType: WalletChangeType.VIDEO,
    changeReason: `创建视频切片任务：${task.name}`,
  });

  // 如果对应的视频没有做切分，先做切分
  const noSLiceMaterials = materials.filter((m) => !m.split_materials?.length);
  for (const material of noSLiceMaterials) {
    const sliceTask = await ctx.execute(createVideoSliceTask, {
      name: material.name,
      materialId: material.id,
      slice_duration: 300,
    });
    await ctx.execute(emitVideoSliceTask, { taskId: sliceTask.id });
  }

  // 创建子任务
  // 任务调度会根据子任务的状态以及对应的数据来判断该如何处理
  // 若选择 slice_duration 为 300，则按照视频长度，每5min一个任务，否则只有一个任务
  await ctx.db.video_generation_sub_tasks.createMany({
    data: materials.map((m) => {
      return {
        tenant_id: ctx.tenant.id,
        user_id: ctx.user.id,
        task_id: task.id,
        sub_task_type: 'slice',
        origin_material_id: m.id,
        generated_material_ids: [],
        status: TaskStatus.PENDING,
        status_desc: JSON.stringify({
          completed: 0,
          expected: task.generate_round * (task.prompts as string[]).length,
          stage: '',
        }),
      };
    }),
  });

  return task;
};

/**
 * 任务触发器
 * @param ctx
 * @returns
 */
export const emitVideoGenerationTask = async (ctx: ActionContext<{ taskId: string }>) => {
  const task = await ctx.db.video_generation_tasks.findUnique({
    where: { id: ctx.data.taskId },
  });
  if (!task) {
    await ctx.feishuRobot.error('任务不存在', [
      '触发【视频生成】子任务',
      `未找到对应的任务，任务ID: ${ctx.data.taskId}`,
    ]);
    throw new Error('任务不存在');
  }

  // 获取挂起的预切片子任务
  const subTasks = await ctx.db.video_generation_sub_tasks.findMany({
    where: { task_id: task.id, sub_task_type: 'slice', status: TaskStatus.PENDING },
    orderBy: { tmp_created_at: 'asc' },
  });

  // 获取预切片子任务中的原始素材
  const originMaterials = await ctx.db.materials.findMany({
    where: { id: { in: subTasks.map((t) => t.origin_material_id!) } },
  });

  for (const subSliceTask of subTasks) {
    const originMaterial = originMaterials.find((m) => m.id === subSliceTask.origin_material_id);
    if (!originMaterial) {
      await ctx.feishuRobot.error('未匹配到原始素材', [
        '触发【视频生成】子任务',
        `任务ID: ${subSliceTask.id}`,
        `原始素材ID: ${subSliceTask.origin_material_id}`,
      ]);
      return;
    }

    if (!(originMaterial.split_materials as any[] | undefined)?.length) {
      // 如果没有切分好的素材，继续等待
      continue;
    }

    // 每一个切分好的素材都是一个trace
    for (const split of originMaterial.split_materials as { video: string; audio: string }[]) {
      // 创建生成子任务
      await ctx.db.video_generation_sub_tasks.create({
        data: {
          tenant_id: ctx.tenant.id,
          user_id: ctx.user.id,
          task_id: task.id,
          sub_task_type: 'generation',
          origin_material_id: originMaterial.id,
          slice_vod_file_id: split.video,
          slice_vod_audio_file_id: split.audio,
          generated_material_ids: [],
          status: TaskStatus.PENDING,
          status_desc: JSON.stringify({
            completed: 0,
            expected: task.generate_round * (task.prompts as string[]).length,
            stage: '',
          }),
        },
      });
    }

    // 最后完成掉预切分任务
    await ctx.db.video_generation_sub_tasks.update({
      where: { id: subSliceTask.id },
      data: { status: TaskStatus.SUCCESS },
    });
  }
};
