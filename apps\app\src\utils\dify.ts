import { Logger } from '@roasmax/utils';

export interface DifyConfig {
  apiUrl: string;
  apiKey: string;
  inputs: Record<string, any>;
  responseMode: 'blocking' | 'streaming';
  query?: string;
  conversationId?: string;
  user?: string;
  onMessage?: (chunk: string) => void;
}

export interface DifyStreamResponse {
  stream: ReadableStream;
  headers: HeadersInit;
}

export interface DifyBlockResponse {
  id: string;
  workflow_id: string;
  status: 'succeeded' | 'failed' | 'pending';
  outputs: {
    result: string;
  };
  error: null | string;
  elapsed_time: number;
  total_tokens: number;
  total_steps: number;
  created_at: number;
  finished_at: number;
}

export class DifyService {
  private logger: Logger;

  constructor(requestId: string = 'system', requestIp: string = 'unknown') {
    this.logger = new Logger('dify-service', requestIp, requestId);
  }

  async request(config: DifyConfig): Promise<DifyBlockResponse> {
    if (!config.apiUrl || !config.apiKey) {
      throw new Error('API URL or API Key is not set');
    }

    const requestBody = {
      inputs: config.inputs,
      query: config.query,
      response_mode: config.responseMode,
      conversation_id: config.conversationId || '',
      user: config.user || 'system',
    };

    try {
      const response = await fetch(config.apiUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        this.logger.error('Dify API response error:', response.status);
        throw new Error(`Dify API error: ${response.status}`);
      }

      if (config.responseMode === 'streaming') {
        if (!response.body) {
          throw new Error('No response body');
        }

        const reader = response.body.getReader();
        this.processStream(reader, config.onMessage);
        return {} as DifyBlockResponse;
      } else {
        return (await response.json()) as DifyBlockResponse;
      }
    } catch (error) {
      this.logger.error('Error in Dify request:', error);
      throw error;
    }
  }

  private async processStream(reader: ReadableStreamDefaultReader<Uint8Array>, onMessage?: (chunk: string) => void) {
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        onMessage?.(chunk);
      }
    } catch (error) {
      this.logger.error('Stream processing error:', error);
      throw error;
    }
  }
}
