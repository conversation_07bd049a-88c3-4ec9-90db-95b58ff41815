'use client';

import { ProFilter } from '@/components/pro/pro-filter';
import { ProPagination } from '@/components/pro/pro-pagination';
import ProTable from '@/components/pro/pro-table';
import { TaskPreviewModal, TaskPreviewModalRef } from '@/components/TaskPreviewModal';
import { Button, Panel } from '@/components/ui';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { ActionParams, useAction } from '@/utils/server-action/action';
import { useRef, useState } from 'react';
import { filterColumns, useColumns } from './config';
import { useRouter } from 'next/navigation';

export default function Distributions() {
  const router = useRouter();
  const taskPreviewModal = useRef<TaskPreviewModalRef>(null);
  const [filters, setFilters] = useState<NonNullable<ActionParams<typeof pageVideoGenerationTasks>['filters']>>({});
  const [pagination, setPagination] = useState<{ page: number; pageSize: number }>({ page: 1, pageSize: 100 });

  const { data: dataSource, loading } = useAction(pageVideoGenerationTasks, {
    pagination: { page: pagination.page, limit: pagination.pageSize },
    filters: filters,
  });

  const columns = useColumns({
    onShowTaskPreview: (id) => taskPreviewModal.current?.show([id]),
  });

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter
          value={filters}
          onSubmit={(f) => {
            setPagination({ page: 1, pageSize: 100 });
            setFilters(f);
          }}
          columns={filterColumns}
          className="mb-2"
        />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="text-base font-bold">视频生成任务</div>
          <div className="flex items-center justify-end gap-2">
            <Button variant="link" className="h-[32px]" onClick={() => router.push('/distributions/cut')}>
              切片任务
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable columns={columns} loading={loading} dataSource={dataSource?.list || []} />
        </div>
        <div className="mt-1">
          <ProPagination
            pagination={{ ...pagination, total: dataSource?.pagination.total }}
            onPaginationChange={setPagination}
          />
        </div>
      </Panel>
      <TaskPreviewModal ref={taskPreviewModal} />
    </div>
  );
}
