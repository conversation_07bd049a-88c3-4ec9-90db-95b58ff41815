import { ArrowFullfil } from '@/components/icon/ArrowFullfil';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui';
import { cn } from '@/utils/cn';

interface Option {
  value: string;
  label: string;
  [key: string]: any;
}

interface CustomSelectProps {
  value: string;
  options: Option[];
  placeholder?: string;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onValueChange: (value: string) => void;
}

const CustomSelect = ({
  value,
  options,
  placeholder,
  className,
  open,
  onOpenChange,
  onValueChange,
}: CustomSelectProps) => {
  return (
    <div className={className}>
      <Select value={value} onOpenChange={onOpenChange} onValueChange={onValueChange}>
        <SelectTrigger
          className={cn(
            'h-10 w-full rounded-lg border transition-all duration-200',
            'border-[#242938] bg-[#1F2434] text-sm font-medium text-white',
            'hover:border-[#00E1FF]/30 hover:bg-[#273048]',
            'focus:border-[#00E1FF] focus:outline-none focus:ring-2 focus:ring-[#00E1FF]/20',
            'data-[state=open]:border-[#00E1FF] data-[state=open]:ring-2 data-[state=open]:ring-[#00E1FF]/20',
          )}
          arrow={
            <ArrowFullfil
              className={cn(
                'text-[#7E8495] transition-transform duration-200',
                open ? 'rotate-180 text-[#00E1FF]' : '',
              )}
            />
          }
        >
          <SelectValue placeholder={placeholder} className="font-medium text-white placeholder:text-[#7E8495]" />
        </SelectTrigger>
        <SelectContent className="rounded-lg border-[#242938] bg-[#1F2434]">
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className={cn(
                'cursor-pointer text-sm text-white transition-colors duration-150',
                'hover:bg-[#273048] hover:text-[#00E1FF]',
                'focus:bg-[#273048] focus:text-[#00E1FF]',
                'data-[state=checked]:bg-[#00E1FF]/10 data-[state=checked]:text-[#00E1FF]',
              )}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default CustomSelect;
