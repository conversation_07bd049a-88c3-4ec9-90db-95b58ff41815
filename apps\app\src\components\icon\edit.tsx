export const Edit = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M11.4591 12.1921H7.11503C6.81518 12.1921 6.57259 12.4573 6.57259 12.7852C6.57259 13.113 6.81518 13.3782 7.11503 13.3782H11.4576C11.7574 13.3782 12 13.113 12 12.7852C12 12.4573 11.7574 12.1921 11.4591 12.1921ZM11.4591 9.94826H9.7775C9.47765 9.94826 9.23506 10.2135 9.23506 10.5413C9.23506 10.8692 9.47765 11.1344 9.7775 11.1344H11.4576C11.7574 11.1344 12 10.8692 12 10.5413C12 10.2135 11.7574 9.94826 11.4591 9.94826ZM11.7906 3.87579L8.70618 0.501827C8.58112 0.365089 8.41387 0.289307 8.23758 0.289307C8.05978 0.289307 7.89403 0.365089 7.76897 0.501827L0.194398 8.78681C0.0693352 8.92355 -0.00148321 9.11136 2.35639e-05 9.30411L0.0211184 12.655C0.0241319 13.0487 0.31946 13.3716 0.67958 13.3749L3.74286 13.398H3.74738C3.92217 13.398 4.09243 13.3206 4.21599 13.1855L11.7891 4.90214C12.0482 4.61714 12.0482 4.1575 11.7906 3.87579ZM3.5741 12.2085L1.10298 12.1904L1.08641 9.48698L6.32849 3.75388L8.81618 6.4738L3.5741 12.2085ZM9.58313 5.6369L7.09544 2.91533L8.23758 1.66657L10.7253 4.3865L9.58313 5.6369Z"
        fill="#81889D"
      />
    </svg>
  );
};
