'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { toast } from 'react-hot-toast';

import { cn } from '@/utils/cn';
import { Button } from '@/components/ui/Button';
import { Calendar } from '@/components/ui/Calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';

export function DatePicker({
  onValueChange,
  disabled,
  defaultValue,
  test,
}: {
  onValueChange?: (date: string | undefined) => void;
  disabled?: boolean;
  defaultValue?: string;
  test?: boolean;
}) {
  const now = new Date();
  const [date, setDate] = React.useState<Date>();
  const [hours, setHours] = React.useState(now.getHours().toString().padStart(2, '0'));
  const [minutes, setMinutes] = React.useState(now.getMinutes().toString().padStart(2, '0'));
  const [seconds, setSeconds] = React.useState(now.getSeconds().toString().padStart(2, '0'));

  React.useEffect(() => {
    if (disabled && defaultValue) {
      const parsedDate = new Date(defaultValue);
      if (!isNaN(parsedDate.getTime())) {
        setDate(parsedDate);
        setHours(parsedDate.getHours().toString().padStart(2, '0'));
        setMinutes(parsedDate.getMinutes().toString().padStart(2, '0'));
        setSeconds(parsedDate.getSeconds().toString().padStart(2, '0'));
      }
    }
  }, [defaultValue, disabled]);
  const minDate = React.useMemo(() => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
  }, []);
  const handleTimeChange = (value: string, type: 'hours' | 'minutes' | 'seconds') => {
    if (date) {
      const newDate = new Date(date);
      if (type === 'hours') {
        newDate.setHours(parseInt(value));
        setHours(value);
      } else if (type === 'minutes') {
        newDate.setMinutes(parseInt(value));
        setMinutes(value);
      } else {
        newDate.setSeconds(parseInt(value));
        setSeconds(value);
      }
      console.log(newDate, minDate);

      if (newDate < minDate && !disabled) {
        toast.error('开始时间不能早于当前时间');
        return;
      }
      setDate(newDate);
      // 在时间改变时触发回调
      if (newDate) {
        onValueChange?.(format(newDate, 'yyyy/MM/dd HH:mm:ss'));
      }
    }
  };
  const handleOpenChange = (open: boolean) => {
    if (!open && date) {
      onValueChange?.(format(date, 'yyyy/MM/dd HH:mm:ss'));
    }
  };
  const handleDateSelect = (newDate: Date | undefined) => {
    if (newDate) {
      const dateWithTime = new Date(newDate);
      dateWithTime.setHours(parseInt(hours));
      dateWithTime.setMinutes(parseInt(minutes));
      dateWithTime.setSeconds(parseInt(seconds));

      // 检查是否小于当前时间
      if (dateWithTime < minDate) {
        toast.error('开始时间不能早于当前时间');
        return;
      }

      setDate(dateWithTime);
      onValueChange?.(format(dateWithTime, 'yyyy/MM/dd HH:mm:ss'));
    }
  };
  return (
    <Popover onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          disabled={disabled}
          className={cn(
            'justify-start border border-[#363D54] bg-transparent text-left font-normal',
            !date && 'text-[#9FA4B2]',
            test ? 'h-8 w-[300px] text-xs' : 'h-10 w-full rounded text-sm',
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, 'yyyy/MM/dd HH:mm:ss') : <span>请选择开始时间</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <div className="flex gap-2 border-t p-3">
          <Select value={hours} onValueChange={(value) => handleTimeChange(value, 'hours')}>
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder="HH" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0')).map((hour) => (
                <SelectItem key={hour} value={hour}>
                  {hour}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={minutes} onValueChange={(value) => handleTimeChange(value, 'minutes')}>
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder="MM" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((minute) => (
                <SelectItem key={minute} value={minute}>
                  {minute}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={seconds} onValueChange={(value) => handleTimeChange(value, 'seconds')}>
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder="SS" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')).map((second) => (
                <SelectItem key={second} value={second}>
                  {second}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Calendar mode="single" selected={date} onSelect={handleDateSelect} initialFocus />
      </PopoverContent>
    </Popover>
  );
}
