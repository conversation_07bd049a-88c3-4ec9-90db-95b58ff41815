import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { Button } from '@/components/ui';
import { pageVideoCutTasks } from '@/services/actions/video-cut-task';
import { ActionParams, ActionResult } from '@/utils/server-action/action';
import dayjs from 'dayjs';
import { useMemo } from 'react';

export type CutTaskType = ActionResult<typeof pageVideoCutTasks>['list'][0];

export const useColumns = (config: { onShowTaskPreview?: (id: string) => void }): ProTableColumnType<CutTaskType>[] => {
  const columns = useMemo(() => {
    const inbuilt: ProTableColumnType<CutTaskType>[] = [
      {
        dataIndex: 'name',
        title: '任务名称',
        render: (v, record) => (
          <div>
            <div className="mb-1">{v}</div>
            <div className="text-xs text-gray-500">ID: {record.id}</div>
          </div>
        ),
      },
      {
        dataIndex: 'tmp_created_at',
        title: '创建时间',
        render: (_, record) => <div>{dayjs(record.tmp_created_at).format('YYYY-MM-DD HH:mm:ss')}</div>,
      },
      {
        key: 'status_desc',
        title: '状态',
        render: (_, record) => <div>{record.status_desc}</div>,
      },
      {
        key: 'options',
        title: <div className="ml-4">操作</div>,
        fixed: 'right' as const,
        render: (_, record) => {
          return (
            <div>
              <Button variant="link" className="text-xs" onClick={() => config.onShowTaskPreview?.(record.id)}>
                明细
              </Button>
            </div>
          );
        },
      },
    ];

    return inbuilt;
  }, []);

  return columns;
};

export const filterColumns: ProFilterColumnType<ActionParams<typeof pageVideoCutTasks>['filters']>[] = [
  {
    key: 'name',
    title: '任务名称',
    type: 'text',
  },
];
