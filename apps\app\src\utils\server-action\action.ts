'use client';
import { ActionPayload, ServerActionWrapper } from '@roasmax/serve';
import { useCallback, useState, useEffect, useRef } from 'react';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';

export type ActionResult<T extends ServerActionWrapper<any, any>> = NonNullable<Awaited<ReturnType<T>>['data']>;
export type ActionParams<T extends ServerActionWrapper<any, any>> = NonNullable<NonNullable<Parameters<T>[0]>['data']>;

type IErrorType = 'ignore' | 'return' | 'throw';
type IActionOptions = { errorType: IErrorType };
type IUseActionOptions<P> = { skip?: (data: P) => boolean; manual?: boolean };

type IActionReturn<R, P, ActionOptions extends IActionOptions> = ActionOptions['errorType'] extends 'return'
  ? Awaited<ReturnType<ServerActionWrapper<R, P>>>
  : ActionOptions['errorType'] extends 'throw'
    ? R
    : R | undefined;

export async function action<R, P, ActionOptions extends IActionOptions>(
  action: ServerActionWrapper<R, P>,
  data: P,
  options?: ActionOptions,
): Promise<IActionReturn<R, P, ActionOptions>> {
  const errorType = options?.errorType || 'ignore';
  const payload: ActionPayload<P> = { authorization: `Bearer ${Cookies.get('Authorization')}`, data };
  const res = await action(payload);
  if (errorType === 'return') {
    return res as any;
  }

  if (res.success) {
    return res.data as any;
  }

  if (res.code === 401) {
    if (window.location.pathname !== '/login') {
      localStorage.removeItem('token');
      Cookies.remove('BWAI_ACCESS_TOKEN');
      Cookies.remove('Authorization');
      window.location.href = '/login';
    }
    if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
      toast.error(res.message);
    }
    return undefined as any;
  }

  if (errorType === 'throw') {
    throw new Error(res.message);
  }

  if (errorType === 'ignore') {
    toast.error(res.message);
    return undefined as any;
  }

  return undefined as any;
}

action.currying = function <R, P>(action: ServerActionWrapper<R, P>) {
  return (data: P) => {
    const payload: ActionPayload<P> = { authorization: `Bearer ${Cookies.get('Authorization')}`, data };
    return action(payload);
  };
};

const isServerAction = <R, P>(
  handler: ServerActionWrapper<R, P> | ((data: P) => Promise<R>),
): handler is ServerActionWrapper<R, P> => {
  return typeof handler === 'function' && 'bind' in handler;
};

export const useAction = <R, P, UseActionOptions extends IUseActionOptions<P>>(
  handler: ServerActionWrapper<R, P> | ((data: P) => Promise<R>),
  input: P,
  options?: UseActionOptions,
) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<R | undefined>(undefined);
  const prevDataRef = useRef<string | null>(null);
  const prevInputRef = useRef<string | null>(null);
  const inputRef = useRef(input);

  const handlerRef = useRef(handler);
  const optionsRef = useRef(options);

  useEffect(() => {
    handlerRef.current = handler;
    optionsRef.current = options;
  }, [handler, options]);

  const execute = useCallback(async (data: P, ignoreCache = false): Promise<IActionReturn<R, P, IActionOptions>> => {
    const serializedInput = JSON.stringify(data);
    if (!ignoreCache && serializedInput === prevInputRef.current) {
      return data as any;
    }
    prevInputRef.current = serializedInput;

    setLoading(true);
    setError(null);
    try {
      let result;
      if (isServerAction(handlerRef.current)) {
        result = await action(handlerRef.current, data, {
          ...optionsRef.current,
          errorType: 'throw',
        });
      } else {
        result = await handlerRef.current(data);
      }

      const serializedResult = JSON.stringify(result);
      if (serializedResult !== prevDataRef.current) {
        prevDataRef.current = serializedResult;
        setData(result as R);
      }

      return result as any;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    inputRef.current = input;
  }, [input]);

  useEffect(() => {
    if (optionsRef.current?.skip && optionsRef.current.skip(input)) {
      return;
    }
    if (optionsRef.current?.manual) {
      return;
    }
    execute(input).catch(console.error);
  }, [input]);

  const run = useCallback(
    (overrideInput?: P) => {
      return execute(overrideInput ?? inputRef.current, true);
    },
    [execute],
  );

  const mutate = useCallback(
    (m: (prev: R | undefined) => R | undefined) => {
      const next = m(data);
      setData(next);
    },
    [data],
  );

  return { data, loading, error, run, mutate };
};
