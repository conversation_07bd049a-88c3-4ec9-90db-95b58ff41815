'use client';

import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Input, Button } from '@/components/ui';
import emitter from '@/utils/mitt';

type SearchField = {
  value: string;
  label: string;
};

type ProSearchProps = {
  fields: SearchField[];
  onSearch: (field: string, query: string) => void;
};

const ProSearch = (props: ProSearchProps) => {
  const { fields = [], onSearch } = props;
  const [selectedField, setSelectedField] = useState(fields[0]?.value || '');
  const [query, setQuery] = useState('');

  const handleSearch = () => {
    onSearch(selectedField, query);
  };

  useEffect(() => {
    function clear() {
      setSelectedField(fields[0]?.value || '');
      setQuery('');
    }

    emitter.on('CLEAR_PRO_SEARCH', clear);

    return () => {
      emitter.off('CLEAR_PRO_SEARCH', clear);
    };
  }, [fields]);

  return (
    <div className="flex w-full max-w-sm items-center space-x-2">
      {fields.length === 1 ? (
        <div className="flex w-auto flex-nowrap items-center text-sm text-muted-foreground">{fields[0]?.label}</div>
      ) : (
        <Select value={selectedField} onValueChange={setSelectedField}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select field" />
          </SelectTrigger>
          <SelectContent>
            {fields.map((field) => (
              <SelectItem key={field.value} value={field.value}>
                {field.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      <div className="relative flex-grow">
        <Input
          type="text"
          placeholder="Search..."
          value={query}
          onChange={(e) => setQuery(e.target.value.trim())}
          className="pr-8"
        />
        <Button
          type="submit"
          size="icon"
          variant="ghost"
          className="absolute right-0 top-0 h-full"
          onClick={handleSearch}
        >
          <Search className="h-4 w-4" />
          <span className="sr-only">Search</span>
        </Button>
      </div>
    </div>
  );
};

ProSearch.displayName = 'ProSearch';

export default ProSearch;
