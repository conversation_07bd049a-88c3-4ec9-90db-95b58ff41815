import { prisma } from '@/utils/prisma';
import { Logger } from '@roasmax/utils';
import { NextRequest, NextResponse } from 'next/server';

// 创建小红书 cookie 服务函数
const createRednoteCookie = async ({ data }: { data: { cookie: string } }) => {
  return await prisma.rednote_cookies.create({
    data: {
      cookie: data.cookie,
      tmp_created_at: new Date(),
    },
  });
};

export const POST = async (request: NextRequest) => {
  const logger = new Logger('rednote-cookie', 'api', request.headers.get('x-request-id') || 'unknown');

  try {
    logger._start('开始处理小红书 Cookie 创建请求');
    const { cookie } = await request.json();
    logger.debug('收到创建请求数据', { cookie: !!cookie });

    if (!cookie) {
      logger.warn('缺少必要参数 cookie');
      return NextResponse.json({ error: 'cookie is required' }, { status: 400 });
    }

    logger.info('开始创建小红书 Cookie');
    const result = await createRednoteCookie({
      data: { cookie },
    });

    logger.info('小红书 Cookie 创建成功', { id: result.id });
    logger._end('小红书 Cookie 创建请求处理完成');
    return NextResponse.json(result);
  } catch (error) {
    logger.error('小红书 Cookie 创建失败', (error as Error).message);
    logger._end('小红书 Cookie 创建请求处理失败');
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
};
