import { TmtClient, VodClient } from '@roasmax/utils/tencentcloud';
import crypto from 'node:crypto';
import JWT from 'jsonwebtoken';
import querystring from 'node:querystring';

// TODO: 目前是使用的固定密钥，后续为了提升安全性，考虑转为使用临时密钥 https://cloud.tencent.com/document/product/1278/85305
export const vod = new VodClient({
  credential: { secretId: process.env.VOD_SECRETID, secretKey: process.env.VOD_SECRETKEY },
  region: 'ap-shanghai',
  profile: { httpProfile: { endpoint: 'vod.tencentcloudapi.com' } },
});

//字幕的
export const text = new TmtClient({
  credential: { secretId: process.env.VOD_SECRETID, secretKey: process.env.VOD_SECRETKEY },
  region: 'ap-shanghai',
  profile: { httpProfile: { endpoint: 'tmt.ap-shanghai.tencentcloudapi.com' } },
});

/**
 * 计算上传签名
 */
export const calcUploadSignature = (params: { subAppId: string; classId: string }) => {
  const currentTime = Math.floor(Date.now() / 1000);
  const expiredTime = currentTime + 3600; // 可任意设置过期时间，示例1h

  const arg_list = {
    secretId: process.env.VOD_SECRETID, //云 API 密钥中的 SecretId
    currentTimeStamp: currentTime,
    vodSubAppId: params.subAppId, //当前 Unix 时间戳。
    storageRegion: 'ap-shanghai',
    classId: params.classId, //视频文件分类，默认为0。
    procedure: '上传转码', //视频后续任务处理操作，即完成视频上传后，可自动发起任务流操作。参数值为任务流模板名，云点播支持 创建任务流模板 并为模板命名。
    expireTime: expiredTime, //签名到期 Unix 时间戳。
    random: Math.round(Math.random() * Math.pow(2, 32)), //构造签名明文串的参数。十进制数，
  };

  const original = querystring.stringify(arg_list);
  const original_buffer = Buffer.from(original, 'utf8');

  const hmac = crypto.createHmac('sha1', process.env.VOD_SECRETKEY);
  const hmac_buffer = hmac.update(original_buffer).digest();
  const signature: string = Buffer.concat([hmac_buffer, original_buffer]).toString('base64');
  return signature;
};

/**
 * 计算视频播放鉴权
 */
export const calcPlayAuthToken = (params: { appId: number; fileId: string }) => {
  const audioVideoType = 'RawAdaptive'; // 播放的音视频类型
  const rawAdaptiveDefinition = 10; // 允许输出的未加密的自适应码流模板 ID
  const imageSpriteDefinition = 10; // 做进度条预览的雪碧图模板 ID
  const currentTime = Math.floor(Date.now() / 1000);
  const psignExpire = currentTime + 3600; // 可任意设置过期时间，示例1h
  const urlTimeExpire = psignExpire.toString(16); // 可任意设置过期时间，16进制字符串形式，示例1h
  const playKey = 'YoCDWotFZWuYPwU6FjrY'; // 播放鉴权密钥

  const appId = params.appId; // 获取请求体数据
  const fileId = params.fileId; // 获取请求体数据

  const payload = {
    appId: appId,
    fileId: fileId,
    contentInfo: {
      audioVideoType: audioVideoType,
      rawAdaptiveDefinition: rawAdaptiveDefinition,
      imageSpriteDefinition: imageSpriteDefinition,
    },
    currentTimeStamp: currentTime,
    expireTimeStamp: psignExpire,
    urlAccessInfo: {
      t: urlTimeExpire,
    },
  };
  const token = JWT.sign(payload, playKey);

  return token;
};
