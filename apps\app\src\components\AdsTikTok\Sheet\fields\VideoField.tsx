import { Button, FormField, FormItem, FormLabel, FormControl } from '@/components/ui';
import { CloudIcon, Loader2, Upload } from 'lucide-react';
import { SelectedVideoItem } from '../SelectedVideoItem';
import { VideoFieldProps } from '../types';
import { MaterialItemType } from '@/hooks/useMaterial';
import { cn } from '@/utils/cn';
import { UploadStatus } from '@/types/upload';
import { useAdActions } from '@/store/ads/adStore';

export const VideoField = ({ form, isCreating, uploadRef, uploadList, onLocalUpload }: VideoFieldProps) => {
  const isUploading = uploadList.some((item) => item.status === UploadStatus.UPLOADING);

  const sheetType = form?.getValues('sheetType');
  const { setCloudSheetOpen } = useAdActions();

  const showUploadButton = sheetType === 'quick';

  const handleSelectVideo = () => {
    setCloudSheetOpen(true);
  };

  return (
    <FormField
      control={form.control}
      name="selectedVideoItem"
      render={({ field }) => (
        <FormItem className="my-6 flex h-auto items-center">
          <FormLabel className="w-1/5 text-sm text-white">
            视频 <span className="ml-2 text-red-500">*</span>
          </FormLabel>
          <div className="w-4/5">
            <div className="flex gap-2">
              <FormControl>
                <Button
                  onClick={handleSelectVideo}
                  variant="outline"
                  type="button"
                  disabled={isCreating}
                  className="h-8 rounded-md border border-[#363D54] bg-transparent text-xs text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-white"
                >
                  <CloudIcon className="mr-1.5 h-3 w-3 flex-shrink-0" />
                  选择视频
                </Button>
              </FormControl>
              {showUploadButton && (
                <FormControl>
                  <div className="group relative cursor-pointer">
                    <Button
                      variant="outline"
                      type="button"
                      disabled={isCreating || isUploading || uploadList.length > 0}
                      className={cn(
                        'h-8 rounded-md border border-[#363D54] bg-transparent text-xs text-[#9FA4B2] group-hover:bg-[#CCDDFF33] group-hover:text-white',
                        isUploading && 'cursor-not-allowed',
                      )}
                    >
                      {isUploading ? (
                        <>
                          <Loader2 className="mr-1.5 h-3 w-3 flex-shrink-0 animate-spin" />
                          上传中...
                        </>
                      ) : (
                        <>
                          <Upload className="mr-1.5 h-3 w-3 flex-shrink-0" />
                          本地上传
                        </>
                      )}
                    </Button>
                    <input
                      type="file"
                      multiple={false}
                      accept="video/mp4"
                      disabled={isCreating || isUploading || uploadList.length > 0}
                      onChange={onLocalUpload}
                      ref={uploadRef}
                      className="absolute inset-0 cursor-pointer opacity-0"
                    />
                  </div>
                </FormControl>
              )}
            </div>

            {field?.value && field?.value?.length > 0 && (
              <div className="mt-4 flex flex-col gap-2">
                {field?.value?.map((item: MaterialItemType) => (
                  <SelectedVideoItem
                    key={item.id}
                    item={item}
                    onRemove={(id) => {
                      const newValue = field?.value?.filter((v: MaterialItemType) => v.id !== id);
                      form.setValue('selectedVideoItem', newValue);
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </FormItem>
      )}
    />
  );
};
