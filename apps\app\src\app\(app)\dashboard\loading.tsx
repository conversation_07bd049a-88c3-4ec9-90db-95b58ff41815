'use client';

import { Card } from '@/components/ui';
import { cn } from '@/utils/cn';
import { useSidebar } from '@/components/ui';

const DashboardLoading = () => {
  const { state } = useSidebar();
  const collapsed = state === 'collapsed';

  return (
    <div className={cn('py-6 pr-6', collapsed ? 'w-[calc(100vw-48px)]' : 'w-[calc(100vw-222px)]')}>
      {/* 顶部轮播和数据卡片区域 */}
      <div className="mb-[26px] flex h-[240px] gap-6">
        {/* 轮播区域骨架 */}
        <div className="flex-1 animate-pulse rounded-2xl bg-gray-800" />

        {/* 数据卡片骨架 */}
        <Card className="w-[40%] rounded-2xl p-6">
          <div className="mb-8 h-6 w-24 animate-pulse rounded bg-gray-800" />
          <div className="mb-10 flex gap-[50px]">
            <div className="flex flex-col gap-2">
              <div className="h-4 w-32 animate-pulse rounded bg-gray-800" />
              <div className="h-6 w-12 animate-pulse rounded bg-gray-800" />
            </div>
            <div className="flex flex-col gap-2">
              <div className="h-4 w-32 animate-pulse rounded bg-gray-800" />
              <div className="h-6 w-12 animate-pulse rounded bg-gray-800" />
            </div>
          </div>
          <div className="h-10 w-full animate-pulse rounded-lg bg-gray-800" />
        </Card>
      </div>

      {/* 投流分析区域 */}
      <div>
        <div className="mb-[18px] flex items-center justify-between">
          <div className="h-7 w-48 animate-pulse rounded bg-gray-800" />
          <div className="flex items-center gap-2">
            <div className="h-4 w-32 animate-pulse rounded bg-gray-800" />
            <div className="h-8 w-24 animate-pulse rounded bg-gray-800" />
          </div>
        </div>

        {/* 数据表格区域 */}
        <Card className="w-full rounded-2xl border-none bg-[#CCDDFF1A] p-4">
          {/* 顶部卡片骨架 */}
          <div className="mb-[27px] rounded-xl bg-gray-800 p-5">
            <div className="h-12 w-full animate-pulse rounded" />
          </div>

          {/* 表格标题骨架 */}
          <div className="mb-[18px] flex items-center justify-between">
            <div className="h-5 w-32 animate-pulse rounded bg-gray-800" />
            <div className="h-8 w-24 animate-pulse rounded bg-gray-800" />
          </div>

          {/* 表格骨架 */}
          <div className="overflow-hidden rounded-lg">
            {/* 表头 */}
            <div className="mb-2 grid grid-cols-6 gap-4 px-4 py-3">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="h-6 animate-pulse rounded bg-gray-800" />
              ))}
            </div>
            {/* 表格行 */}
            {[...Array(5)].map((_, rowIndex) => (
              <div key={rowIndex} className="mb-2 grid grid-cols-6 gap-4 px-4 py-3">
                {[...Array(6)].map((_, colIndex) => (
                  <div key={colIndex} className="h-6 animate-pulse rounded bg-gray-800" />
                ))}
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DashboardLoading;
