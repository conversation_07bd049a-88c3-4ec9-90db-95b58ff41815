import type { ActionContext } from '../types';
import { plugins } from './plugins';

/**
 * ServiceContext
 *
 * TODOs
 * 1. Context 更名 ServiceContext，它不依赖于 ServerAction，而是一个独立的用于请求处理上下文的封装
 * 2. 插件自行维护所依赖的其他插件列表，借此自动进行插件加载顺序，而不是通过 plugins 数组进行排序
 * 3. 三方请求的插件，基于 axios 进行封装
 * 4. 用到三方请求的SDK，需要暴露三方请求的接口，以便进行和三方请求插件的组装
 * 5. 存在三方库自行打印日志的情况，对标准输出做拦截重定向，以便统一日志输出
 */
export const createContext = async <P>(params: { data: P }, originContext: Partial<ActionContext<P>>) => {
  const context: ActionContext<P> = {
    token: originContext.token || '',
    user: originContext.user || { id: '' },
    tenant: originContext.tenant || { id: '', name: '', config: {} },
    data: params.data,
    logger: originContext.logger!,
  } as ActionContext<P>;

  for (const pluginLoader of plugins) {
    const p = await pluginLoader(context);
    (context as any)[p.name] = p.plugin as any;
  }

  return context;
};
