import { CSSProperties } from 'react';

export const NotFoundCover = ({ className, style }: { className?: string; style?: CSSProperties }) => {
  return (
    <svg
      width="48"
      height="35"
      viewBox="0 0 48 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      <path
        d="M47.3551 1.71944L47.127 1.50222L46.8694 1.31445L46.5916 1.16611L46.294 1.05722L45.9964 0.978333L45.6689 0.948889L28.4485 0.276667H28.2204L27.794 0.375555L27.4964 0.523889L27.2383 0.721111L27.0802 0.879445L26.8515 1.255L26.2467 2.62833L26.1677 2.95445L26.1276 3.29056V3.62667L26.1772 3.96222L26.2563 4.28833L27.397 7.71722L27.506 8.14222L27.5557 8.56722V9.00222L27.4863 9.42667L27.3677 9.84167L27.1886 10.2472L24.9863 14.4072L24.818 14.7828L24.6994 15.1778L24.6198 15.5833L24.6 15.9978L24.6294 16.4033L24.7186 16.8183L28.2802 24.0417L28.4779 24.2589L28.6964 24.4172L28.9449 24.5256L29.2024 24.595L29.4701 24.6244L29.7383 24.595L29.9958 24.5256L30.2341 24.4072L30.4725 24.2294L34.3311 20.6817L34.5192 20.5333L34.7281 20.435L34.9557 20.3756L35.194 20.3656L35.4318 20.395L35.6599 20.4744L35.8587 20.5928L36.0371 20.7511L42.5336 27.7472L42.6827 27.9444L42.7815 28.1522L42.8414 28.3794L42.8611 28.6167L42.8216 28.8439L42.7521 29.0717L42.6228 29.2789L42.4647 29.4567L42.2857 29.595L42.0677 29.7033L41.8492 29.7628L41.6012 29.7728L24.321 29.1006L23.1803 30.8394L23.1114 31.0967L23.0617 31.6106L23.1408 32.1244L23.2306 32.3817L24.5294 33.9328L44.3395 34.7033L44.6665 34.6939L44.9737 34.6439L45.2719 34.555L45.5593 34.4272L45.827 34.2589L46.0851 34.0611L46.303 33.8239L46.4916 33.5672L46.6407 33.2906L46.7497 33.0039L46.8192 32.6978L46.8587 32.3811L48 3.44944L47.9898 3.13278L47.9407 2.82667L47.8515 2.52056L47.7222 2.23389L47.5635 1.96722L47.3551 1.72V1.71944ZM34.2018 14.9806L33.4677 14.8817L32.803 14.6444L32.3868 14.4167L32.0096 14.15L31.6725 13.8239L31.3845 13.4683L31.1366 13.0733L30.9389 12.6483L30.7599 11.9661L30.7102 11.225L30.8192 10.4939L31.0473 9.83222L31.2755 9.41722L31.5533 9.05111L31.8808 8.71556L32.2377 8.41889L32.6347 8.17167L33.0611 7.98389L33.7455 7.79667L34.4898 7.74667L35.2239 7.85556L35.888 8.09278L36.3048 8.32056L36.6719 8.58722L37.009 8.91333L37.3066 9.26889L37.5551 9.66389L37.7431 10.0889L37.9317 10.7706L37.9814 11.5117L37.8719 12.2433L37.6341 12.905L37.406 13.32L37.1383 13.6856L36.8108 14.0217L36.4539 14.3183L36.0569 14.5556L35.6305 14.7528L34.9461 14.9406L34.2018 14.98V14.9806ZM21.2078 30.7806L22.2389 28.9722L7.16112 29.99L6.91321 29.98L6.69523 29.9311L6.47669 29.8322L6.27847 29.6933L6.11979 29.5256L5.99104 29.3183L5.90125 29.0906L5.87188 28.8633V28.6267L5.93118 28.3989L6.0204 28.1917L6.15932 27.9939L16.873 15.7411L17.0617 15.5733L17.27 15.4444L17.5078 15.3656L17.7563 15.3361L18.0042 15.3556L18.2521 15.425L18.4701 15.5433L18.6587 15.7017L21.3766 18.5178L21.476 18.5867H21.585L21.694 18.5572L21.7731 18.4783L21.803 18.3794L21.7934 18.2511L21.1779 16.67L21.0491 16.2744L20.9796 15.8694L20.9503 15.4544L20.9796 15.0394L21.0587 14.6444L21.1886 14.2389L22.9143 9.89111L23.0527 9.46611L23.1222 9.04167L23.1425 8.60667L23.103 8.18167L23.0036 7.75722L22.8545 7.35167L21.3569 4.08111L21.2377 3.76445L21.1586 3.43889L21.1191 3.11278V2.77667L21.1683 2.44056L21.7335 0.651667L21.7533 0.484445L21.7239 0.326111L21.6545 0.187223L21.5353 0.078889L21.3964 0.00999986L21.2179 0L2.25151 1.29444L1.91438 1.34389L1.60717 1.43278L1.31917 1.55111L1.05149 1.70945L0.803021 1.90722L0.396428 2.37167L0.247908 2.62833L0.119154 2.925L0.0400947 3.22111L0 3.53778V3.87333L2.0036 32.7567L2.04313 33.0733L2.12275 33.3794L2.24134 33.6661L2.40059 33.9328L2.59881 34.18L2.82695 34.4072L3.07542 34.595L3.3527 34.7528L3.6407 34.8717L3.9479 34.9506L4.25567 35H4.58321L22.567 33.8439L21.4461 32.4506L21.3173 32.1839L21.2275 31.9172L21.1485 31.3539L21.2078 30.7806Z"
        fill="#5F6C83"
      />
    </svg>
  );
};
