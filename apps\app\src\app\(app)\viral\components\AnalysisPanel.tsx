'use client';

import { Suggestion } from '@/components/icon';
import { HeartOutline } from '@/components/icon/HeartOutline';
import { MessageOutline } from '@/components/icon/MessageOutline';
import { PlayCircleOutline } from '@/components/icon/PlayCircleOutline';
import { ProImage } from '@/components/pro/pro-image';
import { AnalysisResult } from '@/types/analysis';
import { cn } from '@/utils/cn';
import { Loader2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { useEffect } from 'react';
import { ProCheckbox } from '@/components/pro/pro-checkbox';

interface AnalysisPanelProps {
  isReplaying?: boolean;
  selectedTemplates: string[];
  setSelectedTemplates: (ids: string[]) => void;
  isLoading: boolean;
  analysisResult: AnalysisResult | null;
  isShareMode?: boolean;
  videoIds?: string[];
  setShowTitle?: (showTitle: boolean) => void;
}

const LoadingState = ({ loadingText }: { loadingText: string }) => (
  <div className="flex items-center gap-2 text-[#FFFFFFCC]">
    <Loader2 className="h-4 w-4 animate-spin" />
    <span>{loadingText}</span>
  </div>
);
export const decodeHtmlEntities = (text: string) => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};
export const AnalysisPanel = ({
  selectedTemplates,
  setSelectedTemplates,
  isLoading,
  analysisResult,
  isShareMode = false,
  videoIds,
  setShowTitle,
}: AnalysisPanelProps) => {
  const handleOpenTikTok = (url: string) => {
    console.log('url', url);
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  useEffect(() => {
    if (analysisResult?.template_info && analysisResult.template_info.length > 0) {
      const allTemplateIds = analysisResult.template_info
        ?.filter((item: any) => videoIds?.includes(item['视频id']))
        .map((item: any) => item['视频id']);
      setSelectedTemplates(allTemplateIds);
      setShowTitle?.(false);
    }
  }, [analysisResult, setSelectedTemplates]);

  const handleDivClick = (itemId: string) => {
    if (isShareMode) return;
    const checkbox = document.querySelector(`[data-item-id="${itemId}"]`);
    if (checkbox) {
      const currentChecked = selectedTemplates.includes(itemId);
      const newChecked = !currentChecked;
      if (newChecked) {
        setSelectedTemplates([...selectedTemplates, itemId]);
      } else {
        setSelectedTemplates(selectedTemplates.filter((name) => name !== itemId));
      }
    }
  };

  return (
    <div id="export-content" className="space-y-6">
      {/* 这里的内容将被导出为PDF */}
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm font-normal text-[#9FA4B2]">
          {isShareMode ? '以下为系统匹配的视频模版' : '以下为系统匹配最适合视频的模版'}
        </div>
      </div>
      {/* 模板信息 */}
      <div className="">
        {isLoading && !analysisResult?.template_info ? (
          <LoadingState loadingText="匹配方案中.." />
        ) : (
          <div className="grid grid-cols-2 gap-1 text-sm md:grid-cols-2 lg:grid-cols-3">
            {analysisResult?.template_info && analysisResult?.template_info?.length > 0 ? (
              analysisResult.template_info
                ?.filter((item: any) => videoIds?.includes(item['视频id']))
                .map((item: any, index: number) => (
                  <div key={index} className="relative overflow-hidden rounded-[12px] text-sm font-normal">
                    <div className="flex h-full items-center gap-2 rounded-[12px] border-[1px] border-[#363D54] bg-[linear-gradient(180deg,_rgba(204,221,255,0.1)_0%,_rgba(204,221,255,0)_100%)] p-3 backdrop-blur-[20px]">
                      <div
                        onClick={() => handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item['视频id']}`)}
                        className="cursor-pointer transition-opacity hover:opacity-80"
                        role="link"
                        tabIndex={0}
                        aria-label={`查看视频: ${decodeHtmlEntities(item.商品名称)}`}
                        onKeyDown={(e) =>
                          e.key === 'Enter' &&
                          handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item['视频id']}`)
                        }
                      >
                        <ProImage
                          width={70}
                          height={120}
                          src={`https://bwkj-cos-1324682537.cos.ap-shanghai.myqcloud.com/damai/covers/${item['视频id']}.jpg`}
                          className="h-[120px] w-[70px] min-w-[70px] rounded-[4px]"
                          alt={decodeHtmlEntities(item.商品名称)}
                        />
                      </div>
                      <div className="flex h-full w-full flex-col justify-between overflow-hidden">
                        <div>
                          <div
                            onClick={() =>
                              handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item['视频id']}`)
                            }
                            className="product-name line-clamp-3 cursor-pointer hover:text-blue-400"
                            role="link"
                            tabIndex={0}
                            onKeyDown={(e) =>
                              e.key === 'Enter' &&
                              handleOpenTikTok(`https://tiktok.com/@${item.作者id}/video/${item['视频id']}`)
                            }
                          >
                            {decodeHtmlEntities(item.商品名称)}
                          </div>
                          <div
                            onClick={() => handleOpenTikTok(`https://tiktok.com/@${item.作者id}`)}
                            className="author-name line-clamp-1 cursor-pointer text-xs hover:text-blue-400"
                            role="link"
                            tabIndex={0}
                            onKeyDown={(e) =>
                              e.key === 'Enter' && handleOpenTikTok(`https://tiktok.com/@${item.作者id}`)
                            }
                          >
                            @{item.作者昵称}
                          </div>
                        </div>
                        <div className="flex h-[24px] justify-start" onClick={() => handleDivClick(item['视频id'])}>
                          <div />
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <PlayCircleOutline />
                              <div className="icon1 text-xs">
                                {Number(item.播放量) >= 10000
                                  ? `${Math.floor(Number(item.播放量) / 10000)}w+`
                                  : item.播放量}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <HeartOutline />
                              <div className="icon1 text-xs">
                                {Number(item.点赞数) >= 10000
                                  ? `${Math.floor(Number(item.点赞数) / 10000)}w+`
                                  : item.点赞数}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <MessageOutline />
                              <div className="icon1 text-xs">{item.评论数}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {!isShareMode && (
                      <ProCheckbox
                        checked={selectedTemplates.includes(item['视频id'])}
                        onCheckedChange={(checked) => {
                          console.log('checked', checked);
                          if (checked) {
                            setSelectedTemplates([...selectedTemplates, item['视频id']]);
                          } else {
                            setSelectedTemplates(selectedTemplates.filter((name) => name !== item['视频id']));
                          }
                        }}
                        className={cn('absolute right-3 top-3')}
                        data-item-id={item['视频id']}
                      />
                    )}
                  </div>
                ))
            ) : (
              <p className="text-[#FFFFFFCC]">暂无数据</p>
            )}
          </div>
        )}
      </div>
      <div>
        <div className="mb-4 flex items-center gap-3">
          <div className="analysis1">
            <Suggestion />
          </div>
          <div className="">商品分析报告</div>
        </div>
        <div className="bg-gradient-1 product-analysis mb-4 rounded-[16px] p-[1px]">
          <div className="rounded-[16px] bg-[#070F1F]">
            <div className="h-full border-t">
              <div className="px-6 py-4 text-sm text-[#FFFFFFCC]">
                {isLoading && !analysisResult?.product_analysis ? (
                  <LoadingState loadingText="报告生成中.." />
                ) : (
                  <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
                    {analysisResult?.product_analysis || '暂无数据'}
                  </ReactMarkdown>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="mb-4 flex items-center gap-3">
          <div className="analysis1">
            <Suggestion />
          </div>
          <div className="">短视频内容分析与克隆建议报告</div>
        </div>
        <div className="bg-gradient-1 content-analysis rounded-[16px] p-[1px]">
          <div className="rounded-[16px] bg-[#070F1F]">
            <div className="h-full border-t">
              <div className="px-6 py-4 text-sm text-[#FFFFFFCC]">
                {isLoading && !analysisResult?.suggestion ? (
                  <LoadingState loadingText="报告生成中.." />
                ) : (
                  <ReactMarkdown className="markdown prose prose-invert max-w-none text-sm text-[#FFFFFFCC]">
                    {analysisResult?.suggestion || '暂无数据'}
                  </ReactMarkdown>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
