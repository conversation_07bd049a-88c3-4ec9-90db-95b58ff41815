import { ChangeEventHandler, useRef } from 'react';
import { Input } from '../ui';
import React from 'react';

interface FilePickerProps {
  children: React.ReactElement;
  onSelectedFiles?: ChangeEventHandler<HTMLInputElement>;
}

export const FilePicker: React.FC<FilePickerProps> = (props) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadClick: React.MouseEventHandler<HTMLElement> = () => {
    fileInputRef.current?.click();
  };

  const children = React.Children.map(props.children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as any, { onClick: handleUploadClick });
    }
    return child;
  });

  return (
    <>
      {children}
      <Input
        className="hidden"
        id="bwai-material-upload"
        multiple
        type="file"
        accept=".mp4"
        onChange={props.onSelectedFiles}
        ref={fileInputRef}
      />
    </>
  );
};
