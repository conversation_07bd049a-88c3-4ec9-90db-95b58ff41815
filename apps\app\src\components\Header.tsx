import React, { useEffect } from 'react';
import HistoryCloudDrive from './CloudDrive';
import { Logo, LogoText2 } from '@/components/icon/index';
import Link from 'next/link';
import Distribution from './Distribution';
import { GlobalUploadModalStatusBarItem } from './GlobalUpload/GlobalUploadModalStatusBarItem';
import { getMeInfo } from '@/services/actions/me';
import { action } from '@/utils/server-action/action';
import { useWallet } from '@/hooks/useWallet';
import { SidebarTrigger } from './ui';

const Header = () => {
  const { refresh: refreshWallet } = useWallet();

  const refreshMeInfo = async () => {
    const me = await action(getMeInfo, undefined);
    if (!me) return;
    refreshWallet();
  };

  useEffect(() => {
    refreshMeInfo();
  }, []);

  return (
    <div>
      <header className="flex h-20 items-center justify-between bg-transparent p-4">
        <div className="mx-4 flex items-center">
          <Link href="/">
            <Logo />
          </Link>
          <div className="ml-[14px] mr-[48px]">
            <LogoText2 />
          </div>
          <SidebarTrigger />
        </div>
        <div className="flex items-center justify-between">
          <Distribution />
          <GlobalUploadModalStatusBarItem />
          <HistoryCloudDrive />
        </div>
      </header>
    </div>
  );
};

export default Header;
