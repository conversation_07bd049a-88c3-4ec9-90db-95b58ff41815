import authingPlugin from './authing';
import authingManagePlugin from './authing-manage';
import cosPlugin from './cos';
import dbPlugin from './db';
import executePlugin from './execute';
import feishuRobotPlugin from './feishu';
import fetchTenantPlugin from './fetch-tenant';
import fetchLangfusePlugin from './langfuse';
import fetchLangfuseOpenPlugin from './langfuse-open-api';
import langfuseRpcPlugin from './langfuse-rpc';
import loaderPlugin from './loader';
import mqPlugin from './mq';
import requestPlugin from './request';
import transactionPlugin from './trx';
import vodPlugin from './vod';
import stsPlugin from './sts';

export const plugins = [
  loaderPlugin,
  authingPlugin,
  authingManagePlugin,
  fetchTenantPlugin, // 依赖 loaderPlugin 和 authingPlugin
  dbPlugin,
  transactionPlugin,
  executePlugin,
  feishuRobotPlugin,
  langfuseRpcPlugin,
  requestPlugin,
  mqPlugin,
  vodPlugin,
  fetchLangfusePlugin,
  fetchLangfuseOpenPlugin,
  cosPlugin,
  stsPlugin,
];
