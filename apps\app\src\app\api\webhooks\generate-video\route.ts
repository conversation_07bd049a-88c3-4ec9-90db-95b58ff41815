import { createVideoGenerationSubTasks, emitVideoGenerationTask } from '@/services/domains/task/video-generation-task';
import { onFileUploaded } from '@/services/domains/vod';
import { ActionContext, webhook } from '@roasmax/serve';

/**
 * 创建自动分发任务
 * 根据传入的 IP 商品等信息，自动创建生成任务并分发
 * 主要是用于自动化生成视频
 * 1. 运营人员将视频存入COS
 * 2. COS触发webhook，请求到云函数处理
 * 3. 云函数将文件转推到VOD后，调用此接口，创建生成任务
 */
export const POST = webhook(
  async (
    ctx: ActionContext<{
      /** 素材名称 */
      name: string;
      /** VOD FILE ID */
      vodFileId: string;
      /*** 素材自定义参数，该callback会根据该参数决定是否创建任务 */
      properties: {
        distribute: 'auto' | undefined;
        ipName: string;
        productName: string;
        liveRoom: string;
        liveSession: string;
      };
    }>,
  ) => {
    if (!ctx.data.vodFileId) {
      throw new Error('vodFileId is required');
    }

    const rootDir = await ctx.db.material_directories.findFirst({
      where: { name: '原始素材' },
    });
    if (!rootDir) {
      throw new Error(`未找到原始素材根目录`);
    }

    // 登记素材
    const [material] = await ctx.execute(onFileUploaded, {
      forceUpdate: false,
      info: {
        list: [{ name: ctx.data.name, vodFileId: ctx.data.vodFileId }],
        directoryId: rootDir.id,
        properties: ctx.data.properties,
      },
    });

    if (!material) {
      throw new Error(`素材登记失败`);
    }

    if (ctx.data.properties.distribute) {
      const taskName = `${ctx.data.properties.ipName}-${ctx.data.properties.productName}-${ctx.data.properties.liveRoom}-${ctx.data.properties.liveSession}`;

      let videoGenerationTask = await ctx.db.video_generation_tasks.findFirst({
        where: { name: taskName },
        orderBy: { tmp_created_at: 'desc' },
      });
      if (!videoGenerationTask) {
        const prompts = await ctx.langfuseOpenApi.listPrompts({});
        const ipPrompts = prompts.data.filter((p) => p.tags.includes(`#行业-${ctx.data.properties.ipName}`));
        if (ipPrompts.length === 0) {
          throw new Error(`未找到行业为${ctx.data.properties.ipName}的prompt`);
        }
        videoGenerationTask = await ctx.db.video_generation_tasks.create({
          data: {
            tenant_id: ctx.tenant.id,
            user_id: ctx.user.id,
            name: taskName,
            slice_duration: '300',
            // TODO: 这里的参数需要从商品中获取/或者需要用枚举值，而不是字符串
            video_language: 'zh-CN',
            method: 'normal',
            industry: ctx.data.properties.ipName,
            prompts: ipPrompts.map((p) => p.name),
            kol_style: '',
            generate_round: 1,
            video_speed: '1X',
            subtitle: 0,
            transition_mode: ['帕迪恩北总'].includes(ctx.data.properties.ipName) ? 'null' : 'fade',
            dify_workflow_key: ctx.tenant.config.enabled_sub_systems?.includes('dify')
              ? process.env.DIFY_WORKFLOW_KEY
              : null,
          },
        });
      }

      // 自动创建并执行生成任务
      const generationSubTask = await ctx.execute(createVideoGenerationSubTasks, {
        taskId: videoGenerationTask.id,
        materialIds: [material.id],
      });
      await ctx.execute(emitVideoGenerationTask, { taskId: generationSubTask.id });
    }

    return material;
  },
);
