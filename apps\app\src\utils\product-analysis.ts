import { Step, ProductInfo, CloneSuggestion } from '@/types/product-analysis';
// 创建默认步骤数据的函数
export const createDefaultSteps = (): Step[] => {
  return [
    {
      step_id: 'store_page_information',
      step_title: '数据采集与解析：页面到信息的精准提取',
      step_number: 1,
      description: '通过多维度数据采集技术，深入解析页面信息，确保从源头获取最精准的数据',
      completed_at: new Date().toISOString(),
      content_type: 'markdown',
      content: '阅读页面',
      lodding: '页面阅读分析中',
      contentLodding:
        '## ① 格式化参数：\n- **阅读页面python分掠链接信息...**\n- **Host请求服务器...**\n- **product_url分析产品链接...**\n- **网站排序分析...**',
      data: {},
      processed_data: {},
      sub_steps: [],
    },
    {
      step_id: 'product_analysis',
      step_title: '信息整理与洞察：数据到策略的深度挖掘',
      step_number: 2,
      description: '对采集到的数据进行结构化整理，并深度挖掘商品的核心价值与目标人群特征',
      completed_at: new Date().toISOString(),
      content_type: 'markdown',
      content: '整理信息',
      lodding: '信息整理中',
      contentLodding:
        '## ① 大模型分析：\n- **文本解析...**\n- **意图识别...**\n- **模型推理计算...**\n- **多轮优化...**',
      data: {},
      processed_data: {},
      sub_steps: [],
    },
    {
      step_id: 'template_information',
      step_title: '智能匹配：策略到内容的精准匹配',
      step_number: 3,
      description: '通过智能算法，将商品信息与视频模板进行精准匹配，确保内容的高度相关性',
      completed_at: new Date().toISOString(),
      content_type: 'markdown',
      content: '匹配方案',
      lodding: '方案匹配中',
      contentLodding: '## ① API请求：\n- **API请求中...**\n- **HTTP请求中...**',
      data: {},
      processed_data: {},
      sub_steps: [],
    },
    {
      step_id: 'cloning_recommendations',
      step_title: '报告生成与评估：从内容到效果的全面分析',
      step_number: 4,
      description: '生成详细的视频分析报告，全面评估视频内容的表现与潜力',
      completed_at: new Date().toISOString(),
      content_type: 'markdown',
      content: '生成详细的视频',
      lodding: '报告生成中',
      contentLodding: '## ①  解析视频信息：\n- **API请求...**\n- **数据处理模块处理...**\n- **JSON解析...**',
      data: {},
      processed_data: {},
      sub_steps: [],
    },
  ];
};
// 更新处理商品信息的方法
export const processProductInfo = (data: ProductInfo) => {
  return {
    title: data?.title ?? '',
    price: data?.price ?? '',
    description: data?.describe ?? '',
  };
};

// 更新处理分析数据的方法
export const processAnalysisData = (data: string) => {
  return {
    product_analysis: data || '',
  };
};

// 更新处理模板信息的方法
export const processTemplateInfo = (data: any[]) => {
  // @ts-ignore
  if (typeof data === 'object' && data?.status === false) {
    return {
      template_info: [],
    };
  }
  return {
    template_info: data,
  };
};

// 更新处理克隆建议的方法
export const processCloneSuggestion = (data: CloneSuggestion) => {
  return {
    suggestion: data?.suggestion || '',
    video_ids: data?.video_ids || [],
  };
};
export const getStepTitle = (stepId: string): string => {
  switch (stepId) {
    case 'store_page_information':
      return '数据采集与解析：页面到信息的精准提取';
    case 'product_analysis':
      return '信息整理与洞察：数据到策略的深度挖掘';
    case 'template_information':
      return '智能匹配：策略到内容的精准匹配';
    case 'cloning_recommendations':
      return '报告生成与评估：从内容到效果的全面分析';
    default:
      return stepId;
  }
};
export const getStepDescription = (stepId: string): string => {
  switch (stepId) {
    case 'store_page_information':
      return '通过多维度数据采集技术，深入解析页面信息，确保从源头获取最精准的数据';
    case 'product_analysis':
      return '对采集到的数据进行结构化整理，并深度挖掘商品的核心价值与目标人群特征';
    case 'template_information':
      return '通过智能算法，将商品信息与视频模板进行精准匹配，确保内容的高度相关性';
    case 'cloning_recommendations':
      return '生成详细的视频分析报告，全面评估视频内容的表现与潜力';
    default:
      return '';
  }
};
// 获取步骤内容类型的辅助函数
export const getStepContentType = (stepId: string): 'markdown' | 'image' | 'animation' => {
  switch (stepId) {
    case 'store_page_information':
      return 'markdown';
    case 'product_analysis':
      return 'markdown';
    case 'template_information':
      return 'markdown';
    case 'cloning_recommendations':
      return 'markdown';
    default:
      return 'markdown';
  }
};
