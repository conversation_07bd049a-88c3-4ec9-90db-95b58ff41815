export const CreativeCenter = ({ className }: { className?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M11.3443 24.382C13.1989 24.382 14.7023 22.8786 14.7023 21.024C14.7023 19.1694 13.1989 17.666 11.3443 17.666C9.48976 17.666 7.98633 19.1694 7.98633 21.024C7.98633 22.8786 9.48976 24.382 11.3443 24.382Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.939 18.9274C17.939 18.6622 18.0444 18.4078 18.2319 18.2203C18.4195 18.0327 18.6738 17.9274 18.939 17.9274H23.132C23.3973 17.9274 23.6516 18.0327 23.8392 18.2203C24.0267 18.4078 24.132 18.6622 24.132 18.9274V23.1204C24.132 23.3856 24.0267 23.6399 23.8392 23.8275C23.6516 24.015 23.3973 24.1204 23.132 24.1204H18.938C18.6728 24.1204 18.4185 24.015 18.2309 23.8275C18.0434 23.6399 17.938 23.3856 17.938 23.1204L17.939 18.9274ZM8.24805 8.73438C8.24805 8.46916 8.3534 8.2148 8.54094 8.02727C8.72848 7.83973 8.98283 7.73438 9.24805 7.73438H13.442C13.7073 7.73438 13.9616 7.83973 14.1492 8.02727C14.3367 8.2148 14.442 8.46916 14.442 8.73438V12.9284C14.442 13.1936 14.3367 13.4479 14.1492 13.6355C13.9616 13.823 13.7073 13.9284 13.442 13.9284H9.24805C8.98283 13.9284 8.72848 13.823 8.54094 13.6355C8.3534 13.4479 8.24805 13.1936 8.24805 12.9284V8.73438Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.5512 7.20963C20.5817 7.12639 20.6371 7.05453 20.7098 7.00377C20.7825 6.953 20.869 6.92578 20.9577 6.92578C21.0463 6.92578 21.1329 6.953 21.2056 7.00377C21.2783 7.05453 21.3336 7.12639 21.3642 7.20963L21.9642 8.82963C22.1342 9.28963 22.4972 9.65363 22.9582 9.82363L24.5782 10.4236C24.6614 10.4542 24.7333 10.5095 24.784 10.5822C24.8348 10.6549 24.862 10.7415 24.862 10.8301C24.862 10.9188 24.8348 11.0053 24.784 11.078C24.7333 11.1507 24.6614 11.2061 24.5782 11.2366L22.9582 11.8366C22.4982 12.0066 22.1342 12.3696 21.9632 12.8306L21.3632 14.4506C21.3324 14.5335 21.2771 14.605 21.2045 14.6555C21.1319 14.706 21.0456 14.7331 20.9572 14.7331C20.8688 14.7331 20.7825 14.706 20.7099 14.6555C20.6373 14.605 20.5819 14.5335 20.5512 14.4506L19.9512 12.8306C19.8672 12.6028 19.7349 12.3959 19.5633 12.2241C19.3917 12.0523 19.1849 11.9198 18.9572 11.8356L17.3372 11.2356C17.2543 11.2049 17.1828 11.1495 17.1323 11.0769C17.0818 11.0044 17.0547 10.918 17.0547 10.8296C17.0547 10.7412 17.0818 10.6549 17.1323 10.5823C17.1828 10.5097 17.2543 10.4544 17.3372 10.4236L18.9572 9.82363C19.4182 9.65363 19.7812 9.29063 19.9522 8.82963L20.5512 7.20963Z"
        fill="currentColor"
        stroke="white"
        strokeWidth="1.2"
      />
    </svg>
  );
};
