import { Logger, to } from '@roasmax/utils';
import chalk from 'chalk';
import * as crypto from 'crypto';
import { NextRequest, NextResponse } from 'next/server';
import { createContext } from './context';
import { prisma } from './prisma';
import { ActionContext } from './types';

export const webhook = <R, T>(handler: (context: ActionContext<T>, request: NextRequest) => Promise<R>) => {
  return async (request: NextRequest) => {
    const [error, body] = await to(request.json());
    const payload = (error ? undefined : body) as T;
    const requestIp = request.headers.get('x-forwarded-for');
    const logger = new Logger(`${request.url ?? '-'}`, requestIp ?? '-', generateRandomBase36(10));
    const now = Date.now();
    try {
      logger._start('Start', JSON.stringify(payload));
      const token = request.headers.get('AccessToken');
      if (!token) {
        throw new Error('Unauthorized: no token');
      }
      const tenantId = verifyToken(token, process.env.WEBHOOK_SECRET ?? '');
      if (!tenantId) {
        throw new Error('Unauthorized: invalid tenantId');
      }
      const source_config = await prisma.source_configs.findFirst({
        where: { tenant_id: tenantId },
      });
      if (!source_config) {
        throw new Error('source_config not found');
      }
      const member = await prisma.members.findFirst({
        where: { tenant_id: tenantId, admin: 1 },
      });
      if (!member?.user_id) {
        throw new Error('member not found');
      }
      const context = await createContext(
        { data: payload },
        {
          user: { id: member.user_id },
          tenant: { id: tenantId, name: '', config: source_config },
          logger: logger,
        },
      );

      const data = await handler(context, request);
      logger._end('Success', cc(Date.now() - now));
      return NextResponse.json({ success: true, data });
    } catch (e: any) {
      logger._end('Error', cc(Date.now() - now), e);
      if (e.message.includes('Unauthorized')) {
        return NextResponse.json({ success: false, code: 401, message: e.message, data: null });
      }
      return NextResponse.json({ success: false, code: 500, message: e.message, data: null });
    }
  };
};

const cc = (cost: number) => (cost < 500 ? chalk.green(`${cost}ms`) : chalk.red(`${cost}ms`));

function generateRandomBase36(length: number): string {
  let result = '';
  while (result.length < length) {
    result += Math.random().toString(36).substring(2);
  }
  return result.substring(0, length);
}

function verifyToken(token: string, secret: string): string {
  try {
    const decoded = Buffer.from(token, 'base64').toString('utf8');
    const [tenantId, timestampStr, signature] = decoded.split(':');
    const timestamp = parseInt(timestampStr!, 10);

    // Check if timestamp is within 60 minutes
    if (Date.now() - timestamp > 60 * 60 * 1000) {
      throw new Error('Unauthorized: token expired');
    }

    // Verify signature
    const data = `${tenantId}:${timestamp}`;
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(data);
    const expectedSignature = hmac.digest('hex');

    if (signature !== expectedSignature) {
      throw new Error('Unauthorized: invalid signature');
    }
    return tenantId!;
  } catch (e) {
    console.error(e);
    throw new Error('Unauthorized');
  }
}
