export const EmptyTask = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="74" height="81" viewBox="0 0 74 81" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M49.1052 61.2241L38.4204 79.2555C38.1611 79.693 37.4902 79.5092 37.4902 79.0006V67.2324C37.4902 66.9563 37.2664 66.7324 36.9902 66.7324H26.8747C26.4701 66.7324 26.2331 66.277 26.4651 65.9456L39.261 47.6756C39.5414 47.2752 40.1705 47.4737 40.1705 47.9624V59.9692C40.1705 60.2453 40.3944 60.4692 40.6705 60.4692H48.675C49.0624 60.4692 49.3027 60.8908 49.1052 61.2241Z"
        fill="#00E1FF"
        stroke="#00E1FF"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
      <g filter="url(#filter0_b_738_838)">
        <path
          d="M63.5607 22.3779C62.9 14.0181 55.8964 7.48695 47.4393 7.61757C46.6464 7.61757 45.8536 7.61757 45.0607 7.74819C41.3607 3.30703 35.8107 0.825195 30.1286 0.825195C19.2929 0.825195 10.4393 9.44629 10.4393 20.1573V22.3779C4.22857 24.8598 0 30.7378 0 37.3995C0 46.1512 7.26786 53.3355 16.2536 53.3355H57.8786C66.7321 53.3355 74 46.1512 74 37.3995C74 30.7378 69.9036 24.7291 63.5607 22.3779Z"
          fill="white"
          fillOpacity="0.1"
        />
        <path
          d="M63.0623 22.4173L63.0874 22.7357L63.3869 22.8468C69.5313 25.1244 73.5 30.9443 73.5 37.3995C73.5 45.8697 66.4615 52.8355 57.8786 52.8355H16.2536C7.53585 52.8355 0.5 45.867 0.5 37.3995C0.5 30.9555 4.59143 25.2532 10.6248 22.8422L10.9393 22.7166V22.3779V20.1573C10.9393 9.73222 19.5591 1.3252 30.1286 1.3252C35.668 1.3252 41.0751 3.74538 44.6766 8.06824L44.8597 8.28805L45.142 8.24154C45.8879 8.11866 46.6399 8.11757 47.4393 8.11757V8.11763L47.447 8.11751C55.635 7.99105 62.4219 14.3151 63.0623 22.4173Z"
          stroke="url(#paint0_linear_738_838)"
        />
      </g>
      <path
        d="M29.3027 33.3291V33.3291C32.6025 38.9947 40.7338 39.145 44.2407 33.6052L44.4155 33.3291"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <ellipse cx="28.0535" cy="24.6123" rx="3.03492" ry="3" fill="#D9D9D9" />
      <ellipse cx="45.488" cy="24.6123" rx="3.03492" ry="3" fill="#D9D9D9" />
      <defs>
        <filter
          id="filter0_b_738_838"
          x="-5"
          y="-4.1748"
          width="84"
          height="62.5103"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.5" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_738_838" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_738_838" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_738_838"
          x1="19.1762"
          y1="8.59904"
          x2="36.6459"
          y2="53.4733"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EFEDFD" stopOpacity="0.1" />
          <stop offset="1" stopColor="#EFEDFD" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
