import { buildSocialAccountColumns } from '@/app/(app)/distributions/social-accounts/config';
import { getSocialAccountsByIps, getVideoDistributionTaskDetail } from '@/services/actions/video-distribution-task';
import { cn } from '@/utils/cn';
import { ActionResult, useAction } from '@/utils/server-action/action';
import React, { useImperativeHandle } from 'react';
import { ProImage } from './pro/pro-image';
import ProTable from './pro/pro-table';
import { But<PERSON>, Dialog, DialogContent, DialogHeader } from './ui';

export type VideoDistributionTaskType = ActionResult<typeof getVideoDistributionTaskDetail>;

interface VideoDistributionTaskPreviewModalProps {}

export interface VideoDistributionTaskPreviewModalRef {
  show: (taskId: string) => void;
}

export const VideoDistributionTaskPreviewModal = React.forwardRef<
  VideoDistributionTaskPreviewModalRef,
  VideoDistributionTaskPreviewModalProps
>((_props, ref) => {
  const [open, setOpen] = React.useState(false);
  const [taskId, setTaskId] = React.useState<string>();

  const [status, setStatus] = React.useState<string>('待分发');

  useImperativeHandle(ref, () => ({
    show(_taskId) {
      setOpen(true);
      setTaskId(_taskId);
    },
  }));

  const { data: task, loading: loadingTask } = useAction(
    getVideoDistributionTaskDetail,
    { taskId: taskId! },
    { skip: ({ taskId }) => !taskId },
  );

  const { data: socialAccounts, loading: socialAccountsLoading } = useAction(
    getSocialAccountsByIps,
    {
      ips: task?.sub_tasks.map((t) => t.ip!),
      distributionBatchNo: task?.distribution_batch_no || '',
    },
    { skip: (data) => !data.ips?.length || !data.distributionBatchNo },
  );

  return (
    <Dialog open={open} onOpenChange={(o) => setOpen(o)}>
      <DialogContent className="w-[1024px] max-w-[1024px]">
        <DialogHeader className="h-[56px]">
          <div className="flex items-center gap-2">
            <div>任务详情</div>
            <Button
              variant="link"
              className={cn('text-xs', status === '待分发' && 'text-[#00e1ff]')}
              onClick={() => setStatus('待分发')}
            >
              待分发视频
            </Button>
            <Button
              variant="link"
              className={cn('text-xs', status === '已分发' && 'text-[#00e1ff]')}
              onClick={() => setStatus('已分发')}
            >
              已分发视频
            </Button>
            <Button variant="link" className="text-xs" onClick={() => setStatus('账号列表')}>
              账号列表
            </Button>
          </div>
        </DialogHeader>
        <div>
          {/* 待分发表格 */}
          {status === '待分发' && (
            <ProTable
              loading={loadingTask}
              className="h-[80vh] flex-1 flex-shrink rounded-lg"
              dataSource={task?.sub_tasks.filter((st) => !st.task_id) || []}
              columns={[
                { dataIndex: 'batch_no', title: '批次' },
                {
                  dataIndex: ['material', 'vod_cover_url'],
                  title: '视频',
                  render: (v) => (
                    <ProImage src={v} alt="" width={48} height={48} className="h-[48px] w-[48px] object-cover" />
                  ),
                },
                { dataIndex: 'live_room', title: '直播间' },
                { dataIndex: 'live_session', title: '直播场次' },
                { dataIndex: ['social_account', 'nickname'], title: '分发到账号' },
                { dataIndex: 'status_desc', title: '分发状态' },
              ]}
            />
          )}

          {/* 已分发表格 */}
          {status === '已分发' && (
            <ProTable
              loading={loadingTask}
              className="h-[80vh] flex-1 flex-shrink rounded-lg"
              dataSource={task?.sub_tasks.filter((st) => st.task_id) || []}
              columns={[
                { dataIndex: 'batch_no', title: '批次' },
                {
                  dataIndex: ['material', 'vod_cover_url'],
                  title: '视频',
                  render: (v) => (
                    <ProImage src={v} alt="" width={48} height={48} className="h-[48px] w-[48px] object-cover" />
                  ),
                },
                { dataIndex: 'live_room', title: '直播间' },
                { dataIndex: 'live_session', title: '直播场次' },
                { dataIndex: ['social_account', 'nickname'], title: '分发到账号' },
                { dataIndex: 'status_desc', title: '分发状态' },
              ]}
            />
          )}

          {/* 账号列表 */}
          {status === '账号列表' && (
            <ProTable
              loading={socialAccountsLoading}
              className="h-[80vh] flex-1 flex-shrink rounded-lg"
              columns={buildSocialAccountColumns({
                displayColumns: [
                  'nickname',
                  'status',
                  'fans_count',
                  'account_identity',
                  'account_unique_identity',
                  'daily_video_count_max',
                ],
              })}
              dataSource={socialAccounts || []}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
});

VideoDistributionTaskPreviewModal.displayName = 'VideoDistributionTaskPreviewModal';
