import { batchImport } from '@/utils/batch-import';
import { ActionContext, api } from '@roasmax/serve';

type BatchCreateContext = ActionContext<{
  data: Array<{
    order_id: string;
    [key: string]: any;
  }>;
}>;

export const POST = api(async (ctx: BatchCreateContext) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  return await ctx.trx(async (ctx) => {
    return await batchImport({
      db: ctx.db,
      importData: ctx.data.data,
      modelName: 'orders',
      identityField: 'order_id',
      mergeStrategy: (newData, existingData) => {
        return Object.keys(newData).reduce(
          (acc, key) => {
            const newValue = newData[key];
            const oldValue = existingData[key];
            if (newValue !== oldValue) {
              acc.properties = {
                ...(acc.properties || {}),
                isMerged: true,
              };
            }

            if (key === 'properties' && typeof oldValue === 'object') {
              acc[key] = {
                ...oldValue,
                ...newValue,
              };
            } else {
              acc[key] = newValue ?? oldValue;
            }

            return acc;
          },
          {} as Record<string, any>,
        );
      },
    });
  });
});
