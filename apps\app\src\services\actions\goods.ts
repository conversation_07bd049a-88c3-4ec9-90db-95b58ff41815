'use server';

import { GOODS_STATUS, GoodsStatus } from '@/common/goods';
import { goods, Prisma } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import { uniq } from 'lodash';

type PageGoodsFilterType = Partial<{
  name: string;
  tags: string[];
  ip: string;
  short_sn: string;
  status: string;
  product_identity: string;
  hot_product: boolean;
  shop_name: string;
  platform: string;
  video_distribution_sub_tasks_wait_count: number;
  video_distribution_sub_tasks_generated_count: number;
  video_distribution_sub_tasks_done_count: number;
}>;

const buildFilterType: Record<keyof PageGoodsFilterType, 'boolean' | 'string' | 'string[]' | 'number-min'> = {
  name: 'string',
  tags: 'string[]',
  ip: 'string',
  short_sn: 'string',
  product_identity: 'string',
  hot_product: 'boolean',
  shop_name: 'string',
  platform: 'string',
  status: 'string',
  video_distribution_sub_tasks_wait_count: 'number-min',
  video_distribution_sub_tasks_generated_count: 'number-min',
  video_distribution_sub_tasks_done_count: 'number-min',
};

const buildFilterWhere = (filter?: PageGoodsFilterType) => {
  const where: Prisma.v_goods_with_tasksWhereInput = {};
  if (!filter) return where;
  Object.entries(filter).forEach(([key, value]) => {
    const k = key as keyof PageGoodsFilterType;
    if (buildFilterType[k] === 'boolean') {
      where[k] = value ? 1 : 0;
    } else if (buildFilterType[k] === 'string') {
      where[k] = { contains: value as string };
    } else if (buildFilterType[k] === 'string[]') {
      where.tags = { array_contains: value as string[] };
    } else if (buildFilterType[k] === 'number-min') {
      where[k] = { gte: value as any };
    }
  });
  return where;
};

/**
 * 分页查询商品列表
 */
export const pageGoods = server(
  '分页查询列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters?: PageGoodsFilterType;
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      const where = buildFilterWhere(ctx.data.filters);
      const [list = [], total = 0] = await Promise.all([
        ctx.db.v_goods_with_tasks.findMany({
          where,
          skip: Math.max(0, ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
          take: ctx.data.pagination.pageSize,
          orderBy: [{ name: 'asc' }, { tmp_created_at: 'desc' }],
        }),
        ctx.db.v_goods_with_tasks.count({ where }),
      ]);

      return {
        list: [...list],
        pagination: {
          total,
          page: ctx.data.pagination.page,
          pageSize: ctx.data.pagination.pageSize,
        },
      };
    });
  },
);

/**
 * 获取单个商品信息
 */
export const getGoodsInfo = server('获取单个商品信息', async (ctx: ActionContext<{ goodsId: string }>) => {
  const goods = await ctx.db.goods.findUnique({
    where: {
      id: ctx.data.goodsId,
      tmp_deleted_at: null,
    },
  });

  if (!goods) {
    throw new Error('未找到对应商品');
  }

  return goods;
});

/**
 * 创建商品
 */
export const createGoods = server('创建商品', async (ctx: ActionContext<{ data: Prisma.goodsCreateInput }>) => {
  return await ctx.trx(async (ctx) => {
    if (ctx.data.data.status && !GOODS_STATUS.map((s) => s.key).includes(ctx.data.data.status as GoodsStatus)) {
      throw new Error(`商品状态错误，只能为${GOODS_STATUS.map((s) => s.key).join('、')}`);
    }
    const goods = await ctx.db.goods.create({ data: ctx.data.data });
    return goods;
  });
});

/**
 * 更新商品信息
 */
export const updateGoods = server(
  '更新商品信息',
  async (ctx: ActionContext<{ where: Prisma.goodsWhereUniqueInput; data: Prisma.goodsUpdateInput }>) => {
    return await ctx.trx(async (ctx) => {
      const goods = await ctx.db.goods.findUnique({ where: ctx.data.where });
      if (!goods) throw new Error('未找到对应商品');
      const updateData: Prisma.goodsUpdateArgs['data'] = ctx.data.data;
      if (updateData.status && !GOODS_STATUS.map((s) => s.key).includes(updateData.status as GoodsStatus)) {
        throw new Error(`商品状态错误，只能为${GOODS_STATUS.map((s) => s.key).join('、')}`);
      }
      return await ctx.db.goods.update({ where: { id: goods.id }, data: updateData });
    });
  },
);

/**
 * 删除商品
 */
export const removeGoods = server('删除商品', async (ctx: ActionContext<{ goodsIds: string[] }>) => {
  return await ctx.trx(async (ctx) => {
    // 检查商品是否存在
    const goods = await ctx.db.goods.findMany({
      where: {
        id: { in: ctx.data.goodsIds },
        tmp_deleted_at: null,
      },
    });

    if (goods.length === 0) {
      throw new Error('商品不存在');
    }

    // if (goods.length !== ctx.data.goodsIds.length) {
    //   throw new Error('部分商品不存在');
    // }

    // 检查商品是否被内容分发任务引用
    const referencedGoods = await ctx.db.video_distribution_tasks.findMany({
      where: {
        goods_id: { in: ctx.data.goodsIds },
        tmp_deleted_at: null,
      },
    });

    if (referencedGoods.length > 0) {
      const referencedIds = referencedGoods.map((g) => g.goods_id);
      throw new Error(`以下商品ID正在被内容分发任务引用,无法删除: ${referencedIds.join(', ')}`);
    }

    // 执行逻辑删除
    await ctx.db.goods.updateMany({
      where: { id: { in: ctx.data.goodsIds } },
      data: { tmp_deleted_at: new Date() },
    });

    return true;
  });
});

export const listGoodsByKeys = server(
  '根据多个字段查询商品',
  async (ctx: ActionContext<{ keys: Record<string, any>[] }>) => {
    return await ctx.trx(async (ctx) => {
      return await ctx.db.goods.findMany({ where: { OR: ctx.data.keys } });
    });
  },
);

export const listDuplicateGoods = server(
  '查询重复商品',
  async (ctx: ActionContext<{ onlyAbleAutoMerge?: boolean }>) => {
    const lenLessThanOne = (arr: any[] | null) => !arr || uniq(arr.filter(Boolean)).length <= 1;

    const list = await ctx.db.v_duplicate_goods.findMany();
    if (ctx.data.onlyAbleAutoMerge) {
      return list.filter((g) => {
        return (
          lenLessThanOne(g.links) &&
          lenLessThanOne(g.ips) &&
          lenLessThanOne(g.platforms) &&
          lenLessThanOne(g.short_sns) &&
          lenLessThanOne(g.product_identities) &&
          lenLessThanOne(g.product_urls) &&
          lenLessThanOne(g.shop_names) &&
          lenLessThanOne(g.commission_rates) &&
          lenLessThanOne(g.prices) &&
          lenLessThanOne(g.stock_amounts)
        );
      });
    }
    return list;
  },
);

/**
 * 合并重复商品，合并规则为：
 * 1. 删除重复商品
 * 2. 新增一个新的商品
 */
export const mergeDuplicateGoods = server(
  '合并重复商品',
  async (ctx: ActionContext<{ goodsIds: string[]; data: goods }>) => {
    return await ctx.trx(async (ctx) => {
      // 1. 删除重复商品
      await ctx.db.goods.updateMany({
        where: { id: { in: ctx.data.goodsIds } },
        data: { tmp_deleted_at: new Date() },
      });
      // 2. 新增一个新的商品
      return await ctx.db.goods.create({ data: ctx.data.data as any });
    });
  },
);

/**
 * 自动合并条件
 * 合并条件： 推广链接，IP，平台，短编号，ID，产品链接，店铺名，佣金率，价格，库存 的数量都小于等于1，即没有重复
 * 合并逻辑：除上述字段外，其他字段去重后合并
 */
export const autoMergeDuplicateGoods = server('自动合并重复商品', async (ctx) => {
  const goods = await ctx.db.v_duplicate_goods.findMany();

  const lenLessThanOne = (arr: any[] | null) => !arr || uniq(arr.filter(Boolean)).length <= 1;

  const getUniqueValues = (arr: any[] | null) => uniq(arr?.filter(Boolean) || []);
  const getUniqueValue = (arr: any[] | null) => uniq(arr?.filter(Boolean) || [])?.[0];

  const filteredGoods = goods.filter((g) => {
    return (
      lenLessThanOne(g.links) &&
      lenLessThanOne(g.ips) &&
      lenLessThanOne(g.platforms) &&
      lenLessThanOne(g.short_sns) &&
      lenLessThanOne(g.product_identities) &&
      lenLessThanOne(g.product_urls) &&
      lenLessThanOne(g.shop_names) &&
      lenLessThanOne(g.commission_rates) &&
      lenLessThanOne(g.prices) &&
      lenLessThanOne(g.stock_amounts)
    );
  });

  for (const g of filteredGoods) {
    await ctx.trx(async (ctx) => {
      // 删除已有的重复商品
      await ctx.db.goods.updateMany({
        where: { id: { in: g.ids as string[] } },
        data: { tmp_deleted_at: new Date() },
      });
      // 新增一个新的商品
      await ctx.db.goods.create({
        data: {
          name: g.name,
          tenant_id: g.tenant_id,
          alias: getUniqueValues(g.aliases),
          images: getUniqueValues(g.images),
          tags: getUniqueValues(g.tags),
          live_rooms: getUniqueValues(g.live_rooms),
          hot_product: g.hot_products?.some((hp) => hp === 1) ? 1 : 0,
          remark: g.remarks?.join(';'),
          properties: g.properties?.reduce(
            (acc, cur) => {
              if (cur && cur.key && cur.value) acc[cur.key] = cur.value;
              return acc;
            },
            {} as Record<string, any>,
          ),
          link: getUniqueValue(g.links),
          ip: getUniqueValue(g.ips),
          platform: getUniqueValue(g.platforms),
          short_sn: getUniqueValue(g.short_sns),
          product_identity: getUniqueValue(g.product_identities),
          product_url: getUniqueValue(g.product_urls),
          shop_name: getUniqueValue(g.shop_names),
          commission_rate: getUniqueValue(g.commission_rates),
          price: getUniqueValue(g.prices),
          stock_amount: getUniqueValue(g.stock_amounts),
        },
      });
    });
  }

  return {
    before: filteredGoods.reduce((acc, cur) => acc + (cur.ids?.length || 0), 0),
    after: filteredGoods,
  };
});
