import { AxiosPromise, AxiosRequestConfig } from 'axios';
import queryString from 'node:querystring';
import { request } from '../net/request';

type SessionsAllFilter = {
  type: string;
  column: string;
  key: string;
  operator: string;
  value: string;
};

type SessionsAllQuery = {
  page: number;
  limit: number;
  projectId: string;
  filter: SessionsAllFilter[];
  searchQuery: string | null;
  orderBy: { column: string; order: 'ASC' | 'DESC' };
};

type TracesAllFilter = {
  type: string;
  column: string;
  key: string;
  operator: string;
  value: string;
};

type TracesAllQuery = {
  page: number;
  limit: number;
  projectId: string;
  filter: TracesAllFilter[];
  searchQuery: string | null;
  orderBy: { column: string; order: 'ASC' | 'DESC' };
};

export type LangfuseClientParams = {
  host: string;
  credential?: {
    email: string;
    password: string;
  };
};

export class LangfuseRpc {
  private host: string;
  private credential: {
    email: string;
    password: string;
  };

  private cookies: string[] | undefined = [];

  constructor(options: LangfuseClientParams) {
    if (!options.host) {
      throw new Error('Missing required options: host');
    }
    if (!options.credential) {
      throw new Error('Missing required options: credential.email, credential.password');
    }
    this.host = options.host;
    this.credential = options.credential;
  }

  /**
   * 用于请求rpc
   * 需要通过 credentials 获取登录凭证
   * @param path
   * @returns
   */
  public async request<ResData = any, ReqData = any>(path: string, options: AxiosRequestConfig<ReqData>) {
    if (!this.cookies?.length) {
      await this.credentials();
    }
    return request({
      ...options,
      url: `${this.host}${path}`,
      headers: {
        ...options.headers,
        Cookie: this.cookies?.join('; '),
      },
    }) as AxiosPromise<ResData>;
  }

  public async tracesAll(query: TracesAllQuery) {
    const queryStr = stringify({
      batch: 1,
      input: encode(JSON.stringify({ '0': { json: query } })),
    });

    const res = await this.request<any>(`/api/trpc/traces.all?${queryStr}`, { method: 'get' });
    return res.data;
  }

  public async sessionsAll(query: SessionsAllQuery) {
    const queryStr = stringify({
      input: encode(JSON.stringify({ '0': { json: query } })),
    });

    const res = await this.request<string>(`/api/trpc/sessions.all?${queryStr}`, { method: 'get' });
    return res.data;
  }

  public async authSession() {
    const res = await this.request<{
      environment: {
        disableExpensivePostgresQueries: boolean;
        eeEnabled: boolean;
        enableExperimentalFeatures: boolean;
      };
      expires: string;
      user: {
        admin: boolean;
        canCreateOrganizations: boolean;
        email: string;
        featureFlags: { templateFlag: boolean };
        id: string;
        image: unknown;
        name: string;
        organizations: {
          id: string;
          name: string;
          plan: string;
          projects: { id: string; name: string; role: string }[];
          role: string;
        }[];
      };
    }>('/api/auth/session', { method: 'get' });
    return res.data;
  }

  public async apiKeysByProjectId(projectId: string) {
    const query = { input: JSON.stringify({ json: { projectId: projectId } }) };
    const res = await this.request<{
      result: {
        data: {
          json: {
            [key: number]: {
              createdAt: string;
              displaySecretKey: string;
              expiresAt: string;
              id: string;
              lastUsedAt: string;
              note: string;
              publicKey: string;
            };
          };
        };
      };
    }>(`/api/trpc/apiKeys.byProjectId?${queryString.stringify(query)}`, { method: 'get' });
    return res.data;
  }

  /**
   * 刷新 csrfToken
   */
  private async getCsrfToken() {
    const res = await request.get<{ csrfToken: string }>(`${this.host}/api/auth/csrf`).catch((err) => {
      throw new Error(`Failed to refresh csrfToken: ${err.message}`);
    });
    const cookies = res.headers['set-cookie'];
    this.cookies = cookies;
    return res.data.csrfToken;
  }

  /**
   * 获取登录凭证
   */
  private async credentials() {
    const csrfToken = await this.getCsrfToken();
    if (!csrfToken) {
      throw new Error('Failed to get csrfToken');
    }

    const params = new URLSearchParams();
    params.append('email', this.credential.email);
    params.append('password', this.credential.password);
    params.append('callbackUrl', '/');
    params.append('redirect', String(false));
    params.append('turnstileToken', 'undefined');
    params.append('csrfToken', csrfToken);
    params.append('json', String(true));

    const res = await request<{ url: string }>(`${this.host}/api/auth/callback/credentials`, {
      method: 'post',
      data: params,
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: this.cookies?.join('; '),
      },
    }).catch((err) => {
      throw new Error(`Failed to get credentials: ${err.message}`);
    });
    const cookies = res.headers['set-cookie'];
    this.cookies = cookies;
  }
}

/**
 * 数据转码
 * langfuse的 trpc 中请求时，需要将请求数据进行转码。
 * 由于只有特定字符需要转码，而非标准的 encodeURIComponent，所以这里使用了自定义的转码函数。
 */
function encode(str: string) {
  return str.replace(/"/g, '%22').replace(/{/g, '%7B').replace(/}/g, '%7D');
}

/**
 * query参数组装
 * @param obj
 */
function stringify(obj: any) {
  return Object.entries(obj)
    .map(([key, value]) => `${key}=${String(value)}`)
    .join('&');
}
