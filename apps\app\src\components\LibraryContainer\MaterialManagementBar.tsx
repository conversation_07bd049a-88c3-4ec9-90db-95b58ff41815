'use client';

import CreateDirModal from '@/components/CreateDirModal';
import { DirModal } from '@/components/DirModal';
import { Cancel, Checkbox, RadiusClose } from '@/components/icon';
import { Button, Input, Overlay } from '@/components/ui';
import DirContext from '@/context/directory';
import { useBatchDownload } from '@/hooks/useBatchDownload';
import { MaterialItemType } from '@/hooks/useMaterial';
import { useRoots } from '@/hooks/useRoots';
import { useCloudStorageSize } from '@/hooks/useWallet';
import {
  batchMoveMaterialsAndDirs,
  batchRemoveMaterialsAndDirs,
  getAllMaterialsInDirectory,
} from '@/services/actions/materials';
import useMaterialStore from '@/store/materialStore';
import useSystemStore from '@/store/system';
import { DirItemType } from '@/types/material';
import { action } from '@/utils/server-action/action';
import { to } from '@roasmax/utils';
import { debounce } from 'lodash';
import { Download, Move, Plus, Search } from 'lucide-react';
import { useContext, useMemo } from 'react';
import toast from 'react-hot-toast';
import { ConfirmDeleteDialog } from './ConfirmDeleteDialog';

const MaterialManagementBar = ({
  disabled,
  refreshCurrentPage,
  restoreAllCheckedStatus,
  onSearch,
  materialList,
  removeCheckedItem,
  currParentId,
  dirList,
  handleUploadClick,
  searchTerm,
  setSearchTerm,
  config = {
    upload: true,
    search: true,
    batch: true,
    createDir: true,
    select: false,
  },
}: {
  disabled: boolean;
  refreshCurrentPage: () => void;
  currParentId: string | undefined;
  onSearch: (keyword: string) => void;
  materialList: MaterialItemType[];
  removeCheckedItem: () => void;
  restoreAllCheckedStatus: () => void;
  dirList: DirItemType[];
  handleUploadClick: () => void;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  config: {
    upload: boolean;
    search: boolean;
    batch: boolean;
    createDir: boolean;
    select: boolean;
  };
}) => {
  const { refresh: refreshStorage } = useCloudStorageSize();
  const { mask, setMask } = useSystemStore();
  const { setDirModalOpen, batchMode, setBatchMode, cloudTab } = useMaterialStore();
  const { downloadAndZipVideos, isDownloading } = useBatchDownload();
  const { createDir } = useContext(DirContext);
  const { data: roots } = useRoots();
  const currRoot = roots?.find((item) => item.name === (cloudTab === 1 ? '原始素材' : '生成素材'));
  const handleCreateDir = async (name: string) => {
    await createDir({ name, parentId: currParentId ?? '' });
    refreshCurrentPage();
  };

  const debouncedSearch = useMemo(() => debounce(onSearch, 300), [onSearch]);
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value); // 更新搜索输入状态
    debouncedSearch(value); // 调用防抖搜索
  };
  const clearSearch = () => {
    setSearchTerm(''); // 清空搜索输入状态
    onSearch(''); // 清空搜索
  };
  const handleDownload = async () => {
    if (isDownloading) return;

    const toastId = toast.loading('文件校验中...');

    try {
      // 获取所有选中的文夹
      const selectedDirs = dirList.filter((item) => item.checked);
      const selectedMaterialList = materialList.filter((item) => item.checked);

      // 获取所有选中文件夹中的素材
      const dirMaterialsPromises =
        selectedDirs?.map((dir) =>
          action(getAllMaterialsInDirectory, {
            directoryId: dir.id ?? '',
          }),
        ) ?? [];

      const dirMaterialsResults = await Promise.all(dirMaterialsPromises);
      const dirMaterials = dirMaterialsResults.flatMap((data) => data?.list ?? []);

      // 合并直接选中的素材和文件夹中的素材
      const totalList = [...selectedMaterialList, ...dirMaterials];

      if (!totalList.length) {
        toast.dismiss(toastId);
        toast.error('未找到可下载的素材');
        return;
      }
      const totalSize = totalList?.reduce((acc, curr) => acc + Number(curr.size), 0);

      if (totalSize && totalSize > 1024 * 1024) {
        toast.dismiss(toastId);
        toast.error('文件大小超过1GB');
        return;
      }

      toast.loading('下载中...', { id: toastId });
      await downloadAndZipVideos(totalList);
      toast.success('下载完成', { id: toastId });
    } catch (error) {
      toast.dismiss(toastId);
      toast.error('下载失败');
    } finally {
      restoreAllCheckedStatus();
      toast.remove(toastId);
    }
  };

  const handleMove = async (id: string) => {
    const targetDirId = id === 'root' ? (currRoot?.id ?? '') : id;
    const checkedDirList = dirList?.filter((item) => item.checked);
    const checkedMaterialList = materialList.filter((item) => item.checked);

    if (!checkedDirList.length && !checkedMaterialList.length) return;

    const hasItself = checkedDirList?.some((item) => {
      return item.id === targetDirId;
    });

    // 校验是否存在套娃
    if (hasItself) {
      toast.error('无法移动到自身目录');
      return;
    }

    setMask(true);
    const toastId = toast.loading('移动中...');

    try {
      const [err, res] = await to(
        action(
          batchMoveMaterialsAndDirs,
          {
            materialIds: checkedMaterialList.map((item) => item.id),
            directoryIds: checkedDirList.map((item) => item.id!),
            targetDirectoryId: targetDirId ?? '',
          },
          {
            errorType: 'return',
          },
        ),
      );

      if (err || !res?.success) {
        toast.dismiss(toastId);
        toast.error(`移动失败, ${res?.message}`);
        return;
      }

      toast.success('移动成功');
      setMask(false);
      removeCheckedItem();
      setDirModalOpen(false);
      setBatchMode(false);
      refreshCurrentPage();
    } catch (error) {
      toast.error('移动失败');
    } finally {
      toast.dismiss(toastId);
      setMask(false);
    }
  };

  const handleDelete = async () => {
    const selectedDirList = dirList.filter((item) => item.checked);
    const selectedMaterialList = materialList.filter((item) => item.checked);
    const isContainUsage = selectedMaterialList.some((item) => item.usage);

    if (isContainUsage) {
      toast.error('使用中的素材无法删除');
      setBatchMode(true);
      return;
    }

    setMask(true);
    const toastId = toast.loading('文件删除中...');

    try {
      const [err, res] = await to(
        action(
          batchRemoveMaterialsAndDirs,
          {
            materialIds: selectedMaterialList.map((item) => item.id),
            directoryIds: selectedDirList.map((each) => each.id).filter((id) => id) as string[],
          },
          {
            errorType: 'return',
          },
        ),
      );

      if (err || !res?.success) {
        toast.error(`删除失败, ${res?.message}`);
        setMask(false);
        return;
      }

      toast.dismiss(toastId);
      toast.success('删除成功');
      removeCheckedItem();
      setBatchMode(false);
      refreshCurrentPage();
    } catch (error) {
      toast.dismiss(toastId);
      toast.error('删除失败');
    } finally {
      setMask(false);
      refreshStorage();
    }
  };

  const selectedCount =
    materialList?.filter((item) => item.checked).length + dirList?.filter((item) => item.checked).length;

  if (!config.batch) return null;

  return (
    <>
      <div className="mb-6 flex h-8 w-full items-center justify-between gap-3 pr-8">
        <div className="flex items-center gap-3">
          {config.upload && (
            <>
              <Button
                disabled={batchMode || disabled}
                onClick={handleUploadClick}
                className="h-9 rounded-md px-4 text-xs font-medium text-[#050A1C]"
              >
                <Plus className="mr-1.5 h-3 w-3 flex-shrink-0" />
                上传文件
              </Button>
            </>
          )}
          {config.createDir && <CreateDirModal disabled={batchMode || disabled} onConfirm={handleCreateDir} />}
          {config.search && (
            <div className="relative w-[250px]">
              <Search className="text-muted-foreground absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2" />
              <Input
                type="search"
                className="h-9 w-full rounded-md bg-[#CCDDFF] bg-opacity-10 pl-8 text-xs font-medium shadow-none ring-offset-0 placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A] dark:ring-offset-0"
                placeholder="请输入名称查找"
                value={searchTerm} // 绑定输入框的值
                onChange={handleSearchChange} // 更新输入变化处理
              />
              {searchTerm && (
                <div
                  onClick={clearSearch} // 清空搜索的处理
                  className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-sm text-gray-500"
                >
                  <RadiusClose />
                </div>
              )}
            </div>
          )}
          {config.batch && (
            <div
              onClick={() => {
                setBatchMode(!batchMode);
              }}
            >
              {batchMode ? (
                <Button className="flex h-9 items-center rounded-md bg-[#CCDDFF] bg-opacity-10 px-4 text-xs font-medium text-[#9FA4B2] hover:bg-[#CCDDFF33]">
                  <Cancel />
                  <div className="ml-1.5">退出批量</div>
                </Button>
              ) : (
                <Button className="flex h-9 items-center rounded-md bg-[#CCDDFF] bg-opacity-10 px-4 text-xs font-medium text-[#9FA4B2] hover:bg-[#CCDDFF33]">
                  <Checkbox />
                  <div className="ml-1.5">批量操作</div>
                </Button>
              )}
            </div>
          )}
        </div>
        {batchMode && (
          <div className="absolute bottom-[16px] left-[252px] right-12 z-20 h-[65px] border-b-[1px] bg-[#070F1F]">
            <div className="flex h-12 items-center justify-between rounded-lg bg-[#CCDDFF1A] px-3">
              <div>已选中 {selectedCount} 项</div>
              <div>
                <ConfirmDeleteDialog selectedCount={selectedCount} handleDelete={handleDelete} />
                <Button
                  onClick={() => {
                    setDirModalOpen(true);
                  }}
                  disabled={!selectedCount}
                  className="ml-2 h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 px-6 text-xs font-medium text-white hover:bg-[#CCDDFF33]"
                >
                  <Move className="mr-1.5 h-3 w-3 flex-shrink-0" />
                  <div>移动到</div>
                </Button>
                <Button
                  disabled={!selectedCount}
                  onClick={handleDownload}
                  className="ml-2 h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 px-6 text-xs font-medium text-white hover:bg-[#CCDDFF33]"
                >
                  <Download className="mr-1.5 h-3 w-3 flex-shrink-0" />
                  下载
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      <DirModal
        showTrigger={false}
        onConfirm={handleMove}
        disabledDir={currParentId === currRoot?.id ? 'root' : (currParentId ?? '')}
        mode="move"
      />
      {isDownloading && <Overlay />}
      {mask && <Overlay zIndex="z-[55]" />}
    </>
  );
};

export default MaterialManagementBar;
