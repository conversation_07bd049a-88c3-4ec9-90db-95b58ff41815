import { ActionContext, ActionContextPluginLoader } from '../../types';

type LoaderPlugin = <T>(options: { key: string; reload?: boolean }, loader: () => Promise<T>) => Promise<T>;

/**
 * 在同一个上下文中，缓存异步加载的数据
 * @param ctx 上下文
 * @param options key: 缓存的key, reload: 是否重新加载
 * @param loader 加载器，具体的加载逻辑
 * @returns
 */
async function contextSourceLoader<T>(
  ctx: ActionContext<any>,
  options: { key: string; reload?: boolean },
  loader: () => Promise<T>,
) {
  if (!(ctx as any).__cache) {
    (ctx as any).__cache = {};
  }
  const cache = (ctx as any).__cache;

  if (!cache[options.key] || options.reload) {
    cache[options.key] = await loader();
  }
  return cache[options.key] as T;
}

const loaderPlugin: ActionContextPluginLoader<'loader', LoaderPlugin> = (context) => {
  return {
    name: 'loader',
    plugin: (options, loader) => {
      if (Object.keys(context).includes(options.key)) {
        throw new Error(`key ${options.key} is reserved`);
      }
      return contextSourceLoader(context, options, loader);
    },
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 在同一个上下文中，缓存异步加载的数据
     * @param options key: 缓存的key, reload: 是否重新加载
     * @param loader 加载器，具体的加载逻辑
     * @returns 加载的数据
     */
    loader: LoaderPlugin;
  }
}

export default loaderPlugin;
