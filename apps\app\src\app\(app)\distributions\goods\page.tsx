'use client';

import { confirm } from '@/components/ConfirmDialog';
import { DataImportDialog, DataImportDialogRef } from '@/components/DataImportDialog';
import { ProColumnType } from '@/components/pro/pro-column';
import { ProFilter } from '@/components/pro/pro-filter';
import { ProFormField } from '@/components/pro/pro-form';
import { DEFAULT_PAGINATION, ProPagination } from '@/components/pro/pro-pagination';
import ProTable, { ProTableProps } from '@/components/pro/pro-table';
import { Button, Calendar, Form, Panel, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { usePropertyDefinitions } from '@/hooks/usePropertyDefinitions';
import { createGoods, listGoodsByKeys, pageGoods, updateGoods } from '@/services/actions/goods';
import { createVideoDistributionTasksByGoods } from '@/services/actions/video-distribution-task';
import { cn } from '@/utils/cn';
import { action, ActionParams, ActionResult, useAction } from '@/utils/server-action/action';
import { goods } from '@roasmax/database';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { cloneDeep } from 'lodash';
import { CalendarIcon } from 'lucide-react';
import { useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { MergeDuplicatedGoodsDialog, MergeDuplicatedGoodsDialogRef } from './components/MergeDuplicatedGoodsDialog';
import { createInbuiltColumns, filterColumns } from './config';

dayjs.extend(utc);
dayjs.extend(timezone);

export default function Distributions() {
  const definitions = usePropertyDefinitions('goods');
  const dataImportDialogRef = useRef<DataImportDialogRef>(null);
  const mergeDuplicatedGoodsDialogRef = useRef<MergeDuplicatedGoodsDialogRef>(null);

  const [pagination, setPagination] = useState<{ page: number; pageSize: number }>(DEFAULT_PAGINATION);
  const [filters, setFilters] = useState<ActionParams<typeof pageGoods>['filters']>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<Set<keyof goods>>(new Set());

  const confirmSubmitForm = useForm<{ date: Date }>({ defaultValues: { date: new Date() } });

  const columns = useMemo(() => {
    const inbuilt = createInbuiltColumns({ onRefreshNeeded: () => setFilters({}) });
    const custom: ProColumnType<ActionResult<typeof pageGoods>['list'][number]>[] = definitions.definitions.map(
      (item) => ({
        key: item.name,
        dataIndex: ['properties', item.name],
        title: item.display_name,
      }),
    );
    return [...inbuilt, ...custom];
  }, [definitions.definitions]);

  const { data, loading, mutate } = useAction(pageGoods, { pagination, filters });

  const handleRecordEditSubmit: NonNullable<
    ProTableProps<ActionResult<typeof pageGoods>['list'][number]>['onEdit']
  > = async (v, record, col) => {
    const colKey = col.key as keyof goods;
    if (!colKey) return;
    const res = await action(updateGoods, {
      where: { id: record.id },
      data: { [colKey]: v },
    });
    if (!res) return;
    toast.success('修改成功');
    // 更新数据
    mutate((prev) => {
      if (!prev) return prev;
      const next = cloneDeep(prev);
      next.list = next.list.map((item) => (item.id === record.id ? { ...item, [colKey]: v } : item));
      return next;
    });
  };

  const handleSubmitProcessTask = async () => {
    confirm({
      form: confirmSubmitForm,
      content: ({ form }) => {
        if (!form) return null;
        return (
          <div>
            <div>确定开启任务吗？</div>
            <Form {...form}>
              <form>
                <ProFormField
                  name="date"
                  form={form}
                  renderFormControl={(field) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={'outline'}
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !field.value && 'text-muted-foreground',
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? dayjs(field.value).format('YYYY-MM-DD') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto bg-[#262F40] p-0">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(day) => field.onChange(day)}
                          className={cn(
                            'rounded-[8px] bg-[#CCDDFF1A] font-normal file:border-0 placeholder:font-normal placeholder:text-[#9FA4B2] focus:border-[#00E1FF]',
                          )}
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                />
              </form>
            </Form>
          </div>
        );
      },
      onConfirm: async (formData) => {
        if (!formData?.date) {
          toast.error('请选择日期');
          return;
        }
        // 按照东八区日期作为分发批次号
        const batchNo = dayjs(formData?.date).tz('Asia/Shanghai').format('YYYY-MM-DD');
        const success = await action(createVideoDistributionTasksByGoods, {
          goodsIdList: Array.from(selectedRowKeys),
          batchNo,
        });
        if (success) {
          setSelectedRowKeys(new Set());
        }
      },
    });
  };

  return (
    <div className="h-[100%] w-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter
          value={filters}
          onSubmit={(f) => {
            setPagination(DEFAULT_PAGINATION);
            setFilters(f);
          }}
          columns={filterColumns}
          className="mb-2"
        />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="text-base font-bold">商品资产</div>
          <div className="flex items-center justify-end gap-2">
            <Button
              disabled={selectedRowKeys.size === 0}
              className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={handleSubmitProcessTask}
            >
              开启任务
            </Button>
            <Button
              className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={() => {
                dataImportDialogRef.current?.show({
                  templateResourceName: 'goods',
                  request: async (data, schema) => {
                    const goods = await action(listGoodsByKeys, {
                      keys: data.map((item) => {
                        return schema.uniqueKeys.reduce((acc, key) => {
                          acc[key] = item[key];
                          return acc;
                        }, {} as any);
                      }),
                    });
                    return goods || [];
                  },
                  onSubmit: async (data) => {
                    // 依次新建商品
                    for (const item of data.newData) {
                      await action(createGoods, { data: item });
                    }
                    for (const item of data.updateData) {
                      const data = (item.__diff__ as string[]).reduce((acc: Record<string, any>, key: string) => {
                        acc[key] = item[key];
                        return acc;
                      }, {});
                      await action(updateGoods, { where: { id: item.id }, data });
                    }
                    return true;
                  },
                });
              }}
            >
              导入数据
            </Button>
            <Button
              className="h-[32px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={() => mergeDuplicatedGoodsDialogRef.current?.open()}
            >
              合并重复商品
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable
            columns={columns}
            loading={loading}
            dataSource={data?.list || []}
            className="h-full"
            onEdit={handleRecordEditSubmit}
            selection={{
              selectedRowKeys: selectedRowKeys,
              onSelect: async (k, checked) => {
                const newSelected = new Set(selectedRowKeys);
                if (checked) {
                  newSelected.add(k);
                } else {
                  newSelected.delete(k);
                }
                setSelectedRowKeys(newSelected);
              },
              onSelectAll: () => {
                if (selectedRowKeys.size === data?.list.length) {
                  setSelectedRowKeys(new Set());
                } else {
                  setSelectedRowKeys(new Set(data?.list.map((row) => row.id as keyof goods)));
                }
              },
            }}
          />
        </div>
        <div className="mt-1">
          <ProPagination
            pagination={{ ...pagination, total: data?.pagination.total }}
            onPaginationChange={setPagination}
          />
        </div>
      </Panel>
      <DataImportDialog ref={dataImportDialogRef} />
      <MergeDuplicatedGoodsDialog ref={mergeDuplicatedGoodsDialogRef} />
    </div>
  );
}
