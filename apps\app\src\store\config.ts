import { getAuthingAppId } from '@/services/actions/config';
import { useEffect, useState } from 'react';
import { create } from 'zustand';

const useConfigStore = create<{
  appId?: string;
  setAppId: (appId: string) => void;
}>((set) => ({
  appId: undefined,
  setAppId: (appId: string) => set({ appId }),
}));

export const useAuthingAppId = () => {
  const config = useConfigStore();
  const [appId, setAppId] = useState(config.appId);

  useEffect(() => {
    if (!appId) {
      getAuthingAppId().then((res) => {
        if (!res) return;
        config.setAppId(res);
        setAppId(res);
      });
    }
  });
  return appId;
};
