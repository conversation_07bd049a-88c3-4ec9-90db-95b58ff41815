'use strict';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';
import { EventPayload, Handler } from '..';
import { prisma } from '../utils/prisma';

export class RemoveForDistributedHandler implements Handler {
  async handle(record: EventPayload['Records'][0]) {
    console.log('开始处理文件【自动分发-分发素材】', record.cos.cosObject.key);
    console.log('----------------------------');

    // 获取文件相对于bucket下的key
    const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

    const keyArr = key.split('/');
    if (keyArr.length < 5) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }

    const [, tenantId, , , ...distributed] = keyArr;
    if (!tenantId || !distributed.length) {
      console.log('文件名格式不正确，不处理', key);
      return;
    }

    console.log('接收到删除待分发的视频', JSON.stringify({ tenantId, fileName: distributed }));

    const distributedPath = `roasmax/${tenantId}/自动分发/分发素材/${distributed.join('/')}`;

    // 软删除相关的分发任务
    await prisma.video_distribution_sub_tasks.updateMany({
      where: { distributed_cos_path: distributedPath, tmp_deleted_at: null },
      data: {
        tmp_deleted_at: new Date(),
        status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已取消,
        status_desc: '文件已被删除，任务取消',
      },
    });
    console.log('已删除相关的已分发任务');
  }
}
