import { buildSocialAccountColumns } from '@/app/(app)/distributions/social-accounts/config';
import {
  assignVideoDistributionTask,
  getSocialAccountsByIps,
  getVideoDistributionSubTasksByTaskIds,
  listVideoDistributionTasks,
  withdrawVideoDistributionTask,
} from '@/services/actions/video-distribution-task';
import { cn } from '@/utils/cn';
import { action, useAction } from '@/utils/server-action/action';
import React, { useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { toast } from 'react-hot-toast';
import { confirm } from '../ConfirmDialog';
import { Loader } from '../icon/loader';
import ProTable from '../pro/pro-table';
import { Badge, Button, Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '../ui';
import { ProImage } from '../pro/pro-image';
import { ProSelect } from '../pro/pro-select';
import { calcDailyVideoCountByFansCount } from '@roasmax/utils';

export interface VideoDistributionDialogProps {}

export interface VideoDistributionDialogRef {
  open: (params: { taskIds: string[] }) => void;
}

export const VideoDistributionDialog = React.forwardRef<VideoDistributionDialogRef, VideoDistributionDialogProps>(
  (props, ref) => {
    const [open, setOpen] = useState(false);
    const [taskIds, setTaskIds] = useState<string[]>([]);
    const [selectedIp, setSelectedIp] = useState<string>('_');
    const [expandedProducts, setExpandedProducts] = useState<Set<string>>(new Set());

    const {
      data: tasks,
      loading: tasksLoading,
      run: fetchTasks,
    } = useAction(listVideoDistributionTasks, {
      taskIds: taskIds,
    });

    const {
      data: subTasks,
      loading: subTasksLoading,
      run: fetchSubTasks,
    } = useAction(getVideoDistributionSubTasksByTaskIds, {
      taskIds: taskIds,
    });

    const {
      data: socialAccounts,
      loading: socialAccountsLoading,
      run: fetchSocialAccounts,
    } = useAction(
      getSocialAccountsByIps,
      {
        ips: tasks?.map((t) => t.goods.ip!),
        distributionBatchNo: tasks?.[0]?.distribution_batch_no || '',
      },
      { skip: (data) => !data.ips?.length || !data.distributionBatchNo },
    );

    const ipList = useMemo(() => {
      return [...new Set(tasks?.map((t) => t.goods.ip!))];
    }, [tasks]);

    const toggleProduct = (productId: string) => {
      setExpandedProducts((prev) => {
        const next = new Set(prev);
        if (next.has(productId)) {
          next.delete(productId);
        } else {
          next.add(productId);
        }
        return next;
      });
    };

    useImperativeHandle(ref, () => ({
      open: (params) => {
        setOpen(true);
        setTaskIds(params.taskIds);
      },
    }));

    const handleConfirm = useCallback(async () => {
      confirm({
        content: '确定分发',
        onConfirm: async () => {
          const result = await action(assignVideoDistributionTask, { taskIds }, { errorType: 'return' });
          if (result.success) {
            toast.success('分发成功');
            fetchTasks();
            fetchSubTasks();
            fetchSocialAccounts();
          } else {
            toast.error(result.message);
          }
        },
      });
    }, [taskIds]);

    const handleWithdraw = useCallback(
      async (socialAccountId: string) => {
        confirm({
          content: '确定撤回',
          onConfirm: async () => {
            const success = await action(withdrawVideoDistributionTask, { taskIds, socialAccountId });
            if (success) {
              toast.success('撤回成功');
              fetchTasks();
              fetchSubTasks();
              fetchSocialAccounts();
            }
          },
        });
      },
      [taskIds],
    );

    return (
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          if (!open) {
            setSelectedIp('_');
            setTaskIds([]);
          }
        }}
      >
        <DialogContent className="max-w-[80vw]">
          <DialogHeader className="flex flex-row items-center gap-2">
            <DialogTitle>分发任务</DialogTitle>
          </DialogHeader>
          <div className="flex flex-row items-center gap-2">
            <div>IP</div>
            <ProSelect
              className="mt-0 h-[32px] w-[200px]"
              value={selectedIp}
              options={ipList.map((ip) => ({ value: ip, label: ip })).concat({ value: '_', label: '全部' })}
              onValueChange={(value) => setSelectedIp(value)}
            />
          </div>

          <div className="flex h-[500px] gap-1 overflow-auto">
            {tasksLoading || subTasksLoading || socialAccountsLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Loader />
              </div>
            ) : (
              <div className="flex-1">
                {selectedIp && (
                  <div className="flex h-full gap-1">
                    {/* 商品和视频列表 */}
                    <div className="flex-1">
                      <h3 className="mb-2 font-medium">商品列表</h3>
                      <div className="h-[calc(100%-40px)] space-y-1 overflow-y-auto">
                        {tasks
                          ?.filter((t) => selectedIp === '_' || t.goods.ip === selectedIp)
                          .map((task) => (
                            <div key={task.id}>
                              <div key={task.goods.name} className="rounded-lg border p-1">
                                <div
                                  className="flex cursor-pointer items-center justify-between p-1 hover:text-[#00e1ff]"
                                  onClick={() => toggleProduct(task.goods.name)}
                                >
                                  <div className="flex-1">
                                    <div>{task.goods.name}</div>
                                  </div>
                                  <div className="ml-4 text-center">
                                    <div className="text-sm text-gray-500">已发</div>
                                    <div className="font-medium">{task.distributed_sub_tasks.length || 0}</div>
                                  </div>
                                  <div className="ml-4 text-center">
                                    <div className="text-sm text-gray-500">计划</div>
                                    <div className="font-medium">
                                      {typeof task.plan_count === 'number' ? task.plan_count : '-'}
                                    </div>
                                  </div>
                                  <div className="ml-4 text-center">
                                    <div className="text-sm text-gray-500">可用</div>
                                    <div className="font-medium">
                                      {
                                        subTasks?.filter(
                                          (st) => st.ip === task.goods.ip && st.goods_name === task.goods.name,
                                        ).length
                                      }
                                    </div>
                                  </div>
                                </div>
                                <ProTable
                                  className={cn(
                                    'h-[calc(100%-40px)] px-2',
                                    expandedProducts.has(task.goods.name)
                                      ? 'h-auto opacity-100'
                                      : 'h-0 overflow-hidden opacity-0',
                                  )}
                                  columns={[
                                    {
                                      key: 'cover',
                                      title: '封面',
                                      render: (_, record) => {
                                        return (
                                          <ProImage
                                            className="h-[64px] w-[64px] object-cover"
                                            src={record.material?.vod_cover_url || ''}
                                            alt="cover"
                                            width={100}
                                            height={100}
                                          />
                                        );
                                      },
                                    },
                                    {
                                      width: 500,
                                      key: 'material_name',
                                      title: '视频名称',
                                      render: (_, record) => {
                                        return (
                                          <div className="text-wrap break-all">
                                            <div>{record.material?.name || '未命名视频'}</div>
                                          </div>
                                        );
                                      },
                                    },
                                  ]}
                                  dataSource={
                                    subTasks?.filter(
                                      (st) => st.ip === task.goods.ip && st.goods_name === task.goods.name,
                                    ) || []
                                  }
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>

                    {/* 账号列表 */}
                    <div className="w-[600px]">
                      <h3 className="mb-2 font-medium">账号列表</h3>
                      <ProTable
                        className="h-[calc(100%-40px)] w-full"
                        columns={[
                          { key: 'ip', title: 'IP', editable: true },
                          { key: 'nickname', title: '昵称' },
                          { key: 'fans_count', title: '平台粉丝数' },
                          {
                            key: 'daily_should_distribute',
                            title: '当日应发',
                            render: (_, record) => {
                              const count = calcDailyVideoCountByFansCount(record.fans_count);
                              return <div className="w-full text-right">{count}</div>;
                            },
                          },
                          {
                            key: 'daily_distributed',
                            title: '当日已发',
                            render: (_, record) => {
                              return (
                                <div className="w-full text-right">{record.today_distribution_sub_tasks.length}</div>
                              );
                            },
                          },
                          {
                            key: 'action',
                            title: '操作',
                            render: (_, record) => {
                              return (
                                <Button variant="link" onClick={() => handleWithdraw(record.id)}>
                                  撤回
                                </Button>
                              );
                            },
                          },
                        ]}
                        dataSource={(socialAccounts || []).filter(
                          (item) => selectedIp === '_' || item.ip === selectedIp,
                        )}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button onClick={handleConfirm} className="h-[32px] text-[#050A1C] hover:text-[#050A1C]">
              确定分发
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  },
);
