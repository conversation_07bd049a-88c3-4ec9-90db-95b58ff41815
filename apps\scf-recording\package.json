{"name": "@roasmax/scf-recording", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"dev": "tsx watch src/index.ts", "build": "ncc build src/index.ts -o dist --minify", "start": "node dist/index.js", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@roasmax/database": "workspace:*", "@roasmax/scf-web-server": "workspace:*", "@roasmax/utils": "workspace:*", "axios": "^1.7.9"}, "devDependencies": {"@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*", "@types/fluent-ffmpeg": "^2.1.27", "@types/lodash": "^4.17.12"}}