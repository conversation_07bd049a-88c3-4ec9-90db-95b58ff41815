import pLimit from 'p-limit';
import { useCallback, useEffect, useRef, useState } from 'react';

const useEventQueue = () => {
  const [eventQueue, setEventQueue] = useState<Array<Promise<unknown>>>([]);
  const limitRef = useRef(pLimit(1));

  const clearQueue = useCallback(() => {
    setEventQueue([]);
  }, []);

  const addEvent = useCallback((event: () => Promise<unknown>) => {
    setEventQueue((prev) => {
      return [
        ...prev,
        limitRef.current(() => {
          return new Promise((resolve) => {
            event()
              .then(() => {
                resolve(true);
              })
              .catch((e) => {
                console.error(e);
                resolve('');
              });
          });
        }),
      ];
    });
  }, []);

  useEffect(() => {
    async function func() {
      if (!eventQueue.length) return;

      await Promise.all(eventQueue)
        .then(() => {
          setEventQueue([]);
        })
        .catch((e) => {
          console.error(e);
        });
    }

    func();
  }, [eventQueue]);

  return { addEvent, clearQueue };
};

export { useEventQueue };
