SELECT
  `g`.`id` AS `id`,
  `g`.`tmp_created_at` AS `tmp_created_at`,
  `g`.`tmp_updated_at` AS `tmp_updated_at`,
  `g`.`tmp_deleted_at` AS `tmp_deleted_at`,
  `g`.`tenant_id` AS `tenant_id`,
  `g`.`out_key` AS `out_key`,
  `g`.`name` AS `name`,
  `g`.`status` AS `status`,
  `g`.`alias` AS `alias`,
  `g`.`images` AS `images`,
  `g`.`tags` AS `tags`,
  `g`.`properties` AS `properties`,
  `g`.`live_rooms` AS `live_rooms`,
  `g`.`ip` AS `ip`,
  `g`.`platform` AS `platform`,
  `g`.`short_sn` AS `short_sn`,
  `g`.`product_identity` AS `product_identity`,
  `g`.`product_url` AS `product_url`,
  `g`.`hot_product` AS `hot_product`,
  `g`.`shop_name` AS `shop_name`,
  `g`.`commission_rate` AS `commission_rate`,
  `g`.`remark` AS `remark`,
  `g`.`price` AS `price`,
  `g`.`stock_amount` AS `stock_amount`,
  `g`.`link` AS `link`,
  `g`.`links` AS `links`,
  cast(
    count(
      (
        CASE
          WHEN (
            `vdst`.`status` = '已成片'
          ) THEN `vdst`.`id`
        END
      )
    ) AS signed
  ) AS `video_distribution_sub_tasks_wait_count`,
  cast(
    count(
      (
        CASE
          WHEN (`vdst`.`status_desc` = '已出片') THEN `vdst`.`id`
        END
      )
    ) AS signed
  ) AS `video_distribution_sub_tasks_generated_count`,
  cast(
    count(
      (
        CASE
          WHEN (
            (`vdst`.`batch_no` IS NOT NULL)
            AND (cast(`vdst`.`live_session` AS date) = curdate())
          ) THEN `vdst`.`id`
        END
      )
    ) AS signed
  ) AS `video_distribution_sub_tasks_done_count`
FROM
  (
    `slice_bak`.`goods` `g`
    LEFT JOIN `slice_bak`.`video_distribution_sub_tasks` `vdst` ON(
      (
        (`g`.`tenant_id` = `vdst`.`tenant_id`)
        AND (`g`.`ip` = `vdst`.`ip`)
        AND (`g`.`name` = `vdst`.`goods_name`)
        AND (
          (
            `vdst`.`status` IN (
              '已成片',
              '已出片'
            )
          )
          OR (`vdst`.`batch_no` IS NOT NULL)
        )
        AND (`vdst`.`tmp_deleted_at` IS NULL)
      )
    )
  )
WHERE
  (`g`.`tmp_deleted_at` IS NULL)
GROUP BY
  `g`.`id`