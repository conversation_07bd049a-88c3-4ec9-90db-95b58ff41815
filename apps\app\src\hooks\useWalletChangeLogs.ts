import { useState, useCallback, useEffect } from 'react';
import { action } from '@/utils/server-action/action';
import { getQuotaChangeLogs } from '@/services/actions/pricing_plans';

type WalletChangeLog = {
  id: string;
  tmp_updated_at: Date;
  change_type: string;
  change_reason: string;
  quota: number;
  result_quota: number;
}[];

export function useWalletChangeLogs() {
  const [walletList, setWalletList] = useState<WalletChangeLog>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [noMoreData, setNoMoreData] = useState(false);
  const [activeButton, setActiveButton] = useState('all');
  const [loading, setLoading] = useState(false);

  const refreshWalletChangeLog = useCallback(async (currentPage: number, type: string) => {
    setLoading(true);
    const params = { pagination: { page: currentPage, limit: 24, type } };
    try {
      const changelogs = await action(getQuotaChangeLogs, params);
      if (changelogs?.data.length) {
        if (currentPage === 1) {
          // 如果是第一页，替换整个列表
          setWalletList(changelogs.data);
        } else {
          // 如果不是第一页，追加到现有列表
          setWalletList((prev) => [...prev, ...changelogs.data]);
        }
        setPage(currentPage);
        setNoMoreData(false);
        setHasMore(changelogs.data.length === 24);
      } else {
        if (currentPage === 1) {
          // 如果是第一页且没有数据，清空列表
          setWalletList([]);
        }
        setHasMore(false);
        setNoMoreData(true);
      }
    } catch (error) {
      console.error('获取钱包变更日志失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleButtonClick = useCallback(
    (type: string) => {
      setActiveButton(type);
      setWalletList([]); // 清空钱包列表
      setPage(1); // 重置页码为1

      // 重置滚动位置
      setTimeout(() => {
        // 获取点数明细的滚动容器
        const scrollContainer = document.getElementById('wallet-scroll-container');
        if (scrollContainer) {
          scrollContainer.scrollTop = 0;
        }
        // 获取新数据
        refreshWalletChangeLog(1, type);
      }, 0);
    },
    [refreshWalletChangeLog],
  );

  useEffect(() => {
    refreshWalletChangeLog(1, activeButton);
  }, [activeButton, refreshWalletChangeLog]);

  return {
    walletList,
    page,
    hasMore,
    noMoreData,
    activeButton,
    loading,
    refreshWalletChangeLog,
    handleButtonClick,
  };
}
