import { ActionContext, webhook } from '@roasmax/serve';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';

export const POST = webhook(
  async (
    ctx: ActionContext<{
      id: string;
      result: '成功' | '失败';
      message?: string;
    }>,
  ) => {
    const subTask = await ctx.db.video_distribution_sub_tasks.findUnique({ where: { id: ctx.data.id } });
    if (!subTask) {
      throw new Error(`未找到视频分发子任务 ${ctx.data.id}`);
    }
    if (subTask.status !== VIDEO_DISTRIBUTION_SUB_TASK_STATUS.分发中) {
      throw new Error(`视频分发子任务 ${ctx.data.id} 状态不为分发中，不可执行完成操作`);
    }

    if (ctx.data.result === '成功') {
      await ctx.db.video_distribution_sub_tasks.update({
        where: { id: subTask.id },
        data: { status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已发布, publish_at: new Date() },
      });
    } else {
      await ctx.db.video_distribution_sub_tasks.update({
        where: { id: subTask.id },
        data: { status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.分发失败, status_desc: ctx.data.message },
      });
    }
  },
);
