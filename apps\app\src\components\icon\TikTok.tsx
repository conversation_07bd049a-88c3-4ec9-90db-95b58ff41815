export const TikTok = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1210_1609)">
        <path
          d="M4.5 0H19.5C22.5 0 24 1.5 24 4.5V19.5C24 22.5 22.5 24 19.5 24H4.5C1.5 24 0 22.5 0 19.5V4.5C0 1.5 1.5 0 4.5 0Z"
          fill="black"
        />
        <path
          d="M10.42 9.75598V9.01498C10.1681 8.97432 9.91321 8.95425 9.658 8.95497C8.15869 8.95577 6.72092 9.55131 5.66019 10.6109C4.59945 11.6705 4.00238 13.1077 4 14.607C4 16.5315 4.963 18.2145 6.427 19.2375C5.44741 18.1953 4.90234 16.8187 4.903 15.3885C4.882 12.3225 7.3495 9.81598 10.42 9.75598Z"
          fill="#00F2EA"
        />
        <path
          d="M10.5591 17.9944C11.9451 17.9944 13.0881 16.8919 13.1481 15.5089V3.18189H15.3951C15.3561 2.92089 15.3156 2.66139 15.3156 2.40039H12.2451V14.7274C12.2188 15.3958 11.9345 16.028 11.4521 16.4914C10.9697 16.9548 10.3266 17.2134 9.65762 17.2129C9.21512 17.2129 8.81462 17.1139 8.45312 16.9129C8.93463 17.5729 9.69812 17.9944 10.5606 17.9944H10.5591Z"
          fill="#00F2EA"
        />
        <path
          d="M17.2628 5.98762C16.5839 5.2116 16.213 4.21365 16.2203 3.18262H15.3968C15.5028 3.75177 15.7227 4.29364 16.0434 4.77566C16.364 5.25769 16.7788 5.66993 17.2628 5.98762ZM9.65931 12.0416C8.23431 12.0416 7.07031 13.2041 7.07031 14.6261C7.07031 15.6296 7.63281 16.4906 8.45481 16.9121C8.15481 16.4921 7.97481 15.9701 7.97481 15.4091C7.97481 13.9841 9.13731 12.8231 10.5623 12.8231C10.8218 12.8231 11.0828 12.8621 11.3243 12.9431V9.79462C11.0724 9.75397 10.8175 9.7339 10.5623 9.73462H10.4213V12.1391C10.1723 12.0754 9.91638 12.0422 9.65931 12.0401V12.0416Z"
          fill="#FF004F"
        />
        <path
          d="M19.6097 7.37097V9.75597C18.0738 9.76501 16.5756 9.28017 15.3362 8.37297V14.628C15.3348 15.663 15.0496 16.6778 14.5118 17.562C13.9739 18.4463 13.2039 19.1662 12.2854 19.6434C11.367 20.1206 10.3353 20.3369 9.30258 20.2688C8.26982 20.2007 7.2755 19.8507 6.42773 19.257C6.95987 19.8258 7.60302 20.2794 8.31743 20.5898C9.03184 20.9002 9.80232 21.0607 10.5812 21.0615C12.0805 21.0607 13.5183 20.4651 14.579 19.4055C15.6398 18.3459 16.2369 16.9088 16.2392 15.4095V9.15447C17.4849 10.0504 18.9798 10.534 20.5142 10.5375V7.45047C20.1917 7.45047 19.8917 7.43097 19.6097 7.37097Z"
          fill="#FF004F"
        />
        <path
          d="M15.3153 14.6276V8.37262C16.561 9.26864 18.0559 9.75226 19.5903 9.75562V7.37062C18.6867 7.16972 17.8709 6.68512 17.2623 5.98762C16.7739 5.67277 16.3548 5.26164 16.0306 4.77936C15.7064 4.29708 15.484 3.75377 15.3768 3.18262H13.1493V15.5096C13.1226 16.1778 12.8382 16.8096 12.3558 17.2727C11.8734 17.7358 11.2305 17.9942 10.5618 17.9936C10.1499 17.9936 9.74393 17.8954 9.37746 17.7074C9.01099 17.5193 8.69455 17.2467 8.45431 16.9121C8.03863 16.692 7.69038 16.3633 7.4466 15.9611C7.20281 15.5588 7.0726 15.098 7.06981 14.6276C7.06981 13.2026 8.23381 12.0416 9.65881 12.0416C9.91981 12.0416 10.1793 12.0806 10.4208 12.1616V9.75562C8.94127 9.78508 7.53233 10.3938 6.49679 11.4509C5.46124 12.5081 4.88173 13.9293 4.88281 15.4091C4.88281 16.8926 5.46481 18.2546 6.40831 19.2566C7.36181 19.9218 8.49623 20.2788 9.65881 20.2796C10.4017 20.2808 11.1374 20.1355 11.824 19.8519C12.5107 19.5684 13.1346 19.1522 13.6601 18.6272C14.1857 18.1022 14.6025 17.4787 14.8868 16.7924C15.1711 16.1061 15.3172 15.3705 15.3168 14.6276H15.3153Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1210_1609">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
