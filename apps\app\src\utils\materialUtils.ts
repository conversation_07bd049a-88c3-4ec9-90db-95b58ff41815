import toast from 'react-hot-toast';

/**
 * 验证素材列表中的音频
 * @param list 素材列表
 * @param shouldValidateAudio 是否需要验证音频
 * @returns 验证后的素材列表
 */
export const validateMaterialsAudio = <T extends { is_audio?: boolean | null }>(
  list: T[],
  shouldValidateAudio: boolean,
): T[] => {
  const originListLength = list.length;

  const materialsToAdd = shouldValidateAudio
    ? list.filter((item) => item.is_audio === true || item.is_audio === null)
    : list;

  // 检查是否有被过滤掉的视频（无音频视频）
  if (shouldValidateAudio && materialsToAdd.length < originListLength) {
    const filteredCount = originListLength - materialsToAdd.length;
    toast(`已过滤 ${filteredCount} 个无音频的视频，仅导入有音频的视频`);
  }

  return materialsToAdd;
};
