import { <PERSON>ton, FormField, FormItem, FormLabel, FormControl, Input, Textarea } from '@/components/ui';
import { Loader2, Wand2 } from 'lucide-react';
import { AdTextFieldProps } from '../types';

export const AdTextField = ({ form, isGenerating, type, onGenerate, onCancelGeneration }: AdTextFieldProps) => {
  return (
    <>
      <FormField
        control={form.control}
        name="adName"
        render={({ field }) => (
          <FormItem className="mt-6 flex h-8 items-center">
            <FormLabel className="w-1/5 text-sm text-white">
              广告名称 <span className="ml-2 text-red-500">*</span>
            </FormLabel>
            <FormControl className="w-4/5">
              <Input
                placeholder="请输入广告名称"
                {...field}
                className="flex h-[40px] w-[360px] flex-shrink-0 items-center gap-3 rounded border border-[#363D54] bg-transparent px-3 py-[7px] text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </FormControl>
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="adText"
        render={({ field }) => (
          <FormItem className="mt-6 flex items-start">
            <FormLabel className="w-1/5 text-sm text-white">
              广告文案 <span className="ml-2 text-red-500">*</span>
            </FormLabel>
            <div className="flex gap-2">
              <FormControl className="w-4/5">
                <Textarea
                  placeholder="请输入广告文案"
                  disabled={isGenerating || type === 'edit'}
                  className="min-h-[100px] w-[360px] border border-[#363D54] bg-transparent text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                  {...field}
                />
              </FormControl>
              <Button
                type="button"
                variant="outline"
                size="icon"
                disabled={type === 'edit'}
                onClick={isGenerating ? onCancelGeneration : onGenerate}
                title={isGenerating ? '点击取消生成' : 'AI生成文案'}
                className="border border-[#363D54] bg-transparent hover:bg-[#CCDDFF33]"
              >
                {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Wand2 className="h-4 w-4" />}
              </Button>
            </div>
          </FormItem>
        )}
      />
    </>
  );
};
