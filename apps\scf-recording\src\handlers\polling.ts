'use strict';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import { request } from '@roasmax/utils/net';
import utc from 'dayjs/plugin/utc';
import { LiveClient } from '@roasmax/utils/tencentcloud';
import { prisma } from '@/utils/prisma';
import { sleep, to } from '@roasmax/utils';
import { video_recording_configs } from '@roasmax/database';
import { cos } from '@/utils/cos';
dayjs.extend(utc);
dayjs.extend(timezone);

type LiveStreamInfo = {
  isLive: boolean;
  roomTItle: string;
  ownerNickname: string;
  defaultStreamUrl: string;
  streamUrlMap: {
    FULL_HD1?: string;
    HD1?: string;
    SD1?: string;
    SD2?: string;
  };
};

const liveClient = new LiveClient({
  region: process.env.TENCENT_CLOUD_REGION,
  credential: {
    secretId: process.env.TENCENT_CLOUD_SECRET_ID,
    secretKey: process.env.TENCENT_CLOUD_SECRET_KEY,
  },
});

export const polling = async () => {
  const random = Math.floor(Math.random() * 7000);
  await sleep(random);
  // 获取所有的配置
  const configs = await prisma.video_recording_configs.findMany();

  // 获取所有的录制模板
  const templates = await liveClient.DescribeLiveRecordTemplates({});

  // 依次调用云函数，以获取直播推流链接
  for (const config of configs) {
    const template = templates.Templates.find(
      (t) => config.record_template_id && t.TemplateId === Number(config.record_template_id),
    );
    const [err] = await to(pollingEach(config, template));
    if (err) {
      console.error(`${config.name}-${config.id} 错误: ${err.message}`);
    }
  }
};

const pollingEach = async (
  config: video_recording_configs,
  template:
    | Awaited<NonNullable<ReturnType<typeof liveClient.DescribeLiveRecordTemplates>>>['Templates'][number]
    | undefined,
) => {
  // ------------------------------
  // 取得模板id 如果模板不存在，则创建模板
  console.log('--------------------------------');
  console.log(`config: ${config.name}-${config.id}`);
  let templateId = template?.TemplateId;
  if (!templateId || templateId !== template?.TemplateId) {
    console.log(`录制模板不存在，创建录制模板`);
    // 需要先保证目录存在
    console.log(`创建目录 ${config.ip}/${config.live_room}`);
    await cos.putObject({
      Bucket: process.env.TENCENT_CLOUD_COS_BUCKET_NAME!,
      Region: process.env.TENCENT_CLOUD_COS_BUCKET_REGION!,
      Key: `roasmax/${config.tenant_id}/直播录制/录制视频/${config.ip}/${config.live_room}/直播间.txt`,
      Body: `${config.live_room_identity}\n${config.live_room}`,
    });
    console.log(`创建目录成功`);
    console.log(`创建录制模板`);
    const nameArr = process.env.TENCENT_CLOUD_COS_BUCKET_NAME.split('-');
    const newTemplateId = await liveClient
      .CreateLiveRecordTemplate({
        TemplateName: `${config.name}-${config.id}`,
        CosStore: 1,
        RemoveWatermark: true,
        Mp4Param: {
          Enable: 1,
          RecordInterval: 7200,
          StorageTime: 0,
          StorageMode: 'normal',
          CosBucketName: nameArr.slice(0, -1).join('-'),
          CosBucketRegion: process.env.TENCENT_CLOUD_COS_BUCKET_REGION,
          CosBucketPath: `/roasmax/${config.tenant_id}/直播录制/录制视频/${config.ip}/${config.live_room}/{StartYear}-{StartMonth}-{StartDay}-{StartHour}-{StartMinute}-{StartSecond}__{AppName}-{StreamID}-{RecordId}`,
        },
      })
      .then((res) => res.TemplateId);

    if (!newTemplateId) {
      throw new Error(`创建录制模板失败 ${config.name}-${config.id}`);
    }
    await prisma.video_recording_configs.update({
      where: { id: config.id },
      data: { record_template_id: newTemplateId.toString() },
    });
    templateId = newTemplateId;
  }
  console.log(`录制模板id: ${templateId}`);

  // ------------------------------
  // 检查是否开播并获取推流链接
  console.log(`检查${config.live_room_identity}是否开播`);
  const [err, res] = await to(
    request.get<{ success: boolean; data: LiveStreamInfo }>(
      `${process.env.DOUYIN_LIVE_STATUS_CHECK_API_URL}/${config.live_room_identity}`,
    ),
  );
  if (err) {
    throw new Error(`${config.live_room_identity}请求错误`);
  }
  if (!res?.data.success || !res?.data.data.isLive) {
    throw new Error(`${config.live_room_identity}未开播`);
  }
  const streamUrl = getLiveStreamUrl(res.data.data);
  console.log(`直播推流链接: ${streamUrl}`);

  // ------------------------------
  // 获取是否已有任务
  const SpecifyTaskId = `${config.name}-${config.id}`;
  console.log(`检查是否已有拉流任务 ${SpecifyTaskId}`);
  const tasks = await liveClient.DescribeLivePullStreamTasks({ SpecifyTaskId });

  if (tasks.TaskInfos?.[0]?.TaskId) {
    console.log(`任务已存在 ${tasks.TaskInfos[0].TaskId}，检查任务状态`);
    // 如果任务存在
    const taskStatus = await liveClient.DescribeLivePullStreamTaskStatus({ TaskId: tasks.TaskInfos[0].TaskId });
    if (taskStatus.TaskStatusInfo?.RunStatus === 'active') {
      console.log(`任务已存在且运行中`);
      return;
    }
    if (taskStatus.TaskStatusInfo?.RunStatus === 'inactive') {
      console.log(`任务已存在且未运行，重新启动`);
      await liveClient.ModifyLivePullStreamTask({
        TaskId: tasks.TaskInfos[0].TaskId,
        SourceUrls: [streamUrl],
        Operator: 'scf',
        StartTime: dayjs().tz('UTC').toISOString(),
        EndTime: dayjs().tz('UTC').add(1, 'day').toISOString(),
      });
      await liveClient.RestartLivePullStreamTask({ TaskId: tasks.TaskInfos[0].TaskId, Operator: 'scf' });
      console.log(`任务已存在且未运行，重新启动成功`);
      return;
    }
    throw new Error(`任务已存在且状态未知 ${tasks.TaskInfos[0].TaskId}`);
  }

  // ------------------------------
  // 调用VOD创建拉流转推任务
  console.log(`未找到拉流任务，创建新拉流转推任务`);
  await liveClient.CreateLivePullStreamTask({
    Comment: `${config.name}-${config.live_room_identity}`,
    SpecifyTaskId,
    DomainName: '',
    AppName: `AppName-${config.name}-${config.live_room_identity}`,
    StreamName: `StreamName-${config.name}-${config.live_room_identity}`,
    SourceType: 'PullLivePushLive',
    SourceUrls: [streamUrl],
    StartTime: dayjs().tz('UTC').toISOString(),
    EndTime: dayjs().tz('UTC').add(1, 'day').toISOString(),
    Operator: 'scf',
    RecordTemplateId: templateId.toString(),
  });

  console.log(`创建拉流转推任务成功`);
};

/**
 * 获取直播推流链接
 * 如果存在FULL_HD1，则优先使用FULL_HD1，
 * 如果存在HD1，则优先使用HD1，
 * 否则使用defaultStreamUrl
 * @param info 直播信息
 * @returns 直播推流链接
 */
function getLiveStreamUrl(info: LiveStreamInfo) {
  if (info.streamUrlMap.FULL_HD1) {
    return info.streamUrlMap.FULL_HD1;
  }
  if (info.streamUrlMap.HD1) {
    return info.streamUrlMap.HD1;
  }
  return info.defaultStreamUrl;
}
