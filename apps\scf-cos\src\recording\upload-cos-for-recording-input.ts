'use strict';
import { EventPayload, Handler } from '..';
import { prisma } from '../utils/prisma';

export class UploadFromCosForRecordingInputHandler implements Handler {
  async handle(record: EventPayload['Records'][0]) {
    console.log('开始处理文件【录制视频-原始视频】', record.cos.cosObject.key);
    console.log('----------------------------');

    // 获取文件相对于bucket下的key
    const key = record.cos.cosObject.key.replace(`/${process.env.COS_APPID}/${record.cos.cosBucket.name}/`, '');

    const keyArr = key.split('/');
    const [, tenantId, _workflowName, _stageName, ip, liveRoom, ...fileNameArr] = keyArr;
    if (!ip || !liveRoom) {
      console.log('ip 或 liveRoom 不存在，不处理', key);
      return;
    }

    if (!fileNameArr[fileNameArr.length - 1]?.endsWith('.mp4')) {
      console.log('不是一个.mp4文件，不处理', key);
      return;
    }

    const fileName = fileNameArr.join('/');

    // 创建一个切分任务
    await prisma.video_cut_tasks.create({
      data: {
        tenant_id: tenantId!,
        name: `${ip}-${liveRoom}-${fileName}`,
        origin_url: `/${ip}/${liveRoom}/${fileName}`,
        goods_list: [],
        ip: ip,
        live_room: liveRoom,
        live_session: '',
        language: 'zh-CN',
        cut_mode: 'general',
        status: 'DRAFT',
        status_desc: '等待执行',
      },
    });
  }
}
