import { to } from '@roasmax/utils';
import MP4Box from 'mp4box';
import toast from 'react-hot-toast';

declare global {
  interface Navigator extends NavigatorNetworkInformation {}
}

interface NavigatorNetworkInformation {
  readonly connection?: NetworkInformation;
  readonly mozConnection?: NetworkInformation;
  readonly webkitConnection?: NetworkInformation;
}

interface NetworkInformation extends EventTarget {
  readonly effectiveType?: '2g' | '3g' | '4g' | 'slow-2g';
  readonly downlink?: number;
  readonly rtt?: number;
  readonly saveData?: boolean;
  readonly type?: string;
}

export const trimFileName = (fileName: string) => {
  if (!fileName) return '';
  const point = fileName?.lastIndexOf('.');
  return fileName.slice(0, point);
};

function handleFirstTab(e: KeyboardEvent) {
  if (e.keyCode === 9) {
    // the "I am a keyboard user" key
    document.body.classList.add('user-is-tabbing');
    window.removeEventListener('keydown', handleFirstTab);
  }
}

export const preventTab = () => {
  window.addEventListener('keydown', handleFirstTab);
};

export const getProcessedImageUrl = (url: string, width: number, height: number) => {
  if (!url) return '';
  const format = url.split('.').pop()?.toLowerCase() || 'jpg';
  return `${url}!${width}x${height}.${format}`;
};

export const getVodCoverUrl = (url: string) => {
  return `${url}!30.png`;
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const getFileNameAndExtension = (file: File) => {
  const fileName = file.name.substring(0, file.name.lastIndexOf('.'));
  const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
  return { fileName, fileExtension };
};

// 初始检查网络状态
export const checkConnection = () => {
  if (!navigator.onLine) {
    toast.error('网络连接已断开，请检查网络设置');
    return false;
  }
  try {
    const connection = navigator?.connection || navigator?.mozConnection || navigator?.webkitConnection;
    // 使用 performance.getEntriesByType 检测网络性能
    if (!connection && !performance) return;

    const resources = performance?.getEntriesByType?.('resource') || [];
    const avgDuration = resources.reduce((acc, resource) => acc + resource.duration, 0) / resources.length;

    if (avgDuration > 3000 || connection?.effectiveType === '2g') {
      // 如果平均加载时间超过3秒
      toast.error('当前网络较弱，可能会影响上传速度');
    }
  } catch (error) {
    console.error('检查网络状态失败', error);
    return false;
  }
  return true;
};

export const formatDuration = (duration: number | undefined) => {
  if (duration === undefined) {
    return '00:00';
  }

  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
};

export const getArrayBuffer = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const ab = e.target?.result as ArrayBuffer;
      resolve(ab);
    };
    reader.onerror = (e) => {
      console.error('getArrayBuffer error', e);
      reject(e);
    };
    reader.readAsArrayBuffer(file);
  });
};

export const checkHasAudioTrackNative = (file: File): Promise<boolean> => {
  const retry = (attempt = 1): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const objectUrl = URL.createObjectURL(file);
      let loadMetadataFailed = false;

      video.onloadedmetadata = (e) => {
        const target = e.target as HTMLVideoElement;

        try {
          // 1. 检查标准音轨API
          const hasStandardAudioTrack = Boolean((target as any).audioTracks?.length);
          // 2. 检查Mozilla特有API
          const hasMozAudio = (target as any).mozHasAudio;
          // 3. 检查Webkit特有API
          const hasWebkitAudio = Boolean((target as any).webkitAudioDecodedByteCount);
          // 4. 检查音频上下文API
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
          const mediaElement = audioContext.createMediaElementSource(target);
          const hasAudioContext = mediaElement.channelCount > 0;
          audioContext.close();

          // 5. 综合判断F
          const hasAudioTrack = hasStandardAudioTrack || hasMozAudio || hasWebkitAudio || hasAudioContext;

          console.log('音频检测结果:', {
            hasStandardAudioTrack,
            hasMozAudio,
            hasWebkitAudio,
            hasAudioContext,
          });

          // 清理资源
          target.pause();
          target.src = '';
          URL.revokeObjectURL(objectUrl);
          loadMetadataFailed = false;
          resolve(hasAudioTrack);
        } catch (err) {
          loadMetadataFailed = true;
          target.pause();
          target.src = '';
          URL.revokeObjectURL(objectUrl);
          console.error('Error from checkHasAudioTrackNative onloadedmetadata:', err);

          if (attempt < 3) {
            // 最多重试2次(总共3次)
            console.warn(`音频检测第${attempt}次失败,准备重试:`, err);
            setTimeout(() => {
              retry(attempt + 1)
                .then(resolve)
                .catch(reject);
            }, 1000); // 延迟1秒后重试
          } else {
            console.error('音频检测最终失败:', err);
            reject(err);
          }
        }
      };

      video.onerror = (err) => {
        if (loadMetadataFailed) {
          reject(err);
          console.error('视频加载最终失败:', err);
        }
        URL.revokeObjectURL(objectUrl);
      };

      video.preload = 'metadata';
      video.src = objectUrl;
    });
  };

  return retry();
};

export const checkHasAudioTrack = async (file: File): Promise<boolean> => {
  const timeout = new Promise<boolean>((_, reject) => {
    setTimeout(() => {
      reject(new Error('检查视频音频超时(10s)'));
    }, 10000);
  });

  const checkCodec = new Promise<boolean>(async (resolve, reject) => {
    const mp4boxFile = MP4Box.createFile();

    mp4boxFile.onError = (error) => {
      console.error('checkHasAudioTrack error', error);
      reject(error);
    };
    mp4boxFile.onReady = (info) => {
      const hasAudioTrack = info.audioTracks && info.audioTracks.length > 0;
      resolve(hasAudioTrack);
    };
    const [err, ab] = await to(getArrayBuffer(file));
    if (err || !ab) {
      await checkHasAudioTrackNative(file).then(resolve).catch(reject);
      return;
    }

    const buffer = ab as ArrayBuffer & { fileStart: number };
    buffer.fileStart = 0;
    mp4boxFile.appendBuffer(buffer);
    mp4boxFile.flush();
  });

  return Promise.race([checkCodec, timeout]);
};

export const checkIsMP4 = (file: File) => {
  return file.type === 'video/mp4' || file.type === 'video/mpeg' || file.type === 'video/mpeg4';
};

const getVideoDurationNative = (file: File): Promise<number> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.src = URL.createObjectURL(file);
    video.onloadedmetadata = () => {
      resolve(video.duration);
    };
  });
};

/**
 * 获取视频元信息(原生方法, 获取不到码率)
 * @param file
 * @returns
 */
const getVideoMetadataNative = (
  file: File,
): Promise<{ width: number; height: number; duration: number; bitrate: number }> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.src = URL.createObjectURL(file);
    video.onloadedmetadata = () => {
      resolve({ width: video.videoWidth, height: video.videoHeight, duration: video.duration, bitrate: -1 });
    };
  });
};

/**
 * 获取视频元信息
 * @param file
 * @returns
 */
export const getVideoMetadata = (
  file: File,
): Promise<{ width: number; height: number; duration: number; bitrate: number }> => {
  const timeout = new Promise<{ width: number; height: number; duration: number; bitrate: number }>((_, reject) => {
    setTimeout(() => {
      reject(new Error('检查视频时长超时(10s)'));
    }, 10000);
  });

  const checkDuration = new Promise<{ width: number; height: number; duration: number; bitrate: number }>(
    async (resolve, reject) => {
      const mp4boxFile = MP4Box.createFile();

      mp4boxFile.onError = (error) => {
        console.error('getVideoDuration error', error);
        reject(error);
      };

      mp4boxFile.onReady = async (info) => {
        // 拿到的duration单位为毫秒，转成秒
        const duration = info.duration / 1000;
        const { width, height, bitrate } = info.tracks.reduce(
          (pre, cur) => {
            const bitrate = cur.bitrate > pre.bitrate ? cur.bitrate : pre.bitrate;
            const width = cur.track_width > pre.width ? cur.track_width : pre.width;
            const height = cur.track_height > pre.height ? cur.track_height : pre.height;
            return { width, height, bitrate };
          },
          { width: -1, height: -1, bitrate: -1 },
        );

        // 如果
        // 1. duration为0或者大于 8 小时
        // 2. width/height 为 -1
        // 则使用原生方法
        if (duration === 0 || duration > 8 * 60 * 60 || width === -1 || height === -1) {
          reject('视频时长超时(10s)');
        }
        resolve({ width, height, duration, bitrate });
      };

      const [err, ab] = await to(getArrayBuffer(file));

      if (err || !ab) {
        await getVideoMetadataNative(file).then(resolve).catch(reject);
        return;
      }

      const buffer = ab as ArrayBuffer & { fileStart: number };
      buffer.fileStart = 0;
      mp4boxFile.appendBuffer(buffer);
      mp4boxFile.flush();
    },
  );

  return Promise.race([checkDuration, timeout]);
};

export const getVideoDuration = (file: File): Promise<number> => {
  const timeout = new Promise<number>((_, reject) => {
    setTimeout(() => {
      reject(new Error('检查视频时长超时(10s)'));
    }, 10000);
  });

  const checkDuration = new Promise<number>(async (resolve, reject) => {
    const mp4boxFile = MP4Box.createFile();

    mp4boxFile.onError = (error) => {
      console.error('getVideoDuration error', error);
      reject(error);
    };
    mp4boxFile.onReady = async (info) => {
      // 拿到的duration单位为毫秒，转成秒
      const duration = info.duration / 1000;
      // 如果duration为0或者大于 8 小时，则使用原生方法
      if (duration === 0 || duration > 8 * 60 * 60) {
        const duration = await getVideoDurationNative(file);
        console.log('getVideoDurationNative', duration);
        resolve(duration);
        return;
      }
      resolve(duration);
    };
    const [err, ab] = await to(getArrayBuffer(file));
    if (err || !ab) {
      await getVideoDurationNative(file).then(resolve).catch(reject);
      return;
    }

    const buffer = ab as ArrayBuffer & { fileStart: number };
    buffer.fileStart = 0;
    mp4boxFile.appendBuffer(buffer);
    mp4boxFile.flush();
  });

  return Promise.race([checkDuration, timeout]);
};

const analysisCode = (code: string) => {
  if (code.startsWith('avc')) {
    // h264
    return 'h264';
  } else if (code.startsWith('hvc')) {
    // h265
    return 'h265';
  } else {
    throw new Error('unknown video code');
  }
};

const mp4Parsed = (info: any) => {
  const codecs = [];
  for (let t = 0; t < info.tracks.length; ++t) {
    codecs.push(info.tracks[t].codec);
  }
  try {
    return analysisCode(codecs[0]);
  } catch (error) {
    return 'unknown video code';
  }
};

// 是否为h264编码
export const checkIsH264 = async (file: File) => {
  const timeout = new Promise<boolean>((_, reject) => {
    setTimeout(() => {
      reject(new Error('检查视频编码超时(10s)'));
    }, 10000);
  });

  const checkCodec = new Promise<boolean>(async (resolve, reject) => {
    const mp4boxFile = MP4Box.createFile();

    mp4boxFile.onError = (error: any) => {
      console.error('checkIsH264 error', error);
      reject(error);
    };
    mp4boxFile.onReady = (info) => {
      const fileCode = mp4Parsed(info);
      resolve(fileCode === 'h264');
    };
    const [err, ab] = await to(getArrayBuffer(file));
    if (err || !ab) {
      reject(false);
      return;
    }

    const buffer = ab as ArrayBuffer & { fileStart: number };
    buffer.fileStart = 0;
    mp4boxFile.appendBuffer(buffer);
    mp4boxFile.flush();
  });

  return Promise.race([checkCodec, timeout]);
};

interface ListOptions {
  title?: string; // 自定义标题
  indent?: string; // 自定义缩进
}

export const formatErrorList = (obj: Record<string, any>, options?: ListOptions) => {
  const title = options?.title ?? '文件信息';
  const indent = options?.indent ?? '  ';

  const items = Object.entries(obj).map(([key, value]) => {
    return `${indent}- ${key}: ${value}`;
  });

  return [`- ${title}:`, ...items].join('\n');
};

export const dispatchResize = (container?: HTMLElement) => {
  const event = new Event('resize');
  container ? container.dispatchEvent(event) : window.dispatchEvent(event);
};

export const formatPercent = (value: string) => {
  const numValue = value.toString().replace('%', '');
  return Number(numValue) * 10000;
};

export const formatCurrency = (value: string) => Number(value) * 100;
