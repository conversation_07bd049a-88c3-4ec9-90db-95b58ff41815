const MEDIA_WIDTH = {
  MIN: 0,
  XS: 860,
  S: 1024,
  M: 1280,
  L: 1440,
  XL: 1680,
  XXL: 1920,
  MAX: 9999,
};

export const BoxMediaType = Object.freeze({
  MaterialBox: 'MaterialBox',
  DirBox: 'DirBox',
  VideoBox: 'VideoBox',
});

export type BoxMediaTypeValues = {
  [K in keyof typeof BoxMediaType]: (typeof BoxMediaType)[K];
}[keyof typeof BoxMediaType];

// 这里的 ratio 都是 高 / 宽
export const BoxMedia = {
  MaterialBox: {
    ratio: 1,
    medias: [
      {
        width: [MEDIA_WIDTH.MIN, MEDIA_WIDTH.XS],
        extraHeight: 50,
        column: 5,
      },
      {
        width: [MEDIA_WIDTH.XS, MEDIA_WIDTH.S],
        extraHeight: 50,
        column: 6,
      },
      {
        width: [MEDIA_WIDTH.S, MEDIA_WIDTH.M],
        extraHeight: 50,
        column: 7,
      },
      {
        width: [MEDIA_WIDTH.M, MEDIA_WIDTH.L],
        extraHeight: 50,
        column: 8,
      },
      {
        width: [MEDIA_WIDTH.L, MEDIA_WIDTH.MAX],
        extraHeight: 50,
        column: 9,
      },
    ],
    boxMedias: [],
  },
  DirBox: {
    ratio: 1,
    medias: [
      {
        width: [MEDIA_WIDTH.MIN, MEDIA_WIDTH.XS],
        extraHeight: 50,
        column: 5,
      },
      {
        width: [MEDIA_WIDTH.XS, MEDIA_WIDTH.S],
        extraHeight: 50,
        column: 6,
      },
      {
        width: [MEDIA_WIDTH.S, MEDIA_WIDTH.M],
        extraHeight: 50,
        column: 7,
      },
      {
        width: [MEDIA_WIDTH.M, MEDIA_WIDTH.L],
        extraHeight: 50,
        column: 8,
      },
      {
        width: [MEDIA_WIDTH.L, MEDIA_WIDTH.MAX],
        extraHeight: 50,
        column: 9,
      },
    ],
    boxMedias: [],
  },
  VideoBox: {
    ratio: 16 / 9, // 视频默认16:9比例
    medias: [
      {
        width: [MEDIA_WIDTH.MIN, MEDIA_WIDTH.XS],
        extraHeight: 96, // 底部信息区域高度
        column: 2,
      },
      {
        width: [MEDIA_WIDTH.XS, MEDIA_WIDTH.S],
        extraHeight: 96,
        column: 3,
      },
      {
        width: [MEDIA_WIDTH.S, MEDIA_WIDTH.M],
        extraHeight: 96,
        column: 4,
      },
      {
        width: [MEDIA_WIDTH.M, MEDIA_WIDTH.L],
        extraHeight: 96,
        column: 5,
      },
      {
        width: [MEDIA_WIDTH.L, MEDIA_WIDTH.MAX],
        extraHeight: 96,
        column: 6,
      },
    ],
    boxMedias: [],
  },
};

export type BoxMediaItemType = {
  width: [number, number];
  extraHeight: number;
  column: number;
};

export type BoxType = keyof typeof BoxMedia;

/**
 * @param type 盒子类型，定义了宽高比、额外固定高度，以及不同容器尺寸下的列数
 * @param windowWidth 容器宽度
 * @param itemBoxPadding 盒子之间的间距
 * @param itemBoxContainerPadding 容器内部 padding
 * @param getRatio 用于自定义宽高比，使用瀑布流布局的时候使用这个方法
 */
export const getBoxSizeByMedia = (
  type: BoxType,
  windowWidth: number,
  itemBoxPadding: number = 16, // 每一列之间的 padding
  itemBoxContainerPadding: number = 0,
  getRatio?: (width: number) => number,
): { width: number; height: number; column: number; extraHeight: number } => {
  let width = 0;
  let height = 0;
  let column = 2;
  let extraHeight = 0;

  Object.entries(BoxMedia).forEach(([boxType, { medias, ratio, boxMedias }]) => {
    if (boxType === type) {
      /**
       * 一级适配
       */
      medias.forEach((media) => {
        if (
          typeof media.width[0] === 'number' &&
          typeof media.width[1] === 'number' &&
          media.width[0] <= windowWidth &&
          windowWidth < media.width[1]
        ) {
          column = media.column ?? 0;
          extraHeight = media.extraHeight ?? 0;
        }
      });

      width = (windowWidth - itemBoxContainerPadding * 2 - itemBoxPadding * (column - 1)) / column;
      /**
       * 二级别适配
       */
      if (boxMedias.length) {
        boxMedias.forEach((media: BoxMediaItemType) => {
          if (media.width[0] <= windowWidth && windowWidth < media.width[1]) {
            extraHeight = media.extraHeight ?? 0;
          }
        });
      }

      height = width * (getRatio?.(width) ?? ratio) + extraHeight;
    }
  });

  return {
    width,
    height,
    column,
    extraHeight,
  };
};
