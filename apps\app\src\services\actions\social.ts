'use server';

import { Prisma } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import CryptoJS from 'crypto-js';
import dayjs from 'dayjs';
import jwt from 'jsonwebtoken';
import { uniq } from 'lodash';
import queryString from 'query-string';
import { bindAccount } from '../domains/cloud-terminal';

type PageSocialAccountsFilterType = Partial<{
  nickname: string;
  ip: string;
  account_identity: string;
  account_unique_identity: string;
}>;

const buildFilterType: Record<keyof PageSocialAccountsFilterType, 'boolean' | 'string' | 'string[]' | 'number-min'> = {
  nickname: 'string',
  ip: 'string',
  account_identity: 'string',
  account_unique_identity: 'string',
};

const buildFilterWhere = (filter?: PageSocialAccountsFilterType) => {
  const where: Prisma.social_accountsWhereInput = {};
  if (!filter) return where;
  Object.entries(filter).forEach(([key, value]) => {
    const k = key as keyof PageSocialAccountsFilterType;
    if (buildFilterType[k] === 'string') {
      where[k] = { contains: value as string };
    }
  });
  return where;
};

/**
 * 分页查询社交帐号列表
 */
export const pageSocialAccounts = server(
  '分页查询社交帐号列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters: PageSocialAccountsFilterType;
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      return await ctx.trx(async (ctx) => {
        const where = buildFilterWhere(ctx.data.filters);
        const [list = [], total = 0] = await Promise.all([
          ctx.db.social_accounts.findMany({
            where,
            skip: Math.max(0, ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
            take: ctx.data.pagination.pageSize,
            orderBy: [{ nickname: 'asc' }, { tmp_created_at: 'desc' }],
            include: { cloud_terminal: true },
          }),
          ctx.db.social_accounts.count({ where }),
        ]);

        // 获取账号当天已经分发的视频数
        const today = dayjs().format('YYYY-MM-DD');
        const sub_tasks = await ctx.db.video_distribution_sub_tasks.findMany({
          where: { social_account_id: { in: list.map((item) => item.id) }, task: { distribution_batch_no: today } },
          include: { task: true },
        });

        return {
          list: list.map((item) => ({
            ...item,
            today_distribution_sub_tasks: sub_tasks.filter(
              (sub) => sub.social_account_id === item.id && sub.task?.distribution_batch_no === today,
            ),
          })),
          pagination: {
            total,
            page: ctx.data.pagination.page,
            pageSize: ctx.data.pagination.pageSize,
          },
        };
      });
    });
  },
);

export const listSocialAccountsByKeys = server(
  '根据多个字段查询社交账号',
  async (ctx: ActionContext<{ keys: Record<string, any>[] }>) => {
    return await ctx.trx(async (ctx) => {
      return await ctx.db.social_accounts.findMany({ where: { OR: ctx.data.keys } });
    });
  },
);
/**
 * 获取单个社交账号信息
 */
export const getSocialAccountInfo = server(
  '获取单个社交账号信息',
  async (ctx: ActionContext<{ accountId: string }>) => {
    return await ctx.db.social_accounts.findUnique({ where: { id: ctx.data.accountId } });
  },
);

/**
 * 创建社交账号
 */
export const createSocialAccount = server(
  '创建社交账号',
  async (ctx: ActionContext<Omit<Prisma.social_accountsCreateInput, 'tenant_id'>>) => {
    return await ctx.trx(async (ctx) => {
      const account = await ctx.db.social_accounts.create({
        data: {
          ...ctx.data,
          tenant_id: ctx.tenant.id,
          status: ctx.data.status || '',
          properties: ctx.data.properties ? { ...ctx.data.properties } : {},
        },
      });

      return account;
    });
  },
);

/**
 * 更新社交账号信息
 */
export const updateSocialAccount = server(
  '更新社交账号信息',
  async (ctx: ActionContext<Prisma.social_accountsUpdateArgs>) => {
    const updatedAccount = await ctx.db.social_accounts.update({ where: ctx.data.where, data: ctx.data.data });
    return updatedAccount;
  },
);

export const batchCreateAndBindCloudDisks = server(
  '批量创建云盘类型云终端并绑定',
  async (ctx: ActionContext<{ ids: string[] }>) => {
    const ids = uniq(ctx.data.ids.filter(Boolean));
    const accounts = await ctx.db.social_accounts.findMany({ where: { id: { in: ids } } });
    if (accounts.length !== ids.length) {
      throw new Error('账号有误');
    }
    const alreadyBinds = accounts.filter((item) => !!item.cloud_terminal_id);
    if (alreadyBinds.length) {
      throw new Error(`存在已绑定云终端的账号: ${alreadyBinds.map((item) => item.nickname).join('; ')}`);
    }

    // 创建云终端并绑定
    for (const account of accounts) {
      const terminal = await ctx.db.cloud_terminals.create({
        data: {
          tenant_id: ctx.tenant.id,
          name: `${account.id}`,
          type: '云盘',
          sn: '',
          status: '空闲',
          cloud_host_cos_path: `_/${account.ip}/${account.nickname}-${account.account_identity}-${account.id}`,
        },
      });

      await ctx.execute(bindAccount, {
        cloudTerminalId: terminal.id,
        socialAccountId: account.id,
      });

      await ctx.cos.putObject({
        Bucket: process.env.COS_BUCKET!,
        Region: process.env.COS_REGION!,
        Key: `roasmax/${account.tenant_id}/自动分发/分发素材/_/${account.ip}/${account.nickname}-${account.account_identity}-${account.id}/账户信息.txt`,
        Body: `${account.ip}\n${account.nickname}\n${account.account_identity}\n${account.id}`,
      });
    }
  },
);

/**
 * 解绑云终端与社交账号
 */
export const unbindCloudTerminal = server('解绑云终端', async (ctx: ActionContext<{ id: string }>) => {
  return await ctx.trx(async (ctx) => {
    const account = await ctx.db.social_accounts.findUnique({ where: { id: ctx.data.id } });
    if (!account) {
      throw new Error('未找到对应的社交账号');
    }
    if (!account.cloud_terminal_id) {
      throw new Error('该账号未绑定云终端');
    }

    const cloudTerminal = await ctx.db.cloud_terminals.findUnique({ where: { id: account.cloud_terminal_id } });
    if (!cloudTerminal) {
      throw new Error('未找到指定的云终端');
    }

    // 解绑云终端
    await ctx.db.social_accounts.update({ where: { id: account.id }, data: { cloud_terminal_id: null } });
    await ctx.db.cloud_terminals.update({
      where: { id: cloudTerminal.id },
      data: { bind_social_account_id: null, status: '空闲' },
    });

    return true;
  });
});

/**
 * 删除社交账号
 */
export const removeSocialAccount = server('删除社交账号', async (ctx: ActionContext<{ accountId: string }>) => {
  return await ctx.trx(async (ctx) => {
    await ctx.db.social_accounts.updateMany({
      where: { id: ctx.data.accountId },
      data: { tmp_deleted_at: new Date() },
    });

    return true;
  });
});

/**
 * 获取社交账号分享链接
 */
export const getShareLinkByAccountIds = server(
  '获取社交账号分享链接',
  async (ctx: ActionContext<{ accountIds: string[] }>) => {
    return await ctx.trx(async (ctx) => {
      const accounts = await ctx.db.social_accounts.findMany({
        where: { id: { in: ctx.data.accountIds }, cloud_terminal: { type: '云盘' } },
        select: { id: true, nickname: true, account_identity: true, share_secret: true, ip: true },
      });
      // 如果账号没有分享密钥，则生成一个
      for (const account of accounts) {
        if (!account.share_secret) {
          account.share_secret = CryptoJS.lib.WordArray.random(16).toString();
          await ctx.db.social_accounts.update({
            where: { id: account.id },
            data: { share_secret: account.share_secret },
            select: null,
          });
        }
      }

      const shareLinks = accounts.map((account) => {
        const token = jwt.sign(
          { tenant_id: ctx.tenant.id, social_account_id: account.id, social_account_secret: account.share_secret },
          process.env.WEBHOOK_SECRET!,
        );
        return {
          accountId: account.id,
          shareLink: `${process.env.PUBLIC_SHARE_LINK_HOST}/share/account?${queryString.stringify({ token })}`,
          info: {
            nickname: account.nickname,
            account_identity: account.account_identity,
            ip: account.ip,
          },
        };
      });

      return shareLinks;
    });
  },
);
