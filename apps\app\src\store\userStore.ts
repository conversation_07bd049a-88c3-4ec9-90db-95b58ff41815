import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 定义 store 的类型
interface UserState {
  userId: string | null;
  selectedCategory: string;
  actions: {
    setUserId: (id: string) => void;
    clearUserId: () => void;
    setSelectedCategory: (category: string) => void;
  };
}

// 创建 user store
const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      userId: null,
      selectedCategory: '',
      actions: {
        setUserId: (id: string) => set({ userId: id }),
        clearUserId: () => set({ userId: null }),
        setSelectedCategory: (category: string) => set({ selectedCategory: category }),
      },
    }),
    {
      name: 'BWAI_USER_CACHE',
      partialize: (state) => ({ userId: state.userId, selectedCategory: state.selectedCategory }), // 只持久化 userId
    },
  ),
);

export const useUserId = () => useUserStore((state) => state.userId);
export const useUserActions = () => useUserStore((state) => state.actions);
export const useSelectedCategory = () => useUserStore((state) => state.selectedCategory);

export default useUserStore;
