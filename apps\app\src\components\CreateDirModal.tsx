import { CANCEL, CONFIRM, INPUT_PLACEHOLDER } from '@/common/statics/zh_cn';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
  Button,
  DialogClose,
  Input,
} from '@/components/ui';
import { useRef, useState, useContext, useMemo } from 'react';
import debounce from 'lodash/debounce';
import DirContext from '@/context/directory';
import { NewlyBuilt } from '@/components/icon';
export default function CreateDirModal({
  onConfirm,
  disabled = false,
}: {
  onConfirm: (name: string) => void;
  disabled: boolean;
}) {
  const [createDirModalOpen, setCreateDirModalOpen] = useState(false);
  const { isSubmitting, cancelCreating } = useContext(DirContext);

  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const debouncedHandleConfirm = useMemo(
    () =>
      debounce(async () => {
        if (isSubmitting) return;
        const trimmedValue = inputValue.trim();
        if (!trimmedValue) return;
        await onConfirm(trimmedValue);
        setInputValue('');
        setCreateDirModalOpen(false);
      }, 200),
    [isSubmitting, onConfirm, inputValue],
  );

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isSubmitting) {
      if (e.key === 'Enter' && inputValue.trim()) {
        // 已经有空格校验
        debouncedHandleConfirm();
      } else if (e.key === 'Escape') {
        cancelCreating();
      }
    }
  };

  return (
    <Dialog open={createDirModalOpen} onOpenChange={setCreateDirModalOpen}>
      <DialogTrigger>
        <Button
          onClick={() => {
            setCreateDirModalOpen(true);
          }}
          disabled={disabled}
          className="h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 px-4 text-xs font-medium text-[#9FA4B2] hover:bg-[#CCDDFF33]"
        >
          <div className="mr-1.5">
            <NewlyBuilt />
          </div>
          <div>新建文件夹</div>
        </Button>
      </DialogTrigger>
      <DialogContent className="flex h-[214px] w-[400px] flex-col justify-start gap-8 rounded-2xl border-none bg-[#151c29] px-8 py-6">
        <DialogTitle className="text-center text-base font-medium text-white">新建文件夹</DialogTitle>
        <div className="relative w-full">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={INPUT_PLACEHOLDER}
            maxLength={20}
            className="border bg-transparent text-sm text-white shadow-none placeholder:text-sm placeholder:text-[##81889d] focus:border-[#00E1FF]"
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500">
            {inputValue.length}/20
          </span>
        </div>
        <DialogFooter className="flex h-10 items-center justify-between gap-2">
          <DialogClose asChild>
            <Button
              variant="secondary"
              disabled={isSubmitting}
              className="h-8 w-1/2 rounded-lg bg-[#1f2434] text-white"
              onClick={() => setCreateDirModalOpen(false)}
            >
              {CANCEL}
            </Button>
          </DialogClose>
          <Button
            className="h-10 w-1/2 text-black"
            variant="default"
            type="button"
            disabled={!inputValue.trim() || isSubmitting} // 修改禁用条件，添加 trim()
            onClick={debouncedHandleConfirm}
          >
            {CONFIRM}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
