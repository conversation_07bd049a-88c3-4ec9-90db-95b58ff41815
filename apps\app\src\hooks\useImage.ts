'use client';

import { useState } from 'react';
import html2canvas from 'html2canvas';
import toast from 'react-hot-toast';

interface ExportToImageOptions {
  elementId: string;
  fileName?: string;
  logoPath?: string;
  scale?: number;
  backgroundColor?: string;
  padding?: string;
  includeLink?: boolean;
  link?: string;
  format?: 'png' | 'jpeg';
  quality?: number;
}

export const useExportToImage = () => {
  const [imageLoading, setImageLoading] = useState(false);

  const exportToImage = async ({
    elementId,
    fileName = 'RoasMax.png',
    logoPath = '/logo.png',
    scale = 2,
    backgroundColor = '#070F1F',
    includeLink = false,
    link = '',
    format = 'png',
    quality = 0.95,
  }: ExportToImageOptions) => {
    try {
      setImageLoading(true);
      const element = document.getElementById(elementId);
      if (!element) {
        toast.error('未找到要分享的内容');
        return;
      }

      // 加载 logo
      const logoImg = new Image();
      logoImg.crossOrigin = 'anonymous';
      logoImg.src = logoPath;
      await new Promise((resolve) => {
        logoImg.onload = resolve;
      });

      // 创建一个临时的容器来包含所有内容
      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.style.backgroundColor = backgroundColor;
      container.style.padding = '20px 10px 10px 10px';
      // 获取原始元素的宽度并设置为 2/3
      const originalWidth = element.offsetWidth;
      container.style.width = `${Math.floor((originalWidth * 2) / 3)}px`;

      document.body.appendChild(container);

      // 如果需要包含链接，添加链接部分
      if (includeLink && link) {
        const linkContainer = document.createElement('div');
        linkContainer.style.padding = '4px';
        linkContainer.style.display = 'flex';
        linkContainer.style.alignItems = 'center';
        linkContainer.style.marginBottom = '10px';
        linkContainer.style.marginLeft = '10px';

        const linkLabel = document.createElement('div');
        linkLabel.style.color = '#9FA4B2';
        linkLabel.style.margin = '130px 10px 6px 10px';
        linkLabel.style.fontWeight = 'bold';
        linkLabel.style.fontSize = '24px';
        linkLabel.textContent = '商品链接:';

        const linkText = document.createElement('div');
        linkText.style.color = 'white';
        linkText.style.wordBreak = 'break-all';
        linkText.style.flex = '1';
        linkText.style.fontSize = '18px';
        linkText.style.letterSpacing = '1px';
        linkText.style.marginTop = '130px';
        linkText.textContent = link;

        linkContainer.appendChild(linkLabel);
        linkContainer.appendChild(linkText);
        container.appendChild(linkContainer);
      }

      // 克隆原始元素
      const clonedElement = element.cloneNode(true) as HTMLElement;
      container.appendChild(clonedElement);

      // 应用样式修复
      applyStyleFixes(clonedElement, elementId);

      // 添加logo
      const logoContainer = document.createElement('div');
      logoContainer.style.position = 'absolute';
      logoContainer.style.top = '100px';
      logoContainer.style.left = '30px';
      logoContainer.style.zIndex = '1000';
      const logoElement = document.createElement('img');
      logoElement.src = logoPath;
      logoElement.style.width = '120px';
      logoElement.style.height = 'auto';

      logoContainer.appendChild(logoElement);
      container.appendChild(logoContainer);

      // 添加水印
      addWatermark(container);

      // 转换为canvas
      const canvas = await html2canvas(container, {
        scale: scale,
        useCORS: true,
        logging: true,
        backgroundColor: backgroundColor,
      });

      // 移除临时容器
      document.body.removeChild(container);

      // 转换为图片并下载
      const imageType = format === 'png' ? 'image/png' : 'image/jpeg';
      const imageData = canvas.toDataURL(imageType, quality);

      // 创建下载链接
      const downloadLink = document.createElement('a');
      downloadLink.href = imageData;
      downloadLink.download = fileName;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      toast.success('图片已生成并下载');
      setImageLoading(false);
    } catch (error) {
      console.error('导出图片失败:', error);
      toast.error('图片生成失败');
      setImageLoading(false);
    }
  };

  // 应用样式修复函数
  const applyStyleFixes = (clonedElement: HTMLElement, elementId: string) => {
    // 确保克隆元素有足够的内边距
    clonedElement.style.padding = '30px';

    // 处理背景色
    const elementsWithBg = clonedElement.querySelectorAll('[class*="bg-"]');
    elementsWithBg.forEach((el) => {
      const computedStyle = window.getComputedStyle(el);
      (el as HTMLElement).style.backgroundColor = computedStyle.backgroundColor;
      (el as HTMLElement).style.backgroundImage = computedStyle.backgroundImage;
    });

    // 修复模板名称截断问题
    const templateElements = clonedElement.querySelectorAll('.template-item');

    const templateItemCrad = clonedElement.querySelectorAll('.template-item-crad');

    // 特别处理商品名称和作者昵称，防止重叠
    const productNames = clonedElement.querySelectorAll('.product-name');
    const authorNames = clonedElement.querySelectorAll('.author-name');
    const authoricon1 = clonedElement.querySelectorAll('.icon1');
    templateElements.forEach((el) => {
      // 确保文本元素完全显示
      const textElements = el.querySelectorAll('p, span, div');
      textElements.forEach((textEl) => {
        (textEl as HTMLElement).style.overflow = 'visible';
        (textEl as HTMLElement).style.whiteSpace = 'normal';
        (textEl as HTMLElement).style.lineHeight = '1.5';
        (textEl as HTMLElement).style.textOverflow = 'initial';
        (textEl as HTMLElement).style.maxWidth = 'none';
        (textEl as HTMLElement).style.width = 'auto';
      });

      // 修改模板卡片样式，使其垂直排列
      (el as HTMLElement).style.height = 'auto';
      (el as HTMLElement).style.minHeight = '100%';
      (el as HTMLElement).style.display = 'block'; // 改为 block 布局
      (el as HTMLElement).style.width = '100%';
      (el as HTMLElement).style.boxSizing = 'border-box';
    });
    templateItemCrad.forEach((el) => {
      (el as HTMLElement).style.marginBottom = '16px';
    });
    // 处理商品名称
    if (productNames.length > 0) {
      productNames.forEach((nameEl) => {
        (nameEl as HTMLElement).style.margin = '8px 0 16px 4px';
        (nameEl as HTMLElement).style.padding = '0';
        (nameEl as HTMLElement).style.overflow = 'visible';
        (nameEl as HTMLElement).style.whiteSpace = 'normal';
        (nameEl as HTMLElement).style.textOverflow = 'initial';
        (nameEl as HTMLElement).style.display = 'block';
        (nameEl as HTMLElement).style.color = '#ffffff';
        (nameEl as HTMLElement).style.position = 'relative';
      });
    }

    // 处理作者昵称
    if (authorNames.length > 0) {
      authorNames.forEach((nameEl) => {
        (nameEl as HTMLElement).style.color = '#cccccc';
        (nameEl as HTMLElement).style.margin = '4px 0 16px 4px';
        (nameEl as HTMLElement).style.padding = '0';
        (nameEl as HTMLElement).style.overflow = 'visible';
        (nameEl as HTMLElement).style.whiteSpace = 'normal';
        (nameEl as HTMLElement).style.textOverflow = 'initial';
        (nameEl as HTMLElement).style.display = 'block';
        (nameEl as HTMLElement).style.position = 'relative';
      });
    }

    if (authoricon1.length > 0) {
      authoricon1.forEach((nameEl) => {
        (nameEl as HTMLElement).style.marginBottom = '14px';
        (nameEl as HTMLElement).style.marginRight = '4px';
      });
    }

    const analysisContainersanalysis1 = clonedElement.querySelectorAll('.analysis1');
    analysisContainersanalysis1.forEach((container) => {
      (container as HTMLElement).style.marginTop = '8px';
      (container as HTMLElement).style.marginRight = '6px';
    });

    // 特别处理分析报告部分，确保内容完全显示
    const analysisContainers = clonedElement.querySelectorAll('.product-analysis, .content-analysis');
    analysisContainers.forEach((container) => {
      (container as HTMLElement).style.height = 'auto';
      (container as HTMLElement).style.overflow = 'visible';
      (container as HTMLElement).style.pageBreakInside = 'avoid';

      // 处理内部的markdown内容
      const markdownElements = container.querySelectorAll('.markdown');
      markdownElements.forEach((mdEl) => {
        (mdEl as HTMLElement).style.overflow = 'visible';
        (mdEl as HTMLElement).style.maxHeight = 'none';
        (mdEl as HTMLElement).style.height = 'auto';
        (mdEl as HTMLElement).style.whiteSpace = 'normal';
        (mdEl as HTMLElement).style.wordBreak = 'break-word';

        // 处理markdown内部的所有文本元素
        const mdTextElements = mdEl.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre, code');
        mdTextElements.forEach((textEl) => {
          (textEl as HTMLElement).style.overflow = 'visible';
          (textEl as HTMLElement).style.textOverflow = 'initial';
          (textEl as HTMLElement).style.whiteSpace = 'normal';
          (textEl as HTMLElement).style.wordBreak = 'break-word';
          (textEl as HTMLElement).style.pageBreakInside = 'avoid';
          (textEl as HTMLElement).style.marginBottom = '10px';
        });

        const lastElement = mdTextElements[mdTextElements.length - 1];
        if (lastElement) {
          (lastElement as HTMLElement).style.marginBottom = '30px';
        }
      });
    });
  };

  // 添加水印函数
  const addWatermark = (container: HTMLElement) => {
    const watermarkContainer = document.createElement('div');
    watermarkContainer.style.position = 'absolute';
    watermarkContainer.style.top = '0';
    watermarkContainer.style.left = '0';
    watermarkContainer.style.width = '100%';
    watermarkContainer.style.height = '100%';
    watermarkContainer.style.pointerEvents = 'none';
    watermarkContainer.style.zIndex = '999';

    // 创建两个水印
    for (let i = 0; i < 2; i++) {
      const watermark = document.createElement('div');
      watermark.textContent = 'RoasMax';
      watermark.style.position = 'absolute';
      watermark.style.fontSize = '60px';
      watermark.style.color = 'rgba(150, 150, 150, 0.2)';
      watermark.style.transform = 'rotate(-45deg)';
      watermark.style.transformOrigin = 'center';
      watermark.style.width = '100%';
      watermark.style.textAlign = 'center';

      // 上半部分和下半部分水印
      watermark.style.top = i === 0 ? '25%' : '75%';

      watermarkContainer.appendChild(watermark);
    }

    container.appendChild(watermarkContainer);
  };

  return { exportToImage, imageLoading };
};
