import { Checkbox } from '../ui';

import { cn } from '@/utils/cn';

interface ProCheckboxProps extends React.ComponentPropsWithoutRef<typeof Checkbox> {
  className?: string;
}

export const ProCheckbox = ({ className, ...props }: ProCheckboxProps) => {
  return (
    <Checkbox
      {...props}
      className={cn(
        'rounded border-[#95A0AA]',
        'data-[state=checked]:text-[#050A1C]',
        'data-[state=checked]:border-[#00E1FF]',
        className,
      )}
    />
  );
};
