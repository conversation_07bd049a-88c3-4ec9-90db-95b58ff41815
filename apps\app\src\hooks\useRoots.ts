import { getRootDirectories } from '@/services/actions/materials';
import { action } from '@/utils/server-action/action';
import { to } from '@roasmax/utils';
import useSWR from 'swr';

export function useRoots() {
  const {
    data = [],
    isValidating,
    error,
  } = useSWR('/api/materials/roots', RootFetcher, {
    dedupingInterval: 30 * 60 * 1000,
    refreshInterval: 72 * 60 * 60 * 1000, // 72小时后自动重新验证数据
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  return {
    data,
    isValidating,
    error,
  };
}

const RootFetcher = async () => {
  const [err, res] = await to(action(getRootDirectories, {}, { errorType: 'return' }));
  if (err || !res?.success) {
    throw new Error(`获取根目录失败, ${res?.message}`);
  }
  return res.data;
};
