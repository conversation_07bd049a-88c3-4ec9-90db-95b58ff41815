'use client';

import { asyncConfirm } from '@/components/ConfirmDialog';
import { WarningFullfil } from '@/components/icon/WarningFullfil';
import { usePrompts } from '@/hooks/usePrompts';
import { MaterialType } from '@/types/material';
import { zodResolver } from '@hookform/resolvers/zod';
import { unionBy } from 'lodash';
import { createContext, useCallback, useContext } from 'react';
import { useForm, UseFormReturn, useWatch } from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useKolStyles } from './useKolStyle';

export const GENERATE_ROUND_OPTIONS: ['1', '2', '3', '4', '5'] = ['1', '2', '3', '4', '5'];
export const GENERATE_SPEED_OPTIONS: ['0.9X', '1X', '1.1X', '1.3X', '1.5X', '2.0X'] = [
  '0.9X',
  '1X',
  '1.1X',
  '1.3X',
  '1.5X',
  '2.0X',
];
const baseSchema = z.object({
  sliceType: z.enum(['300', '1800']),
  name: z.string().min(1).max(20),
  generateRound: z.enum(GENERATE_ROUND_OPTIONS),
  speed: z.enum(GENERATE_SPEED_OPTIONS),
  subtitle: z.boolean(),
  transition_mode: z.enum(['fade', 'null']),
});

export const formSchema = z
  .discriminatedUnion('method', [
    baseSchema.extend({
      method: z.literal('normal'),
      language: z.string().min(1),
      industry: z.string().min(1),
      referenceDuration: z.string().min(1),
      prompts: z.array(z.string()).min(1),
      dify: z.boolean().optional(),
    }),
    baseSchema.extend({
      method: z.literal('gc_imitate'),
      productUrl: z.string().min(1),
      sceneImplantation: z.boolean().optional(),
      festiveAtmosphere: z.boolean().optional(),
      generationType: z.enum(['大V严选', 'KOL转移', '大卖推荐']),
      industryId: z.string().optional(),
      kolStyle: z.string().optional(),
      targetLanguage: z.string().optional(),
      customPrompt: z.string().optional(),
      targetVoiceCloneType: z.enum(['default', 'template', 'original']).optional(),
      targetVoiceTemplateId: z.string().optional(),
      targetVoice: z.string().optional(),
      templateVideoKeys: z.array(z.string()).optional(),
      templateVideoVodId: z.string().optional(),
      templateVideoLanguage: z.string().optional(),
      targetVideoDuration: z.number(),
    }),
  ])
  .refine((data) => {
    // 以下是自定义的校验
    if (data.method === 'gc_imitate') {
      if (data.generationType === '大V严选') {
        return data.industryId && data.kolStyle && data.targetLanguage;
      }
      if (data.generationType === 'KOL转移') {
        if (data.targetVoiceCloneType === 'default' && !data.targetVoice) {
          return false;
        }
        if (!data.templateVideoVodId || !data.templateVideoLanguage) {
          return false;
        }
      }
    }
    return true;
  });

export const DEFAULT_FORM_VALUE: Partial<TaskCreatorFormValues> = {
  sliceType: '300',
  name: '',
  industry: '',
  referenceDuration: '',
  method: 'normal',
  prompts: [],
  generateRound: '1',
  language: '',
  speed: '1X',
  subtitle: true,
  transition_mode: 'null',
  dify: true,
};

export type TaskCreatorFormValues<T extends 'normal' | 'gc_imitate' = 'normal' | 'gc_imitate'> = z.infer<
  typeof formSchema
> & { method: T };

// 创建 Context
const TaskCreatorContext = createContext<UseFormReturn<TaskCreatorFormValues> | null>(null);

export const TaskCreatorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const form = useForm<TaskCreatorFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: DEFAULT_FORM_VALUE as TaskCreatorFormValues,
    mode: 'onChange',
    shouldUnregister: false,
  });

  return <TaskCreatorContext.Provider value={form}>{children}</TaskCreatorContext.Provider>;
};

// 主 Hook
export const useTaskCreator = () => {
  const form = useContext(TaskCreatorContext);
  if (!form) {
    throw new Error('useTaskCreator must be used within TaskCreatorProvider');
  }

  const { list: promptList, tree: promptTree, loading: loadingPrompts, getByValue: getPromptByValue } = usePrompts();
  const { list: kolStyles, loading: loadingKolStyles } = useKolStyles();
  const formValues = useWatch({ control: form.control });

  const { selectedMaterials, setSelectedMaterials: _setSelectedMaterials } = useTaskCreatorStore();

  const setSelectedMaterials = useCallback(
    (set: (pre: MaterialType[]) => MaterialType[]) => {
      _setSelectedMaterials((pre) => {
        const next = set(pre);
        if (next.length > 10) {
          toast.error('单次最多可上传 10 个文件');
          return pre;
        }
        if (form.getValues('method') === 'normal' && form.getValues('sliceType') !== '300') {
          const durationGreaterThan300 = next.some((item) => item.video_duration > 20 * 60);
          if (durationGreaterThan300) {
            toast.error('视频 >=20min 无法使用达人精细生成，已自动过滤');
            return pre.filter((item) => item.video_duration <= 20 * 60);
          }
        }
        return next;
      });
    },
    [_setSelectedMaterials, form],
  );

  const addSelectedMaterials = useCallback(
    async (...material: (MaterialType | undefined)[]) => {
      const durationGreaterThan300 = material.filter(Boolean).some((item) => item!.video_duration > 60 * 20);
      if (form.getValues('sliceType') !== '300' && durationGreaterThan300) {
        // 若选择了自动过滤，则继续后续流程并自动过滤，否则直接返回
        const autoFilter = await asyncConfirm({
          content: (
            <div className="mb-[12px] flex gap-[8px]">
              <div className="mt-[3px]">
                <WarningFullfil className="h-[18px] w-[18px]" />
              </div>
              <div className="w-[240px]">
                <div className="mb-[4px]">{'所选内容中某视频时长 > 20min'}</div>
                <div className="text-xs font-normal text-[#95A0AA]">
                  <div>{'达人精细生成仅支持时长 <= 20min 的视频'}</div>
                  <div>{'请移除不合规的视频文件，否则无法生成'}</div>
                </div>
              </div>
            </div>
          ),
          buttonText: { cancel: '返回修改', confirm: '自动过滤' },
          onConfirm: async () => true,
        });
        if (!autoFilter) return false;
      }

      let success = false;
      _setSelectedMaterials((pre) => {
        const next = [...pre, ...material].filter(Boolean) as MaterialType[];
        if (next.length > 10) {
          toast.error('单次最多可上传 10 个文件');
          success = false;
          return pre;
        }

        if (form.getValues('sliceType') !== '300') {
          success = true;
          return next.filter((item) => item.video_duration <= 60 * 20);
        }

        success = true;
        return next as MaterialType[];
      });
      return success;
    },
    [form, _setSelectedMaterials],
  );

  const removeSelectedMaterials = useCallback(
    (materialId: string) => {
      _setSelectedMaterials((pre) => pre.filter((item) => item.id !== materialId));
    },
    [_setSelectedMaterials],
  );

  const updateFormValues = useCallback(
    (value: Partial<TaskCreatorFormValues>) => {
      form.reset({ ...formValues, ...value } as TaskCreatorFormValues); //
      console.log('再次生成', value);
    },
    [form],
  );

  const resetForm = useCallback(() => {
    form.reset(DEFAULT_FORM_VALUE as TaskCreatorFormValues);
  }, [form]);

  return {
    form,
    formValues,
    updateFormValues,
    promptList,
    promptTree,
    loadingPrompts,
    kolStyles,
    loadingKolStyles,
    selectedMaterials,
    addSelectedMaterials,
    removeSelectedMaterials,
    setSelectedMaterials,
    getPromptByValue,
    resetForm,
  };
};

interface TaskCreatorMaterialGalleryStore {
  selectedMaterials: MaterialType[];
  addSelectedMaterials: (...material: (MaterialType | undefined)[]) => void;
  removeSelectedMaterials: (materialId: string) => void;
  setSelectedMaterials: (fn: (pre: MaterialType[]) => MaterialType[]) => void;
  formData: Partial<TaskCreatorFormValues>;
  setFormData: (data: Partial<TaskCreatorFormValues>) => void;
}

/**
 * 用于TaskCreator的store
 */
export const useTaskCreatorStore = create<TaskCreatorMaterialGalleryStore>()(
  devtools((set, get) => ({
    selectedMaterials: [] as MaterialType[],
    addSelectedMaterials: (...materials) => {
      return set((state) => {
        const next = unionBy(
          [...state.selectedMaterials, ...materials].filter(Boolean),
          (item) => item!.id,
        ) as MaterialType[];
        return { selectedMaterials: next };
      });
    },
    removeSelectedMaterials: (materialId) => {
      return set((state) => ({ selectedMaterials: state.selectedMaterials.filter((item) => item.id !== materialId) }));
    },
    setSelectedMaterials: (fn) => {
      const next = unionBy(fn(get().selectedMaterials || []), (m) => m.id);
      return set({ selectedMaterials: next });
    },
    formData: {},
    setFormData: (data) => set({ formData: data }),
  })),
);
