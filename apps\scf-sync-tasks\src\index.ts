'use strict';
import { NodeScfWebServer } from '@roasmax/scf-web-server';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import syncTasks, { ISyncHandlerParams } from './handlers';
dayjs.extend(utc);
dayjs.extend(timezone);

const dispatcher = async (params: ISyncHandlerParams) => {
  return syncTasks(params);
};

const server = new NodeScfWebServer(dispatcher);

server.start(Number(process.env.SCF_PORT) || 9000);
