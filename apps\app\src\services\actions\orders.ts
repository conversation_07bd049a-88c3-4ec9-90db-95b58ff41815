'use server';

import { ActionContext, server } from '@roasmax/serve';
import { orders, Prisma } from '@roasmax/database';

export const pageOrders = server(
  '分页获取订单',
  async (
    ctx: ActionContext<{
      pagination: { page: number; pageSize: number };
      filters?: { product_name?: string; influencer_name?: string; shop_name?: string; order_id?: string };
    }>,
  ) => {
    return await ctx.trx(async (ctx) => {
      const where: Prisma.ordersWhereInput = {};
      if (ctx.data.filters?.product_name) {
        where.product_name = { contains: ctx.data.filters.product_name };
      }
      if (ctx.data.filters?.influencer_name) {
        where.influencer_name = { contains: ctx.data.filters.influencer_name };
      }
      if (ctx.data.filters?.shop_name) {
        where.shop_name = { contains: ctx.data.filters.shop_name };
      }
      if (ctx.data.filters?.order_id) {
        where.order_id = { contains: ctx.data.filters.order_id };
      }

      // 并行查询列表和总数
      const [list = [], total = 0] = await Promise.all([
        ctx.db.orders.findMany({
          skip: (ctx.data.pagination.page - 1) * ctx.data.pagination.pageSize,
          take: ctx.data.pagination.pageSize,
          where,
          orderBy: { tmp_created_at: 'desc' },
        }),
        ctx.db.orders.count({ where }),
      ]);

      return {
        list,
        pagination: {
          total,
          page: ctx.data.pagination.page,
          pageSize: ctx.data.pagination.pageSize,
        },
      };
    });
  },
);

/**
 * 获取单个订单信息
 */
export const getOrderInfo = server('获取单个订单信息', async (ctx: ActionContext<{ orderId: string }>) => {
  const order = await ctx.db.orders.findUnique({
    where: {
      id: ctx.data.orderId,
      tmp_deleted_at: null,
    },
  });

  if (!order) {
    throw new Error('未找到对应订单');
  }

  return order;
});

/**
 * 创建订单
 */
export const createOrder = server('创建订单', async (ctx: ActionContext<{ data: orders }>) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  return await ctx.trx(async (ctx) => {
    const order = await ctx.db.orders.create({
      data: {
        ...ctx.data.data,
        tmp_created_at: new Date(),
        tmp_updated_at: new Date(),
      },
    });

    return order;
  });
});

/**
 * 更新订单信息
 */
export const updateOrder = server('更新订单信息', async (ctx: ActionContext<Prisma.ordersUpdateArgs>) => {
  return await ctx.trx(async (ctx) => {
    const order = await ctx.db.orders.findUnique({ where: ctx.data.where });
    if (!order) throw new Error('未找到对应订单');
    const updateData: Prisma.ordersUpdateArgs['data'] = ctx.data.data;
    return await ctx.db.orders.update({ where: { id: order.id }, data: updateData });
  });
});

/**
 * 删除订单
 */
export const removeOrder = server('删除订单', async (ctx: ActionContext<{ orderIds: string[] }>) => {
  return await ctx.trx(async (ctx) => {
    // 检查订单是否存在
    const orders = await ctx.db.orders.findMany({
      where: {
        id: { in: ctx.data.orderIds },
        tmp_deleted_at: null,
      },
    });

    if (orders.length !== ctx.data.orderIds.length) {
      throw new Error('部分订单不存在');
    }

    // 执行逻辑删除
    await ctx.db.orders.updateMany({
      where: { id: { in: ctx.data.orderIds } },
      data: { tmp_deleted_at: new Date() },
    });

    return true;
  });
});
