import type { RequestStatus } from '@/types/api';
import type { AdvertiserItem, IUploadImageResponse, IUploadVideoResponse, CampaignItem, AdItem } from '@/types/ads';
import { AdGroupListRequestCamel, AdvertiserListResponseCamel } from '@/services/interfaces/ads/res';
import { CreateCampaignReq, SyncCampaignListReq, UpdateAdStatusReq } from '@/services/interfaces/ads/req';
import type { AdFormValues } from '@/components/AdsTikTok/Sheet/AdSheet/adFormSchema';
import { ToCamelCase } from '@/utils/camel';

export type CurrentView = 'advertisers' | 'campaigns' | 'group' | 'ad';

export type AdCreateType = 'normal' | 'quick';

export type AdRightDrawerState =
  | {
      type: 'create';
      show: boolean;
      title: string;
      formValue: Record<string, never>;
      createType?: AdCreateType;
    }
  | {
      type: 'edit';
      show: boolean;
      title: string;
      formValue: AdItem;
      createType?: AdCreateType;
    };

export interface QuickCreateAdParams {
  sliceType: '300' | '1800';
  name: string;
  generateRound: string;
  language: string;
  speed: string;
  subtitle: boolean;
  transition_mode: 'fade' | 'null';
  industry: string;
  referenceDuration: string;
  prompts: string[];
  dify?: boolean;
}

export type UpdateOrCreateAdParams = Partial<
  AdItem & {
    adgroupIds: string[];
    video_id?: string;
    image_ids?: string[];
    selectedVideoItem?: any;
  }
>;
export type DateRangeType = { startTime: string; endTime: string } | undefined;
export interface CampaignModalState {
  type: 'create' | 'edit';
  show: boolean;
  title: string;
  formValue: CampaignItem | null;
}

export type AdStore = {
  currentView: CurrentView;
  currentAdvertiser: AdvertiserItem[] | null;
  currentCampaign: CampaignItem[] | null;
  currentAdGroup: AdGroupListRequestCamel[] | null;
  currentAd: AdItem[] | null;
  cloudSheetOpen: boolean;
  campaignStatus: RequestStatus;
  advertisers: AdvertiserListResponseCamel | null;
  advertiserStatus: RequestStatus;
  campaignModal: CampaignModalState;
  adGroups: {
    list: AdGroupListRequestCamel[];
    page: number;
    pageSize: number;
    total: number;
  };
  adGroupStatus: RequestStatus;
  adStatus: RequestStatus;
  adsIdentityStatus: RequestStatus;
  adRightDrawer: AdRightDrawerState;
  createAdStatus: RequestStatus;
  generateAdTextStatus: {
    loading: boolean;
    error: boolean;
  };
  dateRange: DateRangeType | undefined;
  actions: {
    setDateRange: (dateRange: DateRangeType) => void;
    setCloudSheetOpen: (open: boolean) => void;
    setCurrentAdvertiser: (advertiser: AdvertiserItem[]) => void;
    setCurrentCampaign: (campaign: CampaignItem[] | null) => void;
    setCurrentAdGroup: (adGroup: AdGroupListRequestCamel[] | null) => void;
    setCurrentAd: (ad: AdItem[] | null) => void;
    clearCurrentAdvertiser: () => void;
    syncCampaigns: (params?: SyncCampaignListReq) => Promise<void>;
    setCampaignModal: (modal: Partial<CampaignModalState>) => void;
    resetCampaignModal: () => void;
    createCampaign: (campaignData: ToCamelCase<CreateCampaignReq>) => Promise<void>;
    updateCampaign: (campaignData: Partial<CampaignItem>) => Promise<void>;
    fetchAdGroups: (params: {
      advertiser_ids?: string[];
      campaign_ids?: string[];
      tag_names?: string;
      page?: number;
      page_size?: number;
      start_time?: string;
      end_time?: string;
      operation_status?: string;
    }) => Promise<void>;
    updateCampaignStatus: (params: {
      ids: number[];
      campaignIds?: string;
      advertiserId: string;
      operationStatus: 'ENABLE' | 'DISABLE' | 'DELETE';
    }) => Promise<void>;
    updateAdStatus: (params: ToCamelCase<UpdateAdStatusReq>) => Promise<void>;
    setAdRightDrawer: (drawer: Partial<AdRightDrawerState>) => void;
    createOrUpdateAd: (adData: AdFormValues, record?: AdItem) => Promise<void>;
    clearAdRightDrawer: () => void;
    uploadVideoToTiktok: (params: { file_name?: string; video_url: string }) => Promise<IUploadVideoResponse[]>;
    uploadImageToTiktok: (params: { file_name?: string; image_url: string }) => Promise<IUploadImageResponse>; // 根据实际返回类型调整
    setCurrentView: (view: CurrentView) => void;
    generateAdText: (
      productName: string,
      callback: (text: string) => void,
      abortSignal?: AbortSignal,
    ) => Promise<string | undefined>;
    cancelGenerateAdText: () => void;
  };
};
