import { CompletedIcon } from '@/components/icon/completed';
import { Progress } from '@/components/ui';
import { DownloadingItemType, DownloadingStatus } from '@/hooks/useBatchDownload';
import { cn } from '@/utils/cn';
import Image from 'next/image';

const DownloadingItem: React.FC<DownloadingItemType> = ({ name, coverUrl, progress, status }) => {
  return (
    <div className="flex items-center justify-start gap-3">
      <Image
        src={coverUrl || ''}
        alt={name}
        width={56}
        height={56}
        className="h-14 w-14 rounded-lg object-cover"
        unoptimized
      />
      <div className="flex flex-col items-start justify-between gap-3">
        <div className="text-xs text-[#81889D]">{name}</div>
        <div className="flex h-4 items-center justify-between gap-2">
          <Progress
            value={progress}
            className={cn('h-1.5 min-w-[320px] bg-[#1f2434]')}
            color={`${status === DownloadingStatus.SUCCESS && progress === 100 ? '#60D2A7' : '#FFFFFF'}`}
          />
          {status === DownloadingStatus.SUCCESS && progress === 100 && <CompletedIcon />}
          {status === DownloadingStatus.DOWNLOADING && progress < 100 && (
            <div className="w-8 text-xs text-[#81889D]">
              {Number.isInteger(progress) ? progress : progress.toFixed(2)}%
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { DownloadingItem };
