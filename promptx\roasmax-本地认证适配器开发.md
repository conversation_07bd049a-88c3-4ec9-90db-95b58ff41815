# Roasmax - 本地认证适配器开发

## 📋 任务概述

**任务ID**: mu4ATED6pURxiWPFb1BMH7  
**任务名称**: 本地认证适配器开发  
**优先级**: 高  
**预估工时**: 3-4天  
**前置依赖**: 数据库设计与迁移准备  

## 🎯 任务目标

开发兼容 Authing SDK 接口的本地认证适配器，实现无缝替换 Authing 服务，保持现有业务代码不变。

## 📊 详细任务分解

### 1. 核心适配器架构设计

#### 1.1 AuthenticationClient 适配器
```typescript
// packages/serve/src/adapters/LocalAuthenticationClient.ts
interface IAuthenticationClient {
  signInByEmailPassword(params: SignInParams): Promise<SignInResult>;
  getProfile(params: ProfileParams): Promise<ProfileResult>;
  updatePassword(params: UpdatePasswordParams): Promise<UpdateResult>;
  getRoleList(params: RoleListParams): Promise<RoleListResult>;
  revokeToken(token: string): Promise<boolean>;
  setAccessToken(token: string): void;
}

class LocalAuthenticationClient implements IAuthenticationClient {
  private accessToken: string = '';
  private prisma: PrismaClient;
  
  constructor(config: AuthConfig) {
    this.prisma = new PrismaClient();
  }
  
  // 实现所有 Authing AuthenticationClient 接口
}
```

#### 1.2 ManagementClient 适配器
```typescript
// packages/serve/src/adapters/LocalManagementClient.ts
interface IManagementClient {
  createUser(params: CreateUserParams): Promise<CreateUserResult>;
  updateUser(params: UpdateUserParams): Promise<UpdateUserResult>;
  listGroupMembers(params: ListMembersParams): Promise<MembersResult>;
  getUserRoles(params: GetUserRolesParams): Promise<UserRolesResult>;
  assignRole(params: AssignRoleParams): Promise<AssignRoleResult>;
  createRole(params: CreateRoleParams): Promise<CreateRoleResult>;
  listRoles(params: ListRolesParams): Promise<ListRolesResult>;
  authorizeResources(params: AuthorizeResourcesParams): Promise<AuthorizeResult>;
  getUserAuthorizedResources(params: GetAuthorizedResourcesParams): Promise<AuthorizedResourcesResult>;
  addGroupMembers(params: AddGroupMembersParams): Promise<AddGroupMembersResult>;
  kickUsers(params: KickUsersParams): Promise<KickUsersResult>;
}

class LocalManagementClient implements IManagementClient {
  private prisma: PrismaClient;
  
  constructor(config: ManagementConfig) {
    this.prisma = new PrismaClient();
  }
  
  // 实现所有 Authing ManagementClient 接口
}
```

### 2. 认证核心功能实现

#### 2.1 密码加密和验证
```typescript
// packages/serve/src/utils/password.ts
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export class PasswordManager {
  private static readonly SALT_ROUNDS = 12;
  
  static async hashPassword(password: string): Promise<{ hash: string; salt: string }> {
    const salt = await bcrypt.genSalt(this.SALT_ROUNDS);
    const hash = await bcrypt.hash(password, salt);
    return { hash, salt };
  }
  
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }
  
  static generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  static validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('密码长度至少8位');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }
    
    return { valid: errors.length === 0, errors };
  }
}
```

#### 2.2 JWT 令牌管理
```typescript
// packages/serve/src/utils/jwt.ts
import jwt from 'jsonwebtoken';
import * as jose from 'jose';

export class JWTManager {
  private static readonly SECRET = process.env.APPSECRET!;
  private static readonly EXPIRES_IN = '24h';
  
  static generateToken(user: UserPayload): string {
    const payload = {
      sub: user.user_id,
      data: {
        id: user.user_id,
        email: user.email,
        nickname: user.nickname,
        tenant_id: user.tenant_id
      },
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时
    };
    
    return jwt.sign(payload, this.SECRET);
  }
  
  static async verifyToken(token: string): Promise<UserPayload | null> {
    try {
      const cleanToken = token.replace('Bearer ', '');
      
      // 首先尝试使用 jsonwebtoken 验证
      const decoded = jwt.verify(cleanToken, this.SECRET) as any;
      
      if (typeof decoded === 'string' || !decoded.exp) {
        throw new Error('Invalid token format');
      }
      
      const expired = Date.now() / 1000 > decoded.exp;
      if (expired) {
        return null;
      }
      
      return decoded as UserPayload;
    } catch (e) {
      // 使用 jose 作为备选方案
      try {
        const decoded = jose.decodeJwt(cleanToken);
        if (!decoded.exp || Date.now() / 1000 > decoded.exp) {
          return null;
        }
        return decoded as UserPayload;
      } catch (joseError) {
        return null;
      }
    }
  }
  
  static generatePasswordResetToken(userId: string): string {
    const payload = {
      userId,
      type: 'password_reset',
      exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时
    };
    
    return jwt.sign(payload, this.SECRET);
  }
  
  static verifyPasswordResetToken(token: string): string | null {
    try {
      const decoded = jwt.verify(token, this.SECRET) as any;
      
      if (decoded.type !== 'password_reset' || !decoded.userId) {
        return null;
      }
      
      return decoded.userId;
    } catch (e) {
      return null;
    }
  }
}
```

#### 2.3 会话管理
```typescript
// packages/serve/src/utils/session.ts
export class SessionManager {
  private prisma: PrismaClient;
  
  constructor() {
    this.prisma = new PrismaClient();
  }
  
  async createSession(userId: string, tenantId: string, tokenHash: string, deviceInfo: any): Promise<void> {
    await this.prisma.user_sessions.create({
      data: {
        id: generateUUID(),
        user_id: userId,
        tenant_id: tenantId,
        token_hash: tokenHash,
        device_info: deviceInfo,
        ip_address: deviceInfo.ip,
        user_agent: deviceInfo.userAgent,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
      }
    });
  }
  
  async validateSession(tokenHash: string): Promise<boolean> {
    const session = await this.prisma.user_sessions.findFirst({
      where: {
        token_hash: tokenHash,
        is_active: true,
        expires_at: {
          gt: new Date()
        }
      }
    });
    
    if (session) {
      // 更新最后活跃时间
      await this.prisma.user_sessions.update({
        where: { id: session.id },
        data: { last_active_at: new Date() }
      });
      return true;
    }
    
    return false;
  }
  
  async revokeSession(tokenHash: string): Promise<void> {
    await this.prisma.user_sessions.updateMany({
      where: { token_hash: tokenHash },
      data: { is_active: false }
    });
  }
  
  async revokeAllUserSessions(userId: string): Promise<void> {
    await this.prisma.user_sessions.updateMany({
      where: { user_id: userId },
      data: { is_active: false }
    });
  }
}
```

### 3. 用户管理功能实现

#### 3.1 用户认证服务
```typescript
// packages/serve/src/services/AuthService.ts
export class AuthService {
  private prisma: PrismaClient;
  private sessionManager: SessionManager;
  
  constructor() {
    this.prisma = new PrismaClient();
    this.sessionManager = new SessionManager();
  }
  
  async signInByEmailPassword(email: string, password: string, deviceInfo: any): Promise<SignInResult> {
    // 查找用户
    const user = await this.prisma.members.findFirst({
      where: { 
        email,
        tmp_deleted_at: null 
      }
    });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 检查账户锁定状态
    if (user.locked_until && user.locked_until > new Date()) {
      throw new Error('账户已被锁定，请稍后再试');
    }
    
    // 检查是否需要密码重置
    if (user.password_reset_required) {
      const resetToken = JWTManager.generatePasswordResetToken(user.user_id);
      return {
        statusCode: 200,
        message: 'PASSWORD_RESET_REQUIRED',
        data: {
          resetToken,
          user: {
            userId: user.user_id,
            email: user.email
          }
        }
      };
    }
    
    // 验证密码
    const isValidPassword = await PasswordManager.verifyPassword(password, user.password_hash!);
    
    if (!isValidPassword) {
      // 增加登录失败次数
      await this.handleFailedLogin(user.user_id);
      throw new Error('密码错误');
    }
    
    // 重置登录失败次数
    await this.resetFailedLoginAttempts(user.user_id);
    
    // 生成 JWT
    const token = JWTManager.generateToken({
      user_id: user.user_id,
      email: user.email,
      nickname: user.nickname,
      tenant_id: user.tenant_id
    });
    
    // 创建会话
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    await this.sessionManager.createSession(user.user_id, user.tenant_id, tokenHash, deviceInfo);
    
    // 更新最后登录时间
    await this.prisma.members.update({
      where: { id: user.id },
      data: { last_login_at: new Date() }
    });
    
    return {
      statusCode: 200,
      message: 'success',
      data: {
        access_token: token,
        id_token: token,
        user: {
          userId: user.user_id,
          email: user.email,
          nickname: user.nickname
        }
      }
    };
  }
  
  private async handleFailedLogin(userId: string): Promise<void> {
    const user = await this.prisma.members.findUnique({
      where: { user_id: userId }
    });
    
    if (!user) return;
    
    const newAttempts = user.login_attempts + 1;
    const updateData: any = { login_attempts: newAttempts };
    
    // 如果失败次数达到5次，锁定账户1小时
    if (newAttempts >= 5) {
      updateData.locked_until = new Date(Date.now() + 60 * 60 * 1000);
    }
    
    await this.prisma.members.update({
      where: { user_id: userId },
      data: updateData
    });
  }
  
  private async resetFailedLoginAttempts(userId: string): Promise<void> {
    await this.prisma.members.update({
      where: { user_id: userId },
      data: {
        login_attempts: 0,
        locked_until: null
      }
    });
  }
}
```

### 4. 插件系统集成

#### 4.1 本地认证插件
```typescript
// packages/serve/src/context/plugins/local-auth.ts
import { LocalAuthenticationClient } from '../../adapters/LocalAuthenticationClient';
import { ActionContextPluginLoader } from '../../types';

const localAuthPlugin: ActionContextPluginLoader = (context) => {
  const authClient = new LocalAuthenticationClient({
    // 配置参数
  });
  
  authClient.setAccessToken(context.token);
  
  return {
    name: 'authing', // 保持原名称，确保兼容性
    plugin: authClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    authing: LocalAuthenticationClient;
  }
}

export default localAuthPlugin;
```

#### 4.2 本地管理插件
```typescript
// packages/serve/src/context/plugins/local-auth-manage.ts
import { LocalManagementClient } from '../../adapters/LocalManagementClient';
import { ActionContextPluginLoader } from '../../types';

const localAuthManagePlugin: ActionContextPluginLoader = () => {
  const authManagement = new LocalManagementClient({
    // 配置参数
  });
  
  return {
    name: 'authingManage', // 保持原名称，确保兼容性
    plugin: authManagement,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    authingManage: LocalManagementClient;
  }
}

export default localAuthManagePlugin;
```

## ✅ 验收标准

1. **接口兼容性**
   - [ ] 所有 Authing SDK 接口已实现
   - [ ] 返回数据格式与原接口一致
   - [ ] 错误处理机制兼容

2. **功能完整性**
   - [ ] 用户认证功能正常
   - [ ] 密码管理功能完整
   - [ ] 会话管理功能可用
   - [ ] 权限验证功能正确

3. **安全性要求**
   - [ ] 密码加密强度符合要求
   - [ ] JWT 令牌安全可靠
   - [ ] 会话管理安全有效
   - [ ] 防暴力破解机制有效

## 🔧 Augment Code 提示词

```
请帮我开发 Roasmax 项目的本地认证适配器：

1. 实现兼容 Authing AuthenticationClient 接口的本地适配器
2. 实现兼容 Authing ManagementClient 接口的本地适配器
3. 开发密码加密和验证功能
4. 实现 JWT 令牌管理
5. 开发会话管理系统
6. 创建用户认证服务
7. 集成到现有插件系统

要求：
- 保持与 Authing SDK 接口完全兼容
- 实现安全的密码管理
- 提供可靠的会话管理
- 支持多租户架构
- 包含完整的错误处理
- 提供详细的类型定义

请提供完整的 TypeScript 实现代码。
```

## 📅 时间安排

- **第1天**: 适配器架构设计和核心接口实现
- **第2天**: 认证核心功能开发
- **第3天**: 用户管理功能实现
- **第4天**: 插件系统集成和测试

## 🚨 风险提示

1. **接口兼容性风险**: 可能存在接口不完全兼容的情况
2. **安全性风险**: 本地实现可能存在安全漏洞
3. **性能风险**: 本地实现性能可能不如 Authing
