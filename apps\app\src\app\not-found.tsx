'use client';

import Link from 'next/link';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useInterval } from '@/hooks/useInterval';

export default function NotFound() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(10);

  useInterval(
    () => {
      if (countdown <= 1) {
        router.push('/ai-video');
        return;
      }
      setCountdown((prev) => prev - 1);
    },
    countdown <= 0 ? null : 1000,
  );

  return (
    <div className="flex h-[100vh] w-full items-center justify-center text-white">
      <div>
        <div className="text-[404px]">404</div>
        <div className="-mt-16 flex items-center justify-center text-2xl">你迷路了...</div>
        <div className="mt-4 flex items-center justify-center text-lg">
          {countdown} 秒后
          <Link href="/ai-video" className="ml-1 text-[#00E1FF]" aria-label="返回首页">
            返回首页
          </Link>
        </div>
      </div>
    </div>
  );
}
