// import { ProvidersWrapper } from '@/components/ProvidersWrapper';
import { cn } from '@/utils/cn';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import React from 'react';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '分享 - 大卖推荐',
  description: '大卖推荐分享页面',
};

export default function ShareLayout({ children }: { children: React.ReactNode }) {
  return <div className="flex h-[100vh] w-full flex-col items-center justify-center">{children}</div>;
}
