import tiktokService from '@/services/tiktokService';
import useAdStore, { useCurrentAdGroup, useCurrentAdvertiser, useCurrentCampaign } from '@/store/ads/adStore';
import { useEffect, useState } from 'react';
import useSWR from 'swr';
import emitter from '@/utils/mitt';

export const adListFetcher = async (params: {
  page: number;
  pageSize: number;
  advertiserIds?: string;
  groupIds?: string;
  campaignIds?: string;
  adName?: string;
  startTime?: string;
  endTime?: string;
  operationStatus?: string;
}) => {
  const { page, pageSize, advertiserIds, groupIds, campaignIds, adName, startTime, endTime, operationStatus } = params;

  const data = await tiktokService.getAdList({
    ...(advertiserIds ? { advertiser_ids: advertiserIds } : {}),
    ...(groupIds ? { group_ids: groupIds } : {}),
    ...(campaignIds ? { campaign_ids: campaignIds } : {}),
    ...(adName ? { ad_name: adName } : {}),
    ...(startTime ? { start_time: startTime } : {}),
    ...(endTime ? { end_time: endTime } : {}),
    ...(operationStatus !== 'All' ? { operation_status: operationStatus } : {}),
    page,
    page_size: pageSize,
  });

  return data;
};

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

export function useAdList(initialPage = 1, initialPageSize = 10) {
  const currentAdvertiser = useCurrentAdvertiser();
  const currentAdGroup = useCurrentAdGroup();
  const currentCampaign = useCurrentCampaign();

  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
    total: 0,
  });

  const advertiserIds = currentAdvertiser?.map((advertiser) => advertiser.advertiserId).join(',');
  const groupIds = currentAdGroup?.map((adGroup) => adGroup.groupId).join(',');
  const campaignIds = currentCampaign?.map((campaign) => campaign.campaignId).join(',');
  const [adName, setAdName] = useState<string>('');
  const [operationStatus, setOperationStatus] = useState<string>('All');
  const { dateRange } = useAdStore();
  const swrKey = currentAdvertiser
    ? [
        '/api/ads/adList',
        {
          ...pagination,
          advertiserIds,
          groupIds,
          campaignIds,
          adName,
          startTime: dateRange?.startTime,
          endTime: dateRange?.endTime,
          ...(operationStatus !== 'All' ? { operationStatus } : {}),
        },
      ]
    : null;

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    swrKey,
    ([_, params]) => adListFetcher(params as any),
    {
      keepPreviousData: true,
      refreshInterval: 1000 * 60 * 5, // 5分钟刷新一次
      revalidateOnFocus: false,
      revalidateIfStale: true,
      revalidateOnMount: true, // 组件挂载时进行验证
      revalidateOnReconnect: true, // 重新连接时不自动重新验证
    },
  );

  useEffect(() => {
    const handleRefresh = (params: {
      page?: number;
      pageSize?: number;
      total?: number;
      adName?: string;
      operationStatus?: string;
    }) => {
      const { page, pageSize, total, adName: newAdName, operationStatus: newOperationStatus } = params;

      if (page !== undefined || pageSize !== undefined || total !== undefined) {
        setPagination((prev) => ({
          page: page ?? prev.page,
          pageSize: pageSize ?? prev.pageSize,
          total: total ?? prev.total,
        }));
      }

      if (newAdName !== undefined) {
        setAdName(newAdName);
      }

      if (newOperationStatus !== undefined) {
        setOperationStatus(newOperationStatus);
      }

      mutate();
    };

    emitter.on('REFRESH_AD_LIST', handleRefresh);
    return () => {
      emitter.off('REFRESH_AD_LIST', handleRefresh);
    };
  }, [mutate]);
  const syncRealTimeData = async () => {
    if (!currentAdvertiser || !currentAdGroup) return;

    try {
      // TODO: 需要根据广告主和广告系列来同步
      await tiktokService.getAdRealTimeData({
        group_dates: [],
        start_date: dateRange?.startTime ?? '',
        end_date: dateRange?.endTime ?? '',
      });
      mutate();
    } catch (error) {
      console.error('同步广告实时数据失败:', error);
      throw error;
    }
  };
  return {
    adData: data,
    isLoading: isLoading || isValidating,
    isError: error,
    refresh: () => mutate(),
    syncRealTimeData,
    mutate: (pageInfo: {
      newPage?: number;
      newPageSize?: number;
      total: number;
      newAdName?: string;
      operationStatus?: string;
    }) => {
      if (pageInfo.newPage !== undefined || pageInfo.newPageSize !== undefined) {
        setPagination((prev) => ({
          page: pageInfo.newPage ?? prev.page,
          pageSize: pageInfo.newPageSize ?? prev.pageSize,
          total: pageInfo.total ?? prev.total,
          adName: pageInfo.newAdName,
          operationStatus: pageInfo.operationStatus,
        }));
      }
      if (pageInfo.newAdName !== undefined) {
        setAdName(pageInfo.newAdName);
      }
      if (pageInfo.operationStatus !== undefined) {
        setOperationStatus(pageInfo.operationStatus);
      }
    },
    swrKey,
  };
}
