import {
  AgeGroup,
  BidType,
  BillingEvent,
  BudgetMode,
  DeepBidType,
  Gender,
  OptimizationEvent,
  OptimizationGoal,
  PlacementType,
  ScheduleType,
} from '@/types/ads';

export interface AdvertiserListReq {
  uid: string;
  page: number;
  page_size: number;
  start_time?: string;
  end_time?: string;
}

export interface CampaignListReq {
  advertiser_ids: string;
  campaign_ids?: string;
  campaign_name?: string;
  primary_status?: string;
  pub_status?: string;
  page: number;
  page_size: number;
  start_time?: string;
  end_time?: string;
}
[];

export interface CreateCampaignReq {
  advertiser_id: string;
  campaign_name?: string;
  campaign_product_source: string;
  operation_status: string;
  budget_mode: string;
  budget: number;
  ad_type: string;
}
[];

// 广告组列表请求参数
export interface AdGroupListRequest {
  advertiser_ids?: string[];
  campaign_ids?: string[];
  tag_names?: string;
  start_time?: string;
  end_time?: string;
  page?: number;
  page_size?: number;
  operation_status?: string;
}
export interface SyncAdGroupListRequest {
  advertiser_id: string;
  campaign_id?: string;
  start_date: string;
  end_date: string;
}
export interface IBcAssetsListRequest {
  asset_type?: string; // 可能需要添加这个参数
  bc_id?: string; // 可能需要添加这个参数
  page?: number;
  page_size?: number;
}
export interface IProjectRequest {
  advertiser_id: string;
}
export interface CustomAudienceListRequest {
  advertiser_id: string;
  page?: number;
  page_size?: number;
}
// 创建广告组请求参数
export interface CreateAdGroupRequest {
  // 必填字段
  tags: string[];
  advertiser_id: string;
  campaign_id: string;
  adgroup_name: string;
  name?: string;
  is_template?: boolean;
  actions: UpdateAdGroupActions[];
  ad_format?: string;
  // 投放设置
  frequency?: number | null;
  frequency_schedule?: number | null;
  shopping_ads_retargeting_actions_days: number;
  pacing?: 'PACING_MODE_SMOOTH' | 'PACING_MODE_FAST';
  languages?: string[];
  gender?: Gender;
  dayparting?: string;
  budget: number;
  budget_mode?: BudgetMode;
  // creatives: Creatives[];
  // 定向设置
  network_types?: string[];
  interest_category_ids?: string[];

  // 出价设置
  bid_type?: BidType;
  conversion_bid_price?: number;
  deep_bid_type?: DeepBidType;

  // 创意设置
  bid_price?: number;
  placements: string[];
  // 人群定向
  age_groups?: AgeGroup[];
  location_ids?: string[];

  // 状态相关
  billing_event?: BillingEvent;
  secondary_status?: 'ENABLE' | 'DISABLE';
  placement_type?: PlacementType;
  optimization_event?: OptimizationEvent;
  optimization_goal?: OptimizationGoal;
  // 受众设置
  audience_ids?: string[];
  vertical_video_strategy?: string;
  video_download_disabled?: boolean;
  secondary_optimization_event?: string | null;
  promotion_type?: string;
  // 时间设置
  schedule_start_time?: string;
  schedule_end_time?: string;
  schedule_type?: ScheduleType;

  // 其他设置
  comment_disabled?: boolean;
  product_source?: 'STORE' | string;
  shopping_ads_type?: string;
  store_authorized_bc_id?: string;
  store_id?: string;
  store_type?: string;
}
//更新广告组请求参数
export type UpdateAdGroupActions = {
  action_scene: string;
  action_period: number;
  video_user_actions: string[];
  action_category_ids: string[];
};
export interface UpdateAdGroupRequest {
  id: number;
  advertiser_id: string;
  adgroup_id: string;
  adgroup_name: string;
  location_ids: string[];
  languages: string[];
  gender: string;
  age_groups: string[];
  network_types: string[];
  audience_ids: string[];
  purchase_intention_keyword_ids: string[];
  interest_category_ids: string[];
  actions: UpdateAdGroupActions[];
}
// 更新广告组状态请求参数
export interface UpdateAdGroupStatusRequest {
  ids: string[];
  advertiser_id: string;
  operation_status: string;
  adgroup_ids?: string[];
  allow_partial_success: boolean;
  request_id: string;
}
// 兴趣列表请求参数
export interface GetInterestListRequest {
  advertiser_id: string;
  sub_targeting_types: string;
  search_keywords?: string;
}
// 地域列表请求参数
export interface GetTargetingRegionListsRequest {
  advertiser_id: string;
  placements: string[];
  objective_type: string;
  level_range: string;
  language: string;
  shopping_ads_type: string;
  promotion_type: string;
}
// 导出广告组列表请求参数
export interface ExportAdGroupListRequest {
  advertiser_ids?: string[];
  campaign_ids?: string[];
  adgroup_name?: string;
  operation_status?: string;
  adgroup_id?: string;
  tag_names?: string;
  start_time?: string;
  end_time?: string;
  page?: number;
  page_size?: number;
}

export interface ExportAdListRequest {
  advertiser_id: string;
  campaign_ids: string;
  group_ids: string;
  start_date?: string;
  end_date?: string;
  ad_name?: string;
}
// 广告列表请求参数
export interface AdListRequest {
  advertiser_ids: string; // todo: 改成ids
  group_ids: string; // todo: 改成ids
  ad_name?: string;
  page: number;
  page_size: number;
  start_time?: string;
  end_time?: string;
}

export interface SyncCampaignListReq {
  advertiser_ids?: string[];
  start_date: string;
  end_date: string;
}

export interface UpdateAdStatusReq {
  advertiser_id: string;
  operation_status: 'ENABLE' | 'DISABLE' | 'DELETE';
  ids: string[];
  ad_ids?: string[];
}
interface GroupDate {
  advertiser_id: string;
  campaign_id: string;
  group_ids: string[];
}
export interface SyncAdListRequest {
  group_dates: GroupDate[];
  start_date: string;
  end_date: string;
}
