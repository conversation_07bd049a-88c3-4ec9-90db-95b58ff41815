import { startVideoGenerationTask } from '@/services/actions/video-generation-task';
import { action } from '@/utils/server-action/action';
import { to } from '@roasmax/utils';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { GenerateViralVideoFormValues } from '../app/(app)/viral/page';
import { useWallet } from './useWallet';

interface UseVideoGenerationProps {
  form: UseFormReturn<GenerateViralVideoFormValues>;
  link: string;
  selectedTemplates: string[];
  analysisResult: any;
}

export const useVideoGeneration = ({ form, link, selectedTemplates, analysisResult }: UseVideoGenerationProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const router = useRouter();
  const { quota, refresh: refreshWallet } = useWallet();

  const handleGenerateVideo = async () => {
    if (isGenerating) return;

    try {
      await form.trigger();
      const values = form.getValues();

      // 检查配额是否足够
      if (!quota || quota <= 0) {
        toast.error('余额不足请联系管理员充值');
        return;
      }

      if (Object.keys(form.formState.errors).length > 0) {
        toast.error('请检查表单填写是否完整');
        return;
      }

      // 验证表单
      if (!values.materialIds.length) {
        toast.error('请先上传视频');
        return;
      }

      const requiredFields = {
        taskName: '任务名称',
        generateRound: '生成轮次',
        speed: '生成速度',
        voiceType: '目标音色',
      };

      for (const [field, label] of Object.entries(requiredFields)) {
        if (!values[field as keyof GenerateViralVideoFormValues]) {
          toast.error(`请设置${label}`);
          return false;
        }
      }

      setIsGenerating(true);

      const [err, result] = await to(
        action(
          startVideoGenerationTask,
          {
            name: values.taskName || 'AI搜索爆款克隆',
            sliceType: '300',
            method: 'gc_imitate',
            generateCount: Number(values.generateRound),
            accelerate: values.speed,
            subtitles: values.subtitle,
            transition_mode: values.transitionMode ? 'fade' : 'null',
            materialIds: values.materialIds,
            productUrl: link || '',
            productAnalysis: analysisResult?.product_analysis ?? '',
            targetLanguage: '',
            targetVoice: values.voiceType,
            generationType: '大卖推荐',
            sceneImplantation: values.sceneImplantation,
            festiveAtmosphere: values.festiveAtmosphere,
            templateVideoTiktokIds: selectedTemplates || [],
          },
          { errorType: 'return' },
        ),
      );

      if (err || !result.success) {
        toast.error('生成视频失败');
        return;
      }

      if (result.data) {
        toast.success('创建视频生成任务成功');
        await refreshWallet();
        router.push('/video-generation-tasks');
      }
    } catch (error) {
      console.error('生成视频失败:', error);
      toast.error('生成视频失败');
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    isGenerating,
    handleGenerateVideo,
  };
};
