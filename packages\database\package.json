{"name": "@roasmax/database", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**", "node_modules/.prisma/client"], "scripts": {"build": "prisma generate && tsup", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@prisma/client": "^5.10.2"}, "devDependencies": {"@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*", "prisma": "^5.10.2", "prisma-json-types-generator": "^3.0.3"}}