import { ProButton } from '@/components/pro/pro-button';
import { ProFormField } from '@/components/pro/pro-form';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  Input,
  Switch,
  Textarea,
} from '@/components/ui';
import { createVideoCutTask } from '@/services/actions/video-cut-task';
import { action } from '@/utils/server-action/action';
import React, { useImperativeHandle, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

export type CreateTaskDialogRef = {
  show: () => void;
};

export type CreateTaskDialogProps = {
  onCommitted?: (success: boolean) => void;
};

type FormValue = Parameters<typeof createVideoCutTask>[0]['data']['data'];

export const CreateTaskDialog = React.forwardRef<CreateTaskDialogRef, CreateTaskDialogProps>((props, ref) => {
  const [open, setOpen] = useState(false);
  const form = useForm<FormValue>();

  const [run, setRun] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => {
      setOpen(true);
    },
  }));

  const handleSubmit = form.handleSubmit(async (data) => {
    const res = await action(createVideoCutTask, { data, run }, { errorType: 'return' });
    if (res.success) {
      toast.success('创建成功');
      setOpen(false);
      form.reset({});
      props.onCommitted?.(true);
    } else {
      toast.error('创建失败');
      props.onCommitted?.(false);
    }
  });

  const handleCancel = () => {
    setOpen(false);
    form.reset({});
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[900px] max-w-[900px]">
        <DialogHeader>
          <DialogTitle>创建切分任务</DialogTitle>
        </DialogHeader>
        <div className="max-h-[60vh] overflow-auto">
          <Form {...form}>
            <form>
              <ProFormField
                name="name"
                label="任务名称"
                form={form}
                renderFormControl={(field) => <Input {...field} />}
              />
              <ProFormField name="ip" label="IP名称" form={form} renderFormControl={(field) => <Input {...field} />} />
              <ProFormField
                name="live_room"
                label="直播间"
                form={form}
                renderFormControl={(field) => <Input {...field} />}
              />
              <ProFormField
                name="live_session"
                label="直播场次"
                form={form}
                renderFormControl={(field) => <Input {...field} />}
              />
              <ProFormField
                name="origin_url"
                label="视频链接"
                form={form}
                renderFormControl={(field) => <Input {...field} />}
              />
              <ProFormField
                name="goods_list"
                label="商品名称列表(一行一个)"
                form={form}
                renderFormControl={(field) => <Textarea {...field} className="h-[100px]" />}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <div className="mt-4 flex justify-end gap-2">
            <div className="flex items-center gap-2">
              立即执行 <Switch checked={run} onCheckedChange={setRun} />
            </div>
            <Button variant="outline" className="h-[32px] w-[92px]" onClick={handleCancel}></Button>
            <ProButton className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]" onClick={handleSubmit}>
              创建
            </ProButton>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});
