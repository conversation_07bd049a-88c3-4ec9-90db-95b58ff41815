// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/typescript-node
{
  "name": "roasmax",
  "build": {
    // Path is relative to the devcontainer.json file.
    "dockerfile": "Dockerfile"
  },
  "runArgs": ["--name=roasmax"],
  // Features to add to the dev container. More info: https://containers.dev/features.
  "features": {
    "ghcr.io/devcontainers/features/docker-outside-of-docker:1": {}
  },
  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],
  // Use 'postCreateCommand' to run commands after the container is created.
  // "postCreateCommand": "git config --global --add safe.directory /workspaces/nextjsVideoPlayback",
  // "postStartCommand": ["chmod -R 777 node_modules"],
  // Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": [
        // Linting / Formatting
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        "rvest.vs-code-prettier-eslint",
        // Coding Assist
        "Prisma.prisma",
        "ChakrounAnas.turbo-console-log",
        "streetsidesoftware.code-spell-checker",
        "Gruntfuggly.todo-tree",
        "naumovs.color-highlight",
        "bradlc.vscode-tailwindcss",
        // Git Enhancements
        "eamodio.gitlens",
        "mhutchie.git-graph",
        // Docker
        "ms-azuretools.vscode-docker",
        // Theme
        "PKief.material-icon-theme"
      ]
    }
  },
  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  "remoteUser": "root"
}
