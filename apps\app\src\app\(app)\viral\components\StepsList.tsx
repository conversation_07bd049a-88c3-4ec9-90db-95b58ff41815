import { Magic, StepSuccessful } from '@/components/icon';
import { Step, SubStep } from '@/types/product-analysis';
import { cn } from '@/utils/cn';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';

interface StepsListPro {
  playbackSteps: Step[];
  currentMainStep: number;
  currentSubStep: number;
  flattenedSteps: any[];
  currentFlatIndex: number;
  completedSteps: number;
  replayCompleted: boolean;
  stepRefs: any; // 用于存储每个步骤的引用
  onStepChange?: (mainIndex: number, subIndex: number | null) => void; // 新增：步骤变更回调函数
}

const Typewriter = ({
  text,
  typingSpeed = 20,
  onComplete,
  shouldForceComplete = false,
}: {
  text: string;
  typingSpeed?: number;
  onComplete: () => void;
  shouldForceComplete?: boolean;
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);
  const typingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef<number>(Date.now());
  const forceCompleteRef = useRef(shouldForceComplete);
  const typewriterRef = useRef<HTMLDivElement>(null);
  // 添加滚动到底部的函数
  const scrollToBottom = useCallback(() => {
    // 找到最近的滚动容器并滚动到底部
    if (typewriterRef.current) {
      const scrollContainer = typewriterRef.current.closest('.overflow-auto');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, []);
  // 使用MutationObserver监听内容变化
  useEffect(() => {
    if (!typewriterRef.current) return;

    // 创建一个MutationObserver实例
    const observer = new MutationObserver(() => {
      // 当内容变化时滚动到底部
      scrollToBottom();
    });

    // 配置观察选项
    const config = { childList: true, subtree: true, characterData: true };

    // 开始观察目标节点
    observer.observe(typewriterRef.current, config);

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, [scrollToBottom]);

  // 实时更新forceCompleteRef
  useEffect(() => {
    forceCompleteRef.current = shouldForceComplete;
    // 立即检查是否需要强制完成
    if (shouldForceComplete && !isCompleted) {
      handleForceComplete();
    }
  }, [shouldForceComplete]);

  // 强制完成的处理函数
  const handleForceComplete = useCallback(() => {
    // 清理定时器
    if (typingRef.current) {
      clearTimeout(typingRef.current);
      typingRef.current = null;
    }
    // 立即显示完整文本
    setDisplayText(text);
    setIsCompleted(true);
  }, [text]);

  // 清理函数
  const cleanup = useCallback(() => {
    if (typingRef.current) {
      clearTimeout(typingRef.current);
      typingRef.current = null;
    }
  }, []);

  // 正常打字效果
  useEffect(() => {
    // 如果已经完成或应该强制完成，则不执行打字效果
    if (isCompleted || shouldForceComplete) {
      if (shouldForceComplete && !isCompleted) {
        handleForceComplete();
      }
      return cleanup;
    }

    let currentIndex = 0;
    setDisplayText('');
    setIsCompleted(false);

    const typeNextChar = () => {
      // 检查时间差，如果超过 1 秒，强制完成
      const currentTime = Date.now();
      if (currentTime - lastUpdateTimeRef.current > 1000) {
        handleForceComplete();
        return;
      }

      // 每次检查是否应该强制完成
      if (forceCompleteRef.current) {
        handleForceComplete();
        return;
      }

      if (currentIndex < text.length) {
        setDisplayText((prev) => {
          const newText = prev + text[currentIndex];
          lastUpdateTimeRef.current = currentTime;
          return newText;
        });
        currentIndex++;
        lastUpdateTimeRef.current = currentTime;
        typingRef.current = setTimeout(typeNextChar, typingSpeed);
      } else {
        setIsCompleted(true);
      }
    };

    // 开始打字
    typeNextChar();

    // 组件卸载或依赖变化时的清理
    return cleanup;
  }, [text, typingSpeed, isCompleted, shouldForceComplete, cleanup, handleForceComplete]);

  // 完成回调
  useEffect(() => {
    if (isCompleted) {
      onComplete();
    }
  }, [isCompleted, onComplete]);

  return (
    <div ref={typewriterRef}>
      <ReactMarkdown className="markdownShouldShow ml-2 mt-2">{displayText.replace('undefined', '')}</ReactMarkdown>
    </div>
  );
};

export default function StepsList({
  playbackSteps,
  currentMainStep,
  currentSubStep,
  flattenedSteps,
  currentFlatIndex,
  completedSteps,
  replayCompleted,
  stepRefs,
  onStepChange,
}: StepsListPro) {
  const containerRef = useRef<HTMLDivElement>(null);
  // 添加状态来跟踪每个主步骤的lodding显示状态
  const [hiddenLoddingSteps, setHiddenLoddingSteps] = useState<Record<number, boolean>>({});
  // 添加状态来跟踪每个子步骤的lodding显示状态
  const [hiddenSubLoddingSteps, setHiddenSubLoddingSteps] = useState<Record<string, boolean>>({});
  // 添加状态来跟踪当前应该显示的子步骤索引
  const [visibleSubSteps, setVisibleSubSteps] = useState<Record<number, number>>({});
  // 添加状态来跟踪子步骤打字机效果完成情况
  const [showBriefContent, setShowBriefContent] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // 为每个有lodding状态的主步骤设置定时器
    const timers: NodeJS.Timeout[] = [];

    playbackSteps.forEach((step, index) => {
      // 处理主步骤的loading状态
      if (step.lodding && !hiddenLoddingSteps[index]) {
        // 只处理当前应该显示的主步骤（基于completedSteps）
        if (index <= completedSteps - 1) {
          const timer = setTimeout(() => {
            setHiddenLoddingSteps((prev) => ({
              ...prev,
              [index]: true,
            }));
            // 当主步骤loading结束时，初始化该主步骤的可见子步骤索引为0（第一个子步骤）
            if (step.sub_steps && step.sub_steps.length > 0) {
              setVisibleSubSteps((prev) => ({
                ...prev,
                [index]: 0,
              }));
            }
          }, 3000); // 3秒后隐藏
          timers.push(timer);
        }
      }

      // 只有当主步骤的loading已经结束时，才处理子步骤的loading
      if (hiddenLoddingSteps[index] && step.sub_steps && step.sub_steps.length > 0) {
        // 只处理当前可见的子步骤
        const currentVisibleSubIndex = visibleSubSteps[index] || 0;
        if (currentVisibleSubIndex < step.sub_steps.length) {
          const subStep = step.sub_steps[currentVisibleSubIndex];
          const subStepKey = `${index}-${currentVisibleSubIndex}`;

          if (subStep?.lodding && !hiddenSubLoddingSteps[subStepKey]) {
            const subTimer = setTimeout(() => {
              setHiddenSubLoddingSteps((prev) => ({
                ...prev,
                [subStepKey]: true,
              }));
            }, 3000); // 3秒后隐藏
            timers.push(subTimer);
          }
        }
      }
    });

    // 清理定时器
    return () => {
      timers.forEach((timer) => clearTimeout(timer));
    };
  }, [playbackSteps, hiddenLoddingSteps, hiddenSubLoddingSteps, visibleSubSteps, completedSteps, showBriefContent]);

  const handleSubStepTypewriterComplete = useCallback(
    (mainIndex: number, subIndex: number) => {
      const subStepKey = `${mainIndex}-${subIndex}`;
      const playbackStep = playbackSteps[mainIndex];

      // 在打字机效果完成后，设置该子步骤显示简略内容
      setShowBriefContent((prev) => ({
        ...prev,
        [subStepKey]: true,
      }));

      const isLastSubStep = playbackStep?.sub_steps && subIndex === playbackStep.sub_steps.length - 1;

      // 检查是否应该显示下一个子步骤
      setVisibleSubSteps((prev) => {
        const currentVisible = prev[mainIndex] || 0;

        if (
          subIndex === currentVisible &&
          playbackStep?.sub_steps &&
          currentVisible < playbackStep.sub_steps.length - 1
        ) {
          const nextSubIndex = currentVisible + 1;

          // 通知父组件步骤已变更
          if (onStepChange) {
            setTimeout(() => {
              onStepChange(mainIndex, nextSubIndex);
            }, 1000); // 1秒后进入下一个主步骤
          }
          return {
            ...prev,
            [mainIndex]: nextSubIndex,
          };
        }

        // 如果是最后一个子步骤，且下一个主步骤存在，则准备进入下一个主步骤
        if (isLastSubStep && mainIndex < playbackSteps.length - 1 && mainIndex + 1 <= completedSteps - 1) {
          // 检查当前主步骤的所有子步骤是否都已完成
          const allSubStepsCompleted = playbackStep.sub_steps?.every((_, idx) => {
            const key = `${mainIndex}-${idx}`;
            return showBriefContent[key];
          });

          // 只有当所有子步骤都完成后，才进入下一个主步骤
          if (allSubStepsCompleted) {
            // 延迟一段时间后再进入下一个主步骤，给用户时间阅读当前内容
            setTimeout(() => {
              // 初始化下一个主步骤的可见子步骤索引为0
              setVisibleSubSteps((prevState) => ({
                ...prevState,
                [mainIndex + 1]: 0,
              }));

              // 设置下一个主步骤的loading状态为已完成
              setHiddenLoddingSteps((prevState) => ({
                ...prevState,
                [mainIndex + 1]: true,
              }));

              // 通知父组件步骤已变更到下一个主步骤
              if (onStepChange) {
                onStepChange(mainIndex + 1, 0);
              }
            }, 1500); // 1秒后进入下一个主步骤
          }
        }

        return prev;
      });
    },
    [playbackSteps, onStepChange, completedSteps, showBriefContent],
  );
  const renderTypewriterContent = (step: Step | SubStep, mainIndex: number, subIndex: number | null) => {
    const stepKey = `${mainIndex}-${subIndex}`;
    const content = step.contentLodding || '';

    if (!content || replayCompleted || (subIndex !== null && showBriefContent[stepKey])) {
      const getTitle = () => {
        if (!content) return '';
        const lines = content.split('\n');
        const titleLine = lines.find((line) => line.trim().startsWith('##')) || lines[0] || '';
        return titleLine.replace(/^##\s*/, '').trim();
      };
      return (
        <ReactMarkdown className="markdownShouldShow ml-2 mt-2 inline-block rounded-2xl bg-[#EFEDFD1A] px-2">
          {`${getTitle()}...`}
        </ReactMarkdown>
      );
    }

    // 如果回放已完成且不是当前活动步骤，则直接显示完整内容而不使用打字机效果
    if (replayCompleted) {
      return <ReactMarkdown className="markdownShouldShow ml-2 mt-2">{content}</ReactMarkdown>;
    }

    // 当回放完成或者点击查看结果时，强制完成所有打字效果
    return (
      <Typewriter
        text={content}
        typingSpeed={20}
        shouldForceComplete={replayCompleted}
        onComplete={() => {
          if (subIndex !== null) {
            handleSubStepTypewriterComplete(mainIndex, subIndex);
          }
        }}
      />
    );
  };

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        top: containerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [playbackSteps, currentMainStep, currentSubStep, flattenedSteps, currentFlatIndex]);
  const hasActiveSteps = useCallback(() => {
    // 检查是否有主步骤正在加载
    for (let mainIndex = 0; mainIndex < playbackSteps.length; mainIndex++) {
      const step = playbackSteps[mainIndex];
      // 如果主步骤正在加载且未隐藏loading状态
      if (step?.lodding && !hiddenLoddingSteps[mainIndex] && mainIndex < completedSteps) {
        return true;
      }

      // 检查子步骤
      if (hiddenLoddingSteps[mainIndex] && step?.sub_steps && step.sub_steps.length > 0) {
        const currentVisibleSubIndex = visibleSubSteps[mainIndex] || 0;
        if (currentVisibleSubIndex < step.sub_steps.length) {
          const subStepKey = `${mainIndex}-${currentVisibleSubIndex}`;
          // 如果有子步骤正在加载或者正在显示内容，则认为有活动的步骤
          if (!hiddenSubLoddingSteps[subStepKey] || !showBriefContent[subStepKey]) {
            return true;
          }
        }
      }
    }
    return false;
  }, [playbackSteps, hiddenLoddingSteps, hiddenSubLoddingSteps, visibleSubSteps, showBriefContent, completedSteps]);
  return (
    <div ref={containerRef} className={cn('mb-4 flex-1 overflow-auto')}>
      <div className="mb-3 ml-4 mt-2 flex h-8 w-[207px] items-center rounded-[100px] border bg-[#E2E9F91A]">
        <div className="-ml-[10px] -mt-[2px]">
          <Magic className="transform-origin-center h-[61px] w-[55px] animate-spin" />
        </div>
        <div className="font-medium text-white">ROASMAX 分析助手</div>
      </div>
      <div className="ml-4 flex flex-col gap-4">
        {playbackSteps
          .filter((step, index) => {
            // 只显示已完成的步骤
            if (step.step_number > completedSteps) return false;

            // 检查前面的主步骤是否都已完成其所有子步骤
            for (let i = 0; i < index; i++) {
              const prevStep = playbackSteps[i];
              // 如果前面的主步骤还在loading，则不显示当前步骤
              if (prevStep?.lodding && !hiddenLoddingSteps[i]) return false;

              // 如果前面的主步骤有子步骤，检查是否都已完成
              if (prevStep?.sub_steps && prevStep.sub_steps.length > 0) {
                const allSubStepsCompleted = prevStep.sub_steps.every((_, subIdx) => {
                  const subKey = `${i}-${subIdx}`;
                  // 如果子步骤还在loading或者内容还未显示完成，则不显示当前步骤
                  if (!hiddenSubLoddingSteps[subKey]) return false;
                  if (!showBriefContent[subKey]) return false;
                  return true;
                });
                if (!allSubStepsCompleted) return false;
              }
            }

            return true;
          })
          .map((step, mainIndex) => {
            const currentVisibleSubIndex = visibleSubSteps[mainIndex] || 0;

            return (
              <div key={mainIndex} className="mb-4">
                <div
                  ref={stepRefs.current[`main-${mainIndex}`] || null}
                  className={`cursor-pointer rounded-lg transition-all`}
                >
                  <div className="flex gap-2">
                    <div className={`flex h-6 w-6 items-center justify-center`}>
                      <span className="text-xs font-medium">{`${mainIndex + 1})、`}</span>
                    </div>
                    {step.lodding && !hiddenLoddingSteps[mainIndex] ? (
                      <div className="flex items-center gap-2 text-sm">
                        <span>思考中</span>
                        <span className="dots animate-pulse"></span>
                      </div>
                    ) : (
                      <div>
                        <span className={`text-sm font-medium text-white`}>{step.step_title}</span>
                        {step.description && <p className={`mt-1 text-sm text-[#9fa4b2]`}>{step.description}</p>}
                      </div>
                    )}
                  </div>
                </div>
                {/* 只有当主步骤的loading结束后才显示子步骤 */}
                {hiddenLoddingSteps[mainIndex] && step.sub_steps && step.sub_steps.length > 0 && (
                  <div className="border-muted pl-8">
                    {step.sub_steps.map((subStep, subIndex) => {
                      const subStepKey = `${mainIndex}-${subIndex}`;

                      // 只显示当前可见索引及之前的子步骤
                      if (subIndex > currentVisibleSubIndex) {
                        return null;
                      }
                      // 初始显示子步骤的loading状态，除非它已经被隐藏
                      const showSubStepLoading = subStep.lodding && !hiddenSubLoddingSteps[subStepKey];
                      return (
                        <div
                          key={subIndex}
                          ref={stepRefs.current[`sub-${mainIndex}-${subIndex}`] || null}
                          className={`my-4 cursor-pointer rounded-lg transition-all`}
                        >
                          <div className="flex items-center gap-2">
                            {showSubStepLoading ? (
                              <>
                                <div className="flex items-center justify-center">
                                  <Magic className="transform-origin-center -ml-1 h-[32px] w-[26px] animate-spin" />
                                </div>
                                <div className="flex items-center gap-1">
                                  <span className="text-sm">{subStep.lodding}</span>
                                  <span className="dots animate-pulse"></span>
                                </div>
                              </>
                            ) : (
                              <div className="flex">
                                <div>
                                  <StepSuccessful />
                                  <div className="flex h-full items-center">
                                    <div className="ml-2 h-full w-1 border-l border-dashed border-[#FFFFFF1A]"></div>
                                  </div>
                                </div>
                                <div className="ml-2">
                                  <div className="text-sm text-white">{subStep.step_title}</div>
                                  {subStep.description && (
                                    <p className={`mt-2 text-sm text-white`}>{subStep.description}</p>
                                  )}
                                  {subStep.description1 && (
                                    <p className={`mt-2 text-sm text-white`}>{subStep.description1}</p>
                                  )}
                                  {renderTypewriterContent(subStep, mainIndex, subIndex)}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
      </div>
      {completedSteps > 0 && completedSteps < playbackSteps.length && !hasActiveSteps() && (
        <div className="ml-4 mt-4 flex items-center gap-2">
          <Magic className="transform-origin-center -ml-1 h-[32px] w-[26px] animate-spin" />
          <span className="text-sm">AI正在思考 </span>
          <span className="dots animate-pulse"></span>
        </div>
      )}
    </div>
  );
}
