import { VodClient } from '@roasmax/utils/tencentcloud';
import { ActionContextPluginLoader } from '../../types';

// TODO: 目前是使用的固定密钥，后续为了提升安全性，考虑转为使用临时密钥 https://cloud.tencent.com/document/product/1278/85305
const vod = new VodClient({
  credential: { secretId: process.env.VOD_SECRETID, secretKey: process.env.VOD_SECRETKEY },
  region: 'ap-shanghai',
  profile: { httpProfile: { endpoint: 'vod.tencentcloudapi.com' } },
});

const vodPlugin: ActionContextPluginLoader = () => {
  return {
    name: 'vod',
    plugin: vod,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 消息队列操作API
     */
    vod: typeof vod;
  }
}

export default vodPlugin;
