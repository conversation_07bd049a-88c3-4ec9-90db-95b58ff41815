'use server';

// API Base URL
const API_BASE_URL = 'https://bowongai--text2video-api-dev-fastapi-app.modal.run';

// 查询任务状态的server action
export async function queryVideoGenerationTaskStatus(taskId: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/task/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;

    let status = 'processing';
    let progress = 50;
    let videoUrl = '';
    let thumbnailUrl = '';
    let errorMessage = '';

    // 处理最新的返回格式: {status: "completed", data: {video_url: "...", image_url: "..."}}
    if (
      (parsedResult.status === 'completed' || parsedResult.status === 'failed') &&
      parsedResult.data &&
      typeof parsedResult.data === 'object'
    ) {
      if (parsedResult.status === 'completed') {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data.video_url;
        thumbnailUrl = parsedResult.data.image_url;
      } else {
        status = 'failed';
        progress = 0;
        errorMessage = parsedResult.data.error || parsedResult.message || '任务失败';
      }
    }
    // 处理新的返回格式
    else if (parsedResult.status === true && parsedResult.task_status === 'completed' && parsedResult.data) {
      status = 'completed';
      progress = 100;
      videoUrl = parsedResult.data.generated_video_url || parsedResult.data.result_url;
      thumbnailUrl = parsedResult.data.generated_image_url;
    }
    // 检查新的完整响应格式
    else if (parsedResult.result) {
      const resultStatus = parsedResult.result.status;

      if (resultStatus === 'failed') {
        status = 'failed';
        progress = 0;
        errorMessage = parsedResult.result.error || parsedResult.message;
      } else if (resultStatus === 'completed' && parsedResult.result.video_url) {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.result.video_url;
        thumbnailUrl = parsedResult.result.thumbnail_url || '';
      }
    }
    // 检查旧的API响应格式
    else if (parsedResult.status === true && parsedResult.data) {
      if (typeof parsedResult.data === 'string' && parsedResult.data.startsWith('http')) {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data;
      } else if (typeof parsedResult.data === 'object') {
        status = 'completed';
        progress = 100;
        videoUrl = parsedResult.data.video_url || parsedResult.data.videoUrl;
        thumbnailUrl = parsedResult.data.thumbnail_url || parsedResult.data.thumbnailUrl || '';
      }
    }

    return {
      taskId,
      status,
      progress,
      videoUrl,
      thumbnailUrl,
      errorMessage,
      apiResponse: parsedResult,
    };
  } catch (error) {
    console.error('查询任务失败:', error);

    throw new Error(`查询任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}
