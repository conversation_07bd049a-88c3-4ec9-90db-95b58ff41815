import { Button } from '@/components/ui/Button';
import { FormField, FormItem, FormLabel, FormMessage, FormControl } from '@/components/ui/Form';
import { Input } from '@/components/ui/Input';
import { format } from 'date-fns';
import { UseFormReturn } from 'react-hook-form';
import { Switch } from '@/components/ui/Switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/RadioGroup';
import { Label } from '@/components/ui/Label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { toast } from 'react-hot-toast';
import { AnswerIcon } from '@/components/icon';

interface AdGroupNameSectionProps {
  form: UseFormReturn<any>;
  isEditAdGroup: boolean;
  type: string;
  customize: boolean;
  customTagInput: string;
  setCustomize: (value: boolean) => void;
  setCustomTagInput: (value: string) => void;
  adgroupNameTags: string[];
  setAdgroupNameTags: (value: string[]) => void;
  storeType: string;
  setStoreType: (value: string) => void;
  storeList: any[];
  locationData: any[];
  advertiser: any;
  templateName: string;
  setTemplateName: (value: string) => void;
}
export function AdGroupNameSection({
  form,
  isEditAdGroup,
  type,
  customize,
  customTagInput,
  setCustomize,
  setCustomTagInput,
  adgroupNameTags,
  setAdgroupNameTags,
  storeType,
  setStoreType,
  storeList,
  locationData,
  advertiser,
  templateName,
  setTemplateName,
}: AdGroupNameSectionProps) {
  const defaultTags = [format(new Date(), 'yyyyMMdd'), '系列名称', '商品数据源', '优化目标', '是否出价'];
  const handleAddTag = (type: string) => {
    const tagMap: { [key: string]: string } = {
      date: format(new Date(), 'yyyyMMdd'),
      campaign: '系列名称',
      store: '商品数据源',
      optimization: '优化目标',
      bid: '是否出价',
    };

    const tag = tagMap[type];
    if (!tag) return;

    // 获取当前广告组名称
    const currentName = form.getValues('adgroup_name') || '';

    // 如果标签已存在,不重复添加
    if (adgroupNameTags.includes(tag)) {
      toast.error('标签已存在');
      return;
    }

    // 更新广告组名称和标签列表
    form.setValue('adgroup_name', currentName + tag);
    setAdgroupNameTags([...adgroupNameTags, tag]);
  };

  // 处理添加自定义标签
  const handleAddCustomTag = () => {
    if (!customTagInput) {
      toast.error('请输入标签内容');
      return;
    }
    const customTags = adgroupNameTags.filter((tag) => !defaultTags.includes(tag));
    if (customTags.length >= 5) {
      toast.error('自定义标签不能超过5个');
      return;
    }
    // 获取当前广告组名称
    const currentName = form.getValues('adgroup_name') || '';

    // 更新广告组名称
    form.setValue('adgroup_name', currentName + customTagInput);
    setAdgroupNameTags([...adgroupNameTags, customTagInput]);
    setCustomTagInput(''); // 清空输入框
  };
  const handleRemoveTag = (tagToRemove: string) => {
    // 从广告组名称中移除该标签
    const currentName = form.getValues('adgroup_name') || '';
    const newName = currentName.replace(tagToRemove, '');
    form.setValue('adgroup_name', newName);
    // 从标签列表中移除
    setAdgroupNameTags(adgroupNameTags.filter((tag) => tag !== tagToRemove));
  };
  return (
    <>
      <div className="mb-3 mt-10 flex items-center gap-3">
        <div className="h-4 w-1 bg-[#00E1FF]"></div>
        <div className="text-base font-semibold">广告组</div>
      </div>
      <div className="pl-10">
        <FormField
          control={form.control}
          name="adgroup_name"
          render={({ field }) => (
            <FormItem className="mt-6 flex items-center">
              <FormLabel className="w-1/5 text-sm text-white">
                广告组名称
                <span className="ml-2 text-red-500">*</span>
              </FormLabel>
              <FormControl className="w-4/5">
                <Input
                  className="h-10 border border-[#363D54] bg-transparent text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                  placeholder="请输入"
                  {...field}
                  disabled={type === 'template'}
                  maxLength={200}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    // 当广告组名称变化时，更新标签列表
                    const newValue = e.target.value;
                    const updatedTags = adgroupNameTags.filter((tag) => newValue.includes(tag));
                    setAdgroupNameTags(updatedTags);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="mt-4">
          <div className="flex">
            <div className="w-1/5"></div>
            <div className="flex w-4/5 justify-end space-x-2">
              <Button
                className={`h-[28px] text-xs text-[#9FA4B2] ${
                  adgroupNameTags.includes(format(new Date(), 'yyyyMMdd'))
                    ? 'border-[#00E1FF] bg-[#00E1FF1A] text-white'
                    : ''
                }`}
                type="button"
                variant="outline"
                onClick={() => handleAddTag('date')}
                disabled={type === 'template'}
              >
                当前日期
              </Button>
              <Button
                className={`h-[28px] text-xs text-[#9FA4B2] ${
                  adgroupNameTags.includes('系列名称') ? 'border-[#00E1FF] bg-[#00E1FF1A] text-white' : ''
                }`}
                type="button"
                variant="outline"
                onClick={() => handleAddTag('campaign')}
                disabled={type === 'template'}
              >
                系列名称
              </Button>
              <Button
                className={`h-[28px] text-xs text-[#9FA4B2] ${
                  adgroupNameTags.includes('商品数据源') ? 'border-[#00E1FF] bg-[#00E1FF1A] text-white' : ''
                }`}
                type="button"
                variant="outline"
                onClick={() => handleAddTag('store')}
                disabled={type === 'template'}
              >
                商品数据源
              </Button>
              <Button
                className={`h-[28px] text-xs text-[#9FA4B2] ${
                  adgroupNameTags.includes('优化目标') ? 'border-[#00E1FF] bg-[#00E1FF1A] text-white' : ''
                }`}
                type="button"
                variant="outline"
                onClick={() => handleAddTag('optimization')}
                disabled={type === 'template'}
              >
                优化目标
              </Button>
              <Button
                className={`h-[28px] text-xs text-[#9FA4B2] ${
                  adgroupNameTags.includes('是否出价') ? 'border-[#00E1FF] bg-[#00E1FF1A] text-white' : ''
                }`}
                type="button"
                variant="outline"
                onClick={() => handleAddTag('bid')}
                disabled={type === 'template'}
              >
                是否出价
              </Button>
              <div
                className={`flex h-[28px] cursor-pointer items-center justify-center text-xs text-[#00E1FF] ${customize ? 'text-white' : ''}`}
                onClick={() => {
                  if (type !== 'template') {
                    setCustomize(!customize);
                  }
                }}
              >
                自定义
              </div>
            </div>
          </div>
          <div className="mt-1 flex">
            <div className="w-1/5"></div>
            <div className="flex w-4/5 flex-wrap space-x-2">
              {adgroupNameTags
                .filter((tag) => !defaultTags.includes(tag))
                .map((tag) => (
                  <div key={tag} className="relative inline-flex">
                    <Button
                      className="h-[28px] border-[#00E1FF] pr-8 text-xs text-white"
                      type="button"
                      variant="outline"
                      disabled={type === 'template'}
                    >
                      {tag}
                      <span className="absolute right-2 cursor-pointer" onClick={() => handleRemoveTag(tag)}>
                        ×
                      </span>
                    </Button>
                  </div>
                ))}
            </div>
          </div>
          {customize && (
            <div className="relative mt-1 flex h-7 items-center justify-end">
              <Input
                placeholder="请输入标签内容"
                className="h-8 w-4/5 border border-[#363D54] bg-[#272F3E] text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                value={customTagInput}
                onChange={(e) => setCustomTagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();
                    handleAddCustomTag();
                  }
                }}
                disabled={type === 'template'}
              />
              <Button
                variant="link"
                className="absolute right-2 top-0 h-full text-xs text-white"
                onClick={(e: React.MouseEvent) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleAddCustomTag();
                }}
                disabled={type === 'template'}
              >
                使用
              </Button>
            </div>
          )}
        </div>
        {type === 'create' && (
          <FormField
            control={form.control}
            name="name"
            rules={{ required: '请输入模板名称' }}
            render={({ field }) => (
              <FormItem className="mt-6 flex items-center">
                <FormLabel className="w-1/5 text-sm text-white">模板名称</FormLabel>
                <FormControl className="w-4/5">
                  <Input
                    className="h-10 border border-[#363D54] bg-transparent text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                    placeholder="请输入"
                    {...field}
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    disabled={isEditAdGroup}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
        <FormField
          control={form.control}
          name="operation_status"
          defaultValue="ENABLE"
          render={({ field }) => (
            <FormItem className="mt-6 flex items-center">
              <FormLabel className="w-1/5 text-sm text-white">广告组状态</FormLabel>
              <FormControl className="w-4/5">
                <div className="flex items-center">
                  <Switch
                    className="h-[16px] w-[28px]"
                    thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
                    checked={field.value === 'ENABLE'}
                    onCheckedChange={(checked) => {
                      field.onChange(checked ? 'ENABLE' : 'DISABLE');
                    }}
                    disabled={type === 'template' || isEditAdGroup}
                  />
                </div>
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="product_source"
          defaultValue="STORE"
          rules={{ required: '请选择商品数据源' }}
          render={({ field }) => (
            <FormItem className="mt-6 flex items-center">
              <FormLabel className="w-1/5 text-sm text-white">
                商品数据源
                <span className="ml-2 text-red-500">*</span>
              </FormLabel>
              <FormControl className="w-4/5">
                <RadioGroup
                  defaultValue="STORE"
                  onValueChange={(value) => {
                    field.onChange(value);
                    setStoreType(value);
                  }}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="STORE" id="STORE" disabled={isEditAdGroup} />
                    <Label htmlFor="STORE" className="flex items-center gap-1 text-sm">
                      <div>商店</div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                            }}
                          >
                            <AnswerIcon />
                          </TooltipTrigger>
                          <TooltipContent>已上传到TikTokSellerCenter的商品列表</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                  </div>
                  {/* <div className="flex items-center space-x-2">
                    <RadioGroupItem value="SHOWCASE" id="SHOWCASE" disabled={isEditAdGroup} />
                    <Label htmlFor="SHOWCASE" className="flex items-center gap-1 text-sm">
                      <div>橱窗</div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <AnswerIcon />
                          </TooltipTrigger>
                          <TooltipContent>已上传到TikTokSellerCenter的商品列表</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                  </div> */}
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />
        <div>
          <FormField
            control={form.control}
            name="store_id"
            render={({ field }) => (
              <FormItem className="mt-4 flex items-center">
                <FormLabel className="w-1/5"></FormLabel>
                <FormControl className="w-4/5">
                  <div>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        const store = storeList.find((store) => store.storeId === value);
                        // setSelectedStore(store);
                        form.setValue('store_authorized_bc_id', store?.storeAuthorizedBcId);
                        // setSelectedRowKeys([]);
                        if (advertiser?.advertiserId && store) {
                          // handleStoreChange(store);
                          //   const matchingLocationIds: string[] = [];
                          //   const findMatchingLocations = (locations: any[]) => {
                          //     for (const location of locations) {
                          //       if (store?.targetingRegionCodes?.includes(location.regionCode)) {
                          //         matchingLocationIds.push(location.locationId);
                          //         break;
                          //       }
                          //       if (location.children) {
                          //         findMatchingLocations(location.children);
                          //       }
                          //     }
                          //   };
                          //   findMatchingLocations(locationData);
                          //   form.setValue('location_ids', matchingLocationIds);
                        }
                      }}
                      value={field.value}
                      disabled={isEditAdGroup}
                    >
                      <SelectTrigger className="h-10 w-[360px] rounded border-[#363D54] bg-transparent text-sm">
                        <SelectValue placeholder="请选择商店" className="text-muted-foreground" />
                      </SelectTrigger>
                      <SelectContent>
                        {storeList.map((store) => (
                          <SelectItem key={store.storeId} value={store.storeId} className="text-xs">
                            <div>
                              <div className="flex items-center gap-2 text-sm font-medium tracking-widest text-white">
                                {store.storeName}
                                <span className="text-xs text-[#9FA4B2]">{store.targetingRegionCodes}</span>
                              </div>
                              <div className="text-sm text-[#9FA4B2]">{store.storeId}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </>
  );
}
