import { ActionContext, webhook } from '@roasmax/serve';
import { VIDEO_DISTRIBUTION_SUB_TASK_STATUS } from '@roasmax/utils';

export const POST = webhook(
  async (
    ctx: ActionContext<{
      id: string;
    }>,
  ) => {
    const subTask = await ctx.db.video_distribution_sub_tasks.findUnique({ where: { id: ctx.data.id } });
    if (!subTask) {
      throw new Error(`未找到视频分发子任务 ${ctx.data.id}`);
    }
    if (subTask.status !== VIDEO_DISTRIBUTION_SUB_TASK_STATUS.已分发) {
      throw new Error(`视频分发子任务 ${ctx.data.id} 状态不为已分发，不可执行发布操作`);
    }
    if (subTask.publish_plan_at && subTask.publish_plan_at > new Date()) {
      throw new Error(`视频分发子任务 ${ctx.data.id} 计划发布时间未到，不可执行发布操作`);
    }

    await ctx.db.video_distribution_sub_tasks.update({
      where: { id: subTask.id },
      data: { status: VIDEO_DISTRIBUTION_SUB_TASK_STATUS.发布中 },
    });
  },
);
