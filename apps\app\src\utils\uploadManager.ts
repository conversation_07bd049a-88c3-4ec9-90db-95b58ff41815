import { UploadItemType, UploadStatus } from '@/types/upload';
import TcVod from 'vod-js-sdk-v6';
import Uploader, { IGetSignature } from 'vod-js-sdk-v6/lib/src/uploader';
import { getVodClient } from './upload';
import { getVideoDuration, getVideoMetadata } from './common';

type MaterialType = 'video' | 'image' | 'audio' | 'srt';

class UploadQueue {
  private queue: Array<() => Promise<any>> = [];
  private running = false;
  private activeCount = 0;
  private readonly maxConcurrent: number;

  constructor(maxConcurrent = 2) {
    this.maxConcurrent = maxConcurrent;
  }

  async add(task: () => Promise<any>) {
    this.queue.push(task);
    if (!this.running) {
      this.running = true;
      await this.process();
    }
  }

  private async process() {
    while (this.queue.length > 0 || this.activeCount > 0) {
      // 当队列不为空且正在执行的任务数小于最大并发数时,继续处理
      while (this.queue.length > 0 && this.activeCount < this.maxConcurrent) {
        const task = this.queue.shift();
        if (task) {
          this.activeCount++;
          // 使用 Promise 包装任务执行,这样可以在任务完成后减少计数
          this.executeTask(task);
        }
      }
      // 等待一小段时间再检查,避免过度消耗 CPU
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    this.running = false;
  }

  private async executeTask(task: () => Promise<any>) {
    try {
      await task();
    } catch (error) {
      console.error('Queue task error:', error);
    } finally {
      this.activeCount--;
    }
  }
}

export class UploadManager {
  static maxFileSize = 10 * 1024 * 1024 * 1024;
  static maxFileDuration = 8 * 60 * 60;
  static maxFileCount = 10;
  static instance: UploadManager | null = null;
  static client: TcVod | null = null;
  static getSignature: IGetSignature | null = null;
  private static uploadQueue = new UploadQueue();

  constructor(getSignature: IGetSignature) {
    UploadManager.getSignature = getSignature;
  }

  static getInstance = (getSignature: IGetSignature) => {
    if (!this.instance) {
      this.instance = new UploadManager(getSignature);
    }
    return this.instance;
  };

  static fileValidator = (file: File, type: MaterialType, validator?: (err: string) => void) => {
    const handleErr = (err: string) => {
      if (validator) {
        validator(err);
      } else {
        throw new Error(err);
      }
    };

    if (file.name.length === 0 || file.name.includes(' ')) {
      handleErr(`文件名不能包含空格或为空，无法上传。`);
      return false;
    }

    const fileName = file.name.substring(0, file.name.lastIndexOf('.'));
    const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();

    if (!fileName.match(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/)) {
      handleErr(`文件名 ${file.name} 中包含特殊字符，无法上传。`);
      return false;
    }

    if (file.name.lastIndexOf('.') === 0) {
      handleErr(`文件 ${file.name} 的名称不全，无法上传。`);
      return false;
    }

    if (type === 'video') {
      if (fileExtension !== 'mp4' || (!file.type.startsWith('video/mp4') && !file.type.startsWith('video/mpeg'))) {
        handleErr(`文件 ${file.name} 不是 MP4 格式，无法上传。`);
        return false;
      }
    }

    if (file.size > UploadManager.maxFileSize) {
      handleErr(`文件 ${file.name} 大小超过10GB，无法上传。`);
      return false;
    }

    return true;
  };

  static videoValidator = (file: UploadItemType, validator?: (err: string) => void) => {
    const handleErr = (err: string) => {
      if (validator) {
        validator(err);
      } else {
        throw new Error(err);
      }
    };

    if (file.duration && file.duration > UploadManager.maxFileDuration) {
      handleErr(`视频 ${file.name} 时长超出8小时，无法上传。`);
      return false;
    }

    return true;
  };

  static getValidVideos = async (fileList: UploadItemType[]) => {
    const validList = await Promise.all(
      fileList.slice(0, UploadManager.maxFileCount).map(async (each) => {
        const { file } = each as { file: File };
        const slicedName = file.name.slice(0, 20);
        try {
          if (!UploadManager.fileValidator(file, 'video')) {
            return { fileName: slicedName, duration: 0, ...each };
          }

          const { duration } = await getVideoMetadata(file);
          return { fileName: slicedName, duration, ...each };
        } catch (e: unknown) {
          return {
            fileName: slicedName,
            ...each,
            status: UploadStatus.VALIDATE_FAILED,
            message: (e as Error)?.message ?? '',
          };
        }
      }),
    );

    return validList;
  };

  // 新增检测黑帧的函数
  private static isBlackFrame = (canvas: HTMLCanvasElement, threshold = 0.95): boolean => {
    try {
      const context = canvas.getContext('2d');
      if (!context) return false;

      const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      let darkPixels = 0;
      const totalPixels = data.length / 4;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        // 计算像素亮度
        // @ts-ignore
        const brightness = (r + g + b) / 3;
        if (brightness < 20) {
          // 亮度阈值，可以调整
          darkPixels++;
        }
      }

      const isBlackFrame = darkPixels / totalPixels > threshold;
      return isBlackFrame;
    } catch (error) {
      console.error('检测黑帧时发生错误:', error);
      return false; // 发生错误时返回 false，表示不是黑帧
    }
  };

  static getVideoFirstFrameAsCover = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const dvd = document.createElement('video');
      // @ts-ignore
      dvd.src = (window.URL || window.webkitURL || window?.mozURL)?.createObjectURL(file);
      let currentSecond = 1;
      const maxAttempts = 3; // 最多尝试3秒

      dvd.onloadedmetadata = function () {
        dvd.currentTime = currentSecond;
      };

      dvd.addEventListener('timeupdate', () => {
        if (dvd.currentTime > currentSecond) {
          dvd.pause();
        }
      });

      function handler() {
        let canvas: HTMLCanvasElement | null = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = dvd.videoWidth;
        canvas.height = dvd.videoHeight;
        context?.drawImage(dvd, 0, 0, dvd.videoWidth, dvd.videoHeight);

        // 检查是否为黑帧
        if (UploadManager.isBlackFrame(canvas) && currentSecond < maxAttempts && currentSecond < dvd.duration) {
          // 如果是黑帧，尝试下一秒
          currentSecond++;
          dvd.currentTime = currentSecond;
          return;
        }

        URL.revokeObjectURL(dvd.src);
        const imgSrc = canvas.toDataURL('image/jpeg');
        dvd.removeAttribute('src');
        dvd.load();
        canvas = null;
        resolve(imgSrc);
        dvd.removeEventListener('seeked', handler);
      }

      dvd.addEventListener('seeked', handler);
      dvd.onerror = () => {
        reject('获取视频封面失败');
      };
    });
  };

  /**
   * 单个文件分片上传
   * @param file 文件
   * @param id 文件id
   */
  multipartUploadSingleFile = async (file: File, id: string) => {
    try {
      const client = getVodClient();
      const mediaCoverDataUrl = await UploadManager.getVideoFirstFrameAsCover(file);
      const fetchResponse = await fetch(mediaCoverDataUrl);
      const blob = await fetchResponse.blob();
      const mediaCoverFile = new File([blob], 'cover.png', { type: 'image/png' });

      const uploader: Uploader = client.upload({
        mediaFile: file,
        coverFile: mediaCoverFile,
        enableRaceRegion: true,
        enableResume: true,
        chunkParallelLimit: 3,
        fileParallelLimit: 1,
        chunkSize: 5 * 1024 * 1024,
        chunkRetryTimes: 8,
        commitRequestTimeout: 60 * 1000,
        applyRequestTimeout: 60 * 1000,
        retryDelay: 3000,
        // @ts-ignore
        applyRequestRetryCount: 5,
        // @ts-ignore
        commitRequestRetryCount: 5,
      });

      const uploaderWithId = uploader as typeof uploader & { id: string };
      uploaderWithId.id = id;

      return uploaderWithId as typeof uploader & {
        done: () => Promise<{
          fileId: string;
          video: { url: string; verify_content: string };
          cover?: { url: string; verify_content: string };
        }>;
        id: string;
      };
    } catch (error) {
      console.error(`Error in multipartUploadSingleFile for file ${file.name}:`, error);
    }
  };

  batchUpload = async (
    uploadItems: UploadItemType[],
    options: {
      handleValidateFailed?: (item: UploadItemType[]) => void;
      onError?: (error: any, id: string) => void;
      onProgress?: (info: any, id: string) => void;
      onSuccess?: (result: any, id: string) => void;
    },
  ) => {
    const allItems = await UploadManager.getValidVideos(uploadItems);
    const validateFailedItems = allItems?.filter((each) => each.status === UploadStatus.VALIDATE_FAILED);

    if (validateFailedItems?.length) {
      options?.handleValidateFailed?.(validateFailedItems);
    }

    if (allItems?.length === validateFailedItems?.length) {
      return [];
    }

    const validItems = allItems?.filter((each) => each.status !== UploadStatus.VALIDATE_FAILED);

    const uploadTasks = validItems.map((item) => {
      return new Promise<any>(async (resolve) => {
        UploadManager.uploadQueue.add(async () => {
          try {
            const uploader = await this.multipartUploadSingleFile(item.file as File, item.id);

            if (options.onProgress) {
              uploader?.on('media_progress', (info: any) => {
                options.onProgress?.(info, item.id);
              });
            }

            resolve(uploader);

            const result = await uploader?.done();
            options.onSuccess?.(result, item.id);
            return result;
          } catch (error) {
            console.error(error);
            options.onError?.(error, item.id);
            resolve(null); // 报错不能影响队列
          }
        });
      });
    });

    return Promise.all(uploadTasks);
  };
}
