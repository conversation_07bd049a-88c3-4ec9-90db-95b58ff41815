import queryString from 'node:querystring';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { request } from '../net/request';

export type LangfuseOpenApiParams = {
  host: string;
  basicAuth?: { username: string; password: string };
};

type ChatPrompt = {
  type: 'chat';
  name: string;
  version: number;
  config: any;
  labels: string[];
  tags: string[];
  prompts: { role: string; content: string }[];
};

type TextPrompt = {
  type: 'text';
  name: string;
  version: number;
  config: any;
  labels: string[];
  tags: string[];
  prompt: string;
};

type ListPromptsResponse = {
  data: {
    name: string;
    versions: number[];
    labels: string[];
    tags: string[];
    lastUpdatedAt: string;
    lastConfig: {
      display_name: { zh: string };
      output_json_schema: any;
      structured_method: string;
      temperature: number;
      top_p: number;
    };
  }[];
  meta: { page: number; limit: number; totalItems: number; totalPages: number };
};

export class LangfuseOpenApi {
  private host: string;

  private basicAuth: {
    username: string;
    password: string;
  };

  constructor(options: LangfuseOpenApiParams) {
    if (!options.host) {
      throw new Error('Missing required options: host');
    }
    if (!options.basicAuth) {
      throw new Error('Missing required options: basicAuth.username, basicAuth.password');
    }
    this.host = options.host;
    this.basicAuth = options.basicAuth;
  }

  /**
   * 用于请求开放接口
   * 通过 Basic Auth 方式进行认证
   * @param path
   * @param options
   * @returns
   */
  public async request<T>(path: string, options?: AxiosRequestConfig) {
    try {
      const { username, password } = this.basicAuth || {};
      const res = await request<T>({
        ...(options || {}),
        url: `${this.host}${path}`,
        auth: { username, password },
      });
      return res as AxiosResponse<T>;
    } catch (e: any) {
      console.error(`Failed to request: ${e.message}. request path: ${path}; options: ${JSON.stringify(options)}`);
      throw new Error(`Failed to request: ${e.message}`);
    }
  }

  /**
   * 获取Prompts列表
   */
  public async listPrompts(params: { name?: string; label?: string; tag?: string }) {
    const allData: ListPromptsResponse['data'] = [];
    const limit = 100;
    const MAX_PAGES = 10;

    try {
      for (let page = 1; page <= MAX_PAGES; page++) {
        const pageQuery = queryString.stringify({ ...clearEmpty(params), page, limit });
        const res = await this.request<ListPromptsResponse>(`/api/public/v2/prompts?${pageQuery}`, { method: 'get' });
        allData.push(...res.data.data);
        // 如果已经到达最后一页，提前结束
        if (page >= res.data.meta.totalPages) {
          break;
        }
      }
      return { data: allData };
    } catch (error) {
      console.error('获取Prompts列表失败:', error);
      throw new Error(`获取Prompts列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取单个Prompt
   */
  public async getPromptByName(params: { promptName: string; version?: number; label?: string }) {
    const { promptName, version, label } = params;
    const query = queryString.stringify(clearEmpty({ version, label }));
    const res = await this.request<ChatPrompt | TextPrompt>(`/api/public/v2/prompts/${promptName}?${query}`, {
      method: 'get',
    });
    return res.data;
  }

  /**
   * 获取Datasets列表
   */
  public async listDatasets(params: { page: number; limit: number }) {
    const { page, limit } = params;
    const res = await this.request<any>(`/api/public/v2/datasets?page=${page}&limit=${limit}`, { method: 'get' });
    return res.data;
  }

  /**
   * 获取单个Dataset
   */
  public async getDatasetItemById(params: { itemId: string }) {
    const { itemId } = params;
    const res = await this.request<any>(`/api/public/dataset-items/${itemId}`, { method: 'get' });
    return res.data;
  }

  /**
   * 获取单个Trace
   */
  public async getTraceById(traceId: string) {
    const res = await this.request<any>(`/api/public/traces/${traceId}`, { method: 'get' });
    return res.data;
  }

  /**
   * 获取Sessions列表
   */
  public async listSessions(params: { page: number; limit: number; fromTimestamp?: number; toTimestamp?: number }) {
    const { page, limit, fromTimestamp, toTimestamp } = params || {};
    const query = queryString.stringify(clearEmpty({ page, limit, fromTimestamp, toTimestamp }));
    const res = await this.request<any>(`/api/public/sessions?${query}`, { method: 'get' });
    return res.data;
  }

  /**
   * 获取单个Session
   */
  public async getSessionById(sessionId: string) {
    const res = await this.request<any>(`/api/public/sessions/${sessionId}`, { method: 'get' });
    return res.data;
  }
}

/**
 * 清除对象中值为空的属性
 */
export function clearEmpty(obj: any) {
  const newObj = { ...obj };
  Object.keys(newObj).forEach((key) => {
    if (newObj[key] === '' || newObj[key] === undefined || newObj[key] === null) {
      delete newObj[key];
    }
  });
  return newObj;
}
