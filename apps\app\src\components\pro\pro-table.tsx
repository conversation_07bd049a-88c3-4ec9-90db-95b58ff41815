import { useMemo, useState } from 'react';
import { Checkbox, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui';
import { cn } from '@/utils/cn';
import { ProColumnBaseDataType, ProColumnContent, ProColumnType } from './pro-column';
import { Loader } from '../icon/loader';
import { ArrowFullfil } from '../icon/ArrowFullfil';
import { ProCheckbox } from './pro-checkbox';

interface SelectionProps<T, K extends keyof T> {
  selectedRowKeys?: Set<K>;
  onSelect: (key: K, checked: boolean, selectedKeys: K[]) => void;
  onSelectAll?: (allChecked: boolean) => void;
}

export interface ProTableColumnType<T extends ProColumnBaseDataType = any> extends Omit<ProColumnType<T>, 'editable'> {
  fixed?: 'left' | 'right';
  editable?: boolean | ProColumnType<T>['editable'];
  sort?: boolean;
}

export interface ProTableProps<T extends Record<string, any> = Record<string, any>, K extends keyof T = keyof T> {
  columns: ProTableColumnType<T>[];
  dataSource: T[];
  loading?: boolean;
  rowKey?: keyof T;
  selection?: SelectionProps<T, K>;
  className?: string;
  onEdit?: (value: any, record: T, col: ProTableColumnType<T>) => void | Promise<void>;
  onSort?: (sort: { field: string; order: 'asc' | 'desc' }[]) => void;
}

export default function ProTable<T extends Record<string, any>, K extends keyof T = 'id'>(props: ProTableProps<T, K>) {
  const { selection, columns, dataSource, rowKey = 'id', className } = props;
  const [internalSelectedRowKeys, setInternalSelectedRowKeys] = useState<Set<K>>(new Set());
  const [sort, setSort] = useState<{ field: string; order: 'asc' | 'desc' }[]>([]);

  const isControlled = selection && selection.selectedRowKeys;

  const selectedRowKeys = isControlled ? selection.selectedRowKeys! : internalSelectedRowKeys;

  const handleSelect = (key: K, checked: boolean) => {
    if (isControlled) {
      if (checked) {
        selection.onSelect(key, checked, Array.from(selectedRowKeys).concat(key));
      } else {
        const newSelected = new Set(selectedRowKeys);
        newSelected.delete(key);
        selection.onSelect(key, checked, Array.from(newSelected) as K[]);
      }
    } else {
      const newSelected = new Set(selectedRowKeys);
      if (newSelected.has(key)) {
        newSelected.delete(key);
      } else {
        newSelected.add(key);
      }
      setInternalSelectedRowKeys(newSelected);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (selection) {
      selection.onSelectAll?.(checked);
    } else {
      if (selectedRowKeys.size === dataSource.length) {
        setInternalSelectedRowKeys(new Set());
      } else {
        setInternalSelectedRowKeys(new Set(dataSource.map((row) => row[rowKey])));
      }
    }
  };

  if (props.loading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loader className="pb-18" />
      </div>
    );
  }

  return (
    <div className={cn('relative max-h-full overflow-auto text-xs', className)}>
      <table className="w-full min-w-full flex-col whitespace-nowrap">
        <TableHeader className="sticky left-0 top-[-1px] z-20 text-[#81889D]">
          <TableRow className="h-[18px] pb-4 hover:bg-opacity-10">
            {selection && (
              <TableHead className={cn('sticky left-0 z-10 bg-[#101624]')}>
                <div className="flex h-[18px] pr-4 align-middle">
                  <ProCheckbox
                    checked={selectedRowKeys.size === dataSource.length}
                    onCheckedChange={handleSelectAll}
                    className={cn('m-auto')}
                  />
                </div>
              </TableHead>
            )}
            {columns.map((col, index) => {
              const columnKey = String(col.dataIndex || col.key);
              const offset = calcStickyOffset(index, columns, { containsSelection: !!selection });
              return (
                <TableHead
                  key={columnKey}
                  className="bg-[#101624]"
                  style={
                    col.fixed === 'right'
                      ? { position: 'sticky', right: offset }
                      : col.fixed === 'left'
                        ? { position: 'sticky', left: offset }
                        : {}
                  }
                >
                  <div className="flex items-center justify-between">
                    {col.title}
                    {!!col.sort && (
                      <div
                        className="ml-4 flex cursor-pointer flex-col justify-between"
                        onClick={() => {
                          const sortItem = sort.find((s) => s.field === columnKey);
                          if (!sortItem || !sortItem.order) {
                            const nextSort = sort
                              .filter((s) => s.field !== columnKey)
                              .concat({ field: columnKey, order: 'asc' });
                            setSort(nextSort);
                            props.onSort?.(nextSort);
                            return;
                          }
                          if (sortItem.order === 'asc') {
                            const nextSort = sort
                              .filter((s) => s.field !== columnKey)
                              .concat({ field: columnKey, order: 'desc' });
                            setSort(nextSort);
                            props.onSort?.(nextSort);
                            return;
                          }
                          if (sortItem.order === 'desc') {
                            const nextSort = sort.filter((s) => s.field !== columnKey);
                            setSort(nextSort);
                            props.onSort?.(nextSort);
                            return;
                          }
                        }}
                      >
                        <ArrowFullfil
                          className={cn(
                            'size-[8px] rotate-180',
                            sort.find((s) => s.field === columnKey)?.order === 'asc' ? 'text-[#fff]' : '',
                          )}
                        />
                        <ArrowFullfil
                          className={cn(
                            'size-[8px]',
                            sort.find((s) => s.field === columnKey)?.order === 'desc' ? 'text-[#fff]' : '',
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TableHead>
              );
            })}
          </TableRow>
        </TableHeader>
        <TableBody className="max-h-full w-full">
          {dataSource.map((row) => {
            const key = row[rowKey];
            return (
              <TableRow key={key} className={cn('text-xs')}>
                {selection && (
                  <TableCell className="sticky left-0 z-10 bg-[#101624]">
                    <div className="flex h-[18px] pr-4 align-middle">
                      <ProCheckbox
                        checked={selectedRowKeys.has(key)}
                        onCheckedChange={(c) => handleSelect(key, c as boolean)}
                        className={cn('m-auto')}
                      />
                    </div>
                  </TableCell>
                )}
                {columns.map((col, index) => {
                  const columnKey = String(col.dataIndex || col.key);
                  const offset = calcStickyOffset(index, columns, { containsSelection: !!selection });
                  return (
                    <TableCell
                      key={columnKey}
                      className="bg-[#101624]"
                      style={
                        col.fixed === 'right'
                          ? { position: 'sticky', right: offset }
                          : col.fixed === 'left'
                            ? { position: 'sticky', left: offset }
                            : {}
                      }
                    >
                      <ProColumnContent
                        value={row}
                        col={{
                          ...col,
                          editable: col.editable
                            ? {
                                ...(typeof col.editable === 'object' ? col.editable || {} : {}),
                                onEditCommit: async (v) => await props.onEdit?.(v, row, col),
                              }
                            : undefined,
                        }}
                      />
                    </TableCell>
                  );
                })}
              </TableRow>
            );
          })}
        </TableBody>
      </table>
    </div>
  );
}

/**
 * 表格行选择
 * @param props
 * @returns
 */
export const useTableRowSelect = <T extends Record<string, any>, K extends keyof T = 'id'>(props: {
  dataSource: T[] | undefined;
  rowKey: K;
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<Set<K>>(new Set());

  const selectedRows = useMemo(() => {
    return props.dataSource?.filter((row) => selectedRowKeys.has(row[props.rowKey]));
  }, [selectedRowKeys, props.dataSource, props.rowKey]);

  const onSelect = (k: K, checked: boolean) => {
    const newSelected = new Set(selectedRowKeys);
    if (checked) {
      newSelected.add(k);
    } else {
      newSelected.delete(k);
    }
    setSelectedRowKeys(newSelected);
  };

  const onSelectAll = () => {
    if (selectedRowKeys.size === props.dataSource?.length) {
      setSelectedRowKeys(new Set());
    } else {
      setSelectedRowKeys(new Set(props.dataSource?.map((row) => row[props.rowKey])));
    }
  };

  const handleClear = () => {
    setSelectedRowKeys(new Set());
  };

  return useMemo(
    () => ({ selectedRowKeys, selectedRows, onSelect, onSelectAll, handleClear }),
    [selectedRowKeys, selectedRows, onSelect, onSelectAll, handleClear],
  );
};

/**
 * 计算固定列的偏移量
 */
const calcStickyOffset = (index: number, columns: ProTableColumnType[], options: { containsSelection: boolean }) => {
  const col = columns[index];
  if (col?.fixed === 'right') {
    return columns
      .slice(index + 1)
      .filter((c) => c.fixed === 'right')
      .reduce((acc, c) => acc + (c.width || 100), 0);
  }
  if (col?.fixed === 'left') {
    return columns
      .slice(0, index)
      .filter((c) => c.fixed === 'left')
      .reduce((acc, c) => acc + (c.width || 100), options.containsSelection ? 48 : 0);
  }
  return 0;
};
