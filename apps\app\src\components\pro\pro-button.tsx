import { cn } from '@/utils/cn';
import { Button, ButtonProps } from '../ui';
import { useState } from 'react';
import { Loader } from '../icon/loader';
import { cva } from 'class-variance-authority';

type ProButtonProps = Omit<ButtonProps, 'onClick'> & {
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>;
};

const buttonVariants = cva('', {
  variants: {
    variant: {
      default: 'text-[#050A1C]',
      outline: '',
      disabled: '',
      secondary: '',
      ghost: '',
      link: '',
      destructive: '',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

export function ProButton(props: ProButtonProps) {
  const [loading, setLoading] = useState(false);
  const { children, className, onClick, disabled, ...rest } = props;
  return (
    <Button
      {...rest}
      className={cn(buttonVariants({ variant: rest.variant }), 'h-[32px]', className)}
      disabled={loading || disabled}
      onClick={async (e) => {
        setLoading(true);
        await onClick?.(e);
        setLoading(false);
      }}
    >
      {loading && <Loader className="mr-1 size-[16px]" />}
      {children}
    </Button>
  );
}
