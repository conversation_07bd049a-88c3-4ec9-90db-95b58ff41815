import dotenv from 'dotenv';
dotenv.config();

import Koa from 'koa';
import bodyParser from 'koa-bodyparser';

interface NodeScfServerConfig {
  errorHandling?: boolean; // 是否启用错误兜底
  port?: number; // 服务器端口
}

export class NodeScfWebServer {
  private app: Koa;
  private handler: (data: any) => Promise<any>;
  private config: NodeScfServerConfig;

  constructor(handler: (data: any) => Promise<any>, config: NodeScfServerConfig = {}) {
    this.app = new Koa();
    this.handler = handler;
    this.config = {
      errorHandling: true, // 默认启用错误处理
      port: 9000, // 默认端口
      ...config,
    };
    this.setupMiddlewares();
  }

  private setupMiddlewares() {
    if (this.config.errorHandling) {
      // Error handling middleware
      this.app.use(async (ctx: Koa.Context, next: Koa.Next) => {
        try {
          await next();
        } catch (err: any) {
          ctx.status = err.status || 500;
          ctx.body = {
            message: err.message || 'Internal Server Error',
          };
          ctx.app.emit('error', err, ctx);
        }
      });
    }

    this.app.use(bodyParser());
    this.app.use(this.handleRequest.bind(this));
  }

  private async handleRequest(ctx: Koa.Context) {
    try {
      if (ctx.method === 'GET') {
        const data = this.parser(ctx.query);
        console.log('解析参数', JSON.stringify(data));
        console.log('----------------------------');
        const res = await this.handler(data);
        ctx.body = JSON.stringify(res);
        return;
      }
      console.log('请求参数', JSON.stringify(ctx.request.body));
      console.log('----------------------------');
      const data = this.parser(ctx.request.body);
      console.log('解析参数', JSON.stringify(data));
      console.log('----------------------------');
      const res = await this.handler(data);
      ctx.body = JSON.stringify(res);
    } catch (err) {
      if (!this.config.errorHandling) {
        throw err; // 如果未启用错误处理，则抛出错误
      }
      throw err; // 启用错误处理时，错误会被错误中间件捕获
    }
  }

  /**
   * 通过内部函数调用的方式解析参数
   */
  private parseParamsFromFunctionCall = (data: any) => {
    return data;
  };

  /**
   * 通过http请求的解析参数
   */
  private parseParamsFromHttp = (data: any) => {
    if ('body' in data && typeof data.body === 'string') {
      return this.parseJson(data.body);
    }
  };

  private parseParamsFromTimer = (data: any) => {
    if ('Type' in data && data.Type === 'Timer' && 'Message' in data && typeof data.Message === 'string') {
      return this.parseJson(data.Message);
    }
  };

  private parser = (data: any) => {
    return this.parseParamsFromHttp(data) || this.parseParamsFromTimer(data) || this.parseParamsFromFunctionCall(data);
  };

  private parseJson = (data: any) => {
    try {
      return JSON.parse(data);
    } catch {
      return null;
    }
  };

  public start(port?: number) {
    const serverPort = port || this.config.port;
    this.app.listen(serverPort);
    console.log(`Server started on port ${serverPort}`);
  }
}
