import { ManagementClient } from 'authing-node-sdk';
import { ActionContextPluginLoader } from '../../types';

const authingManagePlugin: ActionContextPluginLoader = () => {
  if (!process.env.PRIVATE_ACCESSKEYID_ID || !process.env.PRIVATE_ACCESSKEYSECRET_ID) {
    throw new Error('PRIVATE_ACCESSKEYID_ID or PRIVATE_ACCESSKEYSECRET_ID is not set');
  }
  const authManagement = new ManagementClient({
    accessKeyId: process.env.PRIVATE_ACCESSKEYID_ID,
    accessKeySecret: process.env.PRIVATE_ACCESSKEYSECRET_ID,
  });

  return {
    name: 'authingManage',
    plugin: authManagement,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * authing Manage 操作API
     */
    authingManage: ManagementClient;
  }
}

export default authingManagePlugin;
