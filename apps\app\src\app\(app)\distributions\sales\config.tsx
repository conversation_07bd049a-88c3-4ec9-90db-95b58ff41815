import { ProFilterColumnType } from '@/components/pro/pro-filter';
import { ProTableColumnType } from '@/components/pro/pro-table';
import { pageOrders } from '@/services/actions/orders';
import { ActionParams, ActionResult } from '@/utils/server-action/action';

export const createInbuiltColumns = (): ProTableColumnType<ActionResult<typeof pageOrders>['list'][number]>[] => {
  return [
    { key: 'product_name', title: '商品名称' },
    { key: 'transaction_amount', title: '成交金额', type: 'currency' },
    { key: 'order_status', title: '订单状态' },
    { key: 'platform', title: '平台' },
    { key: 'order_id', title: '订单ID' },
    { key: 'influencer_name', title: '达人名称' },
    { key: 'influencer_douyin_id', title: '达人抖音号' },
    { key: 'commission_rate', title: '佣金率', type: 'percent' },
    { key: 'commission_amount', title: '佣金收入', type: 'currency' },
    { key: 'settlement_amount', title: '结算金额', type: 'currency' },
    { key: 'order_pay_at', title: '支付时间', type: 'date' },
    { key: 'order_receipt_at', title: '收货时间', type: 'date' },
    { key: 'order_settlement_at', title: '结算时间', type: 'date' },
    { key: 'shop_name', title: '店铺名称' },
  ];
};

export const filterColumns: ProFilterColumnType<NonNullable<ActionParams<typeof pageOrders>['filters']>>[] = [
  {
    key: 'product_name',
    title: '商品名称',
    type: 'text',
  },
  {
    key: 'influencer_name',
    title: '达人名称',
    type: 'text',
  },
  {
    key: 'shop_name',
    title: '店铺名称',
    type: 'text',
  },
  {
    key: 'order_id',
    title: '订单ID',
    type: 'text',
  },
];

/**
 * 商品	销售额	佣金比例	分成比例	预估佣金	抖音号	付款时间	订单状态
 */
export const importChanJianHeaderConfig = [
  { key: 'product_name', content: '商品', required: true, type: 'string' },
  { key: 'transaction_amount', content: '销售额', required: true, type: 'number' },
  { key: 'commission_rate', content: '佣金比例', required: true, type: 'percent' },
  { key: 'commission_amount', content: '预估佣金', required: true, type: 'number' },
  { key: 'account_identity', content: '抖音号', required: true, type: 'string' },
  { key: 'share_ratio', content: '分成比例', required: true, type: 'percent' },
  { key: 'order_pay_at', content: '付款时间', required: true, type: 'date' },
  { key: 'order_status', content: '订单状态', required: true, type: 'string' },
];

/**
 * 订单id	商品id	商品名称	订单状态	超时未结算原因	达人昵称	达人抖音号	成交金额	佣金率	总佣金收入	预估佣金收入-达人	预估佣金收入-机构	结算金额	结算佣金收入-达人	结算佣金收入-机构	订单支付时间	订单收货时间	订单结算时间	分成比例	预估技术服务费	结算技术服务费	新老账户	商品来源	尾款支付时间	定金金额	店铺id	店铺名称	商品数量	安心购	是否阶梯佣金	佣金发票	冻结比例	门槛销量	基础佣金率	升佣佣金率	达人预估奖励佣金收入	机构预估奖励佣金收入	达人结算奖励佣金收入	机构结算奖励佣金收入	阶梯计划ID	支付补贴	平台补贴	达人补贴	运费	税费	运费补贴	计划类型	订单来源	流量细分来源	流量来源	订单类型
 * @prisma/client orders
 *
 */
export const importBaiYinHeaderConfig = [
  { key: 'order_id', content: '订单id', required: true, type: 'string' },
  { key: 'product_identity', content: '商品id', required: true, type: 'string' },
  { key: 'product_name', content: '商品名称', required: true, type: 'string' },
  { key: 'order_status', content: '订单状态', required: true, type: 'string' },
  { key: 'order_pay_at', content: '订单支付时间', required: false, type: 'date' },
  { key: 'order_receipt_at', content: '订单收货时间', required: false, type: 'date' },
  { key: 'order_settlement_at', content: '订单结算时间', required: false, type: 'date' },
  // { key: 'share_ratio', content: '分成比例', required: true, type: 'percent' },
  { key: 'commission_amount', content: '总佣金收入', required: true, type: 'number' },
  {
    key: 'influencer_account_settlement_commission_amount',
    content: '达人结算奖励佣金收入',
    required: true,
    type: 'number',
  },
  {
    key: 'agency_settlement_commission_amount',
    content: '机构结算奖励佣金收入',
    required: true,
    type: 'number',
  },
  { key: 'influencer_name', content: '达人昵称', required: true, type: 'string' },
  { key: 'influencer_douyin_id', content: '达人抖音号', required: true, type: 'string' },
  { key: 'transaction_amount', content: '成交金额', required: true, type: 'number' },
  { key: 'commission_rate', content: '佣金率', required: true, type: 'percent' },
  { key: 'settlement_amount', content: '结算金额', required: true, type: 'number' },
  { key: 'shop_name', content: '店铺名称', required: true, type: 'string' },
  { key: 'shop_id', content: '店铺id', required: true, type: 'string' },
  // { key: 'product_amount', content: '商品数量', required: true, type: 'number' },
  // { key: 'order_type', content: '订单类型', required: true, type: 'string' },
  { key: 'traffic_source', content: '流量来源', required: true, type: 'string' },
  // { key: 'order_source', content: '订单来源', required: true, type: 'string' },
];

export const salesFieldMapper = {
  platform: '平台',
  shop_id: '店铺ID',
  shop_name: '店铺名称',
  product_identity: '商品ID',
  product_name: '商品名称',
  transaction_amount: '交易金额',
  commission_amount: '佣金金额',
  commission_rate: '佣金率',
  share_ratio: '分成比例',
  influencer_account_settlement_commission_amount: '达人结算佣金',
  agency_settlement_commission_amount: '机构结算佣金',
  settlement_amount: '结算金额',
  remark: '备注',
  tmp_created_at: '创建时间',
} as const;

export const salesExportValueTransformer = {
  transaction_amount: (value: number) => value / 100, // 转换为元
  commission_amount: (value: number) => value / 100,
  commission_rate: (value: number) => value / 10000, // 转换为百分比
  share_ratio: (value: number) => value / 10000,
  influencer_account_settlement_commission_amount: (value: number) => value / 100,
  agency_settlement_commission_amount: (value: number) => value / 100,
  settlement_amount: (value: number) => value / 100,
} as const;
