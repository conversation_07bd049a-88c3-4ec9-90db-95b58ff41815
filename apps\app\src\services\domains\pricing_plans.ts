import { UpdateQuotaInput, WalletChangeType } from '@/types/wallet';
import { ActionContext } from '@roasmax/serve';
/**
 * 获取钱包
 */
const PLANS_DEFINITIONS = ['专业版', '标准版', '基础版', '尝鲜版'];
function getPlanIndex(planName: string) {
  return PLANS_DEFINITIONS.indexOf(planName);
}

export const getQuota = async (ctx: ActionContext<any>) => {
  if (!ctx.tenant.id) {
    throw new Error('租户id不能为空');
  }
  const quotas = await ctx.db.pricing_plans.findMany({
    where: { tenant_id: ctx.tenant.id },
  });

  if (!quotas || quotas.length === 0) {
    throw new Error('找不到用户钱包');
  }

  const now = new Date();
  const activePlans = quotas.filter((plan) => new Date(plan.end_time) > now);
  const totalQuota = activePlans.reduce((sum, plan) => sum + plan.quota, 0);

  const validPlans = activePlans.filter((plan) => PLANS_DEFINITIONS.includes(plan.plan_name));

  if (validPlans.length > 0) {
    // 如果存在有效计划，则找到结束时间最晚的那个计划作为 latestPlan，并返回该计划和总配额。
    // const latestPlan = validPlans.reduce((latest, plan) =>
    //   new Date(plan.end_time) > new Date(latest.end_time) ? plan : latest,
    // );
    // // expiredPoints: latestPlan.quota
    // return { grade: latestPlan, quota: totalQuota };
    // 根据PLANS_DEFINITIONS的排序对validPlans进行排序
    validPlans.sort((a, b) => getPlanIndex(a.plan_name) - getPlanIndex(b.plan_name));
    // 取排序后的第一个计划作为latestPlan（实际上是earliestValidPlan）
    const earliestValidPlan = validPlans[0];
    return { grade: earliestValidPlan, quota: totalQuota };
  }

  if (activePlans.length > 0) {
    return { latestExpired: activePlans[0], quota: totalQuota };
  }

  const latestExpiredPlan = quotas.reduce((latest, plan) =>
    new Date(plan.end_time) > new Date(latest.end_time) ? plan : latest,
  );
  return { latestExpired: latestExpiredPlan, quota: 0 };
};
/**
 * 更新钱包额度
 */
export const updateQuota = async (ctx: ActionContext<UpdateQuotaInput>) => {
  const { db } = ctx;
  const { quota, changeType = WalletChangeType.CHARGE, changeReason } = ctx.data;
  // 1. 获取当前用户钱包
  const plans = await db.pricing_plans.findMany({
    where: {
      tenant_id: ctx.tenant.id,
    },
  });
  // 获取当前时间
  const now = new Date();
  // 获取当前用户的套餐信息：过滤出当前有效的套餐（结束时间晚于当前时间）。
  const activePlans = plans.filter((plan) => new Date(plan.end_time) > now);
  // 处理没有有效套餐的情况：
  if (activePlans.length === 0) {
    throw new Error('没有有效的套餐，请联系管理员进行充值');
  }
  // 按结束时间对计划进行排序，最接近当前日期的排在第一位
  const sortedActivePlans = activePlans.sort((a, b) => new Date(a.end_time).getTime() - new Date(b.end_time).getTime());
  const totalQuota = activePlans.reduce((sum, plan) => sum + plan.quota, 0);
  // 2. 计算新的额度
  const newQuota = totalQuota + quota;

  // 验证新的额度不能为负数
  if (newQuota < 0) {
    throw new Error('您账户上的余额不足，请联系管理员进行充值');
  }
  // 如果是转账操作，验证转入和转出的信息
  if (changeType === 'SET_MENU_IN' || changeType === 'SET_MENU_OUT') {
    if (!ctx.tenant.id) {
      throw new Error('转账操作需要提供完整的转入和转出信息');
    }
  }
  // 更新套餐的配额：
  let remainingQuotaToDeduct = Math.abs(quota);
  const updatedPlans = [];
  // 遍历排序后的有效套餐，根据quota的绝对值依次从套餐中扣除配额。
  for (const plan of sortedActivePlans) {
    if (remainingQuotaToDeduct <= 0) break;

    const quotaToDeductFromPlan = Math.min(plan.quota, remainingQuotaToDeduct);
    const newPlanQuota = plan.quota - quotaToDeductFromPlan;
    // 3. 更新钱包额度 更新数据库中的套餐配额。
    await db.pricing_plans.update({
      where: {
        tenant_id: ctx.tenant.id,
        id: plan.id,
      },
      data: {
        quota: newPlanQuota,
      },
    });
    updatedPlans.push({
      id: plan.id,
      deductedQuota: quotaToDeductFromPlan,
      newQuota: newPlanQuota,
    });

    remainingQuotaToDeduct -= quotaToDeductFromPlan;
  }
  // 处理配额扣除不足的情况：
  if (remainingQuotaToDeduct > 0) {
    throw new Error('所有计划的总额度不足以完成扣款操作');
  }

  //   更新创建变更记录的部分;
  await db.quota_changelogs.create({
    data: {
      tenant_id: ctx.tenant.id,
      change_type: changeType,
      change_reason: changeReason,
      quota: quota,
      result_quota: newQuota,
      pricing_plan_id: updatedPlans[0]!.id,
    },
  });

  return { success: true, newQuota, updatedPlans };
};
