import { PrismaClient } from '@roasmax/database';

type BatchCreateInput = {
  [key: string]: any;
};

type BatchOperationOptions<T extends BatchCreateInput> = {
  identityField: keyof T;
  model: any;
  cleanData?: (item: T) => T;
  db: PrismaClient;
  mergeStrategy?: (newData: T, existingData: any) => Record<string, any>;
};

export async function batchCreateOrUpdate<T extends BatchCreateInput>({
  data,
  options,
}: {
  data: T[];
  options: BatchOperationOptions<T>;
}) {
  const { identityField, model, cleanData, db, mergeStrategy } = options;

  // 获取所有待处理记录的唯一标识
  const identities = data.map((item) => item[identityField]);

  // 查找已存在的记录
  const existingRecords = await (db[model] as any).findMany({
    where: {
      [identityField]: { in: identities },
      tmp_deleted_at: null,
    },
  });

  // 分离需要创建和更新的数据
  const toCreate = data.filter(
    (item) => !existingRecords.some((existing: any) => existing[identityField] === item[identityField]),
  );
  const toUpdate = data.filter((item) =>
    existingRecords.some((existing: any) => existing[identityField] === item[identityField]),
  );

  // 批量创建新记录
  let createResult = { count: 0 };
  if (toCreate.length > 0) {
    const cleanedData = cleanData ? toCreate.map(cleanData) : toCreate;

    createResult = await (db[model] as any).createMany({
      data: cleanedData,
    });
  }

  // 更新已存在的记录
  let updateCount = 0;
  for (const item of toUpdate) {
    const existing = existingRecords.find((record: any) => record[identityField] === item[identityField]);

    if (existing) {
      const mergedData = mergeStrategy
        ? mergeStrategy(item, existing)
        : Object.keys(item).reduce(
            (acc, key) => {
              const newValue = item[key];
              const oldValue = existing[key];
              acc[key] = newValue ?? oldValue;
              return acc;
            },
            {} as Record<string, any>,
          );

      const cleanedData = cleanData ? cleanData(mergedData as T) : mergedData;

      await (db[model] as any).update({
        where: { id: existing.id },
        data: cleanedData,
      });
      updateCount++;
    }
  }

  return {
    created: createResult.count,
    updated: updateCount,
    total: createResult.count + updateCount,
  };
}
