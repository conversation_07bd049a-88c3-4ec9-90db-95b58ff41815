import LibraryDirectory from '@/components/icon/LibraryDirectory';
import { DirItemType } from '@/types/material';
import { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/Input';
import { Edit } from '@/components/icon';
import toast from 'react-hot-toast';

const DirBox = ({
  data,
  onClick,
  batchMode = false,
  onSelect,
  updateDir,
}: {
  data: DirItemType;
  onClick: (data: DirItemType) => void;
  batchMode: boolean;
  onSelect: (data: DirItemType) => void;
  updateDir: (data: { name: string; id: string | undefined }) => Promise<void>;
}) => {
  const [isName, setIsName] = useState(false);
  const [inputValue, setInputValue] = useState(''); // 新增状态
  const [isHovering, setIsHovering] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { name = '', tmp_created_at } = data;

  const handleClick = () => {
    if (batchMode) {
      onSelect(data);
    } else {
      onClick(data);
    }
  };

  const handleDoubleClick = () => {
    setIsName(!isName);
    setInputValue(name);
  };
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace('*', '').slice(0, 20);
    setInputValue(newValue);
    if (newValue.length < 20) return;
    toast.error('名称最多只能输入36个字符');
  };
  const nameSubmit = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (inputValue === '' || inputValue === 'undefined') {
        return toast.error('名称不能为空');
      }
      await updateDir({ name: inputValue, id: data.id });
      setIsName(false);
    }
  };
  const handleBlur = () => {
    setIsName(false);
    setInputValue(name);
  };

  useEffect(() => {
    if (isName && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isName]);
  return (
    <div className="space-between relative h-full w-full cursor-pointer">
      {batchMode && (
        <div className="relative">
          <input
            onChange={() => {}}
            checked={data?.checked}
            type="checkbox"
            className="peer absolute right-3 top-7 z-10 h-4 w-4 cursor-pointer appearance-none rounded border border-[#95A0AA] bg-[#000714] bg-opacity-40 checked:border-[#00E1FF] checked:bg-[#00E1FF]"
            onClick={(e) => {
              e.stopPropagation();
              handleClick();
            }}
          />
          <span className="pointer-events-none absolute right-3 top-7 z-20 h-4 w-4 after:absolute after:left-[5px] after:top-[1px] after:h-[10px] after:w-[6px] after:rotate-45 after:border-b-2 after:border-r-2 after:border-black after:opacity-0 after:content-[''] peer-checked:after:opacity-100"></span>
        </div>
      )}
      <div className="mb-3 aspect-square w-full object-cover" onClick={handleClick}>
        <LibraryDirectory />
      </div>
      <div
        className="flex h-[38px] flex-col gap-[3px]"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {isName ? (
          <Input
            ref={inputRef}
            value={inputValue} // 使用 inputValue
            className="mb-[2px] h-5 w-full border border-[#292E3E] bg-[#151C29] pl-1 focus:border-[#00E1FF]"
            onChange={handleNameChange}
            onKeyPress={nameSubmit}
            onBlur={handleBlur}
            maxLength={20}
          />
        ) : (
          <span className="mb-1 flex h-5 w-full items-center justify-between truncate whitespace-nowrap text-[13px] text-[#ebfbff]">
            <span className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{name}</span>
            <span style={{ display: isHovering && !isName ? 'inline' : 'none' }} onClick={handleDoubleClick}>
              <Edit />
            </span>
          </span>
        )}
        <span className="truncate whitespace-nowrap text-xs font-medium text-[#72798E]">
          {new Date(tmp_created_at ?? '').toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
        </span>
      </div>
    </div>
  );
};

export default DirBox;
