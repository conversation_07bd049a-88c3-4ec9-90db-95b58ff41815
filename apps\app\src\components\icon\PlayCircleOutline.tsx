export const PlayCircleOutline = (props: React.HTMLAttributes<SVGElement>) => {
  return (
    <svg {...props} width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.5 1.61905C10.7426 1.61905 13.381 4.25737 13.381 7.5C13.381 10.7426 10.7426 13.381 7.5 13.381C4.25737 13.381 1.61905 10.7426 1.61905 7.5C1.61905 4.25737 4.25737 1.61905 7.5 1.61905ZM7.5 1C3.90952 1 1 3.90952 1 7.5C1 11.0905 3.90952 14 7.5 14C11.0905 14 14 11.0905 14 7.5C14 3.90952 11.0905 1 7.5 1Z"
        fill="white"
        fillOpacity="0.8"
        stroke="white"
        strokeOpacity="0.8"
        strokeWidth="0.2"
      />
      <path
        d="M6.44292 10.2344C6.00222 10.2344 5.64258 9.87623 5.64258 9.43405V5.56206C5.64258 5.12136 6.00222 4.76172 6.44292 4.76172C6.58147 4.76172 6.71854 4.79857 6.84088 4.86932L10.1955 6.80605C10.4461 6.95049 10.595 7.2099 10.595 7.49879C10.595 7.78768 10.4461 8.04709 10.1955 8.19154L6.84235 10.1268C6.71854 10.1975 6.58147 10.2344 6.44292 10.2344ZM6.44292 5.38224C6.35596 5.38224 6.26163 5.45152 6.26163 5.56353V9.43553C6.26163 9.58734 6.42228 9.65514 6.53283 9.59176L9.886 7.65503C9.96707 7.60786 9.97591 7.53122 9.97591 7.49879C9.97591 7.46637 9.96707 7.38972 9.886 7.34256L6.53283 5.40582C6.50335 5.38961 6.47387 5.38224 6.44292 5.38224Z"
        fill="white"
        fillOpacity="0.8"
        stroke="white"
        strokeOpacity="0.8"
        strokeWidth="0.2"
      />
    </svg>
  );
};
