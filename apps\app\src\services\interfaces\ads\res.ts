import {
  AgeGroup,
  BillingEvent,
  BudgetMode,
  DeepBidType,
  Gender,
  OptimizationGoal,
  PlacementType,
  PromotionType,
  ScheduleType,
} from '@/types/ads';
import { PaginatedResponse } from '@/types/api';
import { ToCamelCase } from '@/utils/camel';

interface AdGroupJsonDate {
  budget: number;
  gender: Gender;
  pacing: 'PACING_MODE_SMOOTH' | 'PACING_MODE_FAST';
  actions: Array<{
    action_scene: string;
    action_period: number;
    video_user_actions: string[];
    action_category_ids: string[];
    smart_interest_behavior_enabled: boolean;
  }>;
  bid_type: string;
  store_id: string;
  frequency: string;
  languages: string[];
  adgroup_id: string;
  age_groups: AgeGroup[];
  placements: string[];
  budget_mode: BudgetMode;
  campaign_id: string;
  adgroup_name: string;
  audience_ids: string[];
  location_ids: string[];
  advertiser_id: string;
  billing_event: BillingEvent;
  deep_bid_type: DeepBidType;
  network_types: string[];
  schedule_type: ScheduleType;
  operation_type: string;
  placement_type: PlacementType;
  product_source: string;
  promotion_type: PromotionType;
  comment_disabled: boolean;
  operation_status: string;
  optimization_goal: OptimizationGoal;
  schedule_end_time: string;
  schedule_start_time: string;
  shopping_ads_type: string;
  frequency_schedule: number;
  video_user_actions: string[];
  device_price_ranges: string[];
  creator_user_actions: string[];
  hashtag_user_actions: string[];
  excluded_audience_ids: string[];
  interest_category_ids: string[];
  store_authorized_bc_id: string;
  excluded_custom_actions: string[];
  included_custom_actions: string[];
  video_download_disabled: boolean;
  purchase_intention_keyword_ids: string[];
  shopping_ads_retargeting_actions_days: number;
}

export interface IAdGroupInfoResponse {
  id: string;
  advertiser_id: string;
  campaign_id: string;
  group_id: string;
  pub_status: string;
  group_name: string;
  source: string;
  operation_status: string;
  json_date: AdGroupJsonDate;
  gmt_create: number;
  gmt_modified: number;
  tag_names?: string[];
  advertiser_name?: string;
  metrics_result?: {
    ad_id: string;
    adgroup_id: string;
    campaign_id: string;
    cpc: number;
    cpm: number;
    ctr: number;
    spend: number;
    clicks: number;
    impressions: number;
    initiate_checkout: number;
    value_per_onsite_shopping: number;
    onsite_shopping_rate: number;
    onsite_shopping_roas: number;
    total_onsite_shopping_value: number;
    onsite_on_web_cart: number;
  };
}
export type AdGroupListRequestCamel = ToCamelCase<IAdGroupInfoResponse>;

export interface GetTkTokenListItem {
  id: number;
  tk_id: string;
  uid: string;
  tk_user_name: string;
  access_token: string;
  gmt_create: Record<string, unknown>;
  gmt_modified: Record<string, unknown>;
}
export type GetTkTokenListResCamel = ToCamelCase<GetTkTokenListItem[]>;

//创建更新广告组返回参数
export interface AdGroupInfoResponse {
  adgroup_id: string;
  adgroup_name: string;
  campaign_id: string;
  campaign_name: string;
  advertiser_id: string;
  budget: number;
  budget_mode: string;
  primary_status?: string;
  operation_status: string;
  optimization_goal: string;
  cpm?: number;
  billing_event: string;
  schedule_start_time: string;
  schedule_end_time: string;
  location_ids: string[];
  promotion_type: string;
  placements: string[];
  store_authorized_bc_id: string;
  store_id: string;
}
export type AdGroupInfoResponseCamel = ToCamelCase<AdGroupInfoResponse>;
// todo: fix type
// 广告信息接口
interface AdItemJsonDate {
  campaign_id: string;
  advertiser_id: string;
  ad_name: string;
  ad_format: string;
  ad_text: string;
  adgroup_id: string;
  group_id: string;
  adgroup_name: string;
  identity_type: string;
  identity_id: string;
  identity_authorized_bc_id: string;
  vertical_video_strategy: string;
  dark_post_status: string;
  operation_status: string;
  creative_type: string;
  item_group_ids: string[];
}

export type AdListItem = {
  id: string;
  ad_name: string;
  ad_id: string;
  code: string;
  group_id: string;
  group_name: string;
  material_id: string;
  campaign_id: string;
  campaign_name: string;
  advertiser_name: string;
  advertiser_id: string;
  video_id: string;
  vod_url: string;
  pub_status: string;
  json_date: AdItemJsonDate;
  gmt_modified: number;
  message?: string;
  operation_status?: string;
  metrics_result: {
    campaign_id: string;
    cpc: number;
    cpm: number;
    ctr: number;
    spend: number;
    clicks: number;
    impressions: number;
    initiate_checkout: number;
    value_per_onsite_shopping: number;
    onsite_shopping_rate: number;
    onsite_shopping_roas: number;
    total_onsite_shopping_value: number;
    onsite_on_web_cart: number;
  };
};
export type AdInfoResponse = PaginatedResponse<AdListItem>;
export type AdInfoResponseCamel = ToCamelCase<AdInfoResponse>;

export interface CampaignJsonDate {
  budget: number;
  roas_bid: number;
  objective: string;
  budget_mode: string;
  campaign_id: string;
  create_time: string;
  modify_time: string;
  advertiser_id: string;
  campaign_name: string;
  campaign_type: string;
  objective_type: string;
  is_new_structure: boolean;
  operation_status: string;
  secondary_status: string;
  is_search_campaign: boolean;
  campaign_product_source: string;
  is_smart_performance_campaign: boolean;
  rta_product_selection_enabled: boolean;
  is_advanced_dedicated_campaign: boolean;
  campaign_app_profile_page_state: string;
}
export interface CampaignListItem {
  advertiser_id: string;
  campaign_id: string;
  campaign_name: string;
  gmt_create: number;
  gmt_modified: number;
  id: string;
  json_date: CampaignJsonDate;
  operation_status: string;
  secondary_status: string;
  pub_status: string;
  metrics_result: {
    campaign_id: string;
    cpc: number;
    cpm: number;
    ctr: number;
    spend: number;
    clicks: number;
    impressions: number;
    initiate_checkout: number;
    value_per_onsite_shopping: number;
    onsite_shopping_rate: number;
    onsite_shopping_roas: number;
    total_onsite_shopping_value: number;
    onsite_on_web_cart: number;
  };
}

export type CampaignListResponse = PaginatedResponse<CampaignListItem>;
export type CampaignListResponseCamel = ToCamelCase<CampaignListResponse>;

export type AdvertiserListItem = {
  id: string;
  advertiser_id: string;
  advertiser_name: string;
  json_date: string;
  core_user_id: string;
  display_name: string;
  access_token: string;
  gmt_create: number;
  gmt_modified: number;
};

export type AdvertiserListResponse = PaginatedResponse<AdvertiserListItem>;
export type AdvertiserListResponseCamel = ToCamelCase<AdvertiserListResponse>;

// 广告组模板
export interface AdGroupTemplateResponse {
  id: string;
  json_date: AdGroupJsonDate;
  name: string;
  gmt_create: number;
  gmt_modified: number;
  uid: string;
}
export type AdGroupTemplateResponseCamel = ToCamelCase<AdGroupTemplateResponse>;

export type GetTkAccountResponse = PaginatedResponse<
  {
    advertiser_id: string;
    advertiser_name: string;
  }[]
>;
// 商品列表
export type StoreProduct = {
  currency: string;
  category: string;
  max_price: string;
  min_price: string;
  status: string;
  title: string;
  product_image_url: string;
  store_id: string;
  item_group_id: string;
  historical_sales: null;
};
export interface ProductListResponse {
  store_products: StoreProduct[];
  page_info: {
    page: number;
    page_size: number;
    total_page: number;
    total_number: number;
  };
}

export interface ProductListResponseWrapper {
  code: number;
  message: string;
  data: ProductListResponse;
  requestId: string;
}
interface Excellent {
  old_budget: number;
  new_budget: number;
  total_onsite_shopping_value: number;
  adgroup_name: string;
  group_id: string;
}
export interface ExcellentAdsResponse {
  adGroupList: Excellent[];
  status: string;
  adGroupDeliveryAnalysisId: string;
  updateTime: number;
}
