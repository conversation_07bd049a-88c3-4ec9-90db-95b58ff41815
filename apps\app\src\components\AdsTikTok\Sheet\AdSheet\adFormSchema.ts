import { GENERATE_ROUND_OPTIONS, GENERATE_SPEED_OPTIONS } from '@/hooks/useTaskCreator';
import { z } from 'zod';

const baseAdSchema = z.object({
  adgroupIds: z.array(z.string()).min(1, '请选择广告组'),
  identityId: z.string().min(1, '请选择发布身份'),
  identityType: z.string().min(1, '请选择发布身份'),
  identityAuthorizedBcId: z.string().min(1, '请选择发布身份'),
  itemGroupIds: z.array(z.string()).min(1, '请选择商品详情'),
});

export const adFormSchema = z.discriminatedUnion('sheetType', [
  // 常规创建
  baseAdSchema.extend({
    sheetType: z.literal('normal'),
    adName: z.string().min(1, '请输入广告名称').max(200, '最多输入200个字符'),
    adText: z.string().min(1, '请输入广告文案').max(150, '最多输入150个字符'),
    darkPostStatus: z.boolean(),
    operationStatus: z.boolean(),
    selectedVideoItem: z.array(z.any()).min(1, '请选择至少一个视频'),
    videoId: z.string().optional().nullable(),
    imageIds: z.array(z.string()).optional().nullable(),
  }),
  // 快速创建
  baseAdSchema.extend({
    sheetType: z.literal('quick'),
    selectedVideoItem: z.array(z.any()).min(1, '请选择至少一个视频'),
    generateRound: z.enum(GENERATE_ROUND_OPTIONS),
    speed: z.enum(GENERATE_SPEED_OPTIONS),
    subtitle: z.boolean().optional(),
    language: z.string().min(1, '请选择原视频语言'),
    industry: z.string(),
    transitionMode: z.boolean().optional(),
    prompts: z.array(z.string()),
    darkPostStatus: z.boolean(),
    operationStatus: z.boolean(),
    adTextType: z.boolean().optional().nullable(),
    expectVideoCount: z.number(),
    costQuota: z.number(),
    productNames: z.array(z.string()),
  }),
  // 编辑模式
  z.object({
    sheetType: z.literal('edit'),
    adName: z.string().optional(),
    adText: z.string().optional(),
    darkPostStatus: z.boolean().optional(),
    operationStatus: z.boolean().optional(),
    selectedVideoItem: z.array(z.any()).optional(),
    adgroupIds: z.array(z.string()).optional(),
    videoId: z.string().optional().nullable(),
    imageIds: z.array(z.string()).optional().nullable(),
    itemGroupIds: z.array(z.string()).optional(),
    identityId: z.string(),
    identityType: z.string(),
    identityAuthorizedBcId: z.string(),
    id: z.string(),
    adId: z.string(),
  }),
]);

export type AdFormValues = z.infer<typeof adFormSchema>;
