'use server';
import { ActionContext, server } from '@roasmax/serve';
import { AuthorizeResourceItem } from 'authing-node-sdk/dist/models/AuthorizeResourceItem';
import { ResourceItemDto } from 'authing-node-sdk/dist/models/ResourceItemDto';

/**
 * 获取当前租户下的角色列表
 */
export const getRoleList = server(
  '获取当前租户下的角色列表',
  async (ctx: ActionContext<{ page: number; limit: number; keywords?: string }>) => {
    const { page, limit } = ctx.data;

    const { id: namespace } = await ctx.fetchTenant();
    const res = await ctx.authingManage.listRoles({
      keywords: ctx.data.keywords ?? '',
      namespace: namespace,
      page: ctx.data.page,
      limit: limit,
    });

    if (res.statusCode !== 200) {
      throw new Error(res.message);
    }

    return {
      list: res.data.list,
      pageInfo: {
        page: page,
        limit: limit,
        totalCount: res.data.totalCount,
      },
    };
  },
);

/**
 * 获取单个角色详情
 */
export const getRole = server('获取单个角色详情', async (ctx: ActionContext<{ code: string }>) => {
  const code = ctx.data.code;
  const { id: namespace } = await ctx.fetchTenant();
  const res = await ctx.authingManage.getRole({ code: code, namespace: namespace });
  if (res.statusCode !== 200) {
    throw new Error(res.message);
  }
  return res.data;
});

/**
 * 批量删除角色
 */
export const deleteRolesBatch = server('批量删除角色', async (ctx: ActionContext<{ codeList: string[] }>) => {
  const codeList = ctx.data.codeList;
  const { id: namespace } = await ctx.fetchTenant();
  const res = await ctx.authingManage.deleteRolesBatch({ namespace: namespace, codeList: codeList });
  if (res.statusCode !== 200) {
    throw new Error(res.message);
  }
  return;
});

/**
 * 创建角色
 */
export const createRole = server(
  '创建角色',
  async (
    ctx: ActionContext<{
      roleName: string;
      description: string;
      resourcesInfo: ('_home_accountInfo' | '_home_autoGenerate')[];
    }>,
  ) => {
    const body = ctx.data;
    const description = body.description;
    const name = body.roleName;
    //   入这2个参数  resourcesInfo是一个数组用户选哪个就加哪个    _home_accountInfo（账号信息）  _home_autoGenerate（自动生成）
    const resourcesInfo = body.resourcesInfo;
    const { id: namespace } = await ctx.fetchTenant();
    const lastResourcesInfo: ResourceItemDto[] = [];
    for (const resource of resourcesInfo) {
      lastResourcesInfo.push({
        resourceType: ResourceItemDto.resourceType.MENU,
        actions: [],
        code: namespace + resource,
      });
    }

    const res = await ctx.authingManage.createRole({
      namespace: namespace,
      description: description,
      code: namespace + '_' + name,
      name: name,
    });

    if (res.statusCode !== 200) {
      throw new Error(res.message);
    }

    const assignRes = await ctx.authingManage.authorizeResources({
      namespace: namespace,
      list: [
        {
          targetType: AuthorizeResourceItem.targetType.ROLE,
          targetIdentifiers: [res.data.code],
          resources: lastResourcesInfo,
        },
      ],
    });
    if (assignRes.statusCode !== 200) {
      throw new Error(assignRes.message);
    }
  },
);
