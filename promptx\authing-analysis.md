# Authing 服务使用分析文档

## 概述

本项目是一个基于 Next.js 的多租户视频处理平台，使用 Authing 作为身份认证和用户管理服务。Authing 在项目中承担了用户认证、权限管理、租户管理等核心功能。

## 1. Authing 依赖和配置

### 1.1 依赖包
- **authing-node-sdk**: `^4.0.0` - 服务端 SDK
- **authing-js-sdk**: `^4.23.51` - 客户端 SDK  
- **jsonwebtoken**: `^9.0.2` - JWT 处理
- **jose**: `^5.9.6` - JWT 处理备选方案

### 1.2 环境变量配置
```typescript
// Authing AuthenticationClient 配置
APPHOST: string;          // Authing 应用域名
APPID: string;            // Authing 应用 ID
APPSECRET: string;        // Authing 应用密钥

// Authing ManagementClient 配置  
PRIVATE_ACCESSKEYID_ID: string;     // 管理 API 访问密钥 ID
PRIVATE_ACCESSKEYSECRET_ID: string; // 管理 API 访问密钥
```

## 2. 核心架构组件

### 2.1 认证插件系统

#### AuthenticationClient 插件
**位置**: `packages/serve/src/context/plugins/authing.ts`
- 用于用户认证、登录、密码修改等操作
- 在每个请求上下文中注入 `ctx.authing` 实例
- 自动设置用户访问令牌

#### ManagementClient 插件  
**位置**: `packages/serve/src/context/plugins/authing-manage.ts`
- 用于用户管理、角色管理、权限管理等管理操作
- 在每个请求上下文中注入 `ctx.authingManage` 实例
- 使用管理员权限进行操作

### 2.2 JWT 令牌处理
**位置**: 
- `packages/serve/src/authing.ts`
- `apps/app/src/utils/authing.ts`

**功能**:
- 本地解析和验证 JWT 令牌
- 支持 jsonwebtoken 和 jose 两种解析方式
- 令牌过期检查

## 3. 用户认证流程

### 3.1 登录流程
**位置**: `apps/app/src/app/api/login/route.ts`

1. 接收用户邮箱和密码
2. 调用 Authing `signInByEmailPassword` 接口
3. 获取 `id_token` 并重新签名
4. 设置 Cookie 并返回用户信息

### 3.2 认证中间件
**位置**: 
- `packages/serve/src/server.ts` (Server Actions)
- `packages/serve/src/api.ts` (API Routes)

**流程**:
1. 从请求头获取 Authorization token
2. 验证 token 有效性和过期时间
3. 获取用户租户信息
4. 创建请求上下文

### 3.3 租户获取
**位置**: `packages/serve/src/server.ts#fetchTenant`

- 通过 AuthenticationClient 获取用户租户列表
- 实现租户信息缓存机制
- 支持多租户架构

## 4. 用户管理功能

### 4.1 用户列表和信息获取
**位置**: `apps/app/src/services/actions/user.ts`

**主要接口**:
- `getUserList`: 获取租户下用户列表
- `getUserInfo`: 获取指定用户详细信息  
- `getUserLoginInfo`: 获取当前用户登录信息和权限

### 4.2 用户创建和管理
**主要功能**:
- `createUser`: 创建新用户并分配角色
- `updateUser`: 修改用户信息和密码
- `deleteUser`: 删除用户（当前已禁用）

**创建流程**:
1. 验证管理员额度
2. 调用 Authing 创建用户
3. 添加用户到租户组
4. 分配用户角色和权限
5. 更新本地钱包记录

### 4.3 个人信息管理
**位置**: `apps/app/src/services/actions/me.ts`

- `getMeInfo`: 获取当前用户完整信息
- `updatePassword`: 修改当前用户密码
- 支持用户角色和权限查询

## 5. 角色和权限管理

### 5.1 角色管理
**位置**: `apps/app/src/services/actions/role.ts`

**主要功能**:
- `getRoleList`: 获取租户下角色列表
- `createRole`: 创建新角色并分配资源权限
- 支持基于命名空间的权限隔离

### 5.2 权限资源
**权限类型**:
- `_home_accountInfo`: 账号信息权限
- `_home_autoGenerate`: 自动生成权限
- 基于菜单的资源权限控制

## 6. 前端集成

### 6.1 登录页面
**位置**: `apps/app/src/app/login/page.tsx`
- 邮箱密码登录表单
- 登录状态管理
- 自动跳转逻辑

### 6.2 用户状态管理
**位置**: `apps/app/src/store/userStore.ts`
- Zustand 状态管理
- 用户 ID 持久化存储
- 登录状态维护

### 6.3 用户信息组件
**位置**: `apps/app/src/components/ButtonAvatar.tsx`
- 用户头像和信息显示
- 个人资料编辑
- 登出功能

## 7. 数据库集成

### 7.1 用户数据表
**位置**: `packages/database/prisma/schema.prisma`

```prisma
model members {
    id             String    @id @default(uuid())
    tenant_id      String    // 租户 ID
    user_id        String    // Authing 用户 ID  
    user_status    String    // 用户状态
    nickname       String    // 用户昵称
    email          String    // 用户邮箱
    account        String    // 登录账号
    admin          Int       // 是否管理员
    password       String    // 用户密码
    // ... 其他字段
}
```

### 7.2 钱包系统集成
- `member_wallets`: 用户钱包和额度管理
- 与 Authing 用户 ID 关联
- 支持额度分配和消费记录

## 8. 安全机制

### 8.1 令牌验证
- 双重 JWT 解析机制（jsonwebtoken + jose）
- 令牌过期时间检查
- 请求头 Authorization 验证

### 8.2 权限控制
- 基于角色的访问控制（RBAC）
- 租户级别权限隔离
- 资源级别权限验证

### 8.3 会话管理
- 用户强制下线功能
- 密码修改后自动失效令牌
- 多设备登录控制

## 9. 第三方授权集成

### 9.1 TikTok 授权
**位置**: `apps/app/src/app/(app)/auth/page.tsx`
- 处理 TikTok 授权回调
- 授权码验证和账号绑定
- 授权状态反馈

### 9.2 授权流程
**位置**: `apps/app/src/components/AdsTikTok/AccountContent.tsx`
- 弹窗式授权流程
- 第三方平台账号管理
- 授权状态同步

## 10. 配置和工具

### 10.1 配置获取
**位置**: `apps/app/src/services/actions/config.ts`
- `getAuthingAppId`: 获取 Authing 应用 ID
- 配置信息动态获取

### 10.2 API 客户端
**位置**: `apps/app/src/utils/fetcher/apiClient.ts`
- 自动添加认证头
- 登录状态检查
- 未认证自动跳转

## 总结

Authing 在本项目中扮演了核心的身份认证和用户管理角色，涵盖了：

1. **用户认证**: 登录、注册、密码管理
2. **用户管理**: 用户创建、编辑、删除、状态管理
3. **权限控制**: 角色管理、权限分配、资源访问控制
4. **租户管理**: 多租户架构支持、租户隔离
5. **会话管理**: JWT 令牌处理、会话状态维护
6. **第三方集成**: 支持外部平台授权集成

移除 Authing 需要重新实现上述所有功能，建议分阶段进行替换。
