'use client';

import { confirm } from '@/components/ConfirmDialog';
import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { NotFoundVideoFullfil } from '@/components/icon/NotFoundVideoFullfil';
import { WarningFullfil } from '@/components/icon/WarningFullfil';
import { ProImage } from '@/components/pro/pro-image';
import { <PERSON>ton, Dialog, DialogContent, DialogHeader, DialogTitle, Divider } from '@/components/ui';
import { Card, CardContent } from '@/components/ui/Card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { useBatchDownload } from '@/hooks/useBatchDownload';
import useTaskListFetcher from '@/hooks/useTaskListFetcher';
import { pageVideoGenerationTasks } from '@/services/actions/video-generation-task';
import { MaterialType } from '@/types/material';
import { cn } from '@/utils/cn';
import { ActionResult } from '@/utils/server-action/action';
import React, { useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { ProCheckbox } from '../pro/pro-checkbox';
import SubTaskPreviewCard from './SubTaskPreviewCard';
type TaskType = ArrayItemType<ActionResult<typeof pageVideoGenerationTasks>['list']>;

export type GeneratedMaterialType = TaskType['subTasks'][0]['generated_materials'][0];

export type SubTaskPreviewDialogRef = {
  show: (subTask: TaskType['subTasks'][0]) => void;
};

type SubTaskPreviewDialogProps = {
  enable?: boolean;
};

const SubTaskPreviewDialog = React.forwardRef<SubTaskPreviewDialogRef, SubTaskPreviewDialogProps>((props, ref) => {
  const [open, setOpen] = useState<boolean>(false);
  const [subTask, setSubTask] = useState<TaskType['subTasks'][0]>();
  const { downloadAndZipVideos } = useBatchDownload();
  const { removeMaterials } = useTaskListFetcher();

  useImperativeHandle(ref, () => {
    return {
      show: (data) => {
        setOpen(true);
        setSubTask(data);
        setDisplay(data?.generated_materials?.[0]);
      },
    };
  });

  const [display, setDisplay] = useState<GeneratedMaterialType>();

  const [selectedMaterials, setSelectedMaterials] = useState<GeneratedMaterialType[]>([]);

  const handleOpenChange = useCallback((open: boolean) => {
    setOpen(open);
    if (!open) {
      setDisplay(undefined);
      setSelectedMaterials([]);
    }
  }, []);

  const checkedAll = useMemo(() => {
    if (!selectedMaterials.length) return false;
    if (selectedMaterials.length === subTask?.generated_materials.length) return true;
    return 'indeterminate';
  }, [selectedMaterials.length, subTask?.generated_materials.length]);

  const handleSelectAll = useCallback(() => {
    if (checkedAll === true) {
      setSelectedMaterials([]);
    } else {
      setSelectedMaterials(subTask?.generated_materials || []);
    }
  }, [checkedAll, subTask?.generated_materials]);

  const handleDownload = useCallback(
    (materials: MaterialType[]) => {
      if (materials.length === 0) return;
      downloadAndZipVideos(materials);
    },
    [downloadAndZipVideos],
  );

  const handleRemove = useCallback(
    async (materials: MaterialType[]) => {
      if (materials.length === 0) return;
      confirm({
        content: (
          <div className="mb-[12px] flex gap-[8px]">
            <div className="mt-[3px]">
              <WarningFullfil className="h-[18px] w-[18px]" />
            </div>
            <div className="w-[200px]">
              <div className="mb-[4px]">确定删除吗？</div>
              <div className="text-xs font-normal text-[#95A0AA]">所选内容将会被全部删除，无法找回</div>
            </div>
          </div>
        ),
        buttonText: {
          cancel: '取消',
          confirm: '确定删除',
        },
        onConfirm: async () => {
          await removeMaterials(materials.map((m) => m.id));
          setSelectedMaterials([]);
        },
      });
    },
    [removeMaterials],
  );

  if (!subTask) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="min-w-[1080px] max-w-[1280px] rounded-[16px] bg-[#151C29]">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-sm font-normal">生成预览</DialogTitle>
        </DialogHeader>
        <div className="flex h-[622px] w-full flex-row justify-between gap-4">
          <SubTaskPreviewCard data={display} />
          <Card className="h-full rounded-[16px] border-none bg-[#CCDDFF14]">
            <CardContent className="flex h-full flex-col p-4 pt-6">
              <div className="mb-8 mr-6 flex justify-between">
                <div>共 {subTask?.generated_materials?.length} 个视频</div>
                <div className="flex items-center space-x-2 text-nowrap">
                  <div className="text-xs">全选</div>
                  <ProCheckbox checked={checkedAll} onCheckedChange={handleSelectAll} />
                </div>
              </div>
              <div className="relative flex flex-1 flex-col overflow-hidden text-xs">
                <div className="h-[80px]">
                  <VideoItem
                    key={subTask.id}
                    trace_id={subTask.trace_id}
                    showCheckbox={false}
                    material={subTask?.origin_material}
                    badge="原视频"
                    // className={cn('mr-2', display?.id === subTask?.origin_material?.id ? 'bg-[#CCDDFF1A]' : '')}
                    className={cn(
                      'mr-2',
                      display?.id === subTask?.origin_material?.id || subTask?.generated_materials.length === 0
                        ? 'bg-[#CCDDFF1A]'
                        : '',
                    )}
                    onClick={() => setDisplay(subTask?.origin_material)}
                  />
                </div>
                <Divider variant="gradation" className="my-4">
                  以下为生成的视频
                </Divider>
                <div className={cn('overflow-y-auto', 'mb-12', 'gap-[8px]')}>
                  {subTask?.generated_materials.map((material) => (
                    <VideoItem
                      key={material.id}
                      showCheckbox={true}
                      checked={selectedMaterials.some((m) => m.id === material.id)}
                      material={material}
                      onClick={() => {
                        setDisplay(material);
                      }}
                      className={display?.id === material?.id ? 'bg-[#FFFFFF0D]' : ''}
                      onCheckedChange={(checked) => {
                        setSelectedMaterials((prev) => {
                          if (checked) {
                            return [...prev, material];
                          } else {
                            return prev.filter((m) => m.id !== material.id);
                          }
                        });
                      }}
                    />
                  ))}
                </div>
                <OperationBar data={selectedMaterials} onDownload={handleDownload} onRemove={handleRemove} />
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
});

SubTaskPreviewDialog.displayName = 'SubTaskPreviewDialog';
/**
 * 素材列表项
 * @param props
 * @returns
 */
const VideoItem: React.FC<{
  showCheckbox?: boolean;
  checked?: boolean;
  trace_id?: string | null;
  onCheckedChange?: (checked: boolean) => void;
  children?: React.ReactNode;
  material: GeneratedMaterialType | undefined;
  className?: string;
  badge?: string;
  onClick?: () => void;
}> = (props) => {
  const content = () => {
    if (!props.material) {
      return (
        <div className={cn('flex gap-4')}>
          <div className="relative h-14 w-14 overflow-hidden rounded-[8px]">
            <div className="h-[160px] w-[160px]">
              <NotFoundVideoFullfil />
            </div>
          </div>
          <div className="flex h-14 flex-col justify-center text-[#ff6161]">原视频素材已删除</div>
        </div>
      );
    }
    return (
      <div className={cn('flex gap-4')} onClick={props.onClick}>
        <div className="relative h-14 w-14 min-w-14 overflow-hidden rounded-[6px]">
          {props.material?.vod_cover_url ? (
            <ProImage
              width={56}
              height={56}
              style={{ height: '56px', width: '56px', objectFit: 'cover', objectPosition: 'center' }}
              src={props.material.vod_cover_url || ''}
              alt={props.material.name || ''}
              fallback={() => (
                <div className="flex h-full w-full flex-col items-center justify-center gap-[16px] bg-[#272D3E] text-[#9FA4B2]">
                  <NotFoundCover className="h-[26px] w-[28px]" />
                </div>
              )}
            />
          ) : (
            <div className="h-[160px] w-[160px]">
              <NotFoundVideoFullfil />
            </div>
          )}
          {!!props.badge && (
            <div className="text-primary-foreground absolute right-0 top-0 rounded-bl bg-[#00071480] p-1 text-xs">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div> {props.badge}</div>
                  </TooltipTrigger>
                  {props.trace_id && (
                    <TooltipContent>
                      <p>实例 ID：{props.trace_id}</p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
        <div className="flex h-14 flex-col justify-between">
          <div className={cn('overflow-hidden overflow-ellipsis')}>{props.material?.name}</div>
          <div>{formatDuration(props.material?.video_duration)}</div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        'w-[260px] text-xs text-[#9FA4B2]',
        'mb-2 mr-2 cursor-pointer rounded-[8px] p-4 py-3 hover:bg-[#FFFFFF0D]',
        'group relative border',
        props.checked ? 'border-[#00E1FF] bg-[#00E1FF08]' : 'border-[transparent]',
        props.className,
      )}
    >
      {content()}
      {props.showCheckbox === true && (
        <ProCheckbox
          checked={props.checked ? true : false}
          onCheckedChange={props.onCheckedChange}
          className={cn(!props.checked ? 'hidden group-hover:block' : '', 'absolute right-5 top-6')}
        />
      )}
    </div>
  );
};

const OperationBar: React.FC<{
  data: MaterialType[];
  onDownload?: (data: MaterialType[]) => void;
  onRemove?: (data: MaterialType[]) => void;
}> = (props) => {
  return (
    <div className="absolute bottom-0 flex w-full gap-2">
      <Button
        variant="secondary"
        disabled={!props.data.length}
        className={cn(
          'w-[88px] rounded-[8px] bg-[#FFFFFF1A] text-[#B2B6C3] hover:bg-[#CCDDFF33]',
          !props.data.length
            ? 'disabled:pointer-events-auto disabled:cursor-not-allowed disabled:bg-[#CCDDFF1A] disabled:text-[#9FA4B280] disabled:opacity-100'
            : '',
        )}
        onClick={() => {
          if (!props.data.length) return;
          props.onRemove?.(props.data);
        }}
      >
        删除
      </Button>
      <Button
        variant="default"
        disabled={!props.data.length}
        onClick={() => {
          if (!props.data.length) return;
          props.onDownload?.(props.data);
        }}
        className={cn(
          'flex-1 text-black',
          !props.data.length
            ? 'cursor-not-allowed disabled:pointer-events-auto disabled:bg-[#CCDDFF1A] disabled:text-[#95A0AA80] disabled:opacity-100'
            : '',
        )}
      >
        下载
      </Button>
    </div>
  );
};

const formatDuration = (duration: number | undefined) => {
  if (duration === undefined) {
    return '00 分 00秒';
  }
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes >= 100 ? minutes : minutes.toString().padStart(2, '0')} 分 ${seconds.toString().padStart(2, '0')} 秒`;
};

export default SubTaskPreviewDialog;
