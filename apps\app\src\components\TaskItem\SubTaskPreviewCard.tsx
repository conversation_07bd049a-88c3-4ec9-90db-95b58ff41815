'use client';

import { VideoPlayer } from '@/components/VideoPlayer';
import { getVodMediaSubtitle, translateVodMediaSubtitle } from '@/services/actions/vod';
import { cn } from '@/utils/cn';
import { useAction } from '@/utils/server-action/action';
import { RefreshCcw } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import Markdown from 'react-markdown';
import ProConditional from '../pro/pro-conditional';
import { Button, Skeleton, Tabs, TabsContent, TabsList, TabsTrigger } from '../ui';
import { GeneratedMaterialType } from './SubTaskPreviewDialog';
import { SubtitlePlayer } from '../SubtitlePlayer';
import React from 'react';
import ReactPlayer from 'react-player';

type SubTaskPreviewCardProps = {
  data?: GeneratedMaterialType;
};

const SubTaskPreviewCard = (props: SubTaskPreviewCardProps) => {
  const { data } = props;
  const [currentTime, setCurrentTime] = useState(0);
  const playerRef = useRef<ReactPlayer>(null);

  const prevFileIdRef = useRef(data?.vod_file_id);

  const { data: subtitles, loading: sourceLoading } = useAction(
    getVodMediaSubtitle,
    { fileId: data?.vod_file_id! },
    { skip: (v) => !v.fileId },
  );

  const {
    data: translatedSubtitles,
    loading: translateLoading,
    run: translateSubtitles,
    mutate: mutateTranslation,
  } = useAction(
    translateVodMediaSubtitle,
    { captions: subtitles?.subtitleText!, languages: subtitles?.subtitleLanguage! },
    { skip: (v) => !v.captions || !v.languages, manual: true },
  );

  // 准备多语言字幕数据
  const subtitleSources = React.useMemo(() => {
    const sources: { language: string; content: string }[] = [];

    if (subtitles?.subtitleText) {
      sources.push({
        language: subtitles.subtitleLanguage || '原文',
        content: subtitles.subtitleText,
      });
    }

    if (translatedSubtitles) {
      sources.push({
        language: '中文',
        content: translatedSubtitles,
      });
    }

    return sources;
  }, [subtitles?.subtitleText, subtitles?.subtitleLanguage, translatedSubtitles]);

  useEffect(() => {
    if (data?.vod_file_id !== prevFileIdRef.current) {
      mutateTranslation(() => undefined);
      prevFileIdRef.current = data?.vod_file_id;
    }
  }, [data?.vod_file_id, mutateTranslation]);

  const handleSeek = (time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time);
    }
  };

  return (
    <div className="flex h-full w-full items-center justify-center gap-2 rounded-2xl bg-[#CCDDFF14]">
      {/* 视频预览 */}
      <div className="ml-2 min-w-[340px] overflow-hidden rounded-xl">
        <VideoPlayer
          ref={playerRef}
          src={data?.vod_media_url || data?.vod_media_url}
          height="600px"
          width="337.5px"
          playing
          muted
          onProgress={(state: { playedSeconds: number }) => setCurrentTime(state.playedSeconds)}
        />
      </div>
      {/* 视频信息 */}
      <Tabs defaultValue="style" className="h-full w-full rounded-none border-l-[#EFEDFD1A] p-2 text-sm text-[#9FA4B2]">
        <TabsList>
          <TabsTrigger value="style">风格解析</TabsTrigger>
          <TabsTrigger value="subtitle">视频脚本</TabsTrigger>
        </TabsList>
        {/* 风格 */}
        <TabsContent value="style" className="h-[calc(100%-60px)] overflow-y-auto pl-2 pt-2">
          <ProConditional condition={!!data?.generation_params?.kol_style}>
            <div className="break-words pr-2">
              <Markdown className="markdown markdown-content-compact prose prose-invert prose-pre:whitespace-pre-wrap prose-pre:break-words text-sm">
                {data?.generation_params?.kol_style}
              </Markdown>
            </div>
          </ProConditional>
        </TabsContent>

        {/* 视频文案 */}
        <TabsContent value="subtitle" className="h-[calc(100%-60px)]">
          <ProConditional condition={!!subtitles?.subtitleText} fallback={<SkeletonText className="mt-2" />}>
            <div className="flex h-full flex-col">
              <SubtitlePlayer
                subtitles={subtitleSources}
                currentTime={currentTime}
                className="flex-1"
                onSeek={handleSeek}
              />
              {!translatedSubtitles && (
                <Button
                  variant="link"
                  onClick={() => translateSubtitles()}
                  disabled={translateLoading}
                  className="right-0 mt-2 flex w-28 items-center text-xs text-[#00E1FF] hover:cursor-pointer"
                >
                  <RefreshCcw className={`mr-2 h-3 w-3 ${translateLoading ? 'animate-spin' : ''}`} /> 字幕翻译
                </Button>
              )}
            </div>
          </ProConditional>
        </TabsContent>
      </Tabs>
    </div>
  );
};

SubTaskPreviewCard.displayName = 'SubTaskPreviewCard';

export default SubTaskPreviewCard;

const SkeletonText: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('space-y-2', className)}>
      <Skeleton className="h-4 w-4/5 bg-[#363D54]" />
      <Skeleton className="h-4 w-2/3 bg-[#363D54]" />
      <Skeleton className="h-4 w-5/6 bg-[#363D54]" />
      <Skeleton className="h-4 w-3/4 bg-[#363D54]" />
      <Skeleton className="h-4 w-4/5 bg-[#363D54]" />
    </div>
  );
};
