'use client';
import useMaterialStore from '@/store/materialStore';
import { Button } from '@/components/ui/Button';
import { guide } from '@/store/guide';
import React, { useEffect, useState } from 'react';
import { LOCAL_UPLOAD, UPLOAD_TO_CLOUD_LIBRARY } from '@/common/statics/zh_cn';
import { cn } from '@/utils/cn';
import { useRouter } from 'next/navigation';

const UserGuide = () => {
  const { currentStep, setCurrentStep, steps, setSteps, tipOpen, setTipOpen } = guide();
  const { setMaterialDrawerOpen } = useMaterialStore();
  const [open, setOpen] = useState(false);
  const router = useRouter();

  const handleNextStep = () => {
    const currentStepInfo = steps[currentStep];
    if (!currentStepInfo) return;
    const nextStep = currentStep + 1;
    if (nextStep < steps.length) {
      if (!currentStepInfo.isNextStep) {
        currentStepInfo.isNextStep = true;
      }
      setCurrentStep(nextStep);
      if (currentStepInfo.target === '#next3') {
        setTipOpen(false);
      } else if (currentStepInfo.target === '#next5') {
        setTipOpen(false);
      } else if (currentStepInfo.target === '#next2') {
        setTipOpen(false);
      }
    } else {
      const updatedSteps = steps.map((step, index) => (index === currentStep ? { ...step, isNextStep: true } : step));
      setSteps(updatedSteps);
      const el = document.getElementById('next2');
      if (el) {
        el.style.zIndex = '1';
      }
      setMaterialDrawerOpen(false);
      setTipOpen(false);
      setOpen(false);
    }
  };

  const currentStepInfo = steps[currentStep];
  const target = currentStepInfo?.target ? document.querySelector(currentStepInfo.target) : null;
  const targetRect = target?.getBoundingClientRect();

  const guideStyle: React.CSSProperties = {
    position: 'absolute',
    top: (() => {
      if (currentStep === 4) {
        const el = document.getElementById('BWAI_TASK_CREATOR_SIDEBAR');
        const screenHeight = el?.clientHeight;
        let theTop = 0;
        const targetRect1 = document.querySelector('#next2')?.getBoundingClientRect();
        if (targetRect1 && screenHeight) {
          if (screenHeight < targetRect1.y) {
            el.scrollTo({ top: 9999, behavior: 'smooth' });
            const { top } = el.getBoundingClientRect();
            theTop = top;
          } else {
            return targetRect ? targetRect.top + window.scrollY + targetRect.height / 2 - 20 : 0;
          }
        }
        return targetRect ? theTop - 20 : 0;
      }
      if (currentStep === 0) {
        return targetRect ? targetRect.top + window.scrollY + targetRect.height / 2 - 20 : 0;
      }
      return targetRect ? targetRect.bottom + window.scrollY + 10 : 0;
    })(),
    left: (() => {
      if (currentStep === 4 || currentStep === 0) {
        return targetRect ? targetRect.right + window.scrollX + 10 : 0; // 将引导内容放在目标元素的右侧
      }
      return targetRect ? targetRect.left + window.scrollX + targetRect.width / 2 - 175 : 0;
    })(),
    zIndex: 1000,
  };
  useEffect(() => {
    const session = localStorage.getItem('token');
    if (!session) return;
    const hasSeenTour = steps.slice(0, 5).every((step) => step.isNextStep === true);
    // TODO: 暂时关闭引导
    // if (session && !hasSeenTour) {
    //   const currentPath = window.location.pathname;
    //   if (currentPath !== '/dashboard') {
    //     router.replace('/dashboard');
    //   } else {
    //     // setMaterialDrawerOpen(true);
    //     // setOpen(true);
    //   }
    // }
    if (hasSeenTour) {
      const el = document.getElementById('next2');
      if (el) {
        el.style.zIndex = '1';
      }
    }
  }, [setMaterialDrawerOpen, router, steps]);
  if (!open) return null;

  return (
    <div
      className="user-guide-overlay"
      role="dialog"
      aria-labelledby="user-guide-title"
      aria-describedby="user-guide-description"
    >
      <div className="overlay"></div>
      {currentStep === 2 && targetRect && (
        <div className="highlight2" style={{ left: targetRect.x, top: targetRect.y }}>
          {UPLOAD_TO_CLOUD_LIBRARY}
        </div>
      )}
      {currentStep === 1 && targetRect && (
        <div className={cn('highlight2')} style={{ left: targetRect.x, top: targetRect.y }}>
          {LOCAL_UPLOAD}
        </div>
      )}
      {tipOpen && open && targetRect && (
        <div className="ml-3 mt-3 w-[320px] rounded-2xl bg-[#1E1F24] pb-4 pl-5 pr-4 pt-6" style={guideStyle}>
          {currentStep === 0 || currentStep === 4 ? (
            <div className="absolute left-[-15px] top-[25px] h-0 w-0 -translate-y-1/2 rotate-[-90deg] transform border-b-[10px] border-l-[10px] border-r-[10px] border-b-[#1E1F24] border-l-transparent border-r-transparent"></div>
          ) : (
            <div className="absolute left-1/2 top-[-10px] h-0 w-0 -translate-x-1/2 transform border-b-[10px] border-l-[10px] border-r-[10px] border-b-[#1E1F24] border-l-transparent border-r-transparent"></div>
          )}
          {currentStepInfo?.title && (
            <div className="mb-3 text-base font-medium text-[#00E1FF]">{currentStepInfo.title}</div>
          )}
          <div className="mb-3 text-[13px]">{currentStepInfo?.message}</div>
          <div className="flex w-full flex-row-reverse items-center">
            <Button
              type="button"
              className="h-9 rounded-lg border border-[#00E1FF] bg-[#1E1F24] bg-opacity-10 text-[#00E1FF] hover:bg-[#1E1F24] hover:bg-opacity-10"
              onClick={(e) => {
                e.preventDefault();
                handleNextStep();
              }}
            >
              我知道了
            </Button>
            <div className="mr-2 text-[#95A0AA]">
              {currentStep + 1}/{steps.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserGuide;
