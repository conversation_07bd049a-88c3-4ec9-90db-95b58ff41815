// import tiktokService from '@/services/tiktokService';
// import { to } from '@roasmax/utils';
// import useSWR from 'swr';

// export type AdsSearchParams = {
//   group_id?: string;
//   advertiser_id?: string;
//   page?: number;
//   page_size?: number;
// };

// export const adsFetcher = async (params: AdsSearchParams) => {
//   if (!params) return null;
//   const { group_id, advertiser_id, page, page_size } = params;

//   const [err, res] = await to(
//     tiktokService.getAdList({
//       page,
//       page_size,
//       group_id: group_id ?? '',
//       advertiser_id: advertiser_id ?? '',
//     }),
//   );

//   if (err || !res?.success) {
//     throw new Error(`获取广告列表失败, ${res?.message}`);
//   }

//   return res.data;
// };

// export const useAdsList = (params: AdsSearchParams) => {
//   return useSWR(
//     params ? ['/api/ads/ad', params.group_id, params.advertiser_id, params.page, params.page_size] : null,
//     () => adsFetcher(params),
//   );
// };
