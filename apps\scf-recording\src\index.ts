'use strict';
import { NodeScfWebServer } from '@roasmax/scf-web-server';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { polling } from './handlers/polling';
dayjs.extend(utc);
dayjs.extend(timezone);

const dispatcher = async (params: { type: 'polling' }) => {
  if (params.type === 'polling') {
    return polling();
  }
};

const server = new NodeScfWebServer(dispatcher);

server.start(Number(process.env.SCF_PORT) || 9000);
