'use client';
import { cn } from '@/utils/cn';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ProgressSharing } from './components/ProgressSharing';

import { AnalysisResult } from '@/types/analysis';
import toast from 'react-hot-toast';
import { Step } from '@/types/product-analysis';

const ProductAnalysisSharePage = () => {
  const params = useParams();
  const shareId = params.shareId as string;
  const [picturesUrl, setPicturesUrl] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [playbackSteps, setPlaybackSteps] = useState<Step[]>([]);
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);
  const [completedSteps, setCompletedSteps] = useState(0);
  const [hasViewedResult, setHasViewedResult] = useState(false);
  const [filterCoverData, setFilterCoverData] = useState<any>(null); // 新增状态存储 filterCover 数据
  const [videoIds, setVideoIds] = useState<string[]>([]);
  const [link, setLink] = useState('');
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // 使用新的API路由
        const response = await fetch(`/api/product-analysis/${shareId}`);
        const result = await response.json();
        if (!response.ok || !result.success) {
          setHasError(true);
          toast.error('获取分享数据失败，可能链接已过期或不存在');
          setIsLoading(false);
          return;
        }

        const record = result.record;

        // 检查record是否存在
        if (!record) {
          setHasError(true);
          toast.error('获取分享数据失败，记录不存在');
          setIsLoading(false);
          return;
        }

        // 设置分析结果
        const analysisData: AnalysisResult = {
          product_analysis: record?.product_analysis || '',
          // 使用类型断言处理可能不存在的字段
          suggestion: (record as any)?.suggestion || '', // TODO: 需要处理
          template_info: Array.isArray(record?.template_info) ? (record?.template_info as any[]) : [],
          video_ids: Array.isArray(record?.video_ids) ? (record?.video_ids as string[]) : [],
        };
        setAnalysisResult(analysisData);
        setLink(record?.product_url);
        // 设置回放步骤
        if (record?.playback_steps && Array.isArray(record?.playback_steps) && record?.playback_steps.length > 0) {
          // 使用类型断言将JSON数据转换为Step类型
          setPlaybackSteps(record?.playback_steps as unknown as Step[]);
        }

        // 设置已完成步骤数
        setCompletedSteps(record?.completed_steps || 0);

        // 设置模板ID
        if (record?.template_info && Array.isArray(record?.template_info) && record?.template_info.length > 0) {
          try {
            const templateIds = (record?.template_info as any[]).map((item) => item['视频id'] || '');
            setSelectedTemplates(templateIds);
            setFilterCoverData(record?.template_info);
            setVideoIds(record?.video_ids || []);
            console.log(record.picturesUrl);

            setPicturesUrl(record?.picturesUrl || []);
          } catch (e) {
            console.error('处理模板ID时出错', e);
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error('获取分享数据失败', error);
        setHasError(true);
        toast.error('获取分享数据失败');
        setIsLoading(false);
      }
    };

    if (shareId) {
      fetchData();
    }
  }, [shareId]);
  // 加载状态组件
  const LoadingComponent = () => (
    <div className="flex h-[600px] w-full min-w-[800px] items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-[#CCDDFF] border-t-transparent"></div>
        <p className="text-lg text-white/80">正在加载分析结果...</p>
      </div>
    </div>
  );

  // 错误状态组件
  const ErrorComponent = () => (
    <div className="flex h-[600px] w-full min-w-[800px] flex-col items-center justify-center gap-6 p-8 text-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-20 w-20 text-red-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-white">链接无效或已过期</h2>
        <p className="mx-auto max-w-md text-gray-300">无法获取分享数据，请检查链接是否正确或联系分享者重新生成链接</p>
      </div>
    </div>
  );

  return (
    <div className={cn('h-[100vh] w-full flex-1 overflow-hidden bg-[#070F1F]')}>
      {isLoading ? (
        <LoadingComponent />
      ) : hasError ? (
        <ErrorComponent />
      ) : (
        <ProgressSharing
          isLoading={true} // 在分享页面中，我们始终显示进度
          completedSteps={completedSteps}
          playbackSteps={playbackSteps}
          isReturnFromResult={hasViewedResult} // 只有当用户已经查看过结果页面时，才认为是从结果页返回
          fiilterCoverage={filterCoverData}
          link={link}
          selectedTemplates={selectedTemplates}
          setSelectedTemplates={setSelectedTemplates}
          analysisResult={analysisResult}
          videoIds={videoIds}
          picturesUrl={picturesUrl}
        />
      )}
    </div>
  );
};

export default ProductAnalysisSharePage;
