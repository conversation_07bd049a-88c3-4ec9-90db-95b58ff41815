import { CosClient } from '@roasmax/utils/tencentcloud';
import { snowflake } from './snowflake';
import axios from 'axios';
import fs from 'fs';

export const cos = new CosClient({
  SecretId: process.env.COS_SECRET_ID,
  SecretKey: process.env.COS_SECRET_KEY,
});

/**
 * 从cos下载文件
 * @param bucketName 桶名 bucket-appid
 * @param key 文件key
 * @returns 文件本地路径
 */
export async function downloadFileFromCos(bucketName: string, key: string) {
  const objectLocalPath = `./${snowflake.nextId()}.mp4`;

  await cos.downloadFile({
    Bucket: bucketName,
    Region: process.env.COS_REGION!,
    Key: key,
    FilePath: objectLocalPath,
    ChunkSize: 1024 * 1024 * 10,
    ParallelLimit: 5,
    RetryTimes: 3,
    onProgress: (progress) => {
      console.log(`文件下载中...${progress.percent}`);
    },
  });

  return objectLocalPath;
}

export interface DownloadFileFromUrlOptions {
  /**
   * 分片大小
   */
  chunkSize?: number;
  /**
   * 重试次数
   */
  retryTimes?: number;
  /**
   * 重试延迟
   */
  retryDelay?: number;
  /**
   * 进度更新间隔
   */
  progressInterval?: number;
  /**
   * 下载进度回调
   */
  onProgress?: (loaded: number, total: number) => void;
}

export async function downloadFileFromUrl(
  url: string,
  outputPath: string,
  options?: DownloadFileFromUrlOptions,
): Promise<string> {
  const {
    chunkSize = 10 * 1024 * 1024,
    retryTimes = 3,
    retryDelay = 1000,
    progressInterval = 5000,
    onProgress,
  } = options || {};
  const writer = fs.createWriteStream(outputPath);

  let supportsRange = false;
  try {
    const headResponse = await axios.head(url);
    supportsRange = !!headResponse.headers['accept-ranges'];
  } catch (error: any) {
    console.log('检测断点续传支持失败，将使用普通下载方式: ', error.message);
  }

  let lastProgressTime = 0;
  const updateProgress = (loaded: number, total: number) => {
    const now = Date.now();
    if (now - lastProgressTime >= progressInterval) {
      lastProgressTime = now;
      onProgress?.(loaded, total);
    }
  };

  async function downloadWithRetry(attempt = 0) {
    try {
      const response = await axios({
        url,
        method: 'GET',
        responseType: 'stream',
        headers: supportsRange ? { Range: 'bytes=0-' } : {},
        timeout: 30000,
        onDownloadProgress: (progressEvent) => {
          if (!progressEvent.total) {
            return;
          }
          updateProgress(progressEvent.loaded, progressEvent.total);
        },
      });

      response.data.pipe(writer);

      return new Promise<string>((resolve, reject) => {
        writer.on('finish', () => resolve(outputPath));
        writer.on('error', async (error) => {
          writer.end();
          if (fs.existsSync(outputPath)) {
            fs.unlinkSync(outputPath);
          }

          if (attempt < retryTimes) {
            console.log(`下载失败，${retryDelay / 1000}秒后重试(${attempt + 1}/${retryTimes})...`);
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
            resolve(await downloadWithRetry(attempt + 1));
          } else {
            reject(new Error(`下载失败，已重试 ${retryTimes} 次: ${error.message}`));
          }
        });
      });
    } catch (error: any) {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }

      if (attempt < retryTimes) {
        console.log(`下载失败，${retryDelay / 1000}秒后重试(${attempt + 1}/${retryTimes})...`);
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        return downloadWithRetry(attempt + 1);
      }
      throw new Error(`下载失败，已重试 ${retryTimes} 次: ${error.message}`);
    }
  }

  return downloadWithRetry();
}
