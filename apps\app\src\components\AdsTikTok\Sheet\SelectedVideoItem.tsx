import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { Skeleton } from '@/components/ui/Skeleton';
import { useImageLoad } from '@/hooks/useImageLoad';
import { MaterialItemType } from '@/hooks/useMaterial';
import { UploadItemType, UploadStatus } from '@/types/upload';
import { getVodCoverUrl } from '@/utils/common';
import { X } from 'lucide-react';
import { useState } from 'react';

export function SelectedVideoItem({
  item,
  onRemove,
}: {
  item: MaterialItemType | UploadItemType;
  onRemove: (id: string) => void;
}) {
  const [coverImgMeta, coverLoading] = useImageLoad(getVodCoverUrl(item.vod_cover_url));
  const [loadImgError, setLoadImgError] = useState(false);

  // 判断是否为上传中的item
  const isUploadItem = 'status' in item && item.status === UploadStatus.UPLOADING;
  const uploadProgress = isUploadItem ? (item as UploadItemType).progress : 0;

  return (
    <div className="group relative flex h-16 w-[360px] items-center justify-between rounded-lg border border-[#363D54] bg-[#151C29] px-3">
      <div className="flex items-center gap-3">
        {isUploadItem ? (
          <div className="h-12 w-12 animate-pulse rounded-lg bg-slate-700" />
        ) : coverLoading ? (
          <Skeleton className="h-12 w-12 rounded-lg bg-slate-700" />
        ) : coverImgMeta?.src && !loadImgError ? (
          <img
            src={coverImgMeta.src}
            alt={item.name}
            className="h-12 w-12 rounded-lg object-cover"
            onError={() => setLoadImgError(true)}
          />
        ) : (
          <NotFoundCover className="h-12 w-12 rounded-lg" />
        )}
        <div className="flex flex-col gap-1">
          <span className="max-w-[200px] truncate text-sm text-[#ebfbff]">{item.name}</span>
          {isUploadItem && <span className="text-xs text-gray-400">上传中 {Math.round(uploadProgress)}%</span>}
        </div>
      </div>
      {!isUploadItem && (
        <div onClick={() => onRemove(item.id)} className="mr-4 cursor-pointer text-[#9FA4B2] hover:text-white">
          <X className="h-4 w-4" />
        </div>
      )}

      {/* 上传进度条 */}
      {isUploadItem && (
        <div
          className="absolute bottom-0 left-0 h-0.5 bg-[#00E1FF] transition-all"
          style={{
            width: `${uploadProgress}%`,
          }}
        />
      )}
    </div>
  );
}
