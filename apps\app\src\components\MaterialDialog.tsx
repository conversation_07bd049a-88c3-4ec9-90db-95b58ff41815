import { LOCAL_UPLOAD, UPLOAD_TO_CLOUD_LIBRARY, UPLOAD_TO_CLOUD_NOTICE } from '@/common/statics/zh_cn';
import { DirModal } from '@/components/DirModal';
import LibraryContainer from '@/components/LibraryContainer';
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  VisuallyHidden,
} from '@/components/ui';
import { Uploader } from '@/components/uploader';
import { UploadingList } from '@/components/UploadingList';
import { MaterialItemType } from '@/hooks/useMaterial';
import { useTaskCreator } from '@/hooks/useTaskCreator';
import { useUpload } from '@/hooks/useUpload';
import { useCloudStorageSize } from '@/hooks/useWallet';
import useMaterialStore from '@/store/materialStore';
import { UploadItemType, UploadStatus } from '@/types/upload';
import { cn } from '@/utils/cn';
import { checkConnection } from '@/utils/common';
import { validateMaterialsAudio } from '@/utils/materialUtils';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Drawer as DrawerPrimitive } from 'vaul';

const SelectUploadFooter = ({
  max2Ref,
  uploadList = [],
  onConfirm,
}: {
  max2Ref: React.RefObject<HTMLDivElement>;
  uploadList: UploadItemType[];
  onConfirm: () => void;
}) => {
  const uploadSuccessList = uploadList.filter((each) => each.status === UploadStatus.SUCCESS);

  return (
    <div
      ref={max2Ref}
      className="bg-background absolute bottom-6 left-0 right-0 mx-8 flex h-12 w-auto items-center justify-between rounded-lg px-3"
    >
      <div className="text-[13px]">
        已成功上传 {uploadList?.filter((each) => each.status === UploadStatus.SUCCESS).length}/{uploadList?.length}
      </div>
      <div className="flex items-center justify-between gap-8">
        <Button
          onClick={onConfirm}
          disabled={!uploadSuccessList.length}
          className="h-8 w-[108px] text-sm font-medium text-[#050A1C]"
        >
          导入视频
        </Button>
      </div>
    </div>
  );
};

export default function MaterialDialog({
  onConfirmUpload,
  shouldValidateAudio = false,
}: {
  onConfirmUpload?: () => void;
  shouldValidateAudio?: boolean;
}) {
  const { handleFileChange } = useUpload();
  const [scrollableHeight, setScrollableHeight] = useState('auto');
  const max1Ref = useRef<HTMLDivElement>(null);
  const max2Ref = useRef<HTMLDivElement>(null);
  const { addSelectedMaterials } = useTaskCreator();
  const { storage, refresh: refreshCloudStorageSize } = useCloudStorageSize();

  const {
    setMaterialDrawerOpen,
    setCloudTab,
    setDirModalOpen,
    materialDrawerOpen,
    uploadList,
    setUploadList,
    uploadTargetDir,
    setUploadListModalOpen,
    uploadValidating,
    setSelectedMaterialList,
  } = useMaterialStore();
  const handleConfirmSelect = async (list: MaterialItemType[]) => {
    const materialsToAdd = validateMaterialsAudio(list, shouldValidateAudio);

    const success = await addSelectedMaterials(...materialsToAdd);
    if (success) {
      setSelectedMaterialList([]);
      setMaterialDrawerOpen(false);
    }
  };

  const handleConfirmUpload = async () => {
    if (onConfirmUpload) {
      onConfirmUpload();
      return;
    }

    const materials = uploadList?.filter((each) => each.status === UploadStatus.SUCCESS).map((each) => each.material!);
    const validatedMaterials = validateMaterialsAudio(materials, shouldValidateAudio);
    const success = await addSelectedMaterials(...validatedMaterials); // 将数据添加到任务创建器中
    if (success) {
      setMaterialDrawerOpen(false);
      // clear all success upload items
      const newUploadList = uploadList.filter((each) => each.status !== UploadStatus.SUCCESS);
      setUploadList(newUploadList);
    }
  };

  useEffect(() => {
    if (!materialDrawerOpen) return;
    refreshCloudStorageSize();
  }, [materialDrawerOpen]);

  useEffect(() => {
    // 做这样恶心的操作是因为vaul的drawer组件在打开的时候，会设置body的pointer-events为none，导致无法点击，并且无法通过dismissible={true} 或者modal={false}来解决
    setTimeout(() => {
      document.body.style.pointerEvents = 'auto !important';
    }, 100);
  }, [materialDrawerOpen]);

  useEffect(() => {
    if (materialDrawerOpen) {
      setCloudTab(1);
    }
  }, [materialDrawerOpen]);

  useEffect(() => {
    return () => {
      setMaterialDrawerOpen(false);
      setUploadListModalOpen(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // todo: 后续改到全局
  useEffect(() => {
    if (!materialDrawerOpen) return;

    checkConnection();
  }, [materialDrawerOpen]);

  // Hack to remove focus trap
  useLayoutEffect(() => {
    document.addEventListener('focusin', (e) => e.stopImmediatePropagation());
    document.addEventListener('focusout', (e) => e.stopImmediatePropagation());
  }, []);
  useEffect(() => {
    const calculateScrollableHeight = () => {
      if (max1Ref.current && max2Ref.current) {
        const max1Rect = max1Ref.current.getBoundingClientRect();
        const max2Rect = max2Ref.current.getBoundingClientRect();
        const newHeight = max2Rect.y - max1Rect.y;
        setScrollableHeight(`${newHeight}px`);
      }
    };

    if (materialDrawerOpen && uploadList.length > 0) {
      calculateScrollableHeight();
      window.addEventListener('resize', calculateScrollableHeight);
    }

    return () => {
      window.removeEventListener('resize', calculateScrollableHeight);
    };
  }, [materialDrawerOpen, uploadList]);

  return (
    <Dialog open={materialDrawerOpen} onOpenChange={(open) => setMaterialDrawerOpen(open)}>
      <DialogContent className="min-h-[500px] w-[70vw] max-w-[70vw] bg-[#0D1320]">
        <VisuallyHidden>
          <DrawerPrimitive.Title className="hidden">Only for visually hidden</DrawerPrimitive.Title>
        </VisuallyHidden>
        <Tabs defaultValue="library">
          <TabsList className="gap-8 bg-transparent p-0">
            <TabsTrigger
              className={cn(
                'mr-1 bg-transparent p-0 text-base font-normal hover:text-white data-[state=active]:bg-transparent data-[state=active]:text-[#00e1ff] data-[state=active]:shadow-none',
              )}
              value="library"
              id="next3"
            >
              {UPLOAD_TO_CLOUD_LIBRARY}
            </TabsTrigger>
            <TabsTrigger
              className={cn(
                'z-[999999] mr-1 bg-transparent p-0 text-base font-normal hover:text-white data-[state=active]:bg-transparent data-[state=active]:text-[#00e1ff] data-[state=active]:shadow-none',
              )}
              value="upload"
              id="next4"
            >
              {LOCAL_UPLOAD}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="upload" className="mt-6">
            <div
              style={{
                height: 'calc(100vh - 27px - 100px)',
              }}
            >
              <div className="mb-4 flex items-center justify-start">
                <span className="text-sm text-[#9FA4B2]">{UPLOAD_TO_CLOUD_NOTICE}</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <DirModal
                          onConfirm={() => {
                            setDirModalOpen(false);
                          }}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>更改存入位置</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="mb-10 mr-8">
                <Uploader
                  handleFileChange={handleFileChange}
                  uploadTarDirId={uploadTargetDir?.id ?? ''}
                  disabled={!uploadTargetDir || uploadValidating || storage.used >= storage.total}
                />
              </div>
              <div ref={max1Ref}></div>
              <div className="flex flex-grow flex-col overflow-y-auto" style={{ height: scrollableHeight }}>
                <UploadingList />
              </div>
              {!!uploadList?.length && (
                <SelectUploadFooter max2Ref={max2Ref} onConfirm={handleConfirmUpload} uploadList={uploadList} />
              )}
            </div>
          </TabsContent>
          <TabsContent value="library" className="relative mt-6">
            <LibraryContainer
              handleConfirmSelect={handleConfirmSelect}
              config={{
                upload: false,
                search: false,
                batch: false,
                createDir: false,
                select: true,
              }}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
