import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/Dialog';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import { useTableLogic } from '@/hooks/useTableLogic';
import tiktokService from '@/services/tiktokService';
import { AdGroupTemplateResponse, AdGroupTemplateResponseCamel } from '@/services/interfaces/ads/res';
import { recursiveUnderscoreToCamel } from '@/utils/camel';
import { DEFAULT_DAYPARTING } from '../GroupContent';
import GroupSheet from '../Sheet/AdGroupSheet/GroupShee';
import toast from 'react-hot-toast';
import { useAdvertisers } from '@/hooks/useAdvertisers';
import { useCurrentAdvertiser, useCurrentCampaign } from '@/store/ads/adStore';
import { Toolt<PERSON>, TooltipContent, Too<PERSON><PERSON><PERSON>rovider, Toolt<PERSON>Trigger, But<PERSON>, Checkbox } from '@/components/ui';

export default function TemplateCreation({
  openDialog,
  setOpenDialog,
  fetchGroupData,
}: {
  openDialog: boolean;
  setOpenDialog: (open: boolean) => void;
  fetchGroupData: () => void;
}) {
  const { advertisers } = useAdvertisers(1, 999);
  const [templateList, setTemplateList] = useState<AdGroupTemplateResponseCamel[]>();
  const [sheetOpen, setSheetOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<{ id: string; jsonDate: any } | null>(null);
  const currentAdvertiser = useCurrentAdvertiser(); // 获取当前广告主
  const selectedCampaigns = useCurrentCampaign(); // 选择的当前广告系列
  const { selectedRows, sortState, sortedData, handleSelectAll, handleSelect, handleSort } = useTableLogic(
    templateList || [],
    'template_id',
  );
  const [loading, setLoading] = useState(false);
  const columns = [
    { key: 'switch', title: '模板ID', width: 150 },
    { key: 'groupName', title: '模板名称', width: 100 },
    { key: 'accountName', title: '关联账户', width: 150 },
    { key: 'tagNames', title: '创建时间', width: 150 },
    { key: 'actions', title: '操作', width: 100 },
  ];
  const renderCell = (key: string, record: any) => {
    switch (key) {
      case 'switch':
        return <div className="truncate">{record.id}</div>;
      case 'groupName':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-pointer truncate">{record.name}</div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{record.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'accountName':
        const account = advertisers?.list?.find((item) => item.advertiserId === record.advertiserId);
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-pointer truncate">{account?.advertiserName}</div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{account?.advertiserName}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'tagNames':
        return <div>{new Date(record.gmtCreate).toLocaleString()}</div>;
      case 'actions':
        return (
          <div className="flex items-center">
            <Button
              variant="link"
              size="sm"
              className="text-sm text-[#00E1FF] hover:no-underline"
              onClick={() => handleViewClick(record.id, record.jsonDate)}
            >
              预览
            </Button>
            <Button
              onClick={() => handleConfirm(record)}
              variant="link"
              size="sm"
              className="text-sm text-[#00E1FF] hover:no-underline"
            >
              使用
            </Button>
          </div>
        );
    }
  };
  const handleViewClick = (id: string, jsonDate: any) => {
    setSheetOpen(true);
    setEditingGroup({ id, jsonDate });
  };
  const fetchData = async () => {
    try {
      setLoading(true);
      const res: AdGroupTemplateResponse = await tiktokService.getAdGroupTemplate({
        advertiser_ids: currentAdvertiser?.map((item) => item?.advertiserId) || [],
      });
      const resToCamel = recursiveUnderscoreToCamel(res, ['json_date']);
      setTemplateList(resToCamel);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (openDialog) {
      fetchData();
    }
  }, [openDialog, currentAdvertiser]);

  const handleConfirm = async (record: any) => {
    try {
      setLoading(true);
      await tiktokService.createAdGroupTemplate({
        template_id: record.id,
        campaign_ids: selectedCampaigns?.map((item) => item.campaignId) || [],
      });
      setOpenDialog(false);
      fetchGroupData();
      toast.success('创建成功');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Dialog
      open={openDialog}
      onOpenChange={() => {
        setOpenDialog(false);
        fetchGroupData();
      }}
    >
      <DialogContent className="max-w-[800px] bg-[#151C29]">
        <DialogHeader className="flex items-center justify-center">
          <DialogTitle className="text-base font-medium"></DialogTitle>
        </DialogHeader>
        <CommonTable
          columns={columns}
          dataSource={sortedData}
          selectedRows={selectedRows}
          sortState={sortState}
          rowKey="template_id"
          onSelectAll={handleSelectAll}
          onSelect={handleSelect}
          onSort={handleSort}
          renderCell={renderCell}
          loading={loading}
        />
        <DialogFooter>
          <Button
            className="h-8 w-[90px] rounded"
            variant="outline"
            onClick={() => {
              setOpenDialog(false);
              fetchGroupData();
            }}
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
      <GroupSheet
        open={sheetOpen}
        DEFAULT_DAYPARTING={DEFAULT_DAYPARTING}
        type="template"
        editingGroup={editingGroup}
        setOpen={setSheetOpen}
        loadingDrawer={false}
      />
    </Dialog>
  );
}
