'use client';

import { confirm } from '@/components/ConfirmDialog';
import { DataImportDialog, DataImportDialogRef } from '@/components/DataImportDialog';
import { ProFilter } from '@/components/pro/pro-filter';
import { ProPagination } from '@/components/pro/pro-pagination';
import ProTable, { ProTableProps, useTableRowSelect } from '@/components/pro/pro-table';
import { Button, Panel } from '@/components/ui';
import {
  batchCreateAndBindCloudDisks,
  createSocialAccount,
  getShareLinkByAccountIds,
  listSocialAccountsByKeys,
  pageSocialAccounts,
  updateSocialAccount,
} from '@/services/actions/social';
import { action, ActionParams, ActionResult, useAction } from '@/utils/server-action/action';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { buildFilterColumns, buildSocialAccountColumns } from './config';
import Link from 'next/link';
import dayjs from 'dayjs';
import { uniq } from 'lodash';
import { ProButton } from '@/components/pro/pro-button';

export type SocialAccount = NonNullable<ActionResult<typeof pageSocialAccounts>['list']>[number];

export default function SocialAccounts() {
  const router = useRouter();
  const dataImportDialogRef = useRef<DataImportDialogRef>(null);

  const [pagination, setPagination] = useState<{ page: number; pageSize: number }>({ page: 1, pageSize: 1000 });
  const [filters, setFilters] = useState<ActionParams<typeof pageSocialAccounts>['filters']>({});

  const { data, loading, run, mutate } = useAction(pageSocialAccounts, {
    pagination: { page: pagination.page, pageSize: pagination.pageSize },
    filters: filters,
  });

  const filterColumns = useMemo(() => buildFilterColumns(), []);

  const { selectedRowKeys, onSelect, onSelectAll, handleClear } = useTableRowSelect({
    dataSource: data?.list,
    rowKey: 'id',
  });

  const handleRecordEditSubmit: NonNullable<ProTableProps<SocialAccount>['onEdit']> = async (value, record, col) => {
    const v = typeof value === 'boolean' ? (value ? 1 : 0) : value;
    if (
      col.key === 'ip' ||
      col.key === 'master_id' ||
      col.key === 'phone_no' ||
      col.key === 'remark' ||
      col.key === 'quality'
    ) {
      const res = await action(
        updateSocialAccount,
        { where: { id: record.id }, data: { [col.key]: v } },
        { errorType: 'return' },
      );

      if (!res.success) {
        toast.error(res.message);
        return;
      }
      toast.success('修改成功');

      mutate((pre) => {
        const next = { ...pre };
        const index = next.list?.findIndex((item) => item.id === record.id);
        if (typeof index === 'number' && index !== -1 && next.list?.[index]) {
          next.list[index] = { ...next.list[index], [col.key!]: v };
        }
        return next as typeof pre;
      });
      return;
    }
    toast.error('暂不支持修改该字段');
  };

  const columns = useMemo(() => {
    return buildSocialAccountColumns({ onRefresh: run });
  }, [run]);

  const handleCreateCloudTerminal = useCallback(async () => {
    return confirm({
      content: '确定创建并绑定云盘终端吗？',
      onConfirm: async () => {
        const res = await action(
          batchCreateAndBindCloudDisks,
          { ids: Array.from(selectedRowKeys), cloudDiskType: 'cloud_disk' },
          { errorType: 'return' },
        );
        if (res.success) {
          toast.success('创建云盘终端成功');
          handleClear();
          run();
        } else {
          toast.error(res.message);
        }
      },
    });
  }, [selectedRowKeys, run]);

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter value={filters} onSubmit={(v) => setFilters(v)} columns={filterColumns} className="mb-2" />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="flex h-[32px] items-center gap-2">
            <div className="text-base font-bold">账号资产</div>
            <div className="flex items-center gap-2">
              <div className="text-gray-500">已选择 {selectedRowKeys.size} 条</div>
              <div className="cursor-pointer" onClick={() => handleClear()}>
                清空
              </div>
            </div>
          </div>
          <div className="flex items-center justify-end gap-2">
            <Button variant="link" className="h-[32px]" onClick={() => router.push('/distributions/terminals')}>
              云终端
            </Button>
            <ProButton
              disabled={selectedRowKeys.size === 0}
              onClick={async () => {
                const res = await action(getShareLinkByAccountIds, { accountIds: Array.from(selectedRowKeys) });
                if (res?.length) {
                  confirm({
                    className: 'max-w-[700px] w-auto',
                    content: (
                      <div>
                        <div className="mb-2 text-sm font-bold">有 {res.length} 个账号可以生成分享链接</div>
                        <div className="max-h-[400px] w-[500px] overflow-auto">
                          {res.map((item) => (
                            <div key={item.accountId} className="mb-4">
                              <div>{item.info.nickname}</div>
                              <div className="text-sm text-gray-500">{item.info.account_identity}</div>
                              <Link
                                className="overflow-hidden text-ellipsis text-gray-500"
                                href={item.shareLink}
                                target="_blank"
                              >
                                {item.shareLink}
                              </Link>
                            </div>
                          ))}
                        </div>
                      </div>
                    ),
                    onConfirm: () => {
                      const now = dayjs().format('YYYY-MM-DD_HH-mm-ss');
                      const ips = uniq(res.map((item) => item.info.ip));
                      ips.forEach((ip) => {
                        const content = res
                          .filter((item) => item.info.ip === ip)
                          .map((item) => `${item.info.nickname}\r\n${item.info.account_identity}\r\n${item.shareLink}`)
                          .join('\r\n\r\n');
                        const blob = new Blob([content], { type: 'text/plain' });
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `账号分享链接_${ip}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.txt`;
                        link.click();
                        window.URL.revokeObjectURL(url);
                      });
                    },
                  });
                }
              }}
            >
              导出分享链接
            </ProButton>
            <Button
              disabled={selectedRowKeys.size === 0}
              className="h-[32px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={handleCreateCloudTerminal}
            >
              创建云盘终端
            </Button>
            <Button
              className="h-[32px] w-[128px] text-[#050A1C] hover:text-[#050A1C]"
              onClick={() => {
                dataImportDialogRef.current?.show({
                  templateResourceName: 'social_accounts',
                  request: async (data, schema) => {
                    const socialAccounts = await action(listSocialAccountsByKeys, {
                      keys: data.map((item) => {
                        return schema.uniqueKeys.reduce((acc, key) => {
                          acc[key] = item[key];
                          return acc;
                        }, {} as any);
                      }),
                    });
                    return socialAccounts || [];
                  },
                  onSubmit: async (data) => {
                    // 依次新建账号
                    for (const item of data.newData) {
                      await action(createSocialAccount, item);
                    }
                    for (const item of data.updateData) {
                      const data = (item.__diff__ as string[]).reduce((acc: Record<string, any>, key: string) => {
                        acc[key] = item[key];
                        return acc;
                      }, {});
                      await action(updateSocialAccount, { where: { id: item.id }, data });
                    }
                    return true;
                  },
                });
              }}
            >
              导入数据
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable
            columns={columns}
            onEdit={handleRecordEditSubmit}
            loading={loading}
            dataSource={data?.list || []}
            selection={{ selectedRowKeys: selectedRowKeys, onSelect, onSelectAll }}
          />
        </div>
        <div className="mt-1">
          <ProPagination
            pagination={{ ...pagination, total: data?.pagination.total }}
            onPaginationChange={setPagination}
          />
        </div>
      </Panel>
      <DataImportDialog ref={dataImportDialogRef} />
    </div>
  );
}
