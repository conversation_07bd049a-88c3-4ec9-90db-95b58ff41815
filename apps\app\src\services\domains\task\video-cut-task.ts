import { Prisma } from '@roasmax/database';
import { ActionContext } from '@roasmax/serve';
import { pick } from '@roasmax/utils';

export const addVideoCutTask = async (
  ctx: ActionContext<{
    name: string;
    origin_url: string;
    goods_list: string;
    ip: string;
    live_room: string;
    live_session: string;
  }>,
) => {
  const res = await ctx.db.video_cut_tasks.create({
    data: {
      tenant_id: ctx.tenant.id,
      name: ctx.data.name,
      origin_url: ctx.data.origin_url,
      goods_list: ctx.data.goods_list.split('\n'),
      ip: ctx.data.ip,
      live_room: ctx.data.live_room,
      live_session: ctx.data.live_session,
      language: 'zh-CN',
      cut_mode: 'general',
      status: 'DRAFT',
      status_desc: '等待执行',
    },
  });
  return res;
};

export const updateVideoCutTask = async (
  ctx: ActionContext<{
    where: { id: string };
    data: Pick<
      Prisma.video_cut_tasksUpdateInput,
      'ip' | 'cut_mode' | 'live_room' | 'live_session' | 'origin_url' | 'name' | 'goods_list' | 'language'
    >;
  }>,
) => {
  const data = pick(ctx.data.data, [
    'ip',
    'cut_mode',
    'live_room',
    'live_session',
    'origin_url',
    'name',
    'goods_list',
    'language',
  ]);
  return await ctx.db.video_cut_tasks.update({ where: ctx.data.where, data });
};
