import { useState, useCallback, useRef } from 'react';
import {
  CreateTaskRequest,
  CreateTaskResponse,
  QueryTaskResponse,
  CreateTaskApiRequest,
  CreateTaskApiResponse,
  QueryTaskApiResponse,
} from '../types';
import {
  createAiVideoGenerationTask,
  updateAiVideoGenerationTask,
  queryVideoGenerationTaskStatus,
} from '@/services/actions/ai-video-generation-task';
import { action } from '@/utils/server-action/action';
import { updateQuotas } from '@/services/actions/pricing_plans';
import { WalletChangeType } from '@/types/wallet';
import { useWallet } from '@/hooks/useWallet';

// API Base URL
const API_BASE_URL = 'https://bowongai--text2video-api-dev-fastapi-app.modal.run';

// 将VideoType转换为API需要的task_type
const mapVideoTypeToTaskType = (type: string): string => {
  const typeMap: Record<string, string> = {
    jesus1: 'jesus1',
    jesus2: 'jesus2',
    chop: 'chop',
    lady: 'lady',
    vlog: 'vlog',
    tea: 'tea',
  };
  return typeMap[type] || type;
};

// 创建任务API调用
const createTask = async (request: CreateTaskRequest): Promise<CreateTaskResponse> => {
  try {
    // 准备API请求数据
    const apiRequest: CreateTaskApiRequest = {
      task_type: mapVideoTypeToTaskType(request.type),
      prompt: request.prompt,
      img_url: request.files.length > 0 ? (request.files[0] as any).url || '' : '',
      duration: '10', // 默认10秒，可以根据需要调整
    };

    const response = await fetch(`${API_BASE_URL}/api/task/create/task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const apiResponse = await response.json();

    // 检查API响应格式并正确提取taskId
    // 新的API响应格式: {task_id: "...", message: "...", status: "submitted"}
    if (apiResponse.task_id && apiResponse.status === 'submitted') {
      const taskId = apiResponse.task_id;

      return {
        taskId,
        status: 'pending',
      };
    }
    // 兼容旧的API响应格式: {status: true, data: "taskId"}
    else if (apiResponse.status === true && apiResponse.data) {
      const taskId = apiResponse.data;

      return {
        taskId,
        status: 'pending',
      };
    } else {
      // 如果响应格式不符合预期，抛出错误
      const errorMsg = apiResponse.message || apiResponse.msg || '未知错误';
      throw new Error(`任务创建失败: ${errorMsg}`);
    }
  } catch (error) {
    console.error('创建任务失败:', error);
    throw new Error(`创建任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 查询任务API调用
const queryTask = async (taskId: string): Promise<QueryTaskResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/task/status/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: QueryTaskApiResponse = await response.json();

    // 解析返回的结果字符串
    try {
      const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
      let queryResponse: QueryTaskResponse;

      // 处理最新的包在data中的返回格式: {data: {video_url: "...", image_url: "...", aspect_ratio: "..."}}
      if (
        parsedResult.data &&
        parsedResult.data.video_url &&
        parsedResult.data.image_url &&
        !parsedResult.status &&
        !parsedResult.result
      ) {
        queryResponse = {
          taskId,
          status: 'completed',
          progress: 100,
          result: {
            status: 'completed',
            video_url: parsedResult.data.video_url,
            thumbnail_url: parsedResult.data.image_url,
            videoUrl: parsedResult.data.video_url,
            thumbnailUrl: parsedResult.data.image_url,
            image_url: parsedResult.data.image_url,
          },
          message: '任务完成',
        };
      }
      // 处理最新的返回格式: {status: "completed", data: {video_url: "...", image_url: "..."}}
      else if (
        (parsedResult.status === 'completed' || parsedResult.status === 'failed' || parsedResult.status === 'error') &&
        parsedResult.data &&
        typeof parsedResult.data === 'object'
      ) {
        if (parsedResult.status === 'completed') {
          console.log('parsedResult', parsedResult);
          queryResponse = {
            taskId,
            status: 'completed',
            progress: 100,
            result: {
              status: parsedResult.status,
              video_url: parsedResult.data.video_url,
              thumbnail_url: parsedResult.data.image_url,
              videoUrl: parsedResult.data.video_url,
              thumbnailUrl: parsedResult.data.image_url,
              image_url: parsedResult.data.image_url,
            },
            message: parsedResult.message || '任务完成',
          };
        } else if (parsedResult.status === 'failed' || parsedResult.status === 'error') {
          queryResponse = {
            taskId,
            status: 'failed',
            progress: 0,
            result: {
              status: parsedResult.status,
              error: parsedResult.msg || parsedResult.data?.error || parsedResult.message || '任务失败',
            },
            message: parsedResult.msg || parsedResult.data?.error || parsedResult.message || '任务失败',
          };
        } else {
          // 其他未知状态，默认认为还在处理中
          queryResponse = {
            taskId,
            status: 'running',
            progress: 50,
            result: parsedResult.data
              ? {
                  status: parsedResult.status,
                  video_url: parsedResult.data.video_url || '',
                  thumbnail_url: parsedResult.data.image_url || '',
                  videoUrl: parsedResult.data.video_url || '',
                  thumbnailUrl: parsedResult.data.image_url || '',
                  image_url: parsedResult.data.image_url || '',
                }
              : undefined,
            message: parsedResult.message || '任务处理中',
          };
        }
      }
      // 处理新的返回格式
      else if (parsedResult.status === true && parsedResult.task_status === 'completed' && parsedResult.data) {
        const data = parsedResult.data;
        queryResponse = {
          taskId,
          status: 'completed',
          progress: 100,
          result: {
            status: data.status,
            video_url: data.generated_video_url || data.result_url,
            thumbnail_url: data.generated_image_url,
            videoUrl: data.generated_video_url || data.result_url,
            thumbnailUrl: data.generated_image_url,
            image_url: data.generated_image_url,
            original_prompt: data.original_prompt,
            midjourney_prompt: data.midjourney_prompt,
            task_type: data.task_type,
            timestamp: data.timestamp,
            completed_at: data.completed_at,
            generation_details: {
              image_generation: data.generation_details?.image_generation,
              video_generation: data.generation_details?.video_generation,
            },
            video_prompt: data.video_prompt,
            base_prompt: data.base_prompt,
          },
          message: parsedResult.msg,
        };
      }
      // 检查新的完整响应格式（包含result结构）
      else if (parsedResult.result) {
        const resultStatus = parsedResult.result.status;

        if (resultStatus === 'failed' || resultStatus === 'error') {
          // 处理失败情况
          queryResponse = {
            taskId,
            status: 'failed',
            progress: 0,
            result: {
              ...parsedResult.result,
              error: parsedResult.result.error,
              error_details: parsedResult.result.error_details,
              original_prompt: parsedResult.result.original_prompt,
              midjourney_prompt: parsedResult.result.midjourney_prompt,
              task_type: parsedResult.result.task_type,
              timestamp: parsedResult.result.timestamp,
              completed_at: parsedResult.result.completed_at,
            },
            message: parsedResult.message || parsedResult.result.error,
          };
        } else if (resultStatus === 'completed' && parsedResult.result.video_url) {
          // 处理成功完成情况
          queryResponse = {
            taskId,
            status: 'completed',
            progress: 100,
            result: {
              ...parsedResult.result,
              video_url: parsedResult.result.video_url,
              thumbnail_url: parsedResult.result.image_url || '',
              videoUrl: parsedResult.result.video_url, // 兼容字段
              thumbnailUrl: parsedResult.result.image_url || '', // 兼容字段
            },
            message: parsedResult.message,
          };
        } else {
          // 其他状态（进行中等）
          queryResponse = {
            taskId,
            status: 'running',
            progress: 50,
            result: parsedResult.result,
            message: parsedResult.message,
          };
        }
      }
      // 检查旧的API响应格式：{status: true, msg: "查询成功", data: "video_url"}
      else if (parsedResult.status === true && parsedResult.data) {
        // 如果data是直接的视频URL字符串
        if (typeof parsedResult.data === 'string' && parsedResult.data.startsWith('http')) {
          queryResponse = {
            taskId,
            status: 'completed',
            progress: 100,
            result: {
              videoUrl: parsedResult.data,
              thumbnailUrl: '',
              video_url: parsedResult.data,
            },
            message: parsedResult.msg,
          };
        }
        // 如果data是对象格式
        else if (typeof parsedResult.data === 'object') {
          queryResponse = {
            taskId,
            status: 'completed',
            progress: 100,
            result: {
              videoUrl: parsedResult.data.video_url || parsedResult.data.videoUrl,
              thumbnailUrl: parsedResult.data.image_url || parsedResult.data.imageUrl || '',
              video_url: parsedResult.data.video_url || parsedResult.data.videoUrl,
              thumbnail_url: parsedResult.data.image_url || parsedResult.data.imageUrl || '',
              image_url: parsedResult.data.image_url || parsedResult.data.imageUrl || '',
            },
            message: parsedResult.msg,
          };
        } else {
          // 默认认为还在处理中
          queryResponse = {
            taskId,
            status: 'running',
            progress: 50,
          };
        }
      }
      // 原有的结构处理逻辑
      else if (parsedResult.status && typeof parsedResult.status === 'string') {
        queryResponse = {
          taskId,
          status: parsedResult.status,
          progress: parsedResult.progress || 0,
          result: parsedResult.result
            ? {
                videoUrl: parsedResult.result.video_url || parsedResult.result.videoUrl,
                thumbnailUrl: parsedResult.result.image_url || parsedResult.result.thumbnailUrl,
                video_url: parsedResult.result.video_url,
                thumbnail_url: parsedResult.result.image_url,
                image_url: parsedResult.result.image_url,
              }
            : undefined,
          message: parsedResult.message,
        };
      } else if (parsedResult.video_url || parsedResult.videoUrl) {
        // 如果直接返回了视频URL，说明任务完成
        queryResponse = {
          taskId,
          status: 'completed',
          progress: 100,
          result: {
            videoUrl: parsedResult.video_url || parsedResult.videoUrl,
            thumbnailUrl: parsedResult.image_url || parsedResult.imageUrl || '',
            video_url: parsedResult.video_url || parsedResult.videoUrl,
            thumbnail_url: parsedResult.image_url || parsedResult.imageUrl || '',
            image_url: parsedResult.image_url || parsedResult.imageUrl || '',
          },
        };
      } else if (parsedResult.error) {
        queryResponse = {
          taskId,
          status: 'failed',
          progress: 0,
          message: parsedResult.error,
        };
      } else if ((parsedResult.status === 'failed' || parsedResult.status === 'error') && parsedResult.msg) {
        // 处理新的失败格式: {status: "failed"/"error", msg: "错误信息", data: {...}}
        queryResponse = {
          taskId,
          status: 'failed',
          progress: 0,
          result: {
            status: parsedResult.status,
            error: parsedResult.msg,
          },
          message: parsedResult.msg,
        };
      } else {
        // 默认认为还在处理中
        queryResponse = {
          taskId,
          status: 'running',
          progress: parsedResult.progress || 50,
        };
      }

      console.log('queryResponse', queryResponse);
      return queryResponse;
    } catch (parseError) {
      // 如果解析失败，可能是任务还在处理中
      const queryResponse = {
        taskId,
        status: 'running' as const,
        progress: 30,
      };

      return queryResponse;
    }
  } catch (error) {
    console.error('查询任务失败:', error);
    throw new Error(`查询任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

export const useVideoGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTask, setCurrentTask] = useState<QueryTaskResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const [dbTaskId, setDbTaskId] = useState<string | null>(null);
  const { refresh: refreshWallet } = useWallet();

  const generateVideo = useCallback(
    async (request: CreateTaskRequest) => {
      let currentDbTaskId: string | null = null;

      try {
        setIsGenerating(true);
        setError(null);
        setCurrentTask(null);
        setDbTaskId(null);

        // 1. 先扣除5点数（限时特惠）
        await action(updateQuotas, {
          quota: -5,
          changeType: WalletChangeType.VIDEO,
          changeReason: 'AI视频生成任务',
        });

        // 扣费成功后刷新钱包余额显示
        await refreshWallet();

        // 2. 先创建数据库记录
        const dbTask = await action(createAiVideoGenerationTask, {
          name: `${request.type}视频生成_${new Date().toLocaleString()}`,
          prompt: request.prompt,
          video_type: request.type,
          reference_images: request.files.map((f) => ('url' in f ? f.url : '')),
        });

        currentDbTaskId = dbTask?.id || null;
        setDbTaskId(currentDbTaskId);

        // 3. 创建第三方API任务
        const createResponse = await createTask(request);

        // 4. 更新数据库记录，保存外部任务ID
        if (currentDbTaskId) {
          await action(updateAiVideoGenerationTask, {
            taskId: currentDbTaskId,
            data: {
              external_task_id: createResponse.taskId,
              status: 'running',
              status_desc: '任务已提交，等待处理',
            },
          });
        }

        // 开始轮询
        const pollTask = async () => {
          try {
            const queryResponse = await queryTask(createResponse.taskId);
            setCurrentTask(queryResponse);

            // 检查任务是否完成（外层status或内层result.status）
            const actualStatus = queryResponse.result?.status || queryResponse.status;
            const isCompleted = actualStatus === 'completed' || actualStatus === 'failed' || actualStatus === 'error';

            // 只有任务完成或失败时才更新数据库状态
            if (isCompleted && currentDbTaskId) {
              await action(updateAiVideoGenerationTask, {
                taskId: currentDbTaskId,
                data: {
                  status: actualStatus,
                  progress: queryResponse.progress || (actualStatus === 'completed' ? 100 : 0),
                  status_desc: queryResponse.message || undefined,
                  video_url: queryResponse.result?.video_url || queryResponse.result?.videoUrl,
                  thumbnail_url:
                    queryResponse.result?.thumbnail_url ||
                    queryResponse.result?.image_url ||
                    queryResponse.result?.imageUrl,
                  error_message:
                    actualStatus === 'failed' || actualStatus === 'error'
                      ? queryResponse.result?.error || queryResponse.message
                      : undefined,
                  completed_at: new Date(),
                  properties: queryResponse.result ? JSON.parse(JSON.stringify(queryResponse.result)) : undefined,
                },
              });
            }

            if (isCompleted) {
              // 任务完成，停止轮询
              if (pollingRef.current) {
                clearInterval(pollingRef.current);
                pollingRef.current = null;
              }
              setIsGenerating(false);

              if (actualStatus === 'failed' || actualStatus === 'error') {
                // 任务失败，退还点数
                try {
                  await action(updateQuotas, {
                    quota: 5,
                    changeType: WalletChangeType.REFUND,
                    changeReason: 'AI视频生成任务失败退费',
                  });
                  // 退费成功后刷新钱包余额显示
                  await refreshWallet();
                } catch (refundError) {
                  console.error('退费失败:', refundError);
                }
                setError(queryResponse.result?.error || queryResponse.message || '视频生成失败');
              }
            }
          } catch (err) {
            console.error('轮询任务状态失败:', err);
            setError('查询任务状态失败');

            // 轮询失败，退还点数
            try {
              await action(updateQuotas, {
                quota: 5,
                changeType: WalletChangeType.REFUND,
                changeReason: 'AI视频生成任务轮询失败退费',
              });
              // 退费成功后刷新钱包余额显示
              await refreshWallet();
            } catch (refundError) {
              console.error('退费失败:', refundError);
            }

            // 更新数据库为失败状态
            if (currentDbTaskId) {
              await action(updateAiVideoGenerationTask, {
                taskId: currentDbTaskId,
                data: {
                  status: 'failed',
                  error_message: '查询任务状态失败',
                  completed_at: new Date(),
                },
              });
            }
            if (pollingRef.current) {
              clearInterval(pollingRef.current);
              pollingRef.current = null;
            }
            setIsGenerating(false);
          }
        };

        // 立即执行一次查询并获取任务状态
        const initialQueryResponse = await queryTask(createResponse.taskId);
        setCurrentTask(initialQueryResponse);

        // 检查任务是否完成
        const actualStatus = initialQueryResponse.result?.status || initialQueryResponse.status;
        const isCompleted = actualStatus === 'completed' || actualStatus === 'failed' || actualStatus === 'error';

        // 只有任务完成或失败时才更新数据库状态
        if (isCompleted && currentDbTaskId) {
          await action(updateAiVideoGenerationTask, {
            taskId: currentDbTaskId,
            data: {
              status: actualStatus,
              progress: initialQueryResponse.progress || (actualStatus === 'completed' ? 100 : 0),
              status_desc: initialQueryResponse.message || undefined,
              video_url: initialQueryResponse.result?.video_url || initialQueryResponse.result?.videoUrl,
              thumbnail_url:
                initialQueryResponse.result?.thumbnail_url ||
                initialQueryResponse.result?.image_url ||
                initialQueryResponse.result?.imageUrl,
              error_message:
                actualStatus === 'failed' || actualStatus === 'error'
                  ? initialQueryResponse.result?.error || initialQueryResponse.message
                  : undefined,
              completed_at: new Date(),
              properties: initialQueryResponse.result
                ? JSON.parse(JSON.stringify(initialQueryResponse.result))
                : undefined,
            },
          });
        }

        if (isCompleted) {
          // 任务已完成，停止生成状态
          setIsGenerating(false);

          if (actualStatus === 'failed' || actualStatus === 'error') {
            // 任务失败，退还点数
            try {
              await action(updateQuotas, {
                quota: 5,
                changeType: WalletChangeType.REFUND,
                changeReason: 'AI视频生成任务失败退费',
              });
              // 退费成功后刷新钱包余额显示
              await refreshWallet();
            } catch (refundError) {
              console.error('退费失败:', refundError);
            }
            setError(initialQueryResponse.result?.error || initialQueryResponse.message || '视频生成失败');
          }
        } else {
          // 任务未完成，开始10秒间隔的轮询
          pollingRef.current = setInterval(pollTask, 10000);
        }
      } catch (err) {
        console.error('创建任务失败:', err);
        const errorMessage = err instanceof Error ? err.message : '创建任务失败';
        setError(errorMessage);

        // 创建任务失败，退还点数
        try {
          await action(updateQuotas, {
            quota: 5,
            changeType: WalletChangeType.REFUND,
            changeReason: 'AI视频生成任务创建失败退费',
          });
          // 退费成功后刷新钱包余额显示
          await refreshWallet();
        } catch (refundError) {
          console.error('退费失败:', refundError);
        }

        // 如果数据库任务已创建，更新为失败状态
        if (currentDbTaskId) {
          await action(updateAiVideoGenerationTask, {
            taskId: currentDbTaskId,
            data: {
              status: 'failed',
              error_message: errorMessage,
              completed_at: new Date(),
            },
          });
        }
        setIsGenerating(false);
      }
    },
    [isGenerating],
  );

  const stopGeneration = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    setIsGenerating(false);
    setCurrentTask(null);
  }, []);

  const resetTask = useCallback(() => {
    setCurrentTask(null);
    setError(null);
  }, []);

  const queryTaskStatus = useCallback(async (taskId: string) => {
    try {
      return await queryVideoGenerationTaskStatus(taskId);
    } catch (error) {
      console.error('查询任务状态失败:', error);
      throw error;
    }
  }, []);

  return {
    isGenerating,
    currentTask,
    error,
    generateVideo,
    stopGeneration,
    resetTask,
    queryTaskStatus,
  };
};

// 历史记录轮询管理Hook
interface HistoryTask {
  id: string;
  external_task_id?: string | null;
  status: string;
  [key: string]: any;
}

interface PollingTaskInfo {
  dbTaskId: string;
  externalTaskId: string;
  pollingRef: NodeJS.Timeout;
  retryCount: number;
}

export const useHistoryTaskPolling = () => {
  const pollingTasks = useRef<Map<string, PollingTaskInfo>>(new Map());
  const [isPolling, setIsPolling] = useState(false);
  const [completedTaskCount, setCompletedTaskCount] = useState(0);

  // 轮询单个任务的函数
  const pollSingleTask = useCallback(async (dbTaskId: string, externalTaskId: string): Promise<boolean> => {
    try {
      const queryResponse = await queryTask(externalTaskId);

      // 检查任务是否完成
      const actualStatus = queryResponse.result?.status || queryResponse.status;
      const isCompleted = actualStatus === 'completed' || actualStatus === 'failed' || actualStatus === 'error';

      if (isCompleted) {
        // 更新数据库状态
        await action(updateAiVideoGenerationTask, {
          taskId: dbTaskId,
          data: {
            status: actualStatus,
            progress: queryResponse.progress || (actualStatus === 'completed' ? 100 : 0),
            status_desc: queryResponse.message || undefined,
            video_url: queryResponse.result?.video_url || queryResponse.result?.videoUrl,
            thumbnail_url:
              queryResponse.result?.thumbnail_url || queryResponse.result?.image_url || queryResponse.result?.imageUrl,
            error_message:
              actualStatus === 'failed' || actualStatus === 'error'
                ? queryResponse.result?.error || queryResponse.message
                : undefined,
            completed_at: new Date(),
            properties: queryResponse.result ? JSON.parse(JSON.stringify(queryResponse.result)) : undefined,
          },
        });

        console.log(`任务 ${dbTaskId} (${externalTaskId}) 已完成，状态: ${actualStatus}`);
        setCompletedTaskCount((prev) => prev + 1);
        return true; // 任务完成
      }

      // 任务还在进行中，可以选择更新进度
      if (queryResponse.progress !== undefined) {
        await action(updateAiVideoGenerationTask, {
          taskId: dbTaskId,
          data: {
            progress: queryResponse.progress,
            status_desc: queryResponse.message || '任务处理中...',
          },
        });
      }

      return false; // 任务未完成
    } catch (error) {
      console.error(`轮询任务 ${dbTaskId} (${externalTaskId}) 失败:`, error);
      return false; // 出错时不停止轮询，稍后重试
    }
  }, []);

  // 开始轮询指定的运行中任务
  const startPollingTasks = useCallback(
    (runningTasks: HistoryTask[]) => {
      // 先停止所有正在进行的轮询
      stopAllPolling();
      setCompletedTaskCount(0);

      const tasksToPolling = runningTasks.filter(
        (task) => task.status === 'running' && task.external_task_id && task.external_task_id.trim() !== '',
      );

      if (tasksToPolling.length === 0) {
        setIsPolling(false);
        return;
      }

      console.log(`开始轮询 ${tasksToPolling.length} 个运行中的任务`);
      setIsPolling(true);

      // 为每个任务创建轮询
      tasksToPolling.forEach((task, index) => {
        const dbTaskId = task.id;
        const externalTaskId = task.external_task_id!;

        // 使用延迟来分散轮询请求，避免同时发起过多请求
        const delay = index * 2000; // 每个任务延迟2秒开始

        setTimeout(() => {
          const pollingRef = setInterval(async () => {
            const taskInfo = pollingTasks.current.get(dbTaskId);
            if (!taskInfo) return;

            try {
              const isCompleted = await pollSingleTask(dbTaskId, externalTaskId);

              if (isCompleted) {
                // 任务完成，清理轮询
                clearInterval(taskInfo.pollingRef);
                pollingTasks.current.delete(dbTaskId);

                // 检查是否还有其他轮询任务
                if (pollingTasks.current.size === 0) {
                  setIsPolling(false);
                }
              } else {
                // 增加重试计数
                taskInfo.retryCount++;

                // 如果重试次数超过限制（例如30次，相当于轮询300秒），停止轮询
                if (taskInfo.retryCount > 30) {
                  console.warn(`任务 ${dbTaskId} 轮询次数超限，停止轮询`);
                  clearInterval(taskInfo.pollingRef);
                  pollingTasks.current.delete(dbTaskId);

                  // 可选：将任务标记为超时失败
                  await action(updateAiVideoGenerationTask, {
                    taskId: dbTaskId,
                    data: {
                      status: 'failed',
                      error_message: '任务轮询超时',
                      completed_at: new Date(),
                    },
                  });

                  if (pollingTasks.current.size === 0) {
                    setIsPolling(false);
                  }
                }
              }
            } catch (error) {
              console.error(`轮询任务 ${dbTaskId} 出错:`, error);
              taskInfo.retryCount++;

              if (taskInfo.retryCount > 10) {
                console.error(`任务 ${dbTaskId} 错误次数过多，停止轮询`);
                clearInterval(taskInfo.pollingRef);
                pollingTasks.current.delete(dbTaskId);

                if (pollingTasks.current.size === 0) {
                  setIsPolling(false);
                }
              }
            }
          }, 10000); // 每10秒轮询一次

          // 保存轮询信息
          pollingTasks.current.set(dbTaskId, {
            dbTaskId,
            externalTaskId,
            pollingRef,
            retryCount: 0,
          });
        }, delay);
      });
    },
    [pollSingleTask],
  );

  // 停止所有轮询
  const stopAllPolling = useCallback(() => {
    pollingTasks.current.forEach((taskInfo) => {
      clearInterval(taskInfo.pollingRef);
    });
    pollingTasks.current.clear();
    setIsPolling(false);
  }, []);

  // 停止特定任务的轮询
  const stopTaskPolling = useCallback((dbTaskId: string) => {
    const taskInfo = pollingTasks.current.get(dbTaskId);
    if (taskInfo) {
      clearInterval(taskInfo.pollingRef);
      pollingTasks.current.delete(dbTaskId);

      if (pollingTasks.current.size === 0) {
        setIsPolling(false);
      }
    }
  }, []);

  // 获取当前轮询中的任务数量
  const getPollingTaskCount = useCallback(() => {
    return pollingTasks.current.size;
  }, []);

  // 清理函数 - 在组件卸载时调用
  const cleanup = useCallback(() => {
    stopAllPolling();
  }, [stopAllPolling]);

  return {
    isPolling,
    startPollingTasks,
    stopAllPolling,
    stopTaskPolling,
    getPollingTaskCount,
    completedTaskCount,
    cleanup,
  };
};
