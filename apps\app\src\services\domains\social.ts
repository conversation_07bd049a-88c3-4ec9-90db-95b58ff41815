import { ActionContext } from '@roasmax/serve';
import dayjs from 'dayjs';
import { fetchShareLink } from './cos';

export const generateCloudTerminalShareLink = async (ctx: ActionContext<{ accountId: string }>) => {
  return await ctx.trx(async (ctx) => {
    // 获取社交账号及需要分享的终端
    const account = await ctx.db.social_accounts.findUnique({
      where: { id: ctx.data.accountId },
    });
    if (!account) {
      throw new Error('社交账号不存在');
    }
    if (!account.cloud_terminal_id) {
      throw new Error('社交帐号未绑定终端');
    }
    const terminal = await ctx.db.cloud_terminals.findUnique({
      where: { id: account.cloud_terminal_id },
    });
    if (!terminal) {
      throw new Error('终端不存在');
    }
    if (!terminal.cloud_host_cos_path) {
      throw new Error('终端未绑定COS');
    }

    // 如果在有效期内还剩1小时，则直接返回
    if (account.share_expired_at && dayjs(account.share_expired_at).isAfter(dayjs().add(1, 'hour'))) {
      return { link: account.share_link, extract: account.share_extract };
    }

    const shareInfo = await ctx.execute(fetchShareLink, {
      resource: `自动分发/分发素材/${terminal.cloud_host_cos_path}`,
      allow: 'readonly',
      expire: dayjs().add(1.5, 'hour').toDate(),
    });

    // // 获取临时密钥
    // const resource = `qcs::cos:ap-shanghai:uid/${process.env.COS_APPID}:${process.env.COS_BUCKET}/roasmax/${ctx.tenant.id}/自动分发/分发素材/${terminal.cloud_host_cos_path}*`;
    // ctx.logger.log(`获取临时密钥，资源：${resource}`);
    // const federationToken = await ctx.sts.GetFederationToken({
    //   Name: '分享链接',
    //   DurationSeconds: 3600,
    //   Policy: JSON.stringify({
    //     version: '2.0',
    //     statement: [
    //       {
    //         action: ['cos:GetBucket', 'cos:GetObject', 'cos:HeadObject'],
    //         effect: 'allow',
    //         resource,
    //       },
    //     ],
    //   }),
    // });
    // if (!federationToken || !federationToken.Credentials) {
    //   throw new Error('获取临时密钥失败');
    // }

    // const encodeToBase64 = (arr: any[]) => {
    //   const rawStr = JSON.stringify(arr);
    //   const wordArray = CryptoJS.enc.Utf8.parse(rawStr);
    //   const base64 = CryptoJS.enc.Base64.stringify(wordArray);
    //   return base64;
    // };

    // const shareInfo = {
    //   Bucket: process.env.COS_BUCKET,
    //   Region: 'ap-shanghai',
    //   Prefix: `roasmax/${ctx.tenant.id}/自动分发/分发素材/${terminal.cloud_host_cos_path}`,
    //   action: encodeToBase64(['GetBucket', 'GetObject', 'HeadObject']),
    //   expire: Math.floor(Date.now() / 1000) + 60 * 60 * 1.5, // 1.5小时后过期
    //   files: encodeToBase64([{ name: encodeURIComponent(terminal.cloud_host_cos_path) }]),
    //   isMaz: '0',
    //   sid: federationToken.Credentials.TmpSecretId,
    //   token: federationToken.Credentials.Token,
    // };

    // // 随机生成一个6位数
    // const extract = Math.random().toString(36).substring(2, 8);

    // const response = await ctx.request.post<{ code: number; data: { shareId: string; accessCode: string } }>(
    //   'https://cosbrowser.cloud.tencent.com/api/share/save',
    //   { shareInfo: queryString.stringify(shareInfo), extract },
    //   { headers: { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' } },
    // );

    // const AESEncrypt = (word: string, key: string) => {
    //   const srcs = CryptoJS.enc.Utf8.parse(word);
    //   const accessCode = CryptoJS.enc.Utf8.parse(key); // 密钥
    //   const encrypted = CryptoJS.AES.encrypt(srcs, accessCode, {
    //     iv: CryptoJS.enc.Utf8.parse('cosbrowser-share'),
    //     mode: CryptoJS.mode.CBC,
    //     padding: CryptoJS.pad.Pkcs7,
    //   });
    //   const hexStr = encrypted.ciphertext.toString().toUpperCase();
    //   const oldHexStr = CryptoJS.enc.Hex.parse(hexStr);
    //   // 将密文转为Base64的字符串
    //   const base64Str = CryptoJS.enc.Base64.stringify(oldHexStr);
    //   return base64Str;
    // };

    // const shareId = response.data.data.shareId;
    // const shareToken = AESEncrypt(federationToken.Credentials.TmpSecretKey, response.data.data.accessCode);
    // const query = queryString.stringify({ id: shareId, token: shareToken });

    // const link = `https://cosbrowser.cloud.tencent.com/share/?${query}`;

    await ctx.db.social_accounts.update({
      where: { id: ctx.data.accountId },
      data: {
        share_link: shareInfo.link,
        share_extract: shareInfo.extract,
        share_expired_at: dayjs().add(1.5, 'hour').toDate(),
      },
    });

    return { link: shareInfo.link, extract: shareInfo.extract };
  });
};
