import { useState, useEffect } from 'react';

export const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    // 检查当前窗口宽度
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768); // 768px 是常用的移动设备断点
    };

    // 初始检查
    checkMobile();

    // 添加窗口大小改变的监听器
    window.addEventListener('resize', checkMobile);

    // 清理监听器
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return isMobile;
};
