'use client';

import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import { Button } from '@/components/ui/Button';
import { Checkbox } from '@/components/ui/Checkbox';
import { Input } from '@/components/ui/Input';
import Pagination from '@/components/ui/PaginationAcc';
import { useAdvertisers } from '@/hooks/useAdvertisers';
import { useCheckboxLogic } from '@/hooks/useCheckboxLogic';
import { usePagination } from '@/hooks/usePagination';
import { useTableLogic } from '@/hooks/useTableLogic';
import { useAdActions, useCurrentAdvertiser } from '@/store/ads/adStore';
import { AdvertiserItem } from '@/types/ads';
import { getAdvertiserStatusColor, getAdvertiserStatusText } from '@/types/enum';
import debounce from 'lodash/debounce';
import { Plus, RefreshCw, XIcon } from 'lucide-react';
import { useCallback, useState } from 'react';

export default function AccountContent() {
  const currentAdvertiser = useCurrentAdvertiser();
  const { setCurrentView, setCurrentAdvertiser } = useAdActions();

  const { advertisers, isLoading, refresh, mutate } = useAdvertisers();
  const { currentPage, pageSize, handlePaginationChange } = usePagination({
    totalItems: advertisers?.total || 0,
    initialPageSize: 10,
    initialPage: 1,
    onPaginationChange: mutate,
  });
  const [searchAccount, setSearchAccount] = useState<string>('');
  const { selectedRows, sortState, sortedData, handleSelectAll, handleSelect, handleSort } = useTableLogic(
    advertisers?.list || [],
    'advertiserId',
    currentAdvertiser?.map((item) => item.advertiserId) || [],
  );

  const { handleCheckboxChange, handleAllCheckboxChange } = useCheckboxLogic({
    selectedRows,
    sortedData,
    currentItems: currentAdvertiser || [],
    setCurrentItems: setCurrentAdvertiser,
    itemKey: 'advertiserId',
    handleSelect,
    handleSelectAll,
  });

  const isAllSelected = sortedData.length > 0 && sortedData.every((item) => selectedRows.includes(item.advertiserId));

  const columns = [
    {
      key: 'checkbox',
      title: (
        <Checkbox
          className="h-[14px] w-[14px] border-[#9FA4B2]"
          checked={isAllSelected}
          onCheckedChange={(checked) => handleAllCheckboxChange(!!checked)}
        />
      ),
      width: 50,
    },
    // { key: 'switch', title: '开关', width: 80 },
    { key: 'accountName', title: '广告账户', width: 200 },
    // { key: 'actions', title: '操作', width: 150 },
    { key: 'user', title: '所属用户', width: 150 },
    { key: 'pubStatus', title: '账户状态', width: 120 },
    { key: 'balance', title: '账户余额', width: 120, sortable: true },
    // { key: 'spend', title: '总花费', width: 120, sortable: true },
  ];
  const handleAdvertiserClick = (advertiser: AdvertiserItem) => {
    setCurrentView('campaigns');
    setCurrentAdvertiser([advertiser]); // 将对象包装在数组中
  };
  const renderCell = (key: string, record: any) => {
    switch (key) {
      case 'checkbox':
        return (
          <Checkbox
            className="h-[14px] w-[14px] border-[#9FA4B2]"
            checked={selectedRows.includes(record.advertiserId)}
            onCheckedChange={(checked) => handleCheckboxChange(record, !!checked)}
          />
        );
      case 'accountName':
        return (
          <div className="cursor-pointer" onClick={() => handleAdvertiserClick(record)}>
            {record.advertiserName}
            <div className="mt-[2px] text-xs text-gray-500">{record.advertiserId}</div>
          </div>
        );
      case 'user':
        return record.displayName;
      case 'pubStatus':
        const status = record?.jsonDate?.status;
        const color = getAdvertiserStatusColor(status);
        return (
          <div className="flex items-center">
            <span className={`mr-2 h-2 w-2 rounded-full ${color}`} />
            <div className="text-sm text-gray-500">{getAdvertiserStatusText(record?.jsonDate?.status)}</div>
          </div>
        );
      case 'balance':
        return (
          <div className="flex items-center justify-center">
            <div className="text-sm text-gray-500">{record?.jsonDate?.balance}/</div>
            <div className="text-sm text-gray-500">{record?.jsonDate?.currency}</div>
          </div>
        );
      // case 'spend':
      //   return record.spend;
      default:
        return '';
    }
  };
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      //名称搜索
    }, 1000),
    [],
  );
  const onSearch = (value: string) => {
    setSearchAccount(value);
    debouncedSearch(value);
  };

  const handleAuth = () => {
    const width = 800;
    const height = 600;
    const left = Math.max(0, (window.innerWidth - width) / 2);
    const top = Math.max(0, (window.innerHeight - height) / 2);

    window.open(
      `https://business-api.tiktok.com/portal/auth?app_id=7436226221098172417&redirect_uri=https://video.bowongai.com/auth`,
      'TikTok Auth',
      `width=${width},height=${height},left=${left},top=${top},scrollbars=yes`,
    );
  };

  return (
    <div className="mt-6 h-full">
      {/* <div className="flex items-center justify-between">
        <div className="flex h-8 w-[240px] items-center rounded border border-[#1C2A3F] px-3 text-xs text-white">
          <div className="w-24 border-r border-gray-700 text-xs text-white">广告账户</div>
          <Input
            value={searchAccount}
            placeholder="请输入名称/ID"
            className="h-full border-0 bg-transparent text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
            onChange={(e) => onSearch?.(e.target.value)}
          />
          {searchAccount && (
            <XIcon
              className="mx-2 h-8 cursor-pointer text-[#9FA4B2]"
              onClick={(event) => {
                event.stopPropagation();
                setSearchAccount('');
                debouncedSearch('');
              }}
            />
          )}
        </div>
      </div> */}
      <div className="mt-6 flex items-center">
        <Button className="h-8 rounded px-4 text-xs font-medium text-[#050A1C]" onClick={handleAuth}>
          <Plus className="mr-1.5 h-4 w-4 flex-shrink-0" />
          授权账户
        </Button>
        <Button
          variant="outline"
          className="ml-4 h-8 rounded border-[#363D54] bg-transparent text-xs"
          onClick={() => refresh()}
        >
          <RefreshCw className="mr-1 h-3 w-3" />
          获取实时数据
        </Button>
      </div>
      <CommonTable
        columns={columns}
        dataSource={sortedData}
        loading={isLoading}
        rowKey="advertiserId"
        renderCell={renderCell}
        selectedRows={selectedRows}
        sortState={sortState}
        onSelectAll={handleSelectAll}
        onSelect={handleSelect}
        onSort={handleSort}
      />
      <div className="flex justify-end">
        <Pagination
          totalItems={advertisers?.total || 0}
          currentPage={currentPage}
          pageSize={pageSize}
          handlePaginationChange={handlePaginationChange}
        />
      </div>
    </div>
  );
}
