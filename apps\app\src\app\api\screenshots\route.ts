import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json();
    const baseUrl = process.env.KOLA_API_URL || 'http://124.223.181.78:8000';

    const response = await fetch(`${baseUrl}/api/sync/kola/screenshots`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify({
        url: body.url,
      }),
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    // 错误处理
    return NextResponse.json({ error: '请求失败' }, { status: 500 });
  }
};
