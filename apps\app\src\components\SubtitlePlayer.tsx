import { cn } from '@/utils/cn';
import React, { useEffect, useState, useRef } from 'react';

interface Cue {
  id: string;
  startTime: number;
  endTime: number;
  text: string;
}

interface SubtitleSource {
  language: string;
  content: string;
}

interface SubtitlePlayerProps {
  subtitles: SubtitleSource[];
  currentTime: number;
  className?: string;
  onSeek?: (time: number) => void;
}

const parseVTT = (vttContent: string): Cue[] => {
  const lines = vttContent.split('\n');
  const cues: Cue[] = [];
  let currentCue: Partial<Cue> = {};

  const parseTimestamp = (timestamp: string): number => {
    // 处理 HH:MM:SS.mmm 或 MM:SS.mmm 或 MM:SS 格式
    const parts = timestamp.trim().split(':');
    let hours = 0;
    let minutes = 0;
    let seconds = 0;

    if (parts.length === 3) {
      // HH:MM:SS.mmm
      hours = parseFloat(parts[0] || '0');
      minutes = parseFloat(parts[1] || '0');
      seconds = parseFloat(parts[2] || '0');
    } else if (parts.length === 2) {
      // MM:SS.mmm
      minutes = parseFloat(parts[0] || '0');
      seconds = parseFloat(parts[1] || '0');
    }

    return hours * 3600 + minutes * 60 + seconds;
  };

  let isReadingCueText = false;
  let currentText = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]!.trim();

    if (line === '') {
      if (isReadingCueText && currentCue.startTime !== undefined) {
        currentCue.text = currentText.trim();
        currentCue.id = `cue-${cues.length}`;
        cues.push(currentCue as Cue);
        currentCue = {};
        currentText = '';
      }
      isReadingCueText = false;
      continue;
    }

    if (line.includes('-->')) {
      isReadingCueText = true;
      currentText = '';
      const [startStr, endStr] = line.split('-->').map((str) => str.trim());
      if (startStr && endStr) {
        currentCue.startTime = parseTimestamp(startStr);
        currentCue.endTime = parseTimestamp(endStr);
      }
    } else if (isReadingCueText && !line.includes('WEBVTT')) {
      currentText += (currentText ? '\n' : '') + line;
    }
  }

  // 处理最后一个字幕
  if (isReadingCueText && currentCue.startTime !== undefined) {
    currentCue.text = currentText.trim();
    currentCue.id = `cue-${cues.length}`;
    cues.push(currentCue as Cue);
  }

  return cues;
};

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 合并多个语言的字幕，按时间戳对齐
const mergeCues = (
  subtitlesList: { language: string; cues: Cue[] }[],
): {
  startTime: number;
  endTime: number;
  texts: { [language: string]: string };
}[] => {
  // 收集所有唯一的时间戳
  const timeSet = new Set<number>();
  subtitlesList.forEach(({ cues }) => {
    cues.forEach((cue) => {
      timeSet.add(cue.startTime);
      timeSet.add(cue.endTime);
    });
  });

  const times = Array.from(timeSet).sort((a, b) => a - b);
  const mergedCues: { startTime: number; endTime: number; texts: { [language: string]: string } }[] = [];

  // 对每个时间区间，找到所有语言中对应的字幕
  for (let i = 0; i < times.length - 1; i++) {
    const startTime = times[i]!;
    const endTime = times[i + 1]!;
    const texts: { [language: string]: string } = {};

    subtitlesList.forEach(({ language, cues }) => {
      const activeCue = cues.find((cue) => cue.startTime <= startTime && cue.endTime >= endTime);
      if (activeCue) {
        texts[language] = activeCue.text;
      }
    });

    if (Object.keys(texts).length > 0) {
      mergedCues.push({ startTime, endTime, texts });
    }
  }

  return mergedCues;
};

export const SubtitlePlayer: React.FC<SubtitlePlayerProps> = ({ subtitles, currentTime, className, onSeek }) => {
  const [mergedCues, setMergedCues] = useState<
    { startTime: number; endTime: number; texts: { [language: string]: string } }[]
  >([]);
  const [activeCueId, setActiveCueId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const parsedSubtitles = subtitles.map((sub) => ({
      language: sub.language,
      cues: parseVTT(sub.content),
    }));
    const merged = mergeCues(parsedSubtitles);
    setMergedCues(merged);
  }, [subtitles]);

  useEffect(() => {
    const activeCueIndex = mergedCues.findIndex((cue) => currentTime >= cue.startTime && currentTime <= cue.endTime);

    if (activeCueIndex !== -1) {
      const cueId = `cue-${activeCueIndex}`;
      setActiveCueId(cueId);

      // 滚动到当前字幕
      const activeElement = document.getElementById(cueId);
      if (activeElement && containerRef.current) {
        containerRef.current.scrollTo({
          top: activeElement.offsetTop - containerRef.current.offsetHeight / 2,
          behavior: 'smooth',
        });
      }
    } else {
      setActiveCueId(null);
    }
  }, [currentTime, mergedCues]);

  const handleCueClick = (startTime: number) => {
    onSeek?.(startTime);
  };

  return (
    <div ref={containerRef} className={cn('space-y-1 overflow-y-auto px-4 py-2 text-sm', className)}>
      {mergedCues.map((cue, index) => {
        const entries = Object.entries(cue.texts);
        return (
          <div
            key={`cue-${index}`}
            id={`cue-${index}`}
            onClick={() => handleCueClick(cue.startTime)}
            className={cn(
              'group relative cursor-pointer rounded border border-transparent px-2.5 py-1.5 transition-all duration-300',
              `cue-${index}` === activeCueId
                ? 'scale-[1.01] border-[#CCDDFF15] bg-[#CCDDFF0A] text-white shadow-[0_0_8px_rgba(204,221,255,0.05)]'
                : 'text-[#9FA4B2] hover:bg-[#CCDDFF05]',
            )}
          >
            <div className="grid grid-cols-[1fr_auto] items-start gap-2">
              <div className="flex flex-col gap-0.5">
                {entries.map(([language, text]) => (
                  <div key={language} className="flex-1 leading-[1.4]">
                    {text}
                  </div>
                ))}
              </div>
              <span className="text-xs text-[#9FA4B2]/60 opacity-0 transition-opacity group-hover:opacity-100">
                {formatTime(cue.startTime)}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};
