import { Skeleton } from '@/components/ui';

export default function Loading() {
  return (
    <div className="flex h-full w-full flex-col">
      {/* Tab 骨架屏 */}
      <div className="px-4 py-3">
        <div className="flex gap-4">
          <Skeleton className="h-8 w-24 bg-slate-700" />
          <Skeleton className="h-8 w-24 bg-slate-700" />
        </div>
      </div>
      {/* Management Bar 骨架屏 */}
      <div className="px-4 py-3">
        <div className="flex w-[200px] items-center gap-3">
          <Skeleton className="h-9 w-24 bg-slate-700" />
          <Skeleton className="h-9 w-24 bg-slate-700" />
          <Skeleton className="h-9 w-32 bg-slate-700" />
          <Skeleton className="h-9 w-32 bg-slate-700" />
        </div>
      </div>
      {/* 内容区域骨架屏 */}
      <div className="flex-1 p-4">
        <div className="grid grid-cols-8 gap-4">
          {[...Array(32)].map((_, index) => (
            <Skeleton key={index} className="aspect-square bg-slate-700" />
          ))}
        </div>
      </div>
    </div>
  );
}
