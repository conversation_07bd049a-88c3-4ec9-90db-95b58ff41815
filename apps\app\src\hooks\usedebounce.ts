import * as React from 'react';
import { type InteractionItem, type TreeNode, type RegionNode } from '@/types/enum';

interface UseStatusActionProps {
  selectedRows: any[];
  setAlertType: (type: 'enable' | 'disable' | 'delete') => void;
  setAlertTitle: (title: string) => void;
  setAlertDescription: (desc: string) => void;
  setOpenAlert: (open: boolean) => void;
}
export function useDebounce<T>(value: T, delay?: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay || 500);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}
export const convertToLocalTime = (utcTime: string, timezone: string) => {
  if (!utcTime || !timezone) return '';

  try {
    // 创建 UTC 日期对象
    const utcDate = new Date(`${utcTime}Z`); // 添加 'Z' 表示 UTC 时间
    // 使用 Intl.DateTimeFormat 格式化为指定时区的当地时间
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false, // 24小时制
    };

    const formatter = new Intl.DateTimeFormat('en-US', {
      ...options,
      timeZone: timezone,
    });
    // 格式化日期
    const formattedDate = formatter.format(utcDate);
    const dateParts = formattedDate.split(', ');
    const date = dateParts[0]?.split('/');
    const newFormattedDate = `${date?.[2]}-${date?.[0]}-${date?.[1]} ${dateParts[1]}`;
    return newFormattedDate; // 去掉逗号
  } catch (error) {
    console.error('Time conversion error:', error);
    return '';
  }
};

export const transformToTreeData = (list: InteractionItem[]) => {
  if (!list || !list.length) return [];

  // 按level分组
  const level1 = list.filter((item) => item.level === 1);

  const buildNode = (item: InteractionItem): TreeNode => {
    const children = list.filter((child) => item.childrenIds.includes(child.id)).map((child) => buildNode(child));

    return {
      label: item.name,
      value: item.id,
      key: item.id,
      children: children.length > 0 ? children : undefined,
    };
  };

  return level1.map((item) => buildNode(item));
};

export const buildRegionTree = (regions: RegionNode[]): RegionNode[] => {
  // 创建一个map用于快速查找节点
  const regionMap = new Map<string, RegionNode>();
  for (const region of regions) {
    regionMap.set(region.locationId, { ...region, children: [] });
  }

  // 构建树结构
  const tree: RegionNode[] = [];

  for (const region of regions) {
    const node = regionMap.get(region.locationId);
    if (node) {
      if (region.parentId === '0') {
        // 根节点
        tree.push(node);
      } else {
        // 子节点,添加到父节点的children中
        const parentNode = regionMap.get(region.parentId);
        if (parentNode) {
          if (!parentNode.children) {
            parentNode.children = [];
          }
          parentNode.children.push(node);
        }
      }
    }
  }

  return tree;
};
export const validateScheduleTime = (startTime: string, endTime?: string) => {
  const now = new Date();
  const start = new Date(startTime);
  const twelveHoursAgo = new Date(now.getTime() - 12 * 60 * 60 * 1000);
  const maxStartDate = new Date('2028-01-01T00:00:00Z');
  const maxEndDate = new Date('2038-01-01T00:00:00Z');

  // 验证开始时间
  if (start < twelveHoursAgo) {
    return '开始时间不能早于当前时间12小时以前';
  }
  if (start > maxStartDate) {
    return '开始时间不能晚于2028-01-01 00:00:00';
  }

  // 验证结束时间
  if (endTime) {
    const end = new Date(endTime);
    if (end > maxEndDate) {
      return '结束时间不能晚于2038-01-01 00:00:00';
    }
    if (end <= start) {
      return '结束时间必须晚于开始时间';
    }
  }

  return null;
};
export const formatToUTCString = (localTime: string) => {
  if (!localTime) return undefined;
  const date = new Date(localTime);
  return date.toISOString().slice(0, 19).replace('T', ' ');
};
export const convertToLocalTime1 = (utcTime: string, timezone: string) => {
  if (!utcTime) return undefined;

  // 创建一个UTC日期对象
  const date = new Date(utcTime);

  // 使用指定的时区格式化日期
  return new Date(date.toLocaleString('en-US', { timeZone: timezone })).toISOString();
};
export const useStatusAction = ({
  selectedRows,
  setAlertType,
  setAlertTitle,
  setAlertDescription,
  setOpenAlert,
}: UseStatusActionProps) => {
  const handleStatusAction = (type: 'enable' | 'disable' | 'delete') => {
    setAlertType(type);
    switch (type) {
      case 'enable':
        setAlertTitle('请确认是否启用？');
        setAlertDescription('');
        break;
      case 'disable':
        setAlertTitle('请确认是否暂停？');
        setAlertDescription('');
        break;
      case 'delete':
        const runningTasks = selectedRows.filter(
          (row) => row.jsonDate?.secondaryStatus === 'ADGROUP_STATUS_DELIVERY_OK',
        ).length;
        setAlertTitle(`确定删除这${selectedRows.length}个任务吗？`);
        setAlertDescription(runningTasks > 0 ? '当前选中的任务中包含正在投放的任务，此操作不可逆，请谨慎操作。' : '');
        break;
    }
    setOpenAlert(true);
  };

  return handleStatusAction;
};
