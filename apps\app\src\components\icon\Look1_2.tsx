export const Look1_2 = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <path
        d="M15.3257 13.127C15.8647 13.127 16.373 13.2427 16.8535 13.4727C17.2139 13.6426 17.5391 13.874 17.832 14.1655C18.1235 14.457 18.355 14.7837 18.5249 15.1426C18.2935 15.374 18.0151 15.4883 17.6899 15.4883C17.5757 15.4883 17.4614 15.4707 17.3442 15.437C17.1479 15.1177 16.8784 14.8496 16.5591 14.6519C16.1812 14.4219 15.7695 14.3062 15.3271 14.3062C15.0195 14.3062 14.7207 14.3648 14.4321 14.482C14.1436 14.5991 13.8843 14.772 13.6572 14.999L11.8848 16.7715C11.6577 16.9986 11.4849 17.2578 11.3677 17.5464C11.252 17.8306 11.1919 18.1338 11.1919 18.4414C11.1919 18.749 11.2505 19.0479 11.3677 19.3364C11.4849 19.6265 11.6562 19.8843 11.8848 20.1113C12.1133 20.3399 12.3711 20.5112 12.6597 20.6284C12.9438 20.7442 13.2485 20.8042 13.5562 20.8042C13.8638 20.8042 14.1626 20.7456 14.4512 20.6284C14.7397 20.5112 14.999 20.3384 15.2261 20.1113L16.4668 18.8706C16.8608 18.9776 17.2681 19.0318 17.6899 19.0318C17.7573 19.0318 17.8525 19.0288 17.9756 19.023C17.9287 19.0742 17.8818 19.1255 17.8335 19.1753L16.061 20.9478C15.7197 21.2891 15.333 21.5469 14.8994 21.7227C14.4731 21.897 14.0161 21.9863 13.5547 21.9863C13.0933 21.9863 12.6436 21.8985 12.21 21.7212C11.7749 21.544 11.3882 21.2862 11.0483 20.9478C10.707 20.6065 10.4492 20.2197 10.2734 19.7876C10.0991 19.3613 10.0098 18.9043 10.0098 18.4429C10.0098 17.9815 10.0991 17.5244 10.2734 17.0982C10.4492 16.666 10.707 16.2793 11.0483 15.938L12.8208 14.1655C12.8545 14.1319 12.9058 14.085 12.9731 14.022C13.3027 13.729 13.6704 13.5078 14.0762 13.3555C14.4819 13.2046 14.8979 13.1284 15.3257 13.127ZM19.4609 8.99318C19.9224 8.99318 20.3794 9.08254 20.8057 9.25685C21.2378 9.43117 21.6245 9.69045 21.9673 10.0318C22.3086 10.3731 22.5679 10.7598 22.7422 11.1919C22.9165 11.6196 23.0059 12.0752 23.0044 12.5366C23.0044 12.9981 22.915 13.4463 22.7393 13.8814C22.562 14.3164 22.3042 14.7031 21.9658 15.0415L20.1934 16.814C20.1597 16.8491 20.1084 16.896 20.041 16.9575C19.7129 17.2505 19.3452 17.4717 18.938 17.624C18.5381 17.7749 18.1147 17.8526 17.687 17.8526C17.1494 17.8526 16.6396 17.7368 16.1592 17.5069C15.7988 17.3369 15.4736 17.1055 15.1807 16.814C14.8892 16.5225 14.6577 16.1958 14.4878 15.8369C14.7178 15.6055 14.9961 15.4912 15.3228 15.4912C15.437 15.4912 15.5527 15.5088 15.6685 15.5425C15.8647 15.8618 16.1328 16.1299 16.4521 16.3277C16.8315 16.5576 17.2417 16.6734 17.6841 16.6734C17.9917 16.6734 18.2905 16.6148 18.5791 16.4976C18.8691 16.3804 19.127 16.2075 19.354 15.9805L21.1265 14.208C21.355 13.981 21.5264 13.7217 21.6436 13.4331C21.7593 13.1489 21.8193 12.8457 21.8193 12.5381C21.8193 12.2305 21.7607 11.9317 21.6436 11.6431C21.5264 11.353 21.3535 11.0952 21.1265 10.8682C20.8979 10.6397 20.6401 10.4683 20.3516 10.3511C20.0674 10.2354 19.7627 10.1753 19.4565 10.1753C19.1489 10.1753 18.8501 10.2339 18.5615 10.3511C18.2715 10.4683 18.0137 10.6411 17.7866 10.8682L16.5444 12.1089C16.1504 12.002 15.7432 11.9478 15.3213 11.9478C15.2539 11.9478 15.1587 11.9507 15.0356 11.9566C15.0825 11.9053 15.1294 11.854 15.1792 11.8042L16.9517 10.0318C17.29 9.69338 17.6768 9.43556 18.1118 9.25832C18.5498 9.08107 18.998 8.99172 19.4609 8.99318Z"
        fill="#9FA4B2"
      />
    </svg>
  );
};
