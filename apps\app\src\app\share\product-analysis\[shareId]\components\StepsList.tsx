import { Magic, StepSuccessful } from '@/components/icon';
import { Step, SubStep } from '@/types/product-analysis';
import { cn } from '@/utils/cn';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';

interface StepsListPro {
  playbackSteps: Step[];
  currentMainStep: number;
  currentSubStep: number;
  flattenedSteps: any[];
  currentFlatIndex: number;
  clickedStep: { mainIndex: number; subIndex: number | null } | null;
  completedSteps: number;
  replayCompleted: boolean;
  showTitles: Record<string, boolean>;
  stepRefs: any; // 用于存储每个步骤的引用
  setClickedStep: (step: { mainIndex: number; subIndex: number | null }) => void;
  setCurrentFlatIndex: (index: number) => void;
  setReplayCompleted: (completed: boolean) => void;
  setShowAnalysisPanel: (show: boolean) => void;
  typewriterCompleted: boolean;
  setTypewriterCompleted: () => void;
  activeStep: string | null;
}

const Typewriter = ({
  text,
  typingSpeed = 20,
  onComplete,
  shouldForceComplete = false,
}: {
  text: string;
  typingSpeed?: number;
  onComplete: () => void;
  shouldForceComplete?: boolean;
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);
  const typingRef = useRef<NodeJS.Timeout | null>(null);
  const uniqueId = useRef(`typewriter-${Math.random().toString(36).substring(2, 11)}`);
  // 用于实时检测shouldForceComplete变化的ref
  const forceCompleteRef = useRef(shouldForceComplete);
  const typewriterRef = useRef<HTMLDivElement>(null);
  // 添加滚动到底部的函数
  const scrollToBottom = useCallback(() => {
    // 找到最近的滚动容器并滚动到底部
    if (typewriterRef.current) {
      const scrollContainer = typewriterRef.current.closest('.overflow-auto');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, []);
  // 使用MutationObserver监听内容变化
  useEffect(() => {
    if (!typewriterRef.current) return;

    // 创建一个MutationObserver实例
    const observer = new MutationObserver(() => {
      // 当内容变化时滚动到底部
      scrollToBottom();
    });

    // 配置观察选项
    const config = { childList: true, subtree: true, characterData: true };

    // 开始观察目标节点
    observer.observe(typewriterRef.current, config);

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, [scrollToBottom]);

  // 实时更新forceCompleteRef
  useEffect(() => {
    forceCompleteRef.current = shouldForceComplete;
    // 立即检查是否需要强制完成
    if (shouldForceComplete && !isCompleted) {
      handleForceComplete();
    }
  }, [shouldForceComplete]);

  // 强制完成的处理函数
  const handleForceComplete = useCallback(() => {
    console.log(`[Typewriter ${uniqueId.current}] 强制完成打字效果`);
    // 清理定时器
    if (typingRef.current) {
      clearTimeout(typingRef.current);
      typingRef.current = null;
    }
    // 立即显示完整文本

    setDisplayText(text);
    console.log(text, '打字完成');

    setIsCompleted(true);
  }, [text]);

  // 清理函数
  const cleanup = useCallback(() => {
    if (typingRef.current) {
      clearTimeout(typingRef.current);
      typingRef.current = null;
    }
  }, []);

  // 正常打字效果
  useEffect(() => {
    // 如果已经完成或应该强制完成，则不执行打字效果
    if (isCompleted || shouldForceComplete) {
      if (shouldForceComplete && !isCompleted) {
        handleForceComplete();
      }
      return cleanup;
    }

    console.log(`[Typewriter ${uniqueId.current}] 初始化，文本长度: ${text.length}`);
    let currentIndex = 0;
    setDisplayText('');
    setIsCompleted(false);

    const typeNextChar = () => {
      // 每次检查是否应该强制完成
      if (forceCompleteRef.current) {
        handleForceComplete();
        return;
      }

      if (currentIndex < text.length) {
        setDisplayText((prev) => {
          return prev + text[currentIndex];
        });
        currentIndex++;
        typingRef.current = setTimeout(typeNextChar, typingSpeed);
      } else {
        console.log(`[Typewriter ${uniqueId.current}] 文本打字完成`);
        setIsCompleted(true);
      }
    };

    // 开始打字
    typeNextChar();

    // 组件卸载或依赖变化时的清理
    return cleanup;
  }, [text, typingSpeed, isCompleted, shouldForceComplete, cleanup, handleForceComplete]);

  // 完成回调
  useEffect(() => {
    if (isCompleted) {
      console.log(`[Typewriter ${uniqueId.current}] 已完成，调用 onComplete 回调`);
      onComplete();
    }
  }, [isCompleted, onComplete]);

  return (
    <div ref={typewriterRef}>
      <ReactMarkdown className="markdownShouldShow ml-2 mt-2">{displayText.replace('undefined', '')}</ReactMarkdown>
    </div>
  );
};

export default function StepsList({
  playbackSteps,
  currentMainStep,
  currentSubStep,
  flattenedSteps,
  currentFlatIndex,
  clickedStep,
  completedSteps,
  replayCompleted,
  showTitles,
  stepRefs,
  setClickedStep,
  setCurrentFlatIndex,
  setReplayCompleted,
  setShowAnalysisPanel,
  typewriterCompleted,
  setTypewriterCompleted,
  activeStep,
}: StepsListPro) {
  // 使用ref来存储前一次的replayCompleted状态
  const prevReplayCompletedRef = useRef(replayCompleted);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showThinking, setShowThinking] = useState(true);

  // 添加一个useEffect来控制思考中状态的显示时间
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowThinking(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [currentMainStep]);

  // 添加一个useEffect来控制思考中状态的显示时间
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowThinking(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [currentMainStep]);

  // 检测replayCompleted从false变为true的情况
  useEffect(() => {
    // 如果是从false变为true，这意味着回放被强制完成了
    if (!prevReplayCompletedRef.current && replayCompleted) {
      console.log('[StepsList] 检测到replayCompleted状态从false变为true，强制完成所有效果');
      // 这里可以添加任何需要在强制完成时执行的逻辑
      setTypewriterCompleted(); // 立即完成所有打字机效果
    }
    // 更新ref值
    prevReplayCompletedRef.current = replayCompleted;
  }, [replayCompleted, setTypewriterCompleted]);

  const isStepClicked = (mainIndex: number, subIndex: number | null) => {
    return clickedStep?.mainIndex === mainIndex && clickedStep?.subIndex === subIndex;
  };
  const handleStepClick = (mainIndex: number, subIndex: number | null) => {
    console.log(`[StepsList] 点击步骤: 主步骤=${mainIndex}, 子步骤=${subIndex}`);
    setShowAnalysisPanel(false);
    // 设置点击的步骤
    setClickedStep({ mainIndex, subIndex });

    // 找到对应的flatIndex
    const targetFlatIndex = flattenedSteps.findIndex(
      (item) => item.mainIndex === mainIndex && item.subIndex === subIndex,
    );

    console.log(`[StepsList] 找到对应的平铺索引: ${targetFlatIndex}`);
    // 如果找到了对应的步骤，更新currentFlatIndex以显示对应内容
    if (targetFlatIndex >= 0) {
      setCurrentFlatIndex(targetFlatIndex);
      setReplayCompleted(true);
    }
  };

  const renderTypewriterContent = (step: Step | SubStep, mainIndex: number, subIndex: number | null) => {
    const stepKey = `${mainIndex}-${subIndex}`;
    const isActive = activeStep === stepKey;
    const content = step.contentLodding || '';

    if (!isActive || !content || replayCompleted) {
      const getTitle = () => {
        if (!content) return '';
        const lines = content.split('\n');
        const titleLine = lines.find((line) => line.trim().startsWith('##')) || lines[0] || '';
        return titleLine.replace(/^##\s*/, '').trim();
      };
      return (
        <ReactMarkdown className="markdownShouldShow ml-1 mt-2 inline-block rounded-2xl bg-[#EFEDFD1A] px-2">
          {`${getTitle()}...`}
        </ReactMarkdown>
      );
    }

    // 如果回放已完成且不是当前活动步骤，则直接显示完整内容而不使用打字机效果
    if (replayCompleted && activeStep !== stepKey) {
      return <ReactMarkdown className="markdownShouldShow ml-2 mt-2">{content}</ReactMarkdown>;
    }

    // 当回放完成或者点击查看结果时，强制完成所有打字效果
    return (
      <Typewriter
        text={content}
        typingSpeed={20}
        shouldForceComplete={replayCompleted}
        onComplete={() => {
          console.log(`[StepsList] 打字机 ${stepKey} 完成`);
          setTypewriterCompleted(); // 通知父组件打字机已完成
        }}
      />
    );
  };
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        top: containerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [playbackSteps, currentMainStep, currentSubStep, flattenedSteps, currentFlatIndex]);
  return (
    <div ref={containerRef} className={cn('mb-4 flex-1 overflow-auto', 'pl-16')}>
      <div className="mb-3 flex h-8 w-[207px] items-center rounded-[100px] border bg-[#E2E9F91A]">
        <div className="-ml-4 -mt-[2px]">
          <Magic className="h-[61px] w-[55px] animate-spin" />
        </div>
        <div className="font-medium text-white">ROASMAX 分析助手</div>
      </div>
      <div className="flex flex-col gap-4">
        {playbackSteps.map((step, mainIndex) => {
          const isMainActive =
            currentMainStep === mainIndex && currentSubStep === 0 && !flattenedSteps[currentFlatIndex]?.isSubStep;
          const isMainCompleted = clickedStep
            ? mainIndex < completedSteps || // 点击模式：当前步骤小于已完成步骤
              (mainIndex === completedSteps - 1 && !step.sub_steps?.length) // 或当前是最后一个主步骤且没有子步骤
            : currentMainStep > mainIndex || // 自动模式：已经过了这个主步骤
              (currentMainStep === mainIndex && !step.sub_steps?.length); // 或当前主步骤且没有子步骤
          const hasActiveSubStep = currentMainStep === mainIndex && currentSubStep !== null && currentSubStep >= 0;
          // 只显示已完成、当前活动或下一个即将进行的步骤;
          const shouldShowMainStep =
            replayCompleted || isMainCompleted || isMainActive || hasActiveSubStep || clickedStep !== null;
          if (!shouldShowMainStep) return null;
          // 检查是否应该显示标题
          const mainStepKey = `${mainIndex}-null`;
          const shouldShowTitle = replayCompleted || showTitles[mainStepKey] || isMainCompleted;
          return (
            <div key={mainIndex} className="mb-4">
              <div
                ref={stepRefs.current[`main-${mainIndex}`] || null}
                className={`cursor-pointer rounded-lg p-2 transition-all ${!replayCompleted ? 'pointer-events-none' : ''} ${isStepClicked(mainIndex, null) ? 'hover:bg-[#CCDDFF1A]' : ''}`}
                onClick={() => handleStepClick(mainIndex, null)}
              >
                <div className="flex items-center gap-2">
                  <div className={`flex h-6 w-6 items-center justify-center`}>
                    <span className="text-xs font-medium">{`${mainIndex + 1})、`}</span>
                  </div>
                  {showThinking ? (
                    <div className="flex items-center gap-2 text-sm">
                      <span>思考中</span>
                      <span className="dots animate-pulse"></span>
                    </div>
                  ) : (
                    <span className={`text-sm font-medium text-white`}>{step.step_title}</span>
                  )}
                </div>
                {step.description && shouldShowTitle && (
                  <p className={`mt-1 text-sm text-[#9FA4B2]`}>{step.description}</p>
                )}
              </div>
              {step.sub_steps && step.sub_steps.length > 0 && (
                <div className="border-muted pl-8">
                  {step.sub_steps.map((subStep, subIndex) => {
                    const isSubActive =
                      currentMainStep === mainIndex &&
                      currentSubStep === subIndex &&
                      flattenedSteps[currentFlatIndex]?.isSubStep;
                    const isSubCompleted = clickedStep
                      ? mainIndex < completedSteps || (mainIndex === completedSteps - 1 && subIndex < currentSubStep)
                      : currentMainStep > mainIndex || (currentMainStep === mainIndex && currentSubStep > subIndex);

                    // 修改显示逻辑：只显示已完成和当前活动的子步骤
                    const shouldShowSubStep = replayCompleted || isSubCompleted || isSubActive || clickedStep !== null;
                    if (!shouldShowSubStep) return null;
                    // 检查是否应该显示子步骤标题
                    const subStepKey = `${mainIndex}-${subIndex}`;
                    const shouldShowSubTitle =
                      replayCompleted || (showTitles && showTitles[subStepKey]) || isSubCompleted;
                    return (
                      <div
                        key={subIndex}
                        ref={stepRefs.current[`sub-${mainIndex}-${subIndex}`] || null}
                        className={`my-4 transition-all ${!replayCompleted ? 'pointer-events-none' : ''} ${isStepClicked(mainIndex, subIndex) ? 'bg-[#CCDDFF1A] hover:bg-[#CCDDFF1A]' : ''}`}
                        // onClick={() => handleStepClick(mainIndex, subIndex)}
                      >
                        <div className="flex items-center gap-2">
                          {subStep.lodding && !shouldShowSubTitle ? (
                            <>
                              <div className="flex items-center justify-center">
                                <Magic className="-ml-1 h-[32px] w-[26px] animate-spin" />
                              </div>
                              <div className="flex items-center gap-1">
                                <span className="text-sm">{subStep.lodding}</span>
                                <span className="dots animate-pulse"></span>
                              </div>
                            </>
                          ) : (
                            shouldShowSubTitle && (
                              <div className="flex">
                                <div>
                                  <StepSuccessful />
                                  <div className="flex h-full items-center">
                                    <div className="ml-2 h-full w-1 border-l border-dashed border-[#FFFFFF1A]"></div>
                                  </div>
                                </div>
                                <div className="ml-2">
                                  <div className="text-sm text-white">{subStep.step_title}</div>
                                  {subStep.description && shouldShowSubTitle && (
                                    <div className={`mt-2 text-sm text-white`}>{subStep.description}</div>
                                  )}
                                  {subStep.description1 && shouldShowSubTitle && (
                                    <div className={`mt-2 text-sm text-white`}>{subStep.description1}</div>
                                  )}
                                  {renderTypewriterContent(subStep, mainIndex, subIndex)}
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
