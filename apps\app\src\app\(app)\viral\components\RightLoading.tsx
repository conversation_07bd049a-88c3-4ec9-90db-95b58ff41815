import React from 'react';
import {
  Look1,
  Look1_1,
  Look1_2,
  Look1_3,
  Look1_4,
  Look2,
  Look2_1,
  Look2_2,
  Look3,
  Look3_1,
  Look3_2,
  Look3_3,
  Look3_5,
  Look3_6,
  Look4,
  Look4_1,
  Look4_2,
  Look4_3,
  Look4_6,
  Look,
} from '@/components/icon';
export const RightLoading = ({
  flattenedSteps,
  getCurrentStep,
}: {
  flattenedSteps: any[];
  getCurrentStep: () => any;
}) => {
  return (
    <div>
      {flattenedSteps.length > 0 && (
        <>
          <div className="mb-3 flex h-8 items-center">
            {/* 根据当前步骤编号显示不同的图标 */}
            {(() => {
              const currentStep = getCurrentStep();
              if (!currentStep) return <Look />;
              // 获取步骤编号
              const stepNumber = currentStep.step_number;
              // 根据步骤编号返回对应的图标
              if (stepNumber === 1) return <Look1 />;
              else if (stepNumber === 1.1) return <Look1_1 />;
              else if (stepNumber === 1.2) return <Look1_2 />;
              else if (stepNumber === 1.3) return <Look1_3 />;
              else if (
                stepNumber === 1.4 ||
                stepNumber === 1.5 ||
                stepNumber === 2.3 ||
                stepNumber === 2.4 ||
                stepNumber === 2.5 ||
                stepNumber === 2.6 ||
                stepNumber === 2.7 ||
                stepNumber === 3.4
              )
                return <Look1_4 />;
              else if (stepNumber === 2) return <Look2 />;
              else if (stepNumber === 2.1) return <Look2_1 />;
              else if (stepNumber === 2.2) return <Look2_2 />;
              else if (stepNumber === 3) return <Look3 />;
              else if (stepNumber === 3.1) return <Look3_1 />;
              else if (stepNumber === 3.2) return <Look3_2 />;
              else if (stepNumber === 3.3 || stepNumber === 4.4) return <Look3_3 />;
              else if (stepNumber === 3.5 || stepNumber === 4.5) return <Look3_5 />;
              else if (stepNumber === 3.6) return <Look3_6 />;
              else if (stepNumber === 4) return <Look4 />;
              else if (stepNumber === 4.1) return <Look4_1 />;
              else if (stepNumber === 4.2) return <Look4_2 />;
              else if (stepNumber === 4.3) return <Look4_3 />;
              else if (stepNumber === 4.6) return <Look4_6 />;
              else return <Look />;
            })()}

            {getCurrentStep()?.lodding && (
              <div className="flex items-center">
                <div className="ml-2 mr-1 text-sm text-[#9FA4B2]">{getCurrentStep()?.lodding}</div>
                <span className="dots animate-pulse"></span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};
