import { goods } from '@roasmax/database';
import { ActionContext, webhook } from '@roasmax/serve';
import { isEmpty, uniq } from 'lodash';

type BatchCreateContext = ActionContext<{
  data: goods[];
}>;

export const POST = webhook(async (ctx: BatchCreateContext) => {
  return await ctx.trx(async (ctx) => {
    if (ctx.data.data.some((item) => !item.out_key)) {
      throw new Error('缺少out_key');
    }

    // 获取已存在的数据 由于存在老数据，所以需要同时匹配 out_key 和 product_identity
    const originList = await ctx.db.goods.findMany({
      where: {
        OR: [
          { out_key: { in: ctx.data.data.map((item) => item.out_key!) } },
          { product_identity: { in: ctx.data.data.map((item) => item.product_identity!) } },
        ],
      },
    });

    // 将数据分为需要更新和需要创建的两部分
    const toCreate = ctx.data.data.filter(
      (item) =>
        !originList.some(
          (origin) => origin.out_key === item.out_key || origin.product_identity === item.product_identity,
        ),
    );
    const toUpdate = ctx.data.data.filter((item) =>
      originList.some((origin) => origin.out_key === item.out_key || origin.product_identity === item.product_identity),
    );

    ctx.logger.log(toCreate.length, toUpdate.length);

    // 批量创建
    if (toCreate.length > 0) {
      await ctx.db.goods.createMany({
        data: toCreate.map((item) => ({
          tenant_id: ctx.tenant.id,
          name: item.name,
          out_key: item.out_key,
          alias: [],
          images: item.images || [],
          tags: item.tags || [],
          live_rooms: item.live_rooms || [],
          ip: item.ip,
          platform: item.platform,
          product_identity: item.product_identity,
          product_url: item.product_url,
          hot_product: item.hot_product,
          shop_name: item.shop_name,
          commission_rate: item.commission_rate,
          remark: item.remark,
          price: item.price,
          stock_amount: item.stock_amount,
          link: item.link,
        })),
      });
    }

    // 批量更新
    for (const item of toUpdate) {
      const origin = originList.find(
        (origin) => origin.out_key === item.out_key || origin.product_identity === item.product_identity,
      );
      if (!origin) {
        continue;
      }

      // 如果商品名称发生改变，则把原名称添加到 alias 中
      if (item.name !== origin.name) {
        item.alias = uniq([origin.name, ...(origin.alias || [])]);
      }

      // 如果数据一致，则不更新
      const diff = checkDiff(origin, item);
      if (diff.length === 0) {
        continue;
      }

      await ctx.db.goods.update({
        where: { id: origin!.id },
        data: {
          out_key: item.out_key,
          name: item.name,
          alias: item.alias || origin.alias || [],
          images: item.images || origin.images || [],
          tags: item.tags || origin.tags || [],
          live_rooms: item.live_rooms || origin.live_rooms || [],
          ip: item.ip,
          platform: item.platform,
          product_identity: item.product_identity,
          product_url: item.product_url,
          hot_product: item.hot_product,
          shop_name: item.shop_name,
          commission_rate: item.commission_rate,
          remark: item.remark,
          price: item.price,
          stock_amount: item.stock_amount,
          link: item.link,
          properties: {},
        },
      });
    }

    return {
      createdCount: toCreate.length,
      updatedCount: toUpdate.length,
    };
  });
});

function checkDiff(origin: goods, commit: goods) {
  // 只比较以下字段
  const fields: (keyof goods)[] = [
    'out_key',
    'name',
    'product_identity',
    'alias',
    'images',
    'tags',
    'live_rooms',
    'ip',
    'platform',
    'product_url',
    'hot_product',
    'shop_name',
    'commission_rate',
    'remark',
    'price',
    'stock_amount',
    'link',
  ];
  return fields.filter((field) => {
    // 当commit中该字段为空时，判定相等
    if (typeof commit[field] === 'object' && isEmpty(commit[field])) {
      return false;
    }
    const diff = JSON.stringify(origin[field]) !== JSON.stringify(commit[field]);
    return diff;
  });
}
