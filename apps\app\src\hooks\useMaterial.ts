import { useTaskCreator } from '@/hooks/useTaskCreator';
import { pageMaterials, updateMaterialsName } from '@/services/actions/materials';
import useMaterialStore from '@/store/materialStore';
import { action } from '@/utils/server-action/action';
import { materials } from '@roasmax/database';
import { to } from '@roasmax/utils';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import useSWR from 'swr';

export type SearchType = { name?: string; dirId?: string; page: number; pageSize: number };
export type MaterialItemType = materials & { checked: boolean; locked: boolean; usage: boolean };

const useMaterial = (searchParams: SearchType, isFetchingDir: boolean, clearSearch: () => void) => {
  const { selectedMaterials: taskMaterialList } = useTaskCreator();
  const { selectedMaterialList } = useMaterialStore();
  const [materialList, setMaterialList] = useState<MaterialItemType[]>([]);
  const [hasMore, setHasMore] = useState(false);

  const swrKey = searchParams.dirId && !isFetchingDir ? [`/api/materials/list`, searchParams] : null;

  const { isLoading, isValidating } = useSWR(swrKey, ([_, params]) => materialFetcher(params as SearchType), {
    dedupingInterval: 500,
    revalidateInterval: 3000,
    keepPreviousData: true,
    revalidateIfStale: true,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    onBeforeRevalidate: () => {
      if (searchParams.page === 1) {
        setMaterialList([]);
        setHasMore(false);
      }
    },
    onSuccess: (data) => {
      if (!data) return;
      const { list: materials, total = 0, page, pageSize } = data ?? {};
      const incomingList = materials ?? [];

      // @ts-ignore
      setMaterialList((prevList) => {
        // 如果是第一页，直接替换
        if (page === 1) {
          return incomingList.map((item: materials) => ({
            ...item,
            // usage: false,
            locked: taskMaterialList?.some((material) => material.id === item.id),
            checked: selectedMaterialList?.some((material) => material.id === item.id),
          }));
        }

        // 如果是加载更多，则追加
        return [
          ...prevList,
          ...incomingList.map((item: materials) => ({
            ...item,
            // usage: false,
            locked: taskMaterialList?.some((material) => material.id === item.id),
            checked: selectedMaterialList?.some((material) => material.id === item.id),
          })),
        ];
      });

      setHasMore(page * pageSize < total);
    },
    onError: (err) => {
      toast.error(err.message);
    },
    errorRetryCount: 3,
    shouldRetryOnError: true,
  });

  const unlockMaterial = (id: string) => {
    setMaterialList((prev) =>
      prev.map((material) => ({ ...material, locked: material.id === id ? false : material.locked })),
    );
  };

  useEffect(() => {
    const currentTaskMaterialList = taskMaterialList.map((material) => material.id);
    materialList.forEach((material) => {
      if (!currentTaskMaterialList.includes(material.id)) {
        unlockMaterial(material.id);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskMaterialList.length]);
  const updateMaterials = async (options: { name: string; id?: string }) => {
    const toastId = toast.loading('修改中...');
    try {
      const { name, id } = options;
      const validName = name.trim();
      setMaterialList((prevDirList) =>
        prevDirList.map((dir) => {
          if (dir.id === id) {
            return { ...dir, name: validName };
          }
          return dir;
        }),
      );
      const [err, res] = await to(
        action(
          updateMaterialsName,
          { name: validName, id },
          {
            errorType: 'return',
          },
        ),
      );
      if (err || !res?.success) {
        toast.dismiss(toastId);
        toast.error(`素材名称修改失败，${res?.message}`);
        return;
      }

      if (res) {
        toast.dismiss(toastId);
        toast.success('素材名称修改成功');
        clearSearch(); // 调用 clearSearch 函数
      }
    } catch (error) {
      toast.dismiss(toastId);
      toast.error('素材名称修改失败');
    }
  };
  return {
    materialList,
    loading: isLoading || isValidating,
    hasMore,
    setMaterialList,
    setHasMore,
    updateMaterials,
  };
};

export const materialFetcher = async (params: SearchType) => {
  if (!params) return null;
  const { page, pageSize, dirId: directoryId, name } = params;
  if (!directoryId) return null;

  const [err, res] = await to(
    action(pageMaterials, { page, pageSize, directoryId, ...(name && { name }) }, { errorType: 'return' }),
  );
  if (err || !res?.success) {
    throw new Error(`获取素材列表失败, ${res?.message}`);
  }
  return res.data;
};

export default useMaterial;
