// 首先定义一个工具类型，用于转换单个下划线属性为小驼峰
export type CamelCase<S extends string> = S extends `${infer P}_${infer Q}` ? `${P}${Capitalize<CamelCase<Q>>}` : S;

// 定义一个递归的工具类型，将对象的所有属性（包括嵌套属性）转换为小驼峰
export type ToCamelCase<T> =
  T extends Array<infer U>
    ? Array<ToCamelCase<U>>
    : T extends object
      ? {
          [K in keyof T as CamelCase<string & K>]: ToCamelCase<T[K]>;
        }
      : T;

/**
 * 将小驼峰命名转换为下划线命名
 * @param str 需要转换的字符串
 * @returns 转换后的下划线命名字符串
 */
export const camelToUnderscore = (str: string): string => {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
};

/**
 * 递归处理对象或数组中的所有属性名，将小驼峰转换为下划线
 * @param data 需要处理的数据(对象或数组)
 * @returns 转换后的数据
 */
export const recursiveCamelToUnderscore = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map((item) => recursiveCamelToUnderscore(item));
  }

  if (data !== null && typeof data === 'object') {
    return Object.keys(data).reduce((result: any, key: string) => {
      const newKey = camelToUnderscore(key);
      result[newKey] = recursiveCamelToUnderscore(data[key]);
      return result;
    }, {});
  }

  return data;
};

/**
 * 将下划线命名转换为小驼峰命名
 * @param str 需要转换的字符串
 * @returns 转换后的小驼峰命名字符串
 */
export const underscoreToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
};

/**
 * 递归处理对象或数组中的所有属性名，将下划线转换为小驼峰
 * @param data 需要处理的数据(对象或数组)
 * @param parseKeys 需要进行 JSON parse 的字段名数组
 * @returns 转换后的数据
 */
export const recursiveUnderscoreToCamel = (data: any, parseKeys: string[] = []): any => {
  if (Array.isArray(data)) {
    return data.map((item) => recursiveUnderscoreToCamel(item, parseKeys));
  }

  if (data !== null && typeof data === 'object') {
    return Object.keys(data).reduce((result: any, key: string) => {
      const newKey = underscoreToCamel(key);
      let value = data[key];

      // 如果当前字段在 parseKeys 中，且值是字符串，尝试进行 JSON parse
      if (parseKeys.includes(key) && typeof value === 'string') {
        try {
          value = JSON.parse(value);
        } catch (e) {
          console.warn(`Failed to parse JSON for key: ${key}`, e);
        }
      }

      // 递归处理值，确保解析后的 JSON 对象也被转换为驼峰命名
      result[newKey] = recursiveUnderscoreToCamel(value, parseKeys);
      return result;
    }, {});
  }

  return data;
};
