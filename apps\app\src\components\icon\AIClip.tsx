export const AIClip = ({ className }: { className?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M24.15 22.6671V22.6667L24.15 9.33333L24.15 9.3329C24.1489 8.93996 23.9923 8.56344 23.7144 8.28559C23.4366 8.00774 23.06 7.85114 22.6671 7.85H22.6667L9.33333 7.85L9.3329 7.85C8.93996 7.85114 8.56344 8.00774 8.28559 8.28559C8.00774 8.56344 7.85114 8.93996 7.85 9.3329V9.33333L7.85 22.6667L7.85 22.6671C7.85114 23.06 8.00774 23.4366 8.28559 23.7144C8.56344 23.9923 8.93996 24.1489 9.3329 24.15H9.33333L22.6667 24.15L22.6671 24.15C23.06 24.1489 23.4366 23.9923 23.7144 23.7144C23.9923 23.4366 24.1489 23.06 24.15 22.6671ZM8.66667 6.15H23.3333C24.7172 6.15 25.85 7.28284 25.85 8.66667V23.3333C25.85 24.7172 24.7172 25.85 23.3333 25.85H8.66667C7.28284 25.85 6.15 24.7172 6.15 23.3333V8.66667C6.15 7.28284 7.28284 6.15 8.66667 6.15Z"
        fill="currentColor"
        stroke="#070F1F"
        strokeWidth="0.3"
      />
      <path
        d="M19.9399 18.0123V18.2123H20.1399H21.0264C21.1144 18.2123 21.1988 18.2473 21.2611 18.3096C21.3233 18.3719 21.3583 18.4563 21.3583 18.5444V19.6079C21.3583 19.696 21.3233 19.7804 21.261 19.8426C21.1988 19.9049 21.1144 19.9398 21.0264 19.9399H20.1399H19.9399V20.1399V21.0265C19.9399 21.0701 19.9313 21.1132 19.9146 21.1535C19.8979 21.1938 19.8735 21.2304 19.8426 21.2612C19.8118 21.292 19.7752 21.3164 19.7349 21.3331C19.6946 21.3498 19.6515 21.3583 19.6079 21.3583H19.6078H18.5441C18.4561 21.3583 18.3717 21.3233 18.3094 21.2611C18.2472 21.1989 18.2123 21.1145 18.2123 21.0265V13.7565V13.5565H18.0123H15.5298C15.0528 13.5565 14.6662 13.1698 14.6662 12.6929C14.6662 12.216 15.0528 11.8294 15.5298 11.8294H19.4294C19.5646 11.8294 19.6943 11.883 19.79 11.9786C19.8857 12.0741 19.9396 12.2037 19.9399 12.3389V18.0123ZM13.557 10.7429V10.743V18.0123V18.2123H13.757H16.2388C16.7161 18.2123 17.103 18.5992 17.103 19.0765C17.103 19.5538 16.7161 19.9407 16.2388 19.9407H12.3389C12.2037 19.9404 12.0741 19.8865 11.9786 19.7908L11.837 19.9321L11.9786 19.7908C11.883 19.6951 11.8294 19.5654 11.8294 19.4301V13.7565V13.5565H11.6294H10.7428C10.6548 13.5565 10.5705 13.5215 10.5082 13.4594C10.446 13.3972 10.411 13.3129 10.4109 13.2249L10.4109 12.1615L10.4109 12.1614C10.4109 12.1178 10.4195 12.0746 10.4362 12.0343C10.4528 11.994 10.4773 11.9574 10.5081 11.9266C10.5389 11.8958 10.5755 11.8713 10.6158 11.8546C10.656 11.8379 10.6992 11.8294 10.7428 11.8294H11.6294H11.8294V11.6294V10.743L11.8294 10.7429C11.8293 10.6994 11.8379 10.6562 11.8546 10.6159C11.8712 10.5756 11.8957 10.539 11.9265 10.5082C11.9573 10.4774 11.9939 10.4529 12.0342 10.4362C12.0744 10.4195 12.1176 10.4109 12.1612 10.4109H13.2251C13.2687 10.4109 13.3119 10.4195 13.3522 10.4362C13.3924 10.4529 13.429 10.4774 13.4599 10.5082C13.4907 10.539 13.5151 10.5756 13.5318 10.6159L13.7166 10.5394L13.5318 10.6159C13.5484 10.6562 13.557 10.6994 13.557 10.7429Z"
        fill="currentColor"
        stroke="#070F1F"
        strokeWidth="0.4"
      />
    </svg>
  );
};
