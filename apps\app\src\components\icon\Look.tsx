export const Look = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="8" fill="#CCDDFF" fillOpacity="0.1" />
      <g filter="url(#filter0_ii_5020_254)">
        <path
          d="M23.3676 16C23.3676 11.6398 19.833 8.10522 15.4729 8.10522C11.1127 8.10522 7.57812 11.6398 7.57812 16C7.57812 20.3601 11.1127 23.8947 15.4729 23.8947C19.833 23.8947 23.3676 20.3601 23.3676 16Z"
          fill="url(#paint0_linear_5020_254)"
        />
      </g>
      <path
        d="M15.4729 8.5564H15.4729C11.3619 8.5564 8.0293 11.889 8.0293 16V16C8.0293 20.111 11.3619 23.4437 15.4729 23.4437H15.4729C19.5839 23.4437 22.9166 20.111 22.9166 16V16C22.9166 11.889 19.5839 8.5564 15.4729 8.5564Z"
        stroke="white"
        strokeWidth="1.26316"
      />
      <path
        d="M13.9173 14.6979C13.9598 14.5796 14.053 14.4864 14.1713 14.444L17.7907 13.1441C18.1257 13.0237 18.4496 13.3477 18.3293 13.6827L17.0288 17.3016C16.9864 17.4198 16.8932 17.513 16.775 17.5554L13.1561 18.8559C12.8211 18.9763 12.4971 18.6524 12.6174 18.3173L13.9173 14.6979Z"
        stroke="white"
        strokeWidth="1.76842"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <filter
          id="filter0_ii_5020_254"
          x="6.84128"
          y="7.36838"
          width="17.2627"
          height="17.2632"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="1.47368" dy="1.47368" />
          <feGaussianBlur stdDeviation="0.368421" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0" />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow_5020_254" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1.47368" dy="-1.47368" />
          <feGaussianBlur stdDeviation="0.368421" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
          <feBlend mode="normal" in2="effect1_innerShadow_5020_254" result="effect2_innerShadow_5020_254" />
        </filter>
        <linearGradient
          id="paint0_linear_5020_254"
          x1="15.4729"
          y1="8.10522"
          x2="15.4729"
          y2="23.8947"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopOpacity="0" />
          <stop offset="1" stopColor="white" stopOpacity="0.16" />
        </linearGradient>
      </defs>
    </svg>
  );
};
