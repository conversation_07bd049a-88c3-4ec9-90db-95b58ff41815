import { prisma } from '@/utils/prisma';
import { NextResponse } from 'next/server';
export const POST = async (request: Request) => {
  try {
    const body = await request.json();
    if (!body) {
      return NextResponse.json({ success: false, message: '请求数据缺失', code: '400' }, { status: 400 });
    }

    const wallets = await prisma.members.findMany({
      where: { email: body.email },
    });
    if (wallets.length === 0 || !wallets[0]?.tenant_id) {
      return NextResponse.json({ success: false, message: '获取租户失败', code: '400' }, { status: 400 });
    }
    if (wallets.length > 1) {
      throw new Error('用户存在多个租户');
    }
    const tenant_id = wallets[0].tenant_id;
    const now = new Date();

    // 查找所有有效的套餐
    const allPlans = await prisma.pricing_plans.findMany({
      where: {
        tenant_id: tenant_id,
        end_time: {
          gt: now,
        },
      },
    });
    const totalQuota = allPlans.reduce((sum, plan) => sum + plan.quota, 0);
    // 查找有效期内的非普通用户套餐
    const activePlan = await prisma.pricing_plans.findFirst({
      where: {
        tenant_id: tenant_id,
        end_time: {
          gt: now,
        },
        NOT: {
          plan_name: '普通用户',
        },
      },
      orderBy: {
        end_time: 'desc',
      },
    });
    let newPlan;
    if (activePlan) {
      if (activePlan.plan_name === body.plan_name) {
        // 更新同款套餐
        newPlan = await prisma.pricing_plans.update({
          where: { id: activePlan.id },
          data: {
            quota: activePlan.quota + body.quota,
            end_time: new Date(activePlan.end_time.getTime() + 1000 * 60 * 60 * 24 * 30),
          },
        });
      } else {
        // 创建新套餐并将旧套餐额度转移
        await prisma.pricing_plans.update({
          where: { id: activePlan.id },
          data: { quota: 0 },
        });

        await prisma.quota_changelogs.create({
          data: {
            pricing_plan_id: activePlan.id,
            tenant_id: tenant_id,
            quota: -activePlan.quota,
            result_quota: totalQuota - activePlan.quota,
            change_type: 'SET_MENU_OUT',
            change_reason: '套餐更换转出: ' + activePlan.plan_name,
          },
        });

        // 在原有套餐的过期日期基础上增加一个月
        const newEndTime = new Date(activePlan.end_time.getTime() + 1000 * 60 * 60 * 24 * 30);

        newPlan = await prisma.pricing_plans.create({
          data: {
            tenant_id: tenant_id,
            quota: body.quota + activePlan.quota,
            plan_name: body.plan_name,
            start_time: now,
            end_time: newEndTime,
          },
        });

        await prisma.quota_changelogs.create({
          data: {
            pricing_plan_id: newPlan.id,
            tenant_id: tenant_id,
            quota: activePlan.quota,
            result_quota: totalQuota,
            change_type: 'SET_MENU_IN',
            change_reason: '套餐更换转入: ' + body.plan_name,
          },
        });
      }
    } else {
      // 创建新套餐
      newPlan = await prisma.pricing_plans.create({
        data: {
          tenant_id: tenant_id,
          quota: body.quota,
          plan_name: body.plan_name,
          start_time: now,
          end_time: new Date(now.getTime() + 1000 * 60 * 60 * 24 * 30),
        },
      });
    }

    // 记录购买新套餐的变更日志
    await prisma.quota_changelogs.create({
      data: {
        pricing_plan_id: newPlan.id,
        tenant_id: tenant_id,
        quota: body.quota,
        result_quota: totalQuota + body.quota,
        change_type: 'CHARGE',
        change_reason: '购买套餐: ' + body.plan_name,
      },
    });

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('充值时发生错误:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '服务器内部错误',
        code: '500',
        data: null,
      },
      { status: 500 },
    );
  }
};
