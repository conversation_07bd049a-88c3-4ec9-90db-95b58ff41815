'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, VisuallyHidden } from '@/components/ui';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { FieldValues, UseFormReturn } from 'react-hook-form';
import { cn } from '@/utils/cn';
interface ConfirmDialogStore {
  open: boolean;
  content: React.ReactNode;
  className?: string;
  buttonText?: { cancel?: string; confirm?: string; loading?: string };
  operations?: (props: { setOpen: (value: boolean) => void }) => React.ReactNode[];
  onConfirm?: () => void | Promise<void>;
  setOpen: (value: boolean) => void;
  setContent: (content: React.ReactNode) => void;
  setOnConfirm: (onConfirm?: () => void) => void;
  setButtonText: (buttonText?: { cancel?: string; confirm?: string; loading?: string }) => void;
  setOperations: (operations?: (props: { setOpen: (value: boolean) => void }) => React.ReactNode[]) => void;
  setClassName: (className?: string) => void;
}

const useConfirmDialogStore = create<ConfirmDialogStore>()(
  devtools((set) => ({
    open: false,
    content: null,
    className: undefined,
    buttonText: { cancel: '取消', confirm: '', loading: '处理中' },
    operations: () => [],
    onConfirm: undefined,
    setOpen: (value: boolean) => set({ open: value }),
    setContent: (content) => set({ content }),
    setOnConfirm: (onConfirm) => set({ onConfirm }),
    setButtonText: (buttonText) => set({ buttonText }),
    setOperations: (operations) => set({ operations }),
    setClassName: (className) => set({ className }),
  })),
);

const ConfirmDialog = () => {
  const { open, content, buttonText, operations, onConfirm, setOpen, className } = useConfirmDialogStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    try {
      await onConfirm?.();
    } finally {
      setIsSubmitting(false);
      setOpen(false);
    }
  };
  return (
    <Dialog open={open} onOpenChange={(value) => !isSubmitting && setOpen(value)}>
      <DialogContent className={cn('w-[380px] pb-5 pl-6 pr-4 pt-8', className)}>
        <VisuallyHidden>
          <DialogTitle className="hidden">Only for visually hidden</DialogTitle>
        </VisuallyHidden>
        <div>{content}</div>
        <DialogFooter>
          {operations ? (
            operations({ setOpen })
          ) : (
            <>
              <Button
                className="h-[32px] w-[92px] bg-[#CCDDFF1A] font-normal text-[#FFF]"
                variant="secondary"
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
              >
                {buttonText?.cancel || '取消'}
              </Button>
              {buttonText?.confirm && (
              <Button
                className="h-[32px] w-[92px] bg-[#00E1FF] font-normal text-[#050A1C] hover:bg-[#1AD2D9] hover:text-[#050A1C]"
                onClick={handleConfirm}
                disabled={isSubmitting}
              >
                {isSubmitting ? buttonText?.loading || '处理中' : buttonText?.confirm || '确定'}
              </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

ConfirmDialog.displayName = 'ConfirmDialog';

function confirm<F extends FieldValues = FieldValues, R = any>(props: {
  content: React.ReactNode | ((props: { form?: UseFormReturn<F> }) => React.ReactNode);
  className?: string;
  buttonText?: { cancel?: string; confirm?: string; loading?: '处理中' };
  operations?: (props: { setOpen: (value: boolean) => void }) => React.ReactNode[];
  onConfirm?: (formData?: F) => R | Promise<R>;
  form?: UseFormReturn<F>;
}) {
  const { setOpen, setContent, setOnConfirm, setButtonText, setOperations, setClassName } =
    useConfirmDialogStore.getState();

  const wrappedOnConfirm = async () => {
    if (props.form) {
      const formData = props.form.getValues();
      await props.onConfirm?.(formData);
    } else {
      await props.onConfirm?.();
    }
  };

  setContent(typeof props.content === 'function' ? props.content({ form: props.form }) : props.content);
  setOnConfirm(wrappedOnConfirm);
  setOperations(props.operations);
  setButtonText(props.buttonText);
  setClassName(props.className);
  setOpen(true);
}

export async function asyncConfirm<F extends FieldValues = FieldValues, R = any>(props: {
  content: React.ReactNode | ((props: { form?: UseFormReturn<F> }) => React.ReactNode);
  buttonText?: { cancel?: string; confirm?: string };
  operations?: (props: { setOpen: (value: boolean) => void }) => React.ReactNode[];
  onConfirm?: (formData?: F) => R | Promise<R>;
  form?: UseFormReturn<F>;
}) {
  const promise = new Promise<R>((resolve) => {
    confirm<F>({
      content: props.content,
      buttonText: props.buttonText,
      operations: props.operations,
      form: props.form,
      onConfirm: async (formData?: F) => {
        if (!props.onConfirm) {
          resolve(undefined as any);
          return;
        }
        const res = await props.onConfirm(formData as any);
        resolve(res);
      },
    });
  });
  return promise;
}

export { ConfirmDialog, confirm };
