import { NotFoundCover } from '@/components/icon/NotFoundCover';
import { ProImage } from '@/components/pro/pro-image';
import { ProSelect } from '@/components/pro/pro-select';
import { Badge, Button, Calendar, Input, InputTags, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { cn } from '@/utils/cn';
import dayjs from 'dayjs';
import { CalendarIcon } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

export type ProFieldValueType =
  | 'text'
  | 'number'
  | 'percent'
  | 'link'
  | 'date'
  | 'datetime'
  | 'currency'
  | 'image'
  | 'images'
  | 'tags'
  | 'enum'
  | 'boolean';

const renders: Record<
  ProFieldValueType,
  (value: any, config?: { options?: { label: string; value: any }[] }) => React.ReactNode
> = {
  number: (value) => (typeof value === 'number' ? value : '-'),
  percent: (value) => (typeof value === 'number' ? `${((Number(value) || 0) / 10000).toFixed(2)}%` : '-'),
  link: (value) => {
    const handleCopy = () => {
      navigator.clipboard.writeText(value);
      toast.success('复制成功');
    };

    if (!value) return '-';

    return (
      <Popover>
        <PopoverTrigger>
          <div className="w-[260px] cursor-pointer overflow-hidden text-ellipsis text-left underline">{value}</div>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2">
          <div className="flex gap-2">
            <Button variant="link" className="h-[32px] w-[92px]" onClick={() => window.open(value, '_blank')}>
              跳转
            </Button>
            <Button className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]" onClick={handleCopy}>
              复制
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  },
  date: (value) => (value ? dayjs(value).format('YYYY-MM-DD') : '-'),
  datetime: (value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-'),
  currency: (value) => `￥${(Number(value) / 100).toFixed(2)}`,
  image: (value) => <ProImage height={32} width={32} className="h-[32px] w-[32px]" src={value as string} alt={''} />,
  images: (value) => {
    const src = Array.isArray(value) ? value[0] : value;
    return (
      <ProImage
        height={32}
        width={32}
        className="h-[32px] w-[32px]"
        src={src || ''}
        alt={''}
        fallback={() => <NotFoundCover className="h-[32px] w-[32px]" />}
      />
    );
  },
  enum: (value, config) => {
    const tags = Array.isArray(value)
      ? value
      : value
        ? String(value)
            .split(',')
            .map((t) => t.trim())
        : [];
    return (
      <div className="flex flex-wrap gap-1">
        {tags.map((tag: string, index) => (
          <Badge key={index} variant="secondary" className="mr-1 font-normal">
            {config?.options?.find((o) => o.value === tag)?.label || tag}
          </Badge>
        ))}
      </div>
    );
  },
  tags: (value) => {
    const tags = Array.isArray(value)
      ? value
      : value
        ? String(value)
            .split(',')
            .map((t) => t.trim())
        : [];
    return (
      <div className="flex flex-wrap gap-1">
        {tags.map((tag: string, index) => (
          <Badge key={index} variant="secondary" className="mr-1 font-normal">
            {tag}
          </Badge>
        ))}
      </div>
    );
  },
  boolean: (value: any) => (value ? '是' : '否'),
  text: (value: any) => (value ? value : '-'),
};

const formRenders: Record<
  ProFieldValueType,
  (props: {
    value: any;
    onValueChange: (v: any) => void;
    initialValue: any;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    config?: {
      options?: { label: string; value: any }[];
      switch?: { checked: boolean; value: any; label: string }[];
    };
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  }) => React.ReactNode
> = {
  number: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled, onKeyDown } = props;
    return (
      <Input
        className={cn(
          'h-[32px] w-[150px] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none',
          className,
        )}
        defaultValue={initialValue}
        value={typeof value === 'number' ? value : undefined}
        type="number"
        onChange={(e) => {
          const n = e.target.value ? Number(e.target.value) : undefined;
          onValueChange(n);
        }}
        placeholder={placeholder}
        disabled={disabled}
        onKeyDown={onKeyDown}
      />
    );
  },
  percent: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    return (
      <Input
        className={cn('h-[32px] w-[150px]', className)}
        defaultValue={value || initialValue}
        value={value}
        onChange={(e) => onValueChange(Number(e.target.value) * 10000)}
        placeholder={placeholder}
        disabled={disabled}
      />
    );
  },
  link: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    return (
      <Input
        className={cn('h-[32px] w-[150px]', className)}
        defaultValue={initialValue}
        value={value}
        type="url"
        onChange={(e) => onValueChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
      />
    );
  },
  date: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    const v = value ? dayjs(value) : initialValue ? dayjs(initialValue) : undefined;
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'h-[32px] w-[280px] justify-start text-left font-normal',
              !v && 'text-muted-foreground',
              className,
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 w-4" />
            {v ? v.format('YYYY-MM-DD') : <span>{placeholder}</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto bg-[#262F40] p-0">
          <Calendar mode="single" selected={v?.toDate()} onSelect={onValueChange} />
        </PopoverContent>
      </Popover>
    );
  },
  datetime: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    const v = value ? dayjs(value) : initialValue ? dayjs(initialValue) : undefined;
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'h-[32px] w-[280px] justify-start text-left font-normal',
              !v && 'text-muted-foreground',
              className,
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 w-4" />
            {v ? v.format('YYYY-MM-DD HH:mm:ss') : <span>{placeholder}</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto bg-[#262F40] p-0">
          <Calendar mode="single" selected={v?.toDate()} onSelect={onValueChange} />
        </PopoverContent>
      </Popover>
    );
  },
  image: () => {
    return null;
  },
  images: () => {
    return null;
  },
  currency: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    return (
      <Input
        value={value}
        className={cn('h-[32px] w-[150px]', className)}
        defaultValue={initialValue}
        onChange={(e) => onValueChange(Number(e.target.value))}
        placeholder={placeholder}
        disabled={disabled}
      />
    );
  },
  enum: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, config, disabled } = props;
    return (
      <ProSelect
        value={value}
        defaultValue={initialValue}
        options={config?.options || []}
        onValueChange={onValueChange}
        placeholder={placeholder}
        className={cn('h-[32px] w-[200px]', className)}
        disabled={disabled}
      />
    );
  },
  tags: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled } = props;
    const v = Array.isArray(value) ? value : value ? value.split(',') : [];
    return (
      <InputTags
        value={v}
        className={cn('w-[200px]', className)}
        defaultValue={initialValue}
        onValueChange={(value) => onValueChange(value)}
        placeholder={placeholder}
        disabled={disabled}
      />
    );
  },
  boolean: (props) => {
    const { value, onValueChange, config, placeholder, disabled } = props;
    if (config?.switch) {
      const v = config?.switch?.find((s) => s.value === value);
      return (
        <ProSelect
          value={value}
          defaultValue={v?.value}
          options={config?.switch || []}
          onValueChange={onValueChange}
          placeholder={placeholder}
          className={cn('h-[32px]')}
          disabled={disabled}
        />
      );
    }
    return (
      <div
        className={cn(
          'h-4 w-8 cursor-pointer rounded-full transition-colors',
          value ? 'bg-primary' : 'bg-gray-300',
          disabled && 'cursor-not-allowed',
        )}
        onClick={() => onValueChange(!value)}
      >
        <div
          className={`h-4 w-4 rounded-full bg-white transition-transform ${value ? 'translate-x-4' : 'translate-x-0'}`}
        />
      </div>
    );
  },
  text: (props) => {
    const { value, onValueChange, initialValue, placeholder, className, disabled, onKeyDown } = props;
    return (
      <Input
        className={cn('h-[32px] w-[150px]', className)}
        defaultValue={initialValue}
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        onKeyDown={onKeyDown}
      />
    );
  },
};

export type ProFieldProps = {
  value: any;
  type?: ProFieldValueType;
  render?: (value: any, dom: React.ReactNode) => React.ReactNode;
  config?: { options?: { label: string; value: any }[] };
  className?: string;
};

export const ProField: React.FC<ProFieldProps> = ({ value, type, config, render, className }) => {
  const renderFn = renders[type ?? 'text'] || renders.text;
  const dom = renderFn(value, config);
  return <div className={className}>{render ? render(value, dom) : dom}</div>;
};

export type ProEditFieldProps = {
  className?: string;
  value: any;
  type?: ProFieldValueType;
  disabled?: boolean;
  config?: {
    options?: { label: string; value: any }[];
  };
  onValueChange: (v: any) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  initialValue?: any;
  placeholder?: string;
  render?: (value: any, onChange: (v: any) => void, dom: React.ReactNode) => React.ReactNode;
};

export const ProEditField: React.FC<ProEditFieldProps> = ({
  className,
  value,
  type,
  disabled,
  config,
  onValueChange,
  initialValue,
  placeholder,
  render,
  onKeyDown,
}) => {
  const renderFn = formRenders[type ?? 'text'] || formRenders.text;
  const dom = renderFn({
    value,
    onValueChange,
    placeholder,
    initialValue,
    className,
    config,
    disabled,
    onKeyDown,
  });
  return render ? render(value, onValueChange, dom) : dom;
};
