import React from 'react';

const LibraryDirectory = () => {
  return (
    <svg
      // width="113" height="114"
      viewBox="0 0 113 114"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 14.4873C0 7.85989 5.37258 2.4873 12 2.4873H31.8384C34.5674 2.4873 37.251 3.18531 39.6342 4.51496L46.9915 8.6199C49.3746 9.94955 52.0583 10.6476 54.7872 10.6476H90.7958H101C107.627 10.6476 113 16.0201 113 22.6476V100.487C113 107.115 107.627 112.487 101 112.487H12C5.37258 112.487 0 107.115 0 100.487V14.4873Z"
        fill="#CCDDFF"
        fillOpacity="0.1"
      />
      <path
        opacity="0.2"
        d="M44.8135 70.6956C44.8135 71.2134 44.3937 71.6331 43.876 71.6331C43.3582 71.6331 42.9385 71.2134 42.9385 70.6956V53.1716C42.9385 52.4371 43.2348 51.7328 43.7622 51.2134C44.2897 50.6941 45.0051 50.4023 45.751 50.4023H55.1404C55.6502 50.4023 56.0635 50.8156 56.0635 51.3254C56.0635 51.8352 55.6502 52.2485 55.1404 52.2485H45.751C45.5023 52.2485 45.2639 52.3458 45.0881 52.5189C44.9122 52.692 44.8135 52.9268 44.8135 53.1716V70.6956Z"
        fill="url(#paint0_linear_1650_2607)"
      />
      <path
        d="M48.562 66.1035H52.312C52.937 66.1035 53.2495 66.4112 53.2495 67.0266C53.2495 67.642 52.937 67.9497 52.312 67.9497H48.562C47.937 67.9497 47.6245 67.642 47.6245 67.0266C47.6245 66.4112 47.937 66.1035 48.562 66.1035Z"
        fill="white"
      />
      <path
        d="M69.1875 56.8719H68.25V54.1027C68.25 53.3682 67.9537 52.6639 67.4262 52.1445C66.8988 51.6252 66.1834 51.3335 65.4375 51.3335H56.325L56.0325 50.187C55.982 49.9875 55.8653 49.8104 55.7009 49.6836C55.5365 49.5569 55.3338 49.4878 55.125 49.4873H44.8125C44.0666 49.4873 43.3512 49.7791 42.8238 50.2984C42.2963 50.8177 42 51.5221 42 52.2565V70.7181C42 71.4525 42.2963 72.1569 42.8238 72.6762C43.3512 73.1955 44.0666 73.4873 44.8125 73.4873H69.1875C69.9334 73.4873 70.6488 73.1955 71.1762 72.6762C71.7037 72.1569 72 71.4525 72 70.7181V59.6411C72 58.9067 71.7037 58.2023 71.1762 57.683C70.6488 57.1637 69.9334 56.8719 69.1875 56.8719ZM65.4375 53.1796C65.6861 53.1796 65.9246 53.2769 66.1004 53.45C66.2762 53.6231 66.375 53.8579 66.375 54.1027V56.8719H57.7275L56.79 53.1796H65.4375ZM70.125 70.7181C70.125 70.9629 70.0262 71.1977 69.8504 71.3708C69.6746 71.5439 69.4361 71.6412 69.1875 71.6412H44.8125C44.5639 71.6412 44.3254 71.5439 44.1496 71.3708C43.9738 71.1977 43.875 70.9629 43.875 70.7181V52.2565C43.875 52.0117 43.9738 51.7769 44.1496 51.6038C44.3254 51.4307 44.5639 51.3335 44.8125 51.3335H54.3919L56.0906 58.0202C56.1416 58.2197 56.2587 58.3966 56.4235 58.5231C56.5882 58.6495 56.7911 58.7181 57 58.7181H69.1875C69.4361 58.7181 69.6746 58.8153 69.8504 58.9884C70.0262 59.1615 70.125 59.3963 70.125 59.6411V70.7181Z"
        fill="url(#paint1_linear_1650_2607)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1650_2607"
          x1="49.501"
          y1="50.4023"
          x2="49.501"
          y2="71.6331"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#54FFE0" />
          <stop offset="0.2" stopColor="#2AF0F0" />
          <stop offset="1" stopColor="#9D81FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1650_2607"
          x1="42"
          y1="54.9639"
          x2="68.9256"
          y2="73.8779"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4FA72" />
          <stop offset="0.2" stopColor="#2AF0F0" />
          <stop offset="1" stopColor="#F4FA72" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default LibraryDirectory;
