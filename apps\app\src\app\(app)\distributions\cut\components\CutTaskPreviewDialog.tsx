import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>,
  Textarea,
  Input,
  Button,
  DialogFooter,
  Switch,
} from '@/components/ui';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { action, ActionResult, useAction } from '@/utils/server-action/action';
import { getVideoGenerationTaskById } from '@/services/actions/video-generation-task';
import { createVideoCutTask, getVideoCutTaskById, modifyVideoCutTask } from '@/services/actions/video-cut-task';
import { ProFormField } from '@/components/pro/pro-form';
import { useForm } from 'react-hook-form';
import { pick } from '@roasmax/utils';
import { ProButton } from '@/components/pro/pro-button';
import toast from 'react-hot-toast';
import { Loader } from '@/components/icon/loader';

export type TaskType = ActionResult<typeof getVideoGenerationTaskById>;

interface TaskPreviewModalProps {
  onCommitted?: (success: boolean) => void;
}

type FormValue = Parameters<typeof createVideoCutTask>[0]['data']['data'];

export interface TaskPreviewModalRef {
  show: (taskId: string) => void;
}

export const TaskPreviewModal = React.forwardRef<TaskPreviewModalRef, TaskPreviewModalProps>((props, ref) => {
  const [open, setOpen] = React.useState(false);
  const form = useForm<FormValue>();

  const [run, setRun] = useState(false);

  const [taskId, setTaskId] = useState<string | null>(null);
  const { data, loading } = useAction(getVideoCutTaskById, { id: taskId! }, { skip: () => !taskId });

  useImperativeHandle(ref, () => ({
    show(_taskId) {
      setOpen(true);
      setTaskId(_taskId);
    },
  }));

  useEffect(() => {
    if (!data) return;
    const v = pick(data, ['name', 'origin_url', 'goods_list', 'ip', 'live_room', 'live_session']);
    const initialValues = { ...v, goods_list: v.goods_list.join('\n') };
    form.reset(initialValues);
  }, [data]);

  const handleCancel = () => {
    setOpen(false);
  };

  const handleSubmit = form.handleSubmit(async (data) => {
    const res = await action(
      modifyVideoCutTask,
      { where: { id: taskId! }, data: { ...data, goods_list: data.goods_list.split('\n') }, run },
      { errorType: 'return' },
    );
    if (res.success) {
      toast.success('更新成功');
      setOpen(false);
      form.reset({});
      props.onCommitted?.(true);
    } else {
      toast.error('更新失败');
      props.onCommitted?.(false);
    }
  });

  return (
    <Dialog open={open} onOpenChange={(o) => setOpen(o)}>
      <DialogContent className="w-[1024px] max-w-[1024px]">
        <DialogHeader>切分任务详情</DialogHeader>
        {loading ? (
          <div className="flex h-[300px] w-[1024px] items-center justify-center">
            <Loader />
          </div>
        ) : (
          <>
            <div className="max-h-[60vh] overflow-auto">
              <Form {...form}>
                <form>
                  <ProFormField
                    name="name"
                    label="任务名称"
                    form={form}
                    renderFormControl={(field) => <Input {...field} />}
                  />
                  <ProFormField
                    name="ip"
                    label="IP名称"
                    form={form}
                    renderFormControl={(field) => <Input {...field} />}
                  />
                  <ProFormField
                    name="live_room"
                    label="直播间"
                    form={form}
                    renderFormControl={(field) => <Input {...field} />}
                  />
                  <ProFormField
                    name="live_session"
                    label="直播场次"
                    form={form}
                    renderFormControl={(field) => <Input {...field} />}
                  />
                  <ProFormField
                    name="origin_url"
                    label="视频链接"
                    form={form}
                    renderFormControl={(field) => <Input {...field} />}
                  />
                  <ProFormField
                    name="goods_list"
                    label="商品名称列表(一行一个)"
                    form={form}
                    renderFormControl={(field) => <Textarea {...field} className="h-[100px]" />}
                  />
                </form>
              </Form>
            </div>
            <DialogFooter>
              <div className="mt-4 flex justify-end gap-2">
                <div className="flex items-center gap-2">
                  立即执行 <Switch disabled={data?.status !== 'DRAFT'} checked={run} onCheckedChange={setRun} />
                </div>
                <Button variant="outline" className="h-[32px] w-[92px]" onClick={handleCancel}>
                  取消
                </Button>
                <ProButton
                  disabled={data?.status !== 'DRAFT'}
                  className="h-[32px] w-[92px] text-[#050A1C] hover:text-[#050A1C]"
                  onClick={handleSubmit}
                >
                  更新
                </ProButton>
              </div>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
});

TaskPreviewModal.displayName = 'TaskPreviewModal';
