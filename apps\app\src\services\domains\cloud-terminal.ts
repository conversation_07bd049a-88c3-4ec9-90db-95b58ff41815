import { ActionContext } from '@roasmax/serve';

/**
 * 绑定单个云终端与社交账号
 */
export const bindAccount = async (ctx: ActionContext<{ cloudTerminalId: string; socialAccountId: string }>) => {
  return await ctx.trx(async (ctx) => {
    const cloudTerminal = await ctx.db.cloud_terminals.findFirst({ where: { id: ctx.data.cloudTerminalId } });
    if (!cloudTerminal) {
      throw new Error('未找到指定的云终端');
    }
    const socialAccount = await ctx.db.social_accounts.findUnique({
      where: { id: ctx.data.socialAccountId },
    });
    if (!socialAccount) {
      throw new Error('未找到指定的社交账号');
    }

    // 绑定
    await ctx.db.cloud_terminals.update({
      where: { id: cloudTerminal.id },
      data: { bind_social_account_id: ctx.data.socialAccountId, status: '已绑定' },
    });
    await ctx.db.social_accounts.update({
      where: { id: socialAccount.id },
      data: { cloud_terminal_id: cloudTerminal.id },
    });

    return true;
  });
};
