type StringifyJsonOptions = {
  /**
   * 配置 bigint的序列化
   */
  convertBigint?: { to: 'int' | 'string' };
  replacer?: (this: any, key: string, value: any) => any;
  space?: string | number;
};

/**
 * 序列化JSON
 * @param text
 * @param options
 * @returns
 */
export const stringifyJson = (text: any, options?: StringifyJsonOptions) => {
  return JSON.stringify(
    text,
    (key, value) => {
      if (options?.convertBigint) {
        if (options.convertBigint.to === 'int') {
          return convertBigintToInt(value);
        }
        if (options.convertBigint.to === 'string') {
          return convertBigintToString(value);
        }
        return value;
      }
      return options?.replacer?.(key, value) || value;
    },
    options?.space,
  );
};

type ParseJsonOptions<T> = {
  /**
   * 当为 true 时，会先序列化再反序列化，以便将不可序列化的值转换为可序列化的值，如symbol
   */
  serializable?: StringifyJsonOptions;
  default?: T;
  reviver?: (this: any, key: string, value: any) => any;
};

/**
 * 解析JSON
 * @param text
 * @param options
 * @returns
 */
export const parseJson = <T>(text: any, options?: ParseJsonOptions<T>) => {
  try {
    return JSON.parse(options?.serializable ? stringifyJson(text, options.serializable) : text, (key, value) => {
      return options?.reviver?.(key, value) || value;
    }) as T;
  } catch (e: any) {
    return options?.default ?? null;
  }
};

/**
 * 将bigint转换为int
 * @param value
 * @returns
 */
export const convertBigintToInt = (value: any) => {
  if (typeof value !== 'bigint') {
    return value;
  }

  const n = Number(value);
  if (!Number.isSafeInteger(n)) {
    throw new Error('convert bigint to int failed: Bigint is too large');
  }
  return n;
};

/**
 * 将bigint转换为string
 * @param value
 * @returns
 */
export const convertBigintToString = (value: any) => {
  if (typeof value !== 'bigint') {
    return value;
  }
  return value.toString();
};

/**
 * 从对象中选择指定属性
 * @param obj
 * @param keys
 * @returns
 */
export const pick = <T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
  const result: Partial<T> = {};
  keys.forEach((key) => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result as Pick<T, K>;
};
