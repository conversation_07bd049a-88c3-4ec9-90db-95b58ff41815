import * as React from 'react';

import { cn } from '@/utils/cn';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  leftSlot?: React.ReactNode;
  rightSlot?: React.ReactNode;
  onValueChange?: (value: string) => void;
}

const ProInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, leftSlot, rightSlot, onValueChange, ...props }, ref) => {
    return (
      <div className="relative flex w-full items-center">
        {leftSlot && <div className="pointer-events-none absolute left-3 flex items-center">{leftSlot}</div>}
        <input
          type={type}
          className={cn(
            'border-input ring-offset-background file:text-foreground bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm text-white file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            leftSlot && 'pl-10',
            rightSlot && 'pr-10',
            className,
          )}
          ref={ref}
          onChange={(e) => {
            onValueChange?.(e.target.value);
            props.onChange?.(e);
          }}
          {...props}
        />
        {rightSlot && <div className="absolute right-1 flex items-center">{rightSlot}</div>}
      </div>
    );
  },
);

ProInput.displayName = 'ProInput';

export { ProInput };
