export const CloneViral = ({ className }: { className?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M18.0936 6.00209C18.6009 5.96387 18.9828 6.45622 18.82 6.93871C18.6381 7.47786 18.5442 8.04613 18.5442 8.62819C18.5442 10.1964 19.2321 11.6469 20.386 12.6357L20.4212 12.6655L20.4419 12.6827L20.4473 12.6771C21.0842 12.0135 21.4582 11.1298 21.4742 10.1819L21.4745 10.1532L21.4747 10.1185C21.4747 10.0695 21.4737 10.0207 21.4718 9.97194L21.4699 9.92694L21.4695 9.91372C21.4543 9.29625 22.1786 8.95426 22.645 9.35876C24.7067 11.1471 25.9148 13.7386 25.9148 16.5248C25.9148 21.7576 21.6807 26 16.4574 26C11.2341 26 7 21.7576 7 16.5248C7 14.6724 7.53224 12.8973 8.51706 11.3758C8.86161 10.8434 9.6773 10.9948 9.80832 11.6154C10.0477 12.749 10.7036 13.7341 11.6119 14.3966L11.6395 14.4166L11.6522 14.4256L11.6509 14.4188C11.574 14.0073 11.5333 13.5899 11.5292 13.1713L11.529 13.1304L11.5289 13.0992C11.5289 9.3699 14.402 6.28016 18.0936 6.00209ZM17.2041 7.58542L17.1688 7.59453C14.7301 8.24337 12.9497 10.4731 12.9497 13.0992C12.9497 13.9323 13.1279 14.7388 13.4675 15.4782C13.7222 16.0327 13.2066 16.6296 12.6217 16.4575C11.0888 16.0064 9.81561 14.9663 9.05428 13.6043L9.03137 13.563L9.00266 13.51L8.99206 13.5363C8.62739 14.4503 8.43117 15.4323 8.4211 16.4438L8.42084 16.4775V16.5248C8.42084 20.9725 12.0191 24.5778 16.4574 24.5778C20.8957 24.5778 24.4939 20.9725 24.4939 16.5248C24.4939 14.6717 23.8663 12.9217 22.754 11.5194L22.7201 11.477L22.7149 11.4705L22.7061 11.5024C22.4193 12.5273 21.8181 13.4399 20.9803 14.1124L20.9548 14.1327L20.9178 14.1617C20.7998 14.2535 20.6559 14.3057 20.5065 14.3108C20.3571 14.316 20.2099 14.2739 20.0858 14.1904C18.249 12.9553 17.1234 10.8868 17.1234 8.62819C17.1233 8.29261 17.1482 7.95749 17.1979 7.62562L17.2041 7.58542Z"
        fill="currentColor"
      />
    </svg>
  );
};
