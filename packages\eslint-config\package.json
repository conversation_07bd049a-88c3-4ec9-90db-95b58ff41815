{"name": "@roasmax/eslint-config", "version": "0.0.1", "private": true, "scripts": {"clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "files": ["library.js", "next.js", "react-internal.js"], "devDependencies": {"@vercel/style-guide": "^6.0.0", "eslint-config-turbo": "^2.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-only-warn": "^1.1.0", "@typescript-eslint/parser": "^8.18.0", "@typescript-eslint/eslint-plugin": "^8.18.0"}}