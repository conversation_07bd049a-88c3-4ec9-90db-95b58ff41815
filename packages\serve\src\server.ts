import { Logger, parseJson } from '@roasmax/utils';
import { AuthenticationClient } from 'authing-node-sdk';
import chalk from 'chalk';
import { headers } from 'next/headers';
import { createContext } from './context';
import type { ActionPayload, ServerAction, ServerActionWrapper } from './types';
import { getLoginSessionInfo } from './authing';
import { prisma } from './prisma';

export function server<R = unknown, P = unknown>(name: string, action: ServerAction<R, P>): ServerActionWrapper<R, P> {
  return async (payload: ActionPayload<P>) => {
    const now = Date.now();
    const token = payload.authorization || headers().get('AccessToken') || headers().get('Authorization');
    const requestIp = headers().get('x-forwarded-for');

    const logger = new Logger(`${name ?? '-'}`, requestIp ?? '-', generateRandomBase36(10));
    logger._start('Start', JSON.stringify(payload.data));

    let ctx;
    try {
      // 鉴权
      if (!token) {
        throw new Error('Unauthorized: no token');
      }
      const sessionInfo = await getLoginSessionInfo(token);
      if (!sessionInfo?.data?.id && !sessionInfo?.sub) {
        throw new Error('Unauthorized: invalid token');
      }

      const tenant = await fetchTenant(token);
      const tenantConfig = await fetchTenantSourceConfig(tenant.id);

      ctx = await createContext(
        { data: payload.data },
        {
          token,
          user: { id: sessionInfo?.data?.id ?? sessionInfo?.sub ?? '' },
          tenant: { id: tenant.id, name: tenant.name, config: tenantConfig },
          logger,
        },
      );
    } catch (e: any) {
      logger._end('Error', cc(Date.now() - now), e);
      if (e.message.includes('Unauthorized')) {
        return { success: false, code: 401, message: e.message, data: null };
      }
      return { success: false, code: 500, message: e.message, data: null };
    }

    try {
      const data = await action(ctx);
      logger._end('Success', cc(Date.now() - now));
      // data中可能存在symbol, 需要进行一次序列化
      return {
        success: true,
        data: parseJson(data, { serializable: { convertBigint: { to: 'int' } } })!,
      };
    } catch (e: any) {
      logger._end('Error', cc(Date.now() - now), e);
      if (e.message.includes('Unauthorized')) {
        return { success: false, code: 401, message: e.message, data: null };
      }
      return { success: false, code: 500, message: e.message, data: null };
    }
  };
}

const cc = (cost: number) => (cost < 500 ? chalk.green(`${cost}ms`) : chalk.red(`${cost}ms`));

function generateRandomBase36(length: number): string {
  let result = '';
  while (result.length < length) {
    result += Math.random().toString(36).substring(2);
  }
  return result.substring(0, length);
}

/**
 * 每一个用户只有一个租户，就意味着每次登录的token只会对应一个租户
 * 这里做一层简单的缓存，避免频繁远程请求
 */
const TOKEN_TENANT_MAP: Record<string, { id: string; name: string; expired: number }> = {};

const fetchTenant = async (token: string) => {
  // 这里做一层简单的缓存，使得可以跨请求缓存每一个登录态对应的租户信息
  if (TOKEN_TENANT_MAP[token]) {
    if (TOKEN_TENANT_MAP[token].expired > Date.now()) {
      const { id, name } = TOKEN_TENANT_MAP[token];
      return { id, name };
    }
    delete TOKEN_TENANT_MAP[token];
  }

  if (!process.env.APPID || !process.env.APPSECRET || !process.env.APPHOST) {
    throw new Error('APPID, APPSECRET, or APPHOT is not set');
  }

  const authClient = new AuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  authClient.setAccessToken(token);

  const result = await authClient.getTenantList();
  if (result.statusCode !== 200 || !result.data.length) {
    throw new Error('Unauthorized');
  }
  const data = { id: result.data[0]!.tenantId, name: result.data[0]!.tenantName };
  TOKEN_TENANT_MAP[token] = { ...data, expired: Date.now() + 30000 };
  return data;
};

const fetchTenantSourceConfig = async (tenantId: string) => {
  const sourceConfig = await prisma.source_configs.findUnique({
    where: { tenant_id: tenantId },
  });
  if (!sourceConfig) {
    throw new Error('Unauthorized');
  }
  return sourceConfig;
};
