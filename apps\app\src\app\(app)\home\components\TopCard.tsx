'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/utils/cn';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { ProImage } from '@/components/pro/pro-image';

export default function TopCard({ className }: { className?: string }) {
  const cardContent = [
    {
      title: 'AI搜索爆款克隆',
      description: 'AI学习行业最热卖视频风格和模式，打造大卖视频',
      // btn: '爆款克隆',
      link: '/viral',
    },
    {
      title: '自定义视频裂变',
      description: '智能分析优质视频内容，快速裂变出多个高质量视频',
      // btn: '视频裂变',
      link: '/tools/koc',
    },
    {
      title: 'AI视频精剪',
      description: '长视频智能分解，精准匹配平台规则生成视频',
      // btn: 'AI精剪',
      link: '/originality',
    },
  ];

  return (
    <div className={className}>
      {cardContent.map((item) => {
        const [isHovered, setIsHovered] = useState(false);
        return (
          <Card
            className={cn(
              'bg-custom-gradient relative w-1/3 rounded-3xl border-[#EFEDFD1A] font-medium text-[#C2D7EF] transition-colors duration-300',
              isHovered && 'text-white',
            )}
            key={item.title}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {isHovered && (
              <ProImage
                width={100}
                height={206}
                alt="error"
                src="background/CardBag.svg"
                className="absolute right-1 top-0 h-[206px] w-2/3"
              />
            )}
            <CardHeader className="h-2/3 pb-0">
              <CardTitle className="pt-2">{item.title}</CardTitle>
              <CardDescription className="pt-2 text-[#9FA4B2]">{item.description}</CardDescription>
            </CardHeader>
            <CardContent className="h-1/3">
              <Link
                href={item.link}
                prefetch={true}
                className={cn(
                  'flex h-12 w-[105px] cursor-pointer items-center justify-center rounded-xl bg-[#CCDDFF1A] text-sm transition-all duration-300 hover:scale-105',
                  isHovered && 'bg-[#00E1FF]',
                )}
              >
                {isHovered && <ArrowRight className={cn('mr-1 h-4 w-4 text-black transition-all duration-300')} />}
                <div className={cn('flex items-center text-white', isHovered && 'text-black')}>去生成</div>
              </Link>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
