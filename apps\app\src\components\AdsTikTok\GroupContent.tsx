import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import debounce from 'lodash/debounce';
import { v4 as uuidv4 } from 'uuid';
import { useCampaignList } from '@/hooks/useCampaignList';
import { cn } from '@/utils/cn';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/AlertDialog';
import TemplateCreation from '@/components/AdsTikTok/Dialog/TemplateCreation';
import { RefreshCw, Plus, XIcon, FolderSyncIcon as SyncIcon, FileDown } from 'lucide-react';
import { CommonTable } from '@/components/AdsTikTok/selectTable/CommonTable';
import { useTableLogic } from '@/hooks/useTableLogic';
import GroupSheet from '@/components/AdsTikTok/Sheet/AdGroupSheet/GroupShee';
import SelectStatus from '@/components/AdsTikTok/selectTable/selectAccount/Selectstu';
import { useAdActions, useCurrentCampaign, useCurrentAdvertiser, useCurrentAdGroup } from '@/store/ads/adStore';
import useAdStore from '@/store/ads/adStore';
import tiktokService from '@/services/tiktokService';
import type { AdGroupListRequestCamel } from '@/services/interfaces/ads/res';
import { useCheckboxLogic } from '@/hooks/useCheckboxLogic';
import { getSecondaryStatusText } from '@/types/enum';
import { SyncAdGroupListRequest } from '@/services/interfaces/ads/req';
import Pagination from '@/components/ui/PaginationAcc';
import { usePagination } from '@/hooks/usePagination';
import SelectAccount from './selectTable/selectAccount/Select';
import { useStatusAction } from '@/hooks/usedebounce';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Switch,
  Input,
  Button,
  Checkbox,
} from '@/components/ui';
import { confirm } from '@/components/ConfirmDialog';
import { useGroupsWithRelations } from '@/hooks/useGroupsWithRelations';
export const DEFAULT_DAYPARTING = '1'.repeat(336);
export default function GroupContent() {
  const currentAdGroup = useCurrentAdGroup();
  const { adGroups, dateRange } = useAdStore();
  const [exportLoading, setExportLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { selectedRows, sortState, sortedData, handleSelectAll, handleSelect, handleSort } = useTableLogic(
    adGroups.list,
    'id',
    currentAdGroup?.map((item) => item.id) || [],
  );
  const [syncCooldown, setSyncCooldown] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [cooldownTimer, setCooldownTimer] = useState<NodeJS.Timeout | null>(null);
  const [isUpdating, setUpdating] = useState(false);
  const [openAlert, setOpenAlert] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [searchTagText, setSearchTagText] = useState('');
  const { fetchAdGroups, setCurrentCampaign } = useAdActions();
  const [alertType, setAlertType] = useState<'enable' | 'disable' | 'delete'>('enable');
  const [alertTitle, setAlertTitle] = useState('');
  const [alertDescription, setAlertDescription] = useState('');
  const currentAdvertiser = useCurrentAdvertiser(); // 获取当前广告主
  const { setCurrentView, setCurrentAdGroup } = useAdActions();
  const selectedCampaigns = useCurrentCampaign(); // 选择的当前广告系列
  const [operationStatus, setOperationStatus] = useState<string>('All');
  const { campaigns, isLoading: campaignLoading } = useCampaignList(1, 999, { withDateRange: false });
  const handleStatusAction = useStatusAction({
    selectedRows,
    setAlertType,
    setAlertTitle,
    setAlertDescription,
    setOpenAlert,
  });
  const { campaignRelations } = useGroupsWithRelations();
  const { currentPage, pageSize, handlePaginationChange } = usePagination({
    totalItems: adGroups.total || 0,
    initialPageSize: 10,
    initialPage: 1,
    onPaginationChange: async (page, currentPageSize) => {
      await fetchData({
        customPage: page,
        customPageSize: currentPageSize,
      });
    },
  });

  const fetchData = useCallback(
    async (options?: {
      showLoading?: boolean;
      customPage?: number;
      customPageSize?: number;
      customTagNames?: string;
      dateRange?: object;
      operationStatus?: string;
    }) => {
      const { showLoading = true, customPage, customPageSize, customTagNames, operationStatus } = options || {};
      if (showLoading) setIsLoading(true);
      try {
        await fetchAdGroups({
          campaign_ids: selectedCampaigns?.map((c) => String(c.campaignId)),
          page: customPage || currentPage,
          page_size: customPageSize || pageSize,
          tag_names: customTagNames !== undefined ? customTagNames : searchTagText,
          start_time: dateRange?.startTime,
          end_time: dateRange?.endTime,
          operation_status: operationStatus === 'All' ? undefined : operationStatus,
        });
      } finally {
        if (showLoading) setIsLoading(false);
      }
    },
    [selectedCampaigns, currentPage, pageSize, searchTagText, dateRange, fetchAdGroups],
  );

  const [sheetOpen, setSheetOpen] = useState(false);
  const [sheetType, setSheetType] = useState<'create' | 'edit' | 'template'>('create');
  const [editingGroup, setEditingGroup] = useState<{
    id: string;
    jsonDate: any;
    tagNames: string[];
    groupId: string;
  } | null>(null);
  const { handleCheckboxChange, handleAllCheckboxChange } = useCheckboxLogic({
    selectedRows,
    sortedData: sortedData,
    currentItems: currentAdGroup || [],
    setCurrentItems: (items) => {
      const successItems = items.filter((item) => item.pubStatus === 'SUCCESS');
      setCurrentAdGroup(successItems);
    },
    itemKey: 'id',
    handleSelect,
    handleSelectAll,
  });
  const columns = [
    {
      key: 'checkbox',
      title: (
        <Checkbox
          className="h-[14px] w-[14px] border-[#9FA4B2]"
          checked={selectedRows.length === sortedData.length}
          onCheckedChange={(checked) => handleAllCheckboxChange(!!checked)}
        />
      ),
      width: 50,
    },
    { key: 'switch', title: '开关', width: 60, fixed: 'left' as 'left' },
    { key: 'groupName', title: '广告组', width: 200, fixed: 'left' as 'left' },
    { key: 'accountName', title: '广告账户', width: 180 },
    { key: 'campaignName', title: '广告系列', width: 180 },
    { key: 'tagNames', title: '标签', width: 200 },
    { key: 'actions', title: '操作', width: 150, fixed: 'left' as 'left' },
    { key: 'pubStatus', title: '创建状态', width: 100 },
    { key: 'operationStatus', title: '投放状态', width: 120, fixed: 'left' as 'left' },
    { key: 'budget', title: '预算', width: 80, sortable: true },
    { key: 'spend', title: '消耗', width: 80, sortable: true },
    { key: 'cpc', title: '平均点击成本(CPC)', width: 120, sortable: true },
    { key: 'cpm', title: '千次展示成本(CPM)', width: 120, sortable: true },
    { key: 'impressions', title: '展示量', width: 85, sortable: true },
    { key: 'clicks', title: '点击量', width: 80, sortable: true },
    { key: 'purchases', title: '付费数', width: 80, sortable: true },
    // { key: 'checkout', title: '展示转化率', width: 10 , sortable: true },
    { key: 'onsiteShoppingRate', title: '点击转化率', width: 80, sortable: true },
    { key: 'onsiteShoppingRoas', title: 'ROAS', width: 80, sortable: true },
    { key: 'onsiteShoppingValue', title: '总收入', width: 90, sortable: true },
    { key: 'onsiteOnWebCart', title: '加入购物车次数', width: 90, sortable: true },
    // { key: 'ctr', title: '点击率(CTR)', width: 120, sortable: true },
    // { key: 'checkout', title: '开始结账数', width: 10 , sortable: true },
    { key: 'orderValue', title: '平均订单价值', width: 120, sortable: true },
  ];
  const handleCreateClick = () => {
    if (!selectedCampaigns?.length) {
      toast.error('请选择一个或多个广告系列');
      return;
    }
    setSheetType('create');
    setEditingGroup(null);
    setSheetOpen(true);
  };
  const onOperationStatusChange = (value: string) => {
    setOperationStatus(value);
    fetchData({ operationStatus: value, customTagNames: searchTagText });
  };
  const handleEditClick = (id: string, jsonDate: any, tagNames: string[], groupId: string) => {
    setSheetType('edit');
    setEditingGroup({ id, jsonDate, tagNames, groupId });
    setSheetOpen(true);
  };
  const handleAdGroupClick = (adgroup: AdGroupListRequestCamel) => {
    setCurrentView('ad');
    setCurrentAdGroup([adgroup]);
  };
  const renderCell = (key: string, record: any) => {
    switch (key) {
      case 'checkbox':
        return (
          <Checkbox
            className="h-[14px] w-[14px] border-[#9FA4B2]"
            checked={selectedRows.includes(record.id)}
            onCheckedChange={(checked) => handleCheckboxChange(record, !!checked)}
          />
        );
      case 'switch':
        const handleSwitchChange = async (record: any, checked: boolean) => {
          try {
            setUpdating(true);
            const params: any = {
              advertiser_id: record.advertiserId,
              operation_status: checked ? 'ENABLE' : 'DISABLE',
              allow_partial_success: true,
              request_id: uuidv4(),
              ids: [record.id],
            };

            // 只有当 groupId 存在时才添加 adgroup_ids 参数
            if (record.groupId) {
              params.adgroup_ids = [record.groupId];
            }
            const res = await tiktokService.updateAdGroupStatus(params);
            if (res) {
              toast.success('更新状态成功');
              await fetchData({ showLoading: false });
            }
          } catch {
            return;
          } finally {
            setUpdating(false);
          }
        };
        return (
          <div className="w-[60px]">
            <Switch
              className="h-[16px] w-[28px]"
              thumbClassName="bg-white h-3 w-3 data-[state=checked]:translate-x-[12px]"
              checked={record.operationStatus === 'ENABLE'}
              onCheckedChange={(checked) => handleSwitchChange(record, checked)}
              disabled={record.pubStatus !== 'SUCCESS' || isUpdating}
            />
          </div>
        );
      case 'groupName':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className={`${isUpdating || record?.pubStatus !== 'SUCCESS' ? 'pointer-events-none opacity-50' : ''} cursor-pointer truncate`}
                  onClick={() => handleAdGroupClick(record)}
                >
                  {record.groupName}
                  <div className="mt-[2px] text-xs text-gray-500">{record.id}</div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{record.groupName}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );

      case 'tagNames':
        return <div className="truncate">{record.tagNames}</div>;
      case 'actions':
        return (
          <div className="flex gap-6">
            <Button
              variant="link"
              size="sm"
              className={cn(
                'px-0 text-sm hover:no-underline',
                record.pubStatus === 'SUCCESS' ? 'text-[#00E1FF]' : 'cursor-not-allowed text-gray-500',
              )}
              onClick={() => handleEditClick(record.id, record.jsonDate, record.tagNames, record.groupId)}
              disabled={record.pubStatus !== 'SUCCESS' || isUpdating}
            >
              编辑
            </Button>
            <Button
              variant="link"
              size="sm"
              disabled={isUpdating}
              className="px-0 text-sm text-[#00E1FF] hover:no-underline disabled:opacity-50"
              onClick={() => {
                confirm({
                  content: (
                    <div className="space-y-2">
                      <div className="text-base font-medium">确认删除该广告组?</div>
                      <div className="text-sm text-gray-400">删除后将无法恢复，请谨慎操作</div>
                    </div>
                  ),
                  onConfirm: async () => {
                    try {
                      setUpdating(true);
                      const params: any = {
                        advertiser_id: record.advertiserId,
                        operation_status: 'DELETE',
                        allow_partial_success: true,
                        request_id: uuidv4(),
                        ids: [record.id],
                      };
                      // 只有当 groupId 存在时才添加 adgroup_ids 参数
                      if (record.groupId) {
                        params.adgroup_ids = [record.groupId];
                      }
                      const res = await tiktokService.updateAdGroupStatus(params);
                      if (res) {
                        toast.success('删除成功');
                        const newAdGroups = currentAdGroup?.filter((item) => item.groupId !== record.groupId) || [];
                        setCurrentAdGroup(newAdGroups);
                        await fetchData({ showLoading: false });
                      }
                    } catch {
                      return;
                    } finally {
                      setUpdating(false);
                    }
                  },
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      case 'campaignName':
        const campaignRelation = campaignRelations.find((relation) => relation.campaignId === record.campaignId);
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <div className="truncate">{campaignRelation?.campaignName || record.campaignName}</div>
                  <div className="mt-[2px] text-xs text-gray-500">{record?.campaignId}</div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{campaignRelation?.campaignName || record.campaignName}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'accountName':
        const advertiserRelation = campaignRelations.find((relation) => relation.advertiserId === record.advertiserId);
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <div className="truncate">{advertiserRelation?.advertiserName || record.advertiserName}</div>
                  <div className="mt-[2px] text-xs text-gray-500">{record?.advertiserId}</div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{advertiserRelation?.advertiserName || record.advertiserName}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case 'pubStatus':
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 flex-shrink-0 rounded-full ${
                record.pubStatus === 'SUCCESS'
                  ? 'bg-green-500'
                  : record.pubStatus === 'FAIL'
                    ? 'bg-red-500'
                    : 'bg-orange-500'
              }`}
            />
            <div className="flex flex-col items-start overflow-hidden">
              {record.pubStatus === 'SUCCESS' ? '成功' : record.pubStatus === 'FAIL' ? '失败' : '创建中'}
              {record?.message && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="mt-[2px] block truncate text-xs text-gray-500">{record?.message}</span>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{record?.message}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        );
      case 'operationStatus':
        const isEnabled =
          record?.operationStatus === 'ENABLE' || record?.secondaryStatus === 'ADGROUP_STATUS_DELIVERY_OK';
        const DISABLE = record?.operationStatus === 'DISABLE';
        const FROZEN = record?.operationStatus === 'FROZEN';
        return (
          <div>
            <div className={`text-sm ${isEnabled ? 'text-green-500' : DISABLE ? 'text-[#F3A93C]' : 'text-gray-500'}`}>
              {isEnabled ? '开启' : DISABLE ? '已暂停' : FROZEN ? '已冻结' : '未知状态'}
            </div>
            {record.secondaryStatus && (
              <div className="mt-[2px]">
                <div className={`text-xs text-[#9FA4B2]`}>{getSecondaryStatusText(record.secondaryStatus)}</div>
              </div>
            )}
          </div>
        );
      case 'budget':
        return record?.jsonDate?.budget;
      case 'spend':
        return record?.metricsResult?.spend;
      case 'cpc':
        return record?.metricsResult?.cpc;
      case 'cpm':
        return record?.metricsResult?.cpm;
      case 'impressions':
        return record?.metricsResult?.impressions;
      case 'clicks':
        return record?.metricsResult?.clicks;
      case 'purchases':
        return record?.metricsResult?.onsiteShopping;
      case 'onsiteShoppingRate':
        return record?.metricsResult?.onsiteShoppingRate;
      case 'onsiteShoppingRoas':
        return record?.metricsResult?.onsiteShoppingRoas;
      case 'onsiteShoppingValue':
        return record?.metricsResult?.totalOnsiteShoppingValue;
      case 'onsiteOnWebCart':
        return record?.metricsResult?.onsiteOnWebCart;
      case 'ctr':
        return record?.metricsResult?.ctr;
      // case 'checkout':
      //   return record?.metrics?.initiateCheckout;
      case 'orderValue':
        return record?.metricsResult?.valuePerOnsiteShopping;
      default:
        return '';
    }
  };
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      fetchData({ customTagNames: value, operationStatus, dateRange });
    }, 1000),
    [operationStatus, dateRange],
  );
  const onSearch = (value: string) => {
    setSearchTagText(value);
    debouncedSearch(value);
  };

  useEffect(() => {
    fetchData({ dateRange, operationStatus });
  }, [dateRange]);
  const handleGetRealTimeData = async () => {
    if (syncCooldown) {
      toast.error('广告组同步中，请勿重复操作');
      return;
    }
    if (selectedCampaigns?.length === 0) {
      toast.error('请选择广告系列');
      return;
    }
    setSyncLoading(true);
    try {
      const params =
        currentAdvertiser?.flatMap((advertiser) => {
          const today = new Date();
          const lastYear = new Date(today.setFullYear(today.getFullYear() - 1));
          // 设置开始时间为一年前的今天 00:00:00
          const startDate =
            dateRange?.startTime || lastYear.toLocaleDateString('zh-CN').replace(/\//g, '/') + ' 00:00:00';
          // 设置结束时间为今天的 23:59:59
          const endDate =
            dateRange?.endTime || new Date().toLocaleDateString('zh-CN').replace(/\//g, '/') + ' 23:59:59';
          const baseParams = {
            advertiser_id: String(advertiser.advertiserId),
            start_date: startDate,
            end_date: endDate,
          };

          // 如果有选择广告系列
          if (selectedCampaigns?.length) {
            const advertiserCampaigns = selectedCampaigns.filter(
              (campaign) => String(campaign.advertiserId) === String(advertiser.advertiserId),
            );

            // 如果该广告主下没有选中的广告系列，返回基础参数
            if (!advertiserCampaigns.length) {
              return [baseParams];
            }

            // 返回该广告主下所有选中的广告系列参数
            return advertiserCampaigns.map((campaign) => ({
              ...baseParams,
              campaign_id: String(campaign.campaignId),
            }));
          }

          return [baseParams];
        }) || [];

      await tiktokService.getSyncAdGroupList(params as SyncAdGroupListRequest[]);
      toast.success('同步成功');
      setSyncCooldown(true);
      const timer = setTimeout(
        () => {
          setSyncCooldown(false);
        },
        5 * 60 * 1000,
      ); // 5分钟

      setCooldownTimer(timer);
      await fetchData();
    } catch {
      return;
    } finally {
      setSyncLoading(false);
      setSyncCooldown(false);
    }
  };
  const exportAdGroups = async () => {
    setExportLoading(true);
    try {
      await tiktokService.getExportAdGroupList({
        advertiser_ids: currentAdvertiser?.map((advertiser) => String(advertiser?.advertiserId)),
        campaign_ids: selectedCampaigns?.map((c) => String(c.campaignId)),
        tag_names: searchTagText,
        operation_status: operationStatus === 'All' ? '' : operationStatus,
        start_time: dateRange?.startTime,
        end_time: dateRange?.endTime,
        page: currentPage,
        page_size: pageSize,
      });
      toast.success('导出成功');
    } catch {
      return;
    } finally {
      setExportLoading(false);
    }
  };
  useEffect(() => {
    return () => {
      if (cooldownTimer) {
        clearTimeout(cooldownTimer);
      }
    };
  }, [cooldownTimer]);
  const onStatusChange = async (newStatus: string) => {
    try {
      // const params = {
      //   // advertiser_id: currentAdvertiser?.map((advertiser) => String(advertiser?.advertiserId)),
      //   // operation_status: newStatus,
      //   // adgroup_ids: selectedRows.map((row) => row.groupId),
      //   // allow_partial_success: true,
      //   // request_id: uuidv4(),
      //   // ids: selectedRows.map((row) => row.id),
      // };
      // // 转换为数组格式
      // // const res = await tiktokService.updateAdGroupStatus(params);
      // if (res) {
      //   if (newStatus === 'DELETE') {
      //     toast.success('删除成功');
      //   } else {
      //     toast.success('更新状态成功');
      //   }
      //   await fetchAdGroups({
      //     campaign_ids: selectedCampaigns?.map((c) => String(c.campaignId)),
      //     page: currentPage,
      //     page_size: pageSize,
      //     tag_names: searchTagText,
      //     start_time: dateRange?.startTime,
      //     end_time: dateRange?.endTime,
      //   });
      // } else {
      //   return;
      // }
    } catch {
      return;
    }
  };
  // /**
  //  * 处理状态操作
  //  *
  //  * @param type 操作类型，可选值为 'enable'（启用）、'disable'（暂停）、'delete'（删除）
  //  */
  // const handleStatusAction = (type: 'enable' | 'disable' | 'delete') => {
  //   setAlertType(type);
  //   switch (type) {
  //     case 'enable':
  //       setAlertTitle('请确认是否启用？');
  //       setAlertDescription('');
  //       break;
  //     case 'disable':
  //       setAlertTitle('请确认是否暂停？');
  //       setAlertDescription('');
  //       break;
  //     case 'delete':
  //       const runningTasks = selectedRows.filter(
  //         (row: any) => row.jsonDate?.action?.secondaryStatus === 'ADGROUP_STATUS_DELIVERY_OK',
  //       ).length;
  //       setAlertTitle(`确定删除这${selectedRows.length}个任务吗？`);
  //       setAlertDescription(runningTasks > 0 ? '当前选中的任务中包含正在投放的任务，此操作不可逆，请谨慎操作。' : '');
  //       break;
  //   }
  //   setOpenAlert(true);
  // };
  const handleRefreshGroup = () => {
    setIsLoading(true);
    fetchAdGroups({
      campaign_ids: selectedCampaigns?.map((c) => String(c.campaignId)),
      page: currentPage,
      page_size: pageSize,
      tag_names: searchTagText,
      start_time: dateRange?.startTime,
      end_time: dateRange?.endTime,
    }).finally(() => {
      setIsLoading(false);
    });
  };

  const handleCampaignsChange = (value: string[]) => {
    const newCampaigns = campaigns?.list?.filter((item) => {
      return value.includes(item.campaignId);
    });
    setCurrentCampaign(newCampaigns || []);
    fetchAdGroups({
      campaign_ids: value || [],
      page: currentPage,
      page_size: pageSize,
      tag_names: searchTagText,
      start_time: dateRange?.startTime,
      end_time: dateRange?.endTime,
    });
  };

  return (
    <div className="mt-6">
      <div className="flex items-center">
        {campaigns?.list?.length ? (
          <SelectAccount
            onValueChange={handleCampaignsChange}
            currentAdvertiser={selectedCampaigns?.map((item) => item.campaignId) || []}
            advertisers={
              campaigns?.list
                ?.filter((campaigns) => campaigns.pubStatus === 'SUCCESS') // 仅保留pubStatus为SUCCESS的广告系列
                .map((item) => ({
                  label: item.campaignName,
                  value: item.campaignId,
                })) || []
            }
            placeholderTitle="广告系列"
          />
        ) : (
          campaignLoading && <div className="mr-4 h-8 w-[200px] animate-pulse rounded bg-gray-700/50" />
        )}
        <SelectStatus onChange={(value) => onOperationStatusChange(value)} />
        <div className="flex h-8 w-[240px] items-center rounded border border-[#1C2A3F] px-3 text-[12px] text-white">
          <div className="w-12 border-r border-gray-700 text-white">标签</div>
          <Input
            value={searchTagText}
            placeholder="请输入标签名称"
            className="h-full border-0 bg-transparent placeholder:text-xs placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
            onChange={(e) => onSearch?.(e.target.value)}
          />
          {searchTagText && (
            <XIcon
              className="mx-2 h-8 cursor-pointer text-[#9FA4B2]"
              onClick={(event) => {
                event.stopPropagation();
                setSearchTagText('');
                fetchData({
                  customTagNames: '',
                  operationStatus,
                  dateRange,
                });
              }}
            />
          )}
        </div>
      </div>
      <div className="mt-6 flex items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="h-8 rounded px-4 text-[12px] text-xs font-medium text-[#050A1C]"
              onClick={() => {
                if (currentAdvertiser?.length !== 1) {
                  toast.error('请选择一个广告账户');
                  return;
                }
              }}
            >
              <Plus className="mr-1.5 h-4 w-4 flex-shrink-0" />
              创建广告组
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className={cn('min-w-24 px-0 py-2')}>
            <DropdownMenuItem
              className="h-8 w-24 flex-shrink-0 cursor-pointer items-center justify-center hover:bg-[#354054]"
              disabled={currentAdvertiser?.length !== 1}
              onClick={handleCreateClick}
            >
              普通创建
            </DropdownMenuItem>
            <DropdownMenuItem
              className="h-8 w-24 flex-shrink-0 cursor-pointer items-center justify-center hover:bg-[#354054]"
              disabled={currentAdvertiser?.length !== 1}
              onClick={() => setOpenDialog(true)}
            >
              模板创建
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {/* <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-4 h-8 border-gray-700 text-[12px]"
              disabled={selectedRows.length === 0}
            >
              批量操作
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="flex w-[100px] flex-col items-center justify-center">
            <DropdownMenuItem onClick={() => handleStatusAction('enable')}>开启</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusAction('disable')}>暂停</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusAction('delete')}>删除</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu> */}
        <Button
          variant="outline"
          disabled={exportLoading}
          className="ml-4 h-8 rounded border-[#363D54] bg-transparent text-[12px]"
          onClick={exportAdGroups}
        >
          <FileDown className="mr-1.5 h-4 w-4" />
          {exportLoading ? '导出中...' : '导出Excel表格'}
        </Button>
        <Button
          disabled={isLoading}
          variant="outline"
          className="ml-4 h-8 rounded border-[#363D54] bg-transparent text-[12px]"
          onClick={handleRefreshGroup}
        >
          <RefreshCw className="mr-1.5 h-4 w-4" />
          {isLoading ? '刷新中...' : '获取实时数据'}
        </Button>
        {/* <Button
          disabled={syncLoading}
          variant="outline"
          className="ml-4 h-8 border-[#363D54] bg-transparent text-[12px]"
          onClick={handleGetRealTimeData}
        >
          <SyncIcon className="mr-1.5 h-4 w-4" />
          {syncLoading ? '获取中...' : '同步'}
        </Button> */}
      </div>
      <CommonTable
        columns={columns}
        dataSource={sortedData}
        selectedRows={selectedRows}
        sortState={sortState}
        rowKey="adgroup_id"
        onSelectAll={handleSelectAll}
        onSelect={handleSelect}
        onSort={handleSort}
        renderCell={renderCell}
        loading={isLoading}
      />
      <div className="flex justify-end">
        <Pagination
          totalItems={adGroups.total || 0}
          currentPage={currentPage}
          pageSize={pageSize}
          handlePaginationChange={handlePaginationChange}
        />
      </div>
      <TemplateCreation fetchGroupData={fetchData} openDialog={openDialog} setOpenDialog={setOpenDialog} />
      <GroupSheet
        open={sheetOpen}
        DEFAULT_DAYPARTING={DEFAULT_DAYPARTING}
        type={sheetType}
        editingGroup={editingGroup}
        setOpen={setSheetOpen}
        loadingDrawer={false}
        onConfirm={handleRefreshGroup}
      />
      <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            {alertDescription && (
              <AlertDialogDescription className="text-red-500">{alertDescription}</AlertDialogDescription>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                const status = alertType === 'enable' ? 'ENABLE' : alertType === 'disable' ? 'DISABLE' : 'DELETE';
                onStatusChange(status);
                setOpenAlert(false);
              }}
            >
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
