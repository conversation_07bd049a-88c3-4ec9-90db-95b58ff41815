'use server';
import { createAndEmitVideoGenerationTask } from '@/services/domains/task/video-generation-task';
import { TaskStatus } from '@/types/task';
import { materials, Prisma, video_generation_sub_tasks } from '@roasmax/database';
import { ActionContext, server } from '@roasmax/serve';
import { calcGenerateCount, sleep, to } from '@roasmax/utils';

/**
 * 获取视频生成任务分页列表
 */
export const pageVideoGenerationTasks = server(
  '获取视频生成任务分页列表',
  async (
    ctx: ActionContext<{
      pagination: { page: number; limit: number };
      filters?: { name?: string; tmp_created_at?: string; generation_type?: string };
    }>,
  ) => {
    const { pagination, filters } = ctx.data;
    let method: string | undefined;
    if (filters?.generation_type === '其他') {
      filters.generation_type = undefined;
      method = 'normal';
    }
    const where: Prisma.video_generation_tasksWhereInput = {
      ...(filters?.name ? { name: { contains: filters.name } } : {}),
      ...(filters?.tmp_created_at ? { tmp_created_at: { gte: new Date(filters.tmp_created_at) } } : {}),
      ...(filters?.generation_type ? { generation_type: filters.generation_type } : {}),
      ...(method ? { method } : {}),
    };
    const [total, tasks] = await Promise.all([
      ctx.db.video_generation_tasks.count({ where }),
      ctx.db.video_generation_tasks.findMany({
        where,
        take: pagination.limit,
        skip: (pagination.page - 1) * pagination.limit,
        orderBy: { tmp_created_at: 'desc' },
        include: { sub_tasks: { include: { origin_material: true } } },
      }),
    ]);

    const subTasks = tasks.flatMap((task) => task.sub_tasks);

    const generatedMaterials = await ctx.db.materials.findMany({
      where: { id: { in: subTasks.map((subTask) => subTask.generated_material_ids).flat() } },
    });

    return {
      list: tasks.map((task) => {
        const sub_tasks = subTasks
          .filter((subTask) => subTask.task_id === task.id)
          .map((subTask) => {
            const generated_materials = generatedMaterials.filter((m) => subTask.generated_material_ids.includes(m.id));
            return { ...subTask, generated_materials };
          });

        // TODO: 这里的 subTasks，是一个5min切片一个视频，一个原始视频可能有多个切片，需要按照原始视频进行分组，先兼容现有的数据
        const subTasksGroupByOriginMaterial = sub_tasks.reduce<
          Record<
            string,
            video_generation_sub_tasks & {
              origin_material: materials;
              generated_material_ids: string[];
              generated_materials: materials[];
              progress: { processing: number; total: number };
            }
          >
        >((acc, subTask) => {
          // 如果没有原始素材，直接跳过
          if (!subTask.origin_material_id || !subTask.origin_material) {
            return acc;
          }
          const templateCount =
            task.generation_type === '大卖推荐'
              ? typeof task.template_video_tiktok_ids === 'string'
                ? JSON.parse(task.template_video_tiktok_ids).length
                : (task?.template_video_tiktok_ids?.length ?? 0)
              : 0;
          // 如果不存在对应的聚合数据，初始化一个
          if (!acc[subTask.origin_material_id]) {
            const total = calcGenerateCount({
              method: (task.method || 'normal') as 'normal' | 'gc_imitate',
              materialDurations: [subTask.origin_material?.video_duration || 0],
              sliceDuration: task.slice_duration === '300' ? 300 : 1800,
              generateRound: task.generate_round,
              prompts: task.prompts,
              ...(task.method === 'gc_imitate' &&
                task.generation_type && {
                  generationType: task.generation_type,
                  templateCount: templateCount,
                }),
            });
            acc[subTask.origin_material_id] = {
              ...subTask,
              origin_material: subTask.origin_material!,
              generated_material_ids: [],
              generated_materials: [],
              progress: { processing: 0, total: total },
            };
          }
          // 获取当前子任务对应的聚合数据
          const aggregate = acc[subTask.origin_material_id]!;

          // 任务状态默认为 PENDING
          // 子任务状态提升，只有 generation 类型的子任务会影响主任务状态
          // 对状态进行合并处理，只要有一个是进行中，就都是进行中，若没有进行中的，只要有一个是排队中，就都是排队中
          aggregate.status = aggregate.status || TaskStatus.PENDING;
          if (subTask.sub_task_type === 'generation') {
            if (aggregate.status === TaskStatus.PROCESSING || subTask.status === TaskStatus.PROCESSING) {
              aggregate.status = TaskStatus.PROCESSING;
            } else if (aggregate.status === TaskStatus.PENDING || subTask.status === TaskStatus.PENDING) {
              aggregate.status = TaskStatus.PENDING;
            }
          }
          // 对生成素材列表进行合并
          aggregate.generated_material_ids.push(...(subTask.generated_material_ids || []));
          aggregate.generated_materials.push(...subTask.generated_materials);
          // 对进度进行合并
          aggregate.progress.processing += subTask.generated_materials.length;
          return acc;
        }, {});

        return { ...task, subTasks: Object.values(subTasksGroupByOriginMaterial) };
      }),
      pagination: {
        current: pagination.page,
        pageSize: pagination.limit,
        total,
      },
    };
  },
);
/**
 * 获取视频生成任务详情
 */
export const getVideoGenerationTaskById = server(
  '获取视频生成任务详情',
  async (ctx: ActionContext<{ taskId: string }>) => {
    const task = await ctx.db.video_generation_tasks.findUnique({
      where: { id: ctx.data.taskId },
      include: {
        sub_tasks: {
          include: {
            origin_material: true,
          },
        },
      },
    });
    if (!task) {
      throw new Error('任务不存在');
    }

    const subTasks = task?.sub_tasks || [];

    const generatedMaterials = await ctx.db.materials.findMany({
      where: { id: { in: subTasks.map((subTask) => subTask.generated_material_ids).flat() } },
    });

    const sub_tasks = subTasks
      .filter((subTask) => subTask.task_id === task.id)
      .map((subTask) => {
        const generated_materials = generatedMaterials.filter((m) => subTask.generated_material_ids.includes(m.id));
        return { ...subTask, generated_materials };
      });

    const subTasksGroupByOriginMaterial = sub_tasks.reduce<
      Record<
        string,
        video_generation_sub_tasks & {
          origin_material: materials;
          generated_material_ids: string[];
          generated_materials: materials[];
          progress: { processing: number; total: number };
        }
      >
    >((acc, subTask) => {
      // 如果没有原始素材，直接跳过
      if (!subTask.origin_material_id || !subTask.origin_material) {
        return acc;
      }
      const templateCount =
        task.generation_type === '大卖推荐'
          ? typeof task.template_video_tiktok_ids === 'string'
            ? JSON.parse(task.template_video_tiktok_ids).length
            : (task?.template_video_tiktok_ids?.length ?? 0)
          : 0;

      // 如果不存在对应的聚合数据，初始化一个
      if (!acc[subTask.origin_material_id]) {
        const total = calcGenerateCount({
          method: (task.method || 'normal') as 'normal' | 'gc_imitate',
          materialDurations: [subTask.origin_material?.video_duration || 0],
          sliceDuration: task.slice_duration === '300' ? 300 : 1800,
          generateRound: task.generate_round,
          prompts: task.prompts,
          ...(task.method === 'gc_imitate' &&
            task.generation_type && {
              generationType: task.generation_type,
              templateCount: templateCount,
            }),
        });
        acc[subTask.origin_material_id] = {
          ...subTask,
          origin_material: subTask.origin_material!,
          generated_material_ids: [],
          generated_materials: [],
          progress: { processing: 0, total: total },
        };
      }
      // 获取当前子任务对应的聚合数据
      const aggregate = acc[subTask.origin_material_id]!;

      // 对状态进行合并处理，只要有一个是进行中，就都是进行中
      if (aggregate.status === TaskStatus.PROCESSING || subTask.status === TaskStatus.PROCESSING) {
        aggregate.status = TaskStatus.PROCESSING;
      }
      // 对生成素材列表进行合并
      aggregate.generated_material_ids.push(...(subTask.generated_material_ids || []));
      aggregate.generated_materials.push(...subTask.generated_materials);
      // 对进度进行合并
      aggregate.progress.processing += subTask.generated_materials.length;
      return acc;
    }, {});

    return { ...task, subTasks: Object.values(subTasksGroupByOriginMaterial) };
  },
);
/**
 * 创建视频生成任务
 */
export const startVideoGenerationTask = server(
  '创建视频生成任务',
  async (ctx: ActionContext<Parameters<typeof createAndEmitVideoGenerationTask>[0]['data']>) => {
    return await ctx.execute(createAndEmitVideoGenerationTask, ctx.data);
  },
);

/**
 * 获取提示词列表
 */
export const listPrompts = server(
  '获取提示词列表',
  async (ctx: ActionContext<{ name?: string; label?: string; tag?: string; page?: number; limit?: number }>) => {
    const res = await ctx.langfuseOpenApi.listPrompts({
      name: ctx.data.name,
      label: ctx.data.label,
      tag: ctx.data.tag,
    });
    return res;
  },
);

/**
 * 获取大V模板列表
 */
export const listKolStyles = server(
  '获取大V模板列表',
  async (ctx: ActionContext<{ name?: string; page?: number; limit?: number }>) => {
    const [_, res] = await to(
      ctx.request.get<{
        status: boolean;
        message: string;
        data: { kol: string; industry: string; industry_id: number; native_language: string; category: string }[];
      }>(`${process.env.DIFY_WORKFLOW_GC_CONFIG_HOST}/config/list/kol_config`),
    );
    return res?.data.data || [];
  },
);

export const fetchMatchedVideoTemplates = server(
  '获取匹配的视频模板信息',
  async (ctx: ActionContext<{ productUrl: string }>) => {
    const res = {
      data: {
        task_id: '97e8d88e-ea0e-4fb4-9124-1df4ea0e02fd',
        workflow_run_id: 'e8ebe46a-b3b2-4948-9588-81cccce90dbe',
        data: {
          id: 'e8ebe46a-b3b2-4948-9588-81cccce90dbe',
          workflow_id: 'f2d1ce25-5d6a-43ad-bda5-3b33e5adafde',
          status: 'succeeded',
          outputs: {
            data: {
              matched_video_info: [
                {
                  视频id: '7451952304745434414',
                  播放量: '500900',
                  点赞数: '10500',
                  评论数: '82',
                  推荐指数: '⭐⭐⭐',
                  商品名称: 'Micro Ingredients Lions Mane with L-Ergothioneine',
                },
                {
                  视频id: '7454718364548680990',
                  播放量: '58146',
                  点赞数: '1691',
                  评论数: '17',
                  推荐指数: '⭐⭐⭐⭐⭐',
                  商品名称: 'Lipozem',
                },
                {
                  视频id: '7456193939557715242',
                  播放量: '51175',
                  点赞数: '3111',
                  评论数: '162',
                  推荐指数: '⭐⭐⭐',
                  商品名称: 'Lipozem',
                },
              ],
              recommend: {
                内容匹配度分析:
                  '\n### 商品类别匹配度\n1. 视频2 ⭐⭐⭐⭐⭐\n- 同为保健品补充剂类目\n- 主打能量提升、代谢改善效果\n- 目标受众群体高度重合\n\n2. 视频1 ⭐⭐⭐\n- 同属保健品品类\n- 针对神经系统和大脑功能\n- 部分功效与镁元素补充相关\n\n### 内容契合度\n1. 视频2 ⭐⭐⭐⭐⭐ \n- 直接讲解代谢问题和能量提升\n- 强调自然成分和安全性\n- 突出改善睡眠等效果，与镁元素功效高度相关\n\n2. 视频3 ⭐⭐⭐\n- 强调天然成分的重要性\n- 突出健康生活方式\n- 关注整体健康改善\n\n### 表现形式适用性\n1. 视频2 ⭐⭐⭐⭐\n- 专业知识讲解形式\n- 清晰的问题-解决方案结构\n- 突出产品核心成分和功效\n',
                重点模仿元素:
                  '\n### 叙事结构\n1. 视频2 ⭐⭐⭐⭐⭐\n- 问题引入：代谢缓慢的危害\n- 解决方案：三步骤改善方法\n- 产品介绍：具体成分和使用方法\n- 效果展示：用户反馈和变化\n\n### 商品展示\n1. 视频2 ⭐⭐⭐⭐\n- 重点突出核心成分\n- 强调使用简便性\n- 价格优势对比\n\n### 卖点呈现\n1. 视频2 ⭐⭐⭐⭐⭐\n- 专业术语解释通俗化\n- 多重效果逐一展示\n- 使用场景具象化\n',
                建议优化:
                  '\n### 最佳借鉴模板\n1. 视频2 ⭐⭐⭐⭐⭐\n- 专业性与通俗性结合\n- 清晰的问题解决框架\n- 产品功效逐一展示\n\n### 重点模仿元素\n1. 视频2 ⭐⭐⭐⭐\n- 三步骤讲解结构\n- 专业知识普及方式\n- 效果对比展示\n\n### 建议改进\n1. 增加具体使用场景展示\n2. 加入更多用户真实反馈\n3. 突出镁元素三种形态的独特优势\n4. 强化睡眠改善效果的展示\n',
              },
            },
          },
          error: null,
          elapsed_time: 64.49876050883904,
          total_tokens: 5767,
          total_steps: 17,
          created_at: 1737027556,
          finished_at: 1737027621,
        },
      },
    };
    // const res = await ctx.request.post<{
    //   task_id: string;
    //   workflow_run_id: string;
    //   data: {
    //     id: string;
    //     workflow_id: string;
    //     status: 'succeeded' | 'failed';
    //     outputs: {
    //       data: {
    //         matched_video_info: {
    //           视频id: string;
    //           播放量: string;
    //           点赞数: string;
    //           评论数: string;
    //           推荐指数: string;
    //           商品名称: string;
    //         }[];
    //         recommend: Record<string, string>;
    //       };
    //     };
    //   };
    // }>(
    //   `https://dify.bowongai.com/v1/workflows/run`,
    //   { inputs: { product_url: ctx.data.productUrl }, response_mode: 'blocking', user: 'system' },
    //   { headers: { Authorization: `Bearer app-66W1ll9LiJbNBDeym5QYu1as`, 'Content-Type': 'application/json' } },
    // );
    return res.data.data.outputs.data;
  },
);

/**
 * 获取生成成功的任务总数
 */
export const getSuccessfulTaskCount = server('获取生成成功的任务总数', async (ctx: ActionContext<{}>) => {
  const count = await ctx.db.video_generation_tasks.count({
    where: {
      sub_tasks: {
        every: {
          status: TaskStatus.SUCCESS,
        },
      },
    },
  });

  return count;
});

export const getSuccessVideoCount = server('获取生成成功的视频总数', async (ctx: ActionContext<{}>) => {
  const count = await ctx.db.$queryRaw<{ total_generated_videos: number }[]>`
  SELECT SUM(JSON_LENGTH(generated_material_ids)) as total_generated_videos
  FROM video_generation_sub_tasks where tenant_id = ${ctx.tenant.id};`;

  return count[0]?.total_generated_videos || 0;
});

/**
 * 获取生成中的任务总数
 */
export const getPendingTaskCount = server('获取生成中的任务总数', async (ctx: ActionContext<{}>) => {
  const count = await ctx.db.video_generation_tasks.count({
    where: {
      sub_tasks: {
        some: {
          status: { in: [TaskStatus.PENDING, TaskStatus.PROCESSING] },
        },
      },
    },
  });

  return count;
});
