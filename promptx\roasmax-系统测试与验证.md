# Roasmax - 系统测试与验证

## 📋 任务概述

**任务ID**: oNWvGCx9BgwQjwM1At4fcX  
**任务名称**: 系统测试与验证  
**优先级**: 高  
**预估工时**: 2-3天  
**前置依赖**: 权限系统重构  

## 🎯 任务目标

全面测试新认证系统的功能和性能，确保系统稳定可靠，满足生产环境要求。

## 📊 详细任务分解

### 1. 功能测试

#### 1.1 认证功能测试
```typescript
// tests/auth/authentication.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LocalAuthenticationClient } from '@/adapters/LocalAuthenticationClient';
import { PasswordManager } from '@/utils/password';
import { JWTManager } from '@/utils/jwt';
import { prisma } from '@/utils/prisma';

describe('认证功能测试', () => {
  let authClient: LocalAuthenticationClient;
  let testUser: any;
  
  beforeEach(async () => {
    authClient = new LocalAuthenticationClient({});
    
    // 创建测试用户
    const { hash, salt } = await PasswordManager.hashPassword('Test123456');
    testUser = await prisma.members.create({
      data: {
        id: 'test-user-id',
        tenant_id: 'test-tenant',
        user_id: 'test-user-001',
        email: '<EMAIL>',
        nickname: 'Test User',
        account: 'testuser',
        user_status: 'active',
        admin: 0,
        password: hash,
        password_hash: hash,
        salt,
        password_reset_required: false,
        is_migrated: true,
        email_verified: true
      }
    });
  });
  
  afterEach(async () => {
    // 清理测试数据
    await prisma.members.deleteMany({
      where: { email: '<EMAIL>' }
    });
  });
  
  it('应该能够成功登录', async () => {
    const result = await authClient.signInByEmailPassword({
      email: '<EMAIL>',
      password: 'Test123456'
    });
    
    expect(result.statusCode).toBe(200);
    expect(result.data.access_token).toBeDefined();
    expect(result.data.user.email).toBe('<EMAIL>');
  });
  
  it('应该拒绝错误密码', async () => {
    await expect(
      authClient.signInByEmailPassword({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    ).rejects.toThrow('密码错误');
  });
  
  it('应该拒绝不存在的用户', async () => {
    await expect(
      authClient.signInByEmailPassword({
        email: '<EMAIL>',
        password: 'Test123456'
      })
    ).rejects.toThrow('用户不存在');
  });
  
  it('应该能够获取用户信息', async () => {
    const token = JWTManager.generateToken({
      user_id: testUser.user_id,
      email: testUser.email,
      nickname: testUser.nickname,
      tenant_id: testUser.tenant_id
    });
    
    authClient.setAccessToken(token);
    
    const result = await authClient.getProfile({
      withCustomData: true
    });
    
    expect(result.statusCode).toBe(200);
    expect(result.data.email).toBe('<EMAIL>');
  });
  
  it('应该能够修改密码', async () => {
    const token = JWTManager.generateToken({
      user_id: testUser.user_id,
      email: testUser.email,
      nickname: testUser.nickname,
      tenant_id: testUser.tenant_id
    });
    
    authClient.setAccessToken(token);
    
    const result = await authClient.updatePassword({
      oldPassword: 'Test123456',
      newPassword: 'NewTest123456'
    });
    
    expect(result.statusCode).toBe(200);
    
    // 验证新密码可以登录
    const loginResult = await authClient.signInByEmailPassword({
      email: '<EMAIL>',
      password: 'NewTest123456'
    });
    
    expect(loginResult.statusCode).toBe(200);
  });
});
```

#### 1.2 用户管理功能测试
```typescript
// tests/auth/user-management.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LocalManagementClient } from '@/adapters/LocalManagementClient';
import { prisma } from '@/utils/prisma';

describe('用户管理功能测试', () => {
  let managementClient: LocalManagementClient;
  let testTenant: string;
  
  beforeEach(async () => {
    managementClient = new LocalManagementClient({});
    testTenant = 'test-tenant-001';
    
    // 创建测试角色
    await prisma.roles.create({
      data: {
        id: 'test-role-id',
        tenant_id: testTenant,
        code: 'test_user',
        name: '测试用户',
        description: '测试角色',
        is_system: false
      }
    });
  });
  
  afterEach(async () => {
    // 清理测试数据
    await prisma.user_roles.deleteMany({
      where: { tenant_id: testTenant }
    });
    await prisma.members.deleteMany({
      where: { tenant_id: testTenant }
    });
    await prisma.roles.deleteMany({
      where: { tenant_id: testTenant }
    });
  });
  
  it('应该能够创建用户', async () => {
    const result = await managementClient.createUser({
      email: '<EMAIL>',
      nickname: 'New User',
      username: 'newuser',
      password: 'Test123456',
      tenantIds: [testTenant]
    });
    
    expect(result.statusCode).toBe(200);
    expect(result.data.userId).toBeDefined();
    expect(result.data.email).toBe('<EMAIL>');
  });
  
  it('应该能够获取用户列表', async () => {
    // 创建测试用户
    await managementClient.createUser({
      email: '<EMAIL>',
      nickname: 'User 1',
      username: 'user1',
      password: 'Test123456',
      tenantIds: [testTenant]
    });
    
    await managementClient.createUser({
      email: '<EMAIL>',
      nickname: 'User 2',
      username: 'user2',
      password: 'Test123456',
      tenantIds: [testTenant]
    });
    
    const result = await managementClient.listGroupMembers({
      code: testTenant,
      page: 1,
      limit: 10
    });
    
    expect(result.statusCode).toBe(200);
    expect(result.data.list.length).toBe(2);
    expect(result.data.totalCount).toBe(2);
  });
  
  it('应该能够分配用户角色', async () => {
    // 创建用户
    const userResult = await managementClient.createUser({
      email: '<EMAIL>',
      nickname: 'Role User',
      username: 'roleuser',
      password: 'Test123456',
      tenantIds: [testTenant]
    });
    
    // 分配角色
    const assignResult = await managementClient.assignRole({
      code: 'test_user',
      namespace: testTenant,
      targets: [{
        targetType: 'USER',
        targetIdentifier: userResult.data.userId
      }]
    });
    
    expect(assignResult.statusCode).toBe(200);
    
    // 验证角色分配
    const rolesResult = await managementClient.getUserRoles({
      userId: userResult.data.userId,
      userIdType: 'user_id',
      namespace: testTenant
    });
    
    expect(rolesResult.statusCode).toBe(200);
    expect(rolesResult.data.list.length).toBe(1);
    expect(rolesResult.data.list[0].code).toBe('test_user');
  });
});
```

#### 1.3 权限系统测试
```typescript
// tests/auth/permission.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PermissionService } from '@/services/PermissionService';
import { prisma } from '@/utils/prisma';

describe('权限系统测试', () => {
  let permissionService: PermissionService;
  let testUser: any;
  let testTenant: string;
  
  beforeEach(async () => {
    permissionService = new PermissionService();
    testTenant = 'test-tenant-002';
    
    // 创建测试用户
    testUser = await prisma.members.create({
      data: {
        id: 'perm-test-user',
        tenant_id: testTenant,
        user_id: 'perm-user-001',
        email: '<EMAIL>',
        nickname: 'Permission User',
        account: 'permuser',
        user_status: 'active',
        admin: 0,
        password: 'hashedpassword',
        password_reset_required: false,
        is_migrated: true
      }
    });
    
    // 创建测试角色
    await prisma.roles.create({
      data: {
        id: 'perm-test-role',
        tenant_id: testTenant,
        code: 'editor',
        name: '编辑者',
        description: '编辑权限',
        is_system: false
      }
    });
    
    // 分配角色
    await prisma.user_roles.create({
      data: {
        id: 'perm-test-user-role',
        tenant_id: testTenant,
        user_id: testUser.user_id,
        role_code: 'editor'
      }
    });
    
    // 创建权限
    await prisma.permissions.create({
      data: {
        id: 'perm-test-permission',
        tenant_id: testTenant,
        role_code: 'editor',
        resource_type: 'MENU',
        resource_code: 'content_management',
        actions: ['read', 'write']
      }
    });
  });
  
  afterEach(async () => {
    // 清理测试数据
    await prisma.permissions.deleteMany({
      where: { tenant_id: testTenant }
    });
    await prisma.user_roles.deleteMany({
      where: { tenant_id: testTenant }
    });
    await prisma.roles.deleteMany({
      where: { tenant_id: testTenant }
    });
    await prisma.members.deleteMany({
      where: { tenant_id: testTenant }
    });
    
    // 清理缓存
    permissionService.clearAllCache();
  });
  
  it('应该能够检查用户权限', async () => {
    const hasReadPermission = await permissionService.hasPermission(
      testUser.user_id,
      testTenant,
      'content_management',
      'read'
    );
    
    const hasWritePermission = await permissionService.hasPermission(
      testUser.user_id,
      testTenant,
      'content_management',
      'write'
    );
    
    const hasDeletePermission = await permissionService.hasPermission(
      testUser.user_id,
      testTenant,
      'content_management',
      'delete'
    );
    
    expect(hasReadPermission).toBe(true);
    expect(hasWritePermission).toBe(true);
    expect(hasDeletePermission).toBe(false);
  });
  
  it('应该能够检查用户角色', async () => {
    const hasEditorRole = await permissionService.hasRole(
      testUser.user_id,
      testTenant,
      'editor'
    );
    
    const hasAdminRole = await permissionService.hasRole(
      testUser.user_id,
      testTenant,
      'admin'
    );
    
    expect(hasEditorRole).toBe(true);
    expect(hasAdminRole).toBe(false);
  });
  
  it('应该能够获取用户完整权限', async () => {
    const userPermissions = await permissionService.getUserPermissions(
      testUser.user_id,
      testTenant
    );
    
    expect(userPermissions.userId).toBe(testUser.user_id);
    expect(userPermissions.tenantId).toBe(testTenant);
    expect(userPermissions.roles).toContain('editor');
    expect(userPermissions.resources['content_management']).toContain('read');
    expect(userPermissions.resources['content_management']).toContain('write');
  });
  
  it('权限缓存应该正常工作', async () => {
    // 第一次查询
    const start1 = Date.now();
    await permissionService.getUserPermissions(testUser.user_id, testTenant);
    const time1 = Date.now() - start1;
    
    // 第二次查询（应该使用缓存）
    const start2 = Date.now();
    await permissionService.getUserPermissions(testUser.user_id, testTenant);
    const time2 = Date.now() - start2;
    
    // 缓存查询应该更快
    expect(time2).toBeLessThan(time1);
  });
});
```

### 2. 性能测试

#### 2.1 认证性能测试
```typescript
// tests/performance/auth-performance.test.ts
import { describe, it, expect } from 'vitest';
import { LocalAuthenticationClient } from '@/adapters/LocalAuthenticationClient';
import { PermissionService } from '@/services/PermissionService';

describe('认证性能测试', () => {
  it('登录响应时间应该小于500ms', async () => {
    const authClient = new LocalAuthenticationClient({});
    
    const start = Date.now();
    
    try {
      await authClient.signInByEmailPassword({
        email: '<EMAIL>',
        password: 'Test123456'
      });
    } catch (error) {
      // 忽略认证错误，只测试响应时间
    }
    
    const responseTime = Date.now() - start;
    expect(responseTime).toBeLessThan(500);
  });
  
  it('权限检查响应时间应该小于100ms', async () => {
    const permissionService = new PermissionService();
    
    const start = Date.now();
    
    try {
      await permissionService.hasPermission(
        'test-user-id',
        'test-tenant',
        'test-resource',
        'read'
      );
    } catch (error) {
      // 忽略权限错误，只测试响应时间
    }
    
    const responseTime = Date.now() - start;
    expect(responseTime).toBeLessThan(100);
  });
  
  it('并发登录测试', async () => {
    const authClient = new LocalAuthenticationClient({});
    const concurrentRequests = 10;
    
    const promises = Array.from({ length: concurrentRequests }, () =>
      authClient.signInByEmailPassword({
        email: '<EMAIL>',
        password: 'Test123456'
      }).catch(() => null) // 忽略错误
    );
    
    const start = Date.now();
    await Promise.all(promises);
    const totalTime = Date.now() - start;
    
    // 平均每个请求应该小于1秒
    const avgTime = totalTime / concurrentRequests;
    expect(avgTime).toBeLessThan(1000);
  });
});
```

### 3. 集成测试

#### 3.1 端到端测试
```typescript
// tests/e2e/auth-flow.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { chromium, Browser, Page } from 'playwright';

describe('认证流程端到端测试', () => {
  let browser: Browser;
  let page: Page;
  
  beforeAll(async () => {
    browser = await chromium.launch();
    page = await browser.newPage();
  });
  
  afterAll(async () => {
    await browser.close();
  });
  
  it('完整登录流程测试', async () => {
    // 访问登录页面
    await page.goto('http://localhost:3000/login');
    
    // 填写登录表单
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123456');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待跳转到首页
    await page.waitForURL('http://localhost:3000/');
    
    // 验证登录成功
    const userAvatar = await page.locator('[data-testid="user-avatar"]');
    await expect(userAvatar).toBeVisible();
  });
  
  it('密码重置流程测试', async () => {
    // 访问登录页面
    await page.goto('http://localhost:3000/login');
    
    // 填写需要重置密码的用户信息
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'oldpassword');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待密码重置对话框出现
    const resetDialog = await page.locator('[data-testid="password-reset-dialog"]');
    await expect(resetDialog).toBeVisible();
    
    // 填写新密码
    await page.fill('input[id="newPassword"]', 'NewTest123456');
    await page.fill('input[id="confirmPassword"]', 'NewTest123456');
    
    // 点击设置密码按钮
    await page.click('button[type="submit"]');
    
    // 等待跳转到首页
    await page.waitForURL('http://localhost:3000/');
    
    // 验证登录成功
    const userAvatar = await page.locator('[data-testid="user-avatar"]');
    await expect(userAvatar).toBeVisible();
  });
  
  it('权限控制测试', async () => {
    // 以普通用户身份登录
    await page.goto('http://localhost:3000/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123456');
    await page.click('button[type="submit"]');
    
    // 尝试访问管理员页面
    await page.goto('http://localhost:3000/admin/users');
    
    // 应该显示权限不足提示
    const errorMessage = await page.locator('[data-testid="permission-denied"]');
    await expect(errorMessage).toBeVisible();
  });
});
```

### 4. 安全测试

#### 4.1 安全漏洞测试
```typescript
// tests/security/security.test.ts
import { describe, it, expect } from 'vitest';
import { LocalAuthenticationClient } from '@/adapters/LocalAuthenticationClient';
import { JWTManager } from '@/utils/jwt';

describe('安全测试', () => {
  it('应该防止SQL注入攻击', async () => {
    const authClient = new LocalAuthenticationClient({});
    
    // 尝试SQL注入
    await expect(
      authClient.signInByEmailPassword({
        email: "'; DROP TABLE members; --",
        password: 'Test123456'
      })
    ).rejects.toThrow();
  });
  
  it('应该防止暴力破解攻击', async () => {
    const authClient = new LocalAuthenticationClient({});
    
    // 连续5次错误登录
    for (let i = 0; i < 5; i++) {
      try {
        await authClient.signInByEmailPassword({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      } catch (error) {
        // 预期的错误
      }
    }
    
    // 第6次应该被锁定
    await expect(
      authClient.signInByEmailPassword({
        email: '<EMAIL>',
        password: 'Test123456' // 即使密码正确也应该被拒绝
      })
    ).rejects.toThrow('账户已被锁定');
  });
  
  it('应该验证JWT令牌完整性', async () => {
    // 生成有效令牌
    const validToken = JWTManager.generateToken({
      user_id: 'test-user',
      email: '<EMAIL>',
      nickname: 'Test User',
      tenant_id: 'test-tenant'
    });
    
    // 篡改令牌
    const tamperedToken = validToken.slice(0, -10) + 'tampered123';
    
    // 验证篡改的令牌应该失败
    const result = await JWTManager.verifyToken(tamperedToken);
    expect(result).toBeNull();
  });
  
  it('应该验证令牌过期时间', async () => {
    // 创建已过期的令牌（手动设置过期时间）
    const expiredPayload = {
      sub: 'test-user',
      data: {
        id: 'test-user',
        email: '<EMAIL>'
      },
      iat: Math.floor(Date.now() / 1000) - 3600, // 1小时前
      exp: Math.floor(Date.now() / 1000) - 1800  // 30分钟前过期
    };
    
    const expiredToken = jwt.sign(expiredPayload, process.env.APPSECRET!);
    
    // 验证过期令牌应该失败
    const result = await JWTManager.verifyToken(expiredToken);
    expect(result).toBeNull();
  });
});
```

## ✅ 验收标准

1. **功能测试通过率**
   - [ ] 认证功能测试通过率 100%
   - [ ] 用户管理功能测试通过率 100%
   - [ ] 权限系统测试通过率 100%

2. **性能测试达标**
   - [ ] 登录响应时间 < 500ms
   - [ ] 权限检查响应时间 < 100ms
   - [ ] 并发处理能力满足要求

3. **安全测试通过**
   - [ ] 无SQL注入漏洞
   - [ ] 防暴力破解机制有效
   - [ ] JWT令牌安全可靠

4. **集成测试成功**
   - [ ] 端到端流程测试通过
   - [ ] 与现有系统集成正常
   - [ ] 用户体验符合预期

## 🔧 Augment Code 提示词

```
请帮我为 Roasmax 项目的新认证系统编写全面的测试：

1. 编写认证功能的单元测试
2. 编写用户管理功能的集成测试
3. 编写权限系统的功能测试
4. 编写性能测试用例
5. 编写安全测试用例
6. 编写端到端测试
7. 创建测试数据和测试环境配置

要求：
- 覆盖所有核心功能
- 包含性能和安全测试
- 提供详细的测试报告
- 支持自动化测试执行
- 包含测试数据管理
- 确保测试的可重复性

请提供完整的测试代码和配置。
```

## 📅 时间安排

- **第1天**: 功能测试和单元测试
- **第2天**: 性能测试和安全测试
- **第3天**: 集成测试和端到端测试

## 🚨 风险提示

1. **测试覆盖率风险**: 可能遗漏关键功能的测试
2. **测试环境风险**: 测试环境与生产环境差异
3. **数据安全风险**: 测试数据可能泄露敏感信息
