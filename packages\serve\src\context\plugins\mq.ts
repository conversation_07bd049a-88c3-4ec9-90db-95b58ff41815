import { MqClient } from '@roasmax/utils/net';
import { ActionContextPluginLoader } from '../../types';

const mqPlugin: ActionContextPluginLoader = (context) => {
  const mq = new MqClient({
    host: process.env.BULLMQ_ENDPOINT || '',
    authorization: process.env.BULLMQ_AUTHORIZATION || '',
    request: context.request,
  });

  return {
    name: 'mq',
    plugin: mq,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 消息队列操作API
     */
    mq: MqClient;
  }
}

export default mqPlugin;
