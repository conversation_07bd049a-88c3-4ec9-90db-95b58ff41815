import { prisma } from '@/utils/prisma';
import { ISyncHandlerParams } from '.';
import { mutex } from './mutex';

export enum TaskStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

/**
 * 获取需要同步的租户列表
 * @param params
 * @returns
 */
export const fetchTenants = async (
  params: Pick<ISyncHandlerParams<'tenant'>, 'tenantIds' | 'all' | 'ignoreTenantIds'>,
) => {
  if (params.all) {
    const list = await prisma.source_configs.findMany();
    if (params.ignoreTenantIds?.length) {
      return list.filter((item) => !params.ignoreTenantIds!.includes(item.tenant_id));
    }
    return list;
  }
  if (params.tenantIds?.length) {
    return prisma.source_configs.findMany({ where: { tenant_id: { in: params.tenantIds } } });
  }

  return [];
};

/**
 * 获取需要同步的任务列表
 * @param params
 * @returns
 */
export const fetchTasks = (params: Pick<ISyncHandlerParams<'trace'>, 'traceType' | 'traceIds'>) => {
  if (params.traceType === 'slice') {
    return prisma.video_slice_tasks.findMany({ where: { trace_id: { in: params.traceIds } } });
  } else if (params.traceType === 'generation') {
    return prisma.video_generation_sub_tasks.findMany({ where: { trace_id: { in: params.traceIds } } });
  }
  return [];
};

/**
 * 任务失败时，需要进行退点
 */
export const refundQuota = async (params: { tenantId: string; quota: number; reason: string }) => {
  if (params.quota === 0) {
    return;
  }
  await mutex.quota.runExclusive(async () => {
    await prisma.$transaction(async (trx) => {
      //获取租户的所有活动计划
      const activePlans = await trx.pricing_plans.findMany({
        where: {
          tenant_id: params.tenantId,
        },
        select: {
          id: true,
          quota: true,
          plan_name: true,
        },
        orderBy: {
          end_time: 'desc',
        },
      });

      if (!activePlans.length) {
        throw new Error('当前用户没有可用套餐');
      }

      //将计划分为普通用户和非普通用户
      const normalPlan = activePlans.find((plan) => plan.plan_name === '普通用户');
      const pricingPlan = activePlans.find((plan) => plan.plan_name !== '普通用户');

      //计算总配额
      const selectedPlan = (pricingPlan ?? normalPlan)!;
      const resultQuota = (pricingPlan ? pricingPlan.quota : normalPlan?.quota)! + params.quota;

      // 更新所选计划的配额
      await trx.pricing_plans.update({
        where: {
          tenant_id: params.tenantId,
          id: selectedPlan.id,
        },
        data: {
          quota: resultQuota,
        },
      });

      await trx.quota_changelogs.create({
        data: {
          tenant_id: params.tenantId,
          pricing_plan_id: selectedPlan.id,
          change_type: 'REFUND',
          change_reason: params.reason,
          quota: params.quota,
          result_quota: resultQuota + (pricingPlan ? normalPlan?.quota || 0 : 0),
        },
      });
    });
  });
};

/**
 * 按照租户列表处理任务
 * @param params
 */
export const handleTaskByTenants = async (params: {
  name: string;
  tenantIds: string[];
  handler: (tenantId: string) => Promise<void>;
}) => {
  console.log(`开始处理任务[${params.name}], 数量：`, params.tenantIds.length);
  for (let i = 0; i < params.tenantIds.length; i++) {
    const tenantId = params.tenantIds[i]!;
    console.log(`开始处理任务[${params.name}]`, tenantId, `(${i + 1} / ${params.tenantIds.length})`);
    try {
      await params.handler(tenantId);
    } catch (e) {
      console.error(`处理任务失败[${params.name}]`, tenantId, e);
    }
  }
  console.log(`处理任务完成[${params.name}]`);
};

/**
 * 获取任务目录 若不存在则创建
 */
export const fetchDir = async (params: { tenantId: string; taskDirName: string }) => {
  return await mutex.file.runExclusive(async () => {
    return await prisma.$transaction(async (trx) => {
      // 生成素材文件夹
      const generatedDir = await trx.material_directories.findFirst({
        where: { tenant_id: params.tenantId, name: '生成素材' },
      });
      if (!generatedDir) {
        throw new Error('生成素材文件夹不存在');
      }

      // 任务目录
      let taskDir = await trx.material_directories.findFirst({
        where: { tenant_id: params.tenantId, parent_id: generatedDir.id, name: params.taskDirName },
      });
      if (!taskDir) {
        taskDir = await trx.material_directories.create({
          data: {
            name: params.taskDirName,
            parent_id: generatedDir.id,
            tenant_id: params.tenantId,
          },
        });
      }
      return taskDir;
    });
  });
};
