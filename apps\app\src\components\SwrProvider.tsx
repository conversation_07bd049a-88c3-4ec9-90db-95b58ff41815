'use client';
import { ReactNode } from 'react';
import { SWRConfig } from 'swr';

const localStorageProvider = () => {
  const apiKeysToCache = ['/api/materials/roots', '/api/ads/advertisers', '/api/ads/campaigns']; // 需要缓存的 key 白名单
  if (typeof window === 'undefined') {
    return new Map();
  }

  const map = new Map<string, any>(JSON.parse(localStorage.getItem('BWAI_APP_CACHE') || '[]'));
  window.addEventListener('beforeunload', () => {
    const cacheMap = new Map<string, any>();
    for (const key of apiKeysToCache) {
      cacheMap.set(key, map.get(key));
    }
    const appCache = JSON.stringify(Array.from(cacheMap.entries()));
    localStorage.setItem('BWAI_APP_CACHE', appCache);
  });

  return map;
};

export default function SwrProvider({ children }: { children: ReactNode }) {
  return (
    <SWRConfig
      value={{
        provider: localStorageProvider,
      }}
    >
      {children}
    </SWRConfig>
  );
}
