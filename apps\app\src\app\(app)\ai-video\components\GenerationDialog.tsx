import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Footer, DialogHeader, DialogTitle, Progress, Button } from '@/components/ui';
import type React from 'react';
import { useState, useEffect } from 'react';
import type { QueryTaskResponse } from '../types';

interface GenerationDialogProps {
  isOpen: boolean;
  task: QueryTaskResponse | null;
  isGenerating: boolean;
  error: string | null;
  onClose: () => void;
  onCancel: () => void;
  // 点数相关信息
  pointsCost?: number; // 本次消耗的点数
  userPoints?: { balance: number } | null; // 用户点数余额
}

export const GenerationDialog: React.FC<GenerationDialogProps> = ({
  isOpen,
  task,
  isGenerating,
  error,
  onClose,
  onCancel,
  pointsCost = 0,
  userPoints,
}) => {
  // 排队相关状态
  const [queuePosition, setQueuePosition] = useState<number>(0);
  const [estimatedTime, setEstimatedTime] = useState<number>(3); // 预计3分钟

  // 初始化排队人数（随机生成一个合理的数字）
  useEffect(() => {
    if (isOpen && isGenerating) {
      const initialQueue = Math.floor(Math.random() * 15) + 5; // 5-20人之间
      setQueuePosition(initialQueue);
      setEstimatedTime(3);
    }
  }, [isOpen, isGenerating]);

  // 排队人数递减逻辑
  useEffect(() => {
    if (!isGenerating || queuePosition <= 0) return;

    const interval = setInterval(
      () => {
        setQueuePosition((prev) => {
          const newPosition = prev - 1;
          // 当排队人数减少时，预计时间也相应减少
          if (newPosition > 0) {
            setEstimatedTime(Math.max(1, Math.ceil(newPosition * 0.2))); // 大概每5个人1分钟
          }
          return Math.max(0, newPosition);
        });
      },
      Math.random() * 8000 + 7000,
    ); // 7-15秒之间随机递减一个人

    return () => clearInterval(interval);
  }, [isGenerating, queuePosition]);

  if (!isOpen) return null;

  const getStatusText = () => {
    if (error) return '生成失败';
    if (!task) return '准备中';

    // 检查内层result.status，优先于外层status
    const actualStatus = task.result?.status || task.status;

    switch (actualStatus) {
      case 'pending':
        return '排队中';
      case 'running':
        return '生成中';
      case 'completed':
        return task.result?.video_url ? '生成完成' : '生成完成';
      case 'failed':
        return '生成失败';
      default:
        return '未知状态';
    }
  };

  const getStatusIcon = () => {
    // 检查内层result.status，优先于外层status
    const actualStatus = task?.result?.status || task?.status;

    if (error || actualStatus === 'failed') {
      return (
        <div className="mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
          <svg className="h-5 w-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      );
    }

    if (actualStatus === 'completed' && task?.result?.video_url) {
      return (
        <div className="mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
          <svg
            className="h-5 w-5 text-green-600 dark:text-green-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    }

    return (
      <div className="mx-auto mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
        <svg className="h-5 w-5 animate-spin text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
    );
  };

  // 判断是否显示排队信息
  const shouldShowQueue = () => {
    if (error) return false;
    const actualStatus = task?.result?.status || task?.status;
    return isGenerating && (actualStatus === 'pending' || actualStatus === 'running') && queuePosition > 0;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[85vh] w-[400px] overflow-y-auto p-4 sm:w-[480px]">
        <DialogHeader className="mb-3">
          <DialogTitle className="text-center font-semibold text-gray-900 dark:text-gray-100">生成视频</DialogTitle>
        </DialogHeader>

        <div className="space-y-3 text-center">
          {getStatusIcon()}

          <div className="font-medium text-gray-900 dark:text-gray-100">{getStatusText()}</div>

          {/* 排队信息 */}
          {shouldShowQueue() && (
            <div className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-900/20">
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-orange-700 dark:text-orange-300">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="text-sm font-medium">排队信息</span>
                </div>
                <div className="text-sm text-orange-600 dark:text-orange-400">
                  前面还有 <span className="font-bold text-orange-700 dark:text-orange-300">{queuePosition}</span>{' '}
                  人排队
                </div>
                <div className="text-xs text-orange-500 dark:text-orange-400">
                  预计等待时间：约 <span className="font-medium">{estimatedTime}</span> 分钟
                </div>
              </div>
            </div>
          )}

          {/* Points Information */}
          {(pointsCost > 0 || userPoints) && (
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-2 dark:border-blue-800 dark:bg-blue-900/20">
              <div className="flex items-center justify-between text-sm">
                {pointsCost > 0 && (
                  <div className="text-blue-700 dark:text-blue-300">
                    <span className="font-medium">消耗点数:</span> {pointsCost}
                  </div>
                )}
                {userPoints && (
                  <div className="text-blue-600 dark:text-blue-400">
                    <span className="font-medium">当前余额:</span> {userPoints.balance}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Message */}
          {(error || task?.result?.status === 'failed' || task?.status === 'failed') && (
            <div className="space-y-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20">
              {/* 主要错误信息 */}
              <div className="text-sm font-medium text-red-700 dark:text-red-400">
                {error || task?.result?.error || task?.message || '生成出错'}
              </div>
              {/* 详细错误信息 - 支持多种错误信息格式 */}
              {task?.result?.error_details?.msg && task?.result?.error_details?.msg !== task?.result?.error && (
                <div className="text-xs text-red-600 dark:text-red-300">
                  {`详细信息: ${task.result.error_details.msg}`}
                </div>
              )}
              {/* 支持新的错误响应格式 */}
              {task?.message && task?.message !== error && task?.message !== task?.result?.error && (
                <div className="text-xs text-red-600 dark:text-red-300">{`详细信息: ${task.message}`}</div>
              )}
              {/* 原始提示词 */}
              {task?.result?.original_prompt && (
                <div className="mt-2 border-t border-red-200 pt-2 text-xs text-gray-600 dark:border-red-700 dark:text-gray-400">
                  <div className="mb-1 font-medium">原始提示词</div>
                  <div className="rounded bg-gray-50 p-2 text-xs dark:bg-gray-800">{task.result.original_prompt}</div>
                </div>
              )}
              {/* Midjourney提示词 */}
              {task?.result?.midjourney_prompt && (
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  <div className="mb-1 font-medium">处理后提示词</div>
                  <div className="rounded bg-gray-50 p-2 text-xs dark:bg-gray-800">{task.result.midjourney_prompt}</div>
                </div>
              )}
            </div>
          )}

          {/* Task Message */}
          {task?.message && !error && <div className="text-sm text-gray-600 dark:text-gray-400">{task.message}</div>}

          {/* Result Video */}
          {task?.result?.video_url && (
            <div className="mx-auto overflow-hidden rounded-lg shadow-lg" style={{ width: '169px', height: '300px' }}>
              <video
                src={task.result.video_url}
                controls
                className="h-full w-full object-cover"
                poster={task.result.thumbnail_url}
              >
                {'不支持的视频格式'}
              </video>
            </div>
          )}
        </div>

        {/* Actions */}
        <DialogFooter className="mt-4">
          {isGenerating ? (
            <Button onClick={onCancel} variant="outline" className="w-full">
              取消生成
            </Button>
          ) : (
            <Button onClick={onClose} variant="default" className="w-full">
              {(() => {
                const actualStatus = task?.result?.status || task?.status;
                return actualStatus === 'completed' && task?.result?.video_url ? '完成' : '关闭';
              })()}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
