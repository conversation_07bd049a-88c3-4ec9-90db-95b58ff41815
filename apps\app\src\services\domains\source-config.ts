import { ActionContext } from '@roasmax/serve';

type SliceTaskPoolType = { name: string; workers: string | number[] };

const DEFAULT_SLICE_TASK_POOL: SliceTaskPoolType = {
  name: 'SZ',
  workers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
};

/**
 * 获取一个随机的worker名称
 * 每一个租户都有一个任务池，任务池中有多个worker，需要随机选择一个worker执行，避免单个worker负载过高
 * worker名称由任务池名称+worker编号组成，例如：SZ_prod-1-slice
 * @param ctx
 * @returns
 */
export const getRandomWorkerName = async (ctx: ActionContext<{ taskType: 'slice' | 'asr' }>) => {
  if (process.env.TASK_POOL_MODE !== 'dynamic') {
    return ctx.data.taskType;
  }
  const slice_task_pool = ctx.tenant.config.slice_task_pool as SliceTaskPoolType | null;
  const taskPoolConfig = slice_task_pool ? slice_task_pool : DEFAULT_SLICE_TASK_POOL;

  const worker = taskPoolConfig.workers[Math.floor(Math.random() * taskPoolConfig.workers.length)];
  const workerName = `${taskPoolConfig.name}_prod-${worker}-${ctx.data.taskType}`;
  return workerName;
};
