'use client';
import React, { useEffect, useLayoutEffect, useMemo, useState, useRef } from 'react';
import { debounce, throttle } from 'lodash';
import { useDirs } from '@/hooks/useDirs';
import MaterialList from './MaterialList';
import LibraryDirList from './LibraryDirList';
import useMaterial, { MaterialItemType, SearchType } from '@/hooks/useMaterial';
import { material_directories } from '@roasmax/database';
import BreadCrumbs from './BreadCrumbs';
import { DirItemType } from '@/types/material';
import { useBreadcrumbs } from '@/hooks/useBreadcrumbs';
import MaterialManagementBar from './MaterialManagementBar';
import useMaterialStore from '@/store/materialStore';
import { Button, Input } from '@/components/ui';
import { Search } from 'lucide-react';
import DirContext from '@/context/directory';
import { useEvent } from '@/hooks/useEvent';
import SelectModeFooter from './SelectModeFooter';
import { EmptyStorage } from '../icon';
import { useUpload } from '@/hooks/useUpload';
import { useRoots } from '@/hooks/useRoots';
import { RadiusClose } from '@/components/icon';
import { UploadStatus } from '@/types/upload';

const DEFAULT_PANEL_STYLE = {
  height: 'calc(100vh - 27px - 100px)',
};

const LibraryContainer = ({
  config = {
    upload: true,
    search: true,
    batch: true,
    createDir: true,
    select: false,
  },
  handleConfirmSelect,
}: {
  config?: {
    upload: boolean;
    search: boolean;
    batch: boolean;
    createDir: boolean;
    select: boolean;
  };
  handleConfirmSelect?: (list: MaterialItemType[]) => void;
}) => {
  const {
    cloudTab,
    selectedMaterialList,
    uploadList,
    setUploadList,
    setSelectedMaterialList,
    batchMode,
    setBatchMode,
  } = useMaterialStore();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [waterfallTopHeight, setWaterfallTopHeight] = useState(0);
  const [selectMode, setSelectMode] = useState(true);
  const [containerWidth, setContainerWidth] = useState(1440);
  const [searchTerm, setSearchTerm] = useState(''); // 新增状态来管理搜索输入
  const clearSearch = () => {
    setSearchTerm('');
    onSearch('');
  };
  const { breadcrumbItems, handleBreadcrumbClick, handleAddBreadcrumb } = useBreadcrumbs();
  const {
    getDirs,
    currParentId,
    dirList, // 文件
    loading: dirLoading,
    setDirList,
    createDir,
    isSubmitting,
    cancelCreating,
    updateDir,
    setCurrParentId,
  } = useDirs(clearSearch);
  const { data: roots } = useRoots();
  const currRoot = roots?.find((item) => item.name === (cloudTab === 1 ? '原始素材' : '生成素材'));

  const [searchParams, setSearchParams] = useState<SearchType>({
    dirId: currParentId ?? '',
    page: 1,
    pageSize: 30,
  });
  const {
    materialList,
    loading: materialLoading, // 素材
    setMaterialList,
    updateMaterials,
    hasMore,
    setHasMore,
  } = useMaterial(searchParams, dirLoading, clearSearch);

  const getDistanceFromParent = (childElement: HTMLElement, parentElement: HTMLElement): number => {
    let distance = 0;
    let currentElement = childElement;

    while (currentElement && currentElement !== parentElement) {
      distance += currentElement.offsetTop;
      currentElement = currentElement.offsetParent as HTMLElement;
    }

    return distance;
  };

  const onSearch = useEvent((name: string = '') => {
    setMaterialList([]);
    setSearchParams((prev) => ({
      ...prev,
      page: 1,
      name,
      dirId: currParentId ?? '',
    }));
    getDirs({
      parentId: currParentId,
      name,
    });
  });
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value); // 更新搜索输入状态
    debouncedSearch(value); // 调用防抖搜索
  };

  const getDirListByStorage = async () => {
    getDirs({
      parentId: currRoot?.id ?? '',
    });
  };

  /**
   * 1. 清空list
   * 2. 更新面包屑
   * 3. 更新dirList和materialList
   * @param data
   */
  const onClickDir = async (data: material_directories) => {
    const targetDirId = data.id;
    // 清空list
    setMaterialList([]);
    setDirList([]);
    // 更新面包屑
    handleAddBreadcrumb(data);
    setSearchParams((prev) => ({
      ...prev,
      dirId: targetDirId,
      page: 1,
      pageSize: 30,
      ...(searchParams.name ? { name: searchParams.name } : {}),
    }));
    setCurrParentId(targetDirId); // 更新 currParentId
    // 更新dirList和materialList
    await getDirs({ parentId: targetDirId, name: searchParams?.name ?? '' });
  };

  const onBreadcrumbClick = async (item: DirItemType, index: number) => {
    handleBreadcrumbClick(index);
    // 清空list
    setMaterialList([]);
    setDirList([]);

    setSearchParams((prev) => ({
      ...prev,
      dirId: item.id === 'root' ? '' : (item?.id ?? ''),
      page: 1,
      pageSize: 30,
      ...(searchParams.name ? { name: searchParams.name } : {}),
    }));
    const params = {
      parentId: item.id === 'root' ? (currRoot?.id ?? '') : (item?.id ?? ''),
      name: searchParams?.name ?? '',
    };
    await getDirs(params);
  };

  const handleDirSelect = (data: DirItemType) => {
    setDirList((prev) => {
      return prev.map((each) => {
        if (each.id === data.id) {
          return { ...each, checked: !each.checked };
        }
        return each;
      });
    });
  };

  const onScroll = useEvent(() => {
    if (materialLoading || dirLoading || !hasMore || !searchParams.dirId) return;

    setSearchParams((prev) => {
      return {
        ...prev,
        dirId: currParentId ?? '',
        page: prev?.page ? prev.page + 1 : 1,
      };
    });
  });

  const removeCheckedItem = () => {
    const newDirList = dirList.map((item) => ({ ...item, checked: false }));
    setDirList(newDirList);
    setMaterialList((prev) => prev.filter((item) => !item.checked));
  };

  const restoreAllCheckedStatus = useEvent(() => {
    const newDirList = dirList.map((item) => ({ ...item, checked: false }));
    setDirList(newDirList);
    setSelectedMaterialList([]);
    setMaterialList((prev) => prev.map((item) => ({ ...item, checked: false })));
    setBatchMode(false);
  });

  const refreshCurrentPage = async () => {
    setMaterialList([]);
    setDirList([]);
    setHasMore(true);
    await getDirs({
      dirType: cloudTab,
      parentId: searchParams.dirId ?? '',
      name: searchParams.name ?? '',
    });
    setSearchParams((prev) => ({
      ...prev,
      page: 1,
      dirId: currParentId ?? searchParams.dirId ?? '',
      searchParams: searchParams.name ?? '',
    }));
  };

  const debouncedSearch = useMemo(
    () =>
      debounce((value) => {
        onSearch(value);
      }, 300),
    [onSearch],
  );
  useEffect(() => {
    getDirListByStorage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    function dispatchResize() {
      const event = new Event('resize');
      window.dispatchEvent(event);
    }

    function onResize() {
      const ele = document.getElementById('bwai-library-container');
      if (ele) {
        const w = ele.offsetWidth;
        setContainerWidth(w || 1440);
      }
    }

    window.addEventListener('resize', throttle(onResize, 16));
    dispatchResize();

    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.key === 'Escape') {
        if (selectMode || batchMode) {
          restoreAllCheckedStatus();
          setSelectMode(false);
          setBatchMode(false);
        }
      }
    }
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectMode, batchMode]);

  const { handleFileChange } = useUpload(() => {
    refreshCurrentPage();
  });
  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hasSuccessUpload = uploadList.some((each) => each.status === UploadStatus.SUCCESS);
    if (hasSuccessUpload) {
      const newUploadList = uploadList.filter((each) => each.status !== UploadStatus.SUCCESS);
      setUploadList(newUploadList);
    }
    handleFileChange(e, {
      openModal: true,
      uploadTarDirId: currParentId ?? '',
    });
  };

  useEffect(() => {
    setSearchParams((prev) => ({
      ...prev,
      dirId: currParentId ?? '',
    }));
  }, [currParentId]);

  useEffect(() => {
    const childElement = document.getElementById('bwai-only-to-get-top-height');
    const parentElement = document.getElementById('bwai-library-container');

    if (childElement && parentElement) {
      const distance = getDistanceFromParent(childElement, parentElement);
      setWaterfallTopHeight(distance);
    }
  }, []);
  const handleUploadClick = () => {
    if (batchMode) return;
    fileInputRef.current?.click();
  };

  const dirContextValue = {
    isSubmitting,
    cancelCreating,
    createDir,
  };

  return (
    // @ts-ignore
    <DirContext.Provider value={dirContextValue}>
      <div
        id="bwai-library-container"
        className="h-full w-full overflow-y-auto overflow-x-hidden pb-1"
        style={DEFAULT_PANEL_STYLE}
      >
        <div className="mb-4 flex items-center justify-between">
          <BreadCrumbs
            breadcrumbItems={breadcrumbItems}
            handleBreadcrumbClick={onBreadcrumbClick}
            disabled={dirLoading || materialLoading}
          />
          {config?.select && (
            <div className="mr-6 flex items-center justify-between gap-3">
              <Button
                variant="secondary"
                className="h-9 w-[88px] rounded-md bg-[#CCDDFF] bg-opacity-10 text-xs font-medium text-[#9FA4B2] hover:bg-[#CCDDFF33] hover:text-white"
                onClick={() => {
                  setSelectMode((prev) => !prev);
                  if (selectMode) {
                    restoreAllCheckedStatus();
                  }
                }}
              >
                {selectMode ? '取消选择' : '选择'}
              </Button>
              <div className="relative w-[250px]">
                <Search className="text-muted-foreground absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2" />
                <Input
                  type="search"
                  className="h-9 rounded-md bg-[#CCDDFF] bg-opacity-10 pl-8 text-xs font-medium shadow-none ring-offset-0 placeholder:text-[#9FA4B2] hover:bg-[#CCDDFF33] focus:border-[#00E1FF] focus:bg-[#CCDDFF1A] dark:ring-offset-0"
                  placeholder="请输入名称查找"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {searchTerm && (
                  <div
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500"
                  >
                    <RadiusClose />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <Input
          className="hidden"
          id="bwai-material-upload"
          multiple
          type="file"
          accept=".mp4"
          onChange={onInputChange}
          ref={fileInputRef}
        />
        <MaterialManagementBar
          disabled={!dirList.length && !materialList.length && (materialLoading || dirLoading)}
          removeCheckedItem={removeCheckedItem}
          restoreAllCheckedStatus={restoreAllCheckedStatus}
          refreshCurrentPage={refreshCurrentPage}
          materialList={materialList}
          currParentId={currParentId}
          dirList={dirList}
          onSearch={onSearch}
          handleUploadClick={handleUploadClick}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          config={{
            ...config,
            upload: config.upload && cloudTab === 1,
          }}
        />
        <LibraryDirList
          loading={dirLoading}
          dirList={dirList}
          containerWidth={containerWidth - 32}
          onClickDir={onClickDir}
          batchMode={batchMode}
          onSelect={handleDirSelect}
          updateDir={updateDir}
        />
        <div className="mb-8" id="bwai-only-to-get-top-height" />
        <MaterialList
          selectMode={selectMode && config.select && config.select}
          onScroll={onScroll}
          loading={materialLoading}
          materialList={materialList}
          setMaterialList={setMaterialList}
          dirId={currParentId ?? ''}
          containerWidth={containerWidth - 32}
          waterfallTopHeight={waterfallTopHeight}
          hasMore={hasMore}
          updateMaterials={updateMaterials}
        />
        {!materialList.length && !dirLoading && !materialLoading && (
          <div className="flex h-3/6 flex-col items-center justify-center">
            <EmptyStorage />
            <div className="mt-5 text-sm text-[#9FA4B2]">
              暂无文件
              {!searchParams?.name && cloudTab === 1 && config?.upload && (
                <span className="text-[#00E1FF]" onClick={handleUploadClick}>
                  ，去上传
                </span>
              )}
            </div>
          </div>
        )}
        <div className="w-full">
          {selectMode && config.select && config.select && handleConfirmSelect && (
            <SelectModeFooter
              onConfirm={handleConfirmSelect}
              selectedMaterialList={selectedMaterialList}
              setSelectedMaterialList={setSelectedMaterialList}
              setMaterialList={setMaterialList}
            />
          )}
        </div>
      </div>
    </DirContext.Provider>
  );
};

export default LibraryContainer;
