import { orders } from '@roasmax/database';
import { ActionContext, webhook } from '@roasmax/serve';

type BatchCreateContext = ActionContext<{
  data: orders[];
}>;

export const POST = webhook(async (ctx: BatchCreateContext) => {
  const tenant = await ctx.fetchTenant();
  if (!tenant) {
    throw new Error('未找到租户信息');
  }

  return await ctx.trx(async (ctx) => {
    if (ctx.data.data.some((item) => !item.out_key)) {
      throw new Error('缺少out_key');
    }
    // 获取已存在的数据 由于存在老数据，所以需要同时匹配 out_key 和 order_id
    const originList = await ctx.db.orders.findMany({
      where: {
        OR: [
          { out_key: { in: ctx.data.data.map((item) => item.out_key!) } },
          { order_id: { in: ctx.data.data.map((item) => item.order_id) } },
        ],
      },
    });

    // 将数据分为需要更新和需要创建的两部分
    const toCreate = ctx.data.data.filter(
      (item) => !originList.some((origin) => origin.out_key === item.out_key || origin.order_id === item.order_id),
    );
    const toUpdate = ctx.data.data.filter((item) =>
      originList.some((origin) => origin.out_key === item.out_key || origin.order_id === item.order_id),
    );

    // 批量创建
    if (toCreate.length > 0) {
      await ctx.db.orders.createMany({
        data: toCreate,
      });
    }

    // 批量更新
    for (const item of toUpdate) {
      const origin = originList.find((origin) => origin.out_key === item.out_key || origin.order_id === item.order_id);
      if (!origin) {
        continue;
      }

      // 如果数据一致，则不更新
      const diff = checkDiff(origin, item);
      if (diff.length === 0) {
        continue;
      }

      await ctx.db.orders.update({
        where: { id: origin.id },
        data: item,
      });
    }
  });
});

function checkDiff(origin: orders, commit: orders) {
  const fields: (keyof orders)[] = ['out_key', 'order_id', 'order_status'];
  return fields.filter((field) => {
    return origin[field] !== commit[field];
  });
}
