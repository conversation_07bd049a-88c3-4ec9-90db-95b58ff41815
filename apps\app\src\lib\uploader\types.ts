/** 任务状态 */
export enum TaskStatus {
  PENDING = "pending",
  RUNNING = "running",
  SUCCESS = "success",
  FAILED = "failed",
  CANCELED = "canceled",
}

/** 上传成功信息 */
export interface UploadSuccessInfo {
  /** 文件访问URL（COS存储地址） */
  url: string;
  /** 文件ID */
  fileId: string;
  /** 缩略图URL（可选） */
  thumbnailUrl?: string;
  /** 最终VOD URL（处理后的CDN地址） */
  vodUrl?: string;
  /** VOD处理状态 */
  vodStatus?: "processing" | "ready" | "failed";
}

/** 上传进度信息 */
export interface UploadProgressInfo {
  /** 进度百分比 0-100 */
  percent: number;
}

/** 上传失败信息 */
export interface UploadFailedInfo {
  /** 错误信息 */
  error: string;
}

/** 任务事件类型 */
export type TaskEvent = "progress" | "success" | "failed" | "canceled";

/** 事件回调参数 */
export type TaskEventInfo<T extends TaskEvent> = T extends "progress"
  ? UploadProgressInfo
  : T extends "success"
  ? UploadSuccessInfo
  : T extends "failed"
  ? UploadFailedInfo
  : T extends "canceled"
  ? void
  : never;

/** 任务 JSON 数据 */
export interface TaskJSON {
  id: string;
  name: string;
  status: TaskStatus;
  progress: number;
  error: string | null;
  fileSize: number;
  fileType: string;
  createdAt: string;
}
