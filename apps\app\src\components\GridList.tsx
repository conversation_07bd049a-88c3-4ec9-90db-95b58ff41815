import React, { memo, useMemo, useState, useRef, useEffect } from 'react';
import { maxBy, minBy } from 'lodash';

import MediaBox from '@/components/Waterfall/MediaBox';

type RawDataSourceType = {
  w: number;
  h: number;
  data: any;
};

type RawDataType = RawDataSourceType & {
  x: number;
  y: number;
};

type GirdListProps = {
  list: RawDataSourceType[];
  component: React.ComponentType<any>;
  column: number; // 列数量
  componentProps?: any; // 组件props
  itemPadding?: number; // 盒子之前的间距
  itemPaddingY?: number; // 盒子之前的间距
  containerId?: string; // 容器id
};

const defaultComponentProps: { [key: string]: any } = {};

const GirdList: React.FC<GirdListProps> = ({
  list,
  component: GridItem,
  itemPadding = 16,
  itemPaddingY = 16,
  column,
  componentProps = defaultComponentProps,
  containerId,
}) => {
  const [gridHeight, setGridHeight] = useState(0);
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const itemRefs = useRef<Map<number, HTMLDivElement>>(new Map());
  // 限制最大渲染数量为100
  const MAX_VISIBLE_ITEMS = 100;

  // 更新可见元素列表，保持最近的100个元素
  const updateVisibleItems = (index: number, isVisible: boolean) => {
    setVisibleItems((prev) => {
      const next = new Set(prev);

      if (isVisible) {
        // 如果已经达到最大数量，需要移除最早添加的元素
        if (next.size >= MAX_VISIBLE_ITEMS) {
          // 转换为数组，移除最早的元素
          const itemsArray = Array.from(next);
          const itemsToRemove = itemsArray.slice(0, itemsArray.length - MAX_VISIBLE_ITEMS + 1);
          itemsToRemove.forEach((item) => next.delete(item));
        }
        next.add(index);
      } else {
        next.delete(index);
      }

      return next;
    });
  };

  // 设置 IntersectionObserver
  useEffect(() => {
    // 确保容器已经挂载
    const container = document.getElementById(containerId!);
    if (!container) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = Number(entry.target.getAttribute('data-index'));
          const isFullyVisible = entry.intersectionRatio > 0.1;
          updateVisibleItems(index, isFullyVisible);
        });
      },
      {
        // 使用容器作为根元素
        root: container,
        rootMargin: '100px',
        threshold: [0, 0.1],
      },
    );

    // 重新观察所有已存在的元素
    itemRefs.current.forEach((element, index) => {
      if (observerRef.current) {
        observerRef.current.observe(element);
      }
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // 观察元素
  const observeElement = (element: HTMLDivElement | null, index: number) => {
    if (element && observerRef.current) {
      itemRefs.current.set(index, element);
      observerRef.current.observe(element);
    }
  };

  const renderList = useMemo(() => {
    const columns: { h: number; list: RawDataType[] }[] = new Array(column).fill(true).map(() => ({ h: 0, list: [] }));
    const wList: RawDataType[] = [];

    list?.forEach((item, index) => {
      const minColumn = minBy(columns, (i) => i.h)!;
      const n = columns.indexOf(minColumn);

      const itemW = item.w;
      const itemH = item.h;
      const listItem = {
        x: n * (itemW + itemPadding),
        y: minColumn.h,
        w: itemW,
        h: itemH,
        data: item.data,
        index,
      };
      minColumn.h = minColumn.h > 0 ? minColumn.h + listItem.h + itemPaddingY : listItem.h + itemPaddingY;
      minColumn.list.push(listItem);
      wList.push(listItem);
    });

    const height = maxBy(columns, (i) => i.h)!.h;
    setGridHeight(height);
    return wList;
  }, [column, list, itemPadding, itemPaddingY]);

  const style = useMemo<React.CSSProperties>(
    () => ({
      position: 'relative',
      transform: 'translate3d(0, 0, 0)',
      height: gridHeight,
    }),
    [gridHeight],
  );

  return (
    <div style={style}>
      {renderList.map((item, index) => {
        const isVisible = visibleItems.has(index);

        return (
          <div
            key={item.data.id || index}
            ref={(el) => observeElement(el, index)}
            data-index={index}
            style={{
              position: 'absolute',
              left: item.x,
              top: item.y,
              width: item.w,
              height: item.h,
            }}
          >
            {isVisible && (
              <MediaBox
                x={0}
                y={0}
                w={item.w}
                h={item.h}
                component={GridItem}
                componentProps={{
                  ...componentProps,
                  data: item.data,
                  item,
                }}
                data={item.data}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default memo(GirdList);
