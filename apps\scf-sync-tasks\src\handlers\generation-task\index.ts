import {
  handlePendingGenerationSubGenerationTask,
  handleProcessingGenerationSubGenerationTask,
} from './sub-generation';
import { completeGenerationSubSliceTask } from './sub-slice';

export default {
  slice: {
    handleProcessing: completeGenerationSubSliceTask,
  },
  generation: {
    handlePending: handlePendingGenerationSubGenerationTask,
    handleProcessing: handleProcessingGenerationSubGenerationTask,
  },
};
