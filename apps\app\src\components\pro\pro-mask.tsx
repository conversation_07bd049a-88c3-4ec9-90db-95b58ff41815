import { Loader2 } from 'lucide-react';

interface ProMaskProps {
  loading?: boolean;
  blur?: boolean;
  children?: React.ReactNode;
}

export function ProMask({ loading = false, blur = true, children }: ProMaskProps) {
  if (!loading) return null;

  return (
    <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-900/50 backdrop-blur-[2px]">
      <div className="relative">{children || <Loader2 className="h-8 w-8 animate-spin text-blue-500" />}</div>
    </div>
  );
}
