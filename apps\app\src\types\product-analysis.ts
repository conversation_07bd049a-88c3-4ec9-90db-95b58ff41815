import { z } from 'zod';

export const analysisSchema = z.object({
  product_analysis: z.string().optional(),
  title: z.string().optional(),
  suggestion: z.string().optional(),
  price: z.string().optional(),
  description: z.string().optional(),
  template_info: z.array(z.any()).optional(),
  video_ids: z.array(z.string()).optional(),
  picturesUrl: z.array(z.string()).optional(),
});

export type AnalysisResponse = z.infer<typeof analysisSchema>;

export interface ProductInfo {
  title: string;
  price: string;
  describe: string;
}

export interface CloneSuggestion {
  suggestion: string;
  video_ids: string[];
}

export interface SSEResponse<T> {
  data?: {
    id: string;
    workflow_id?: string;
    sequence_number?: number;
    outputs?: {
      data: string;
    };
    created_at: number;
    node_type?: string;
    event?: string;
    title?: string;
  };
  event?: string;
}

export interface SubStep {
  step_id: string;
  step_title: string;
  step_number: number;
  description?: string;
  description1?: string;
  completed_at: string;
  content_type: 'animation' | 'image' | 'markdown';
  content: string;
  lodding?: string;
  contentLodding?: string;
  data: any;
  processed_data: any;
}

export interface Step {
  step_id: string;
  step_title: string;
  step_number: number;
  description?: string;
  description1?: string;
  completed_at: string;
  content_type: 'animation' | 'image' | 'markdown';
  content: string;
  lodding?: string;
  contentLodding?: string;
  data: any;
  processed_data: any;
  sub_steps?: SubStep[];
}
