import Image, { ImageProps } from 'next/image';
import React, { useEffect, useState } from 'react';
import { NotFoundCover } from '../icon/NotFoundCover';
import { cn } from '@/utils/cn';

export type ProImageProps = ImageProps & { fallback?: () => React.ReactNode | { src: string } };

export const ProImage = ({ src, alt, fallback, onError, ...props }: ProImageProps) => {
  const [error, setError] = useState<boolean>(false);

  const imgSrc = typeof src === 'string' && !src.startsWith('/') && !src.startsWith('http') ? `/${src}` : src;

  useEffect(() => {
    if (src) {
      setError(false);
    }
  }, [src]);

  if (error) {
    const p = fallback?.() ?? (
      <div className={cn('flex h-1/2 w-1/2 items-center justify-center', props.className)}>
        <NotFoundCover />
      </div>
    );

    if (typeof p === 'object' && p && 'src' in p) {
      return (
        <Image
          {...props}
          alt={alt}
          src={imgSrc}
          onError={(e) => {
            setError(true);
            onError?.(e);
          }}
        />
      );
    }
    return p;
  }

  return (
    <Image
      {...props}
      alt={alt}
      src={imgSrc}
      onError={(e) => {
        setError(true);
        onError?.(e);
      }}
    />
  );
};
