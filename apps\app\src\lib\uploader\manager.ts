import { UploadTask } from "./upload-task";
import { TaskStatus, UploadSuccessInfo, TaskJSON } from "./types";

/** 上传管理器配置 */
export interface UploadManagerConfig {
  /** 最大并发数 */
  maxConcurrent: number;
}

/** 上传选项 */
export interface UploadOptions {
  /** 成功回调 */
  onSuccess?: (info: {
    task: UploadTask;
    url: string;
    vodUrl?: string;
    vodStatus?: "processing" | "ready" | "failed";
  }) => void;
}

/**
 * 上传管理器 - 简化版本
 */
export class UploadManager {
  private _config: UploadManagerConfig;
  private _tasks: UploadTask[] = [];
  private _timer: ReturnType<typeof setInterval> | null = null;
  private _globalHandlers: {
    onSuccess?: (info: {
      task: UploadTask;
      url: string;
      vodUrl?: string;
      vodStatus?: "processing" | "ready" | "failed";
    }) => void;
  } = {};

  constructor(config: Partial<UploadManagerConfig> = {}) {
    this._config = {
      maxConcurrent: 3,
      ...config,
    };
  }

  /** 获取所有任务 */
  get tasks(): UploadTask[] {
    return [...this._tasks];
  }

  /** 添加上传任务 */
  push(files: File[], options?: UploadOptions): string[] {
    const taskIds: string[] = [];
    const supportedFiles: File[] = [];
    const unsupportedFiles: File[] = [];

    // 过滤支持的文件类型
    files.forEach((file) => {
      if (this._isImageFile(file)) {
        supportedFiles.push(file);
      } else {
        unsupportedFiles.push(file);
      }
    });

    // 警告不支持的文件
    // if (unsupportedFiles.length > 0) {
    //   console.warn(
    //     `不支持的文件类型: ${unsupportedFiles
    //       .map((f) => f.type)
    //       .join(", ")}，仅支持图片格式`
    //   );
    // }

    // 创建任务
    supportedFiles.forEach((file) => {
      const task = new UploadTask(file);

      // 绑定成功事件
      task.on("success", (info: UploadSuccessInfo) => {
        console.log("上传成功:", info);
        // 调用任务特定的回调
        options?.onSuccess?.({
          task,
          url: info.url,
          vodUrl: info.vodUrl,
          vodStatus: info.vodStatus,
        });
        // 调用全局回调
        this._globalHandlers.onSuccess?.({
          task,
          url: info.url,
          vodUrl: info.vodUrl,
          vodStatus: info.vodStatus,
        });
      });

      this._tasks.push(task);
      taskIds.push(task.id);
    });

    return taskIds;
  }

  /** 取消任务 */
  cancel(taskId: string): void {
    const task = this._tasks.find((t) => t.id === taskId);
    if (task) {
      task.cancel();
    }
  }

  /** 移除任务 */
  remove(taskId: string): void {
    const index = this._tasks.findIndex((t) => t.id === taskId);
    if (index !== -1) {
      const task = this._tasks[index];
      task?.cancel();
      this._tasks.splice(index, 1);
    }
  }

  /** 开始处理任务队列 */
  process(): void {
    if (this._timer) return;

    this._timer = setInterval(() => {
      const runningTasks = this._tasks.filter(
        (t) => t.status === TaskStatus.RUNNING
      );
      const availableSlots = this._config.maxConcurrent - runningTasks.length;

      if (availableSlots <= 0) return;

      const pendingTasks = this._tasks.filter(
        (t) => t.status === TaskStatus.PENDING
      );
      const tasksToStart = pendingTasks.slice(0, availableSlots);

      tasksToStart.forEach((task) => {
        task.process().catch((error) => {
          console.error("任务处理失败:", error);
        });
      });
    }, 100);
  }

  /** 停止处理 */
  stop(): void {
    if (this._timer) {
      clearInterval(this._timer);
      this._timer = null;
    }
  }

  /** 监听全局事件 */
  on(
    event: "success",
    handler: (info: {
      task: UploadTask;
      url: string;
      vodUrl?: string;
      vodStatus?: "processing" | "ready" | "failed";
    }) => void
  ): void {
    if (event === "success") {
      this._globalHandlers.onSuccess = handler;
    }
  }

  /** 清理已完成的任务 */
  cleanup(olderThanDays: number = 30): void {
    const cutoffTime = Date.now() - olderThanDays * 24 * 60 * 60 * 1000;

    this._tasks = this._tasks.filter((task) => {
      const isOld = task.createdAt.getTime() < cutoffTime;
      const isCompleted = [
        TaskStatus.SUCCESS,
        TaskStatus.FAILED,
        TaskStatus.CANCELED,
      ].includes(task.status);
      return !(isOld && isCompleted);
    });
  }

  /** 转换为JSON */
  toJSON(): { tasks: TaskJSON[] } {
    return {
      tasks: this._tasks.map((task) => task.toJSON()),
    };
  }

  /** 从JSON恢复 */
  restore(data: { tasks: TaskJSON[] }, options?: UploadOptions): void {
    data.tasks.forEach((taskJson) => {
      // 检查任务是否已存在
      const existingTask = this._tasks.find((t) => t.id === taskJson.id);
      if (existingTask) return;

      // 只恢复成功的任务
      const task = UploadTask.fromJSON(taskJson);
      if (task) {
        // 绑定事件
        task.on("success", (info: UploadSuccessInfo) => {
          options?.onSuccess?.({
            task,
            url: info.url,
            vodUrl: info.vodUrl,
            vodStatus: info.vodStatus,
          });
          this._globalHandlers.onSuccess?.({
            task,
            url: info.url,
            vodUrl: info.vodUrl,
            vodStatus: info.vodStatus,
          });
        });

        this._tasks.push(task);
      }
    });
  }

  /** 检查是否为图片文件 */
  private _isImageFile(file: File): boolean {
    const imageTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
    ];
    return imageTypes.includes(file.type);
  }
}
