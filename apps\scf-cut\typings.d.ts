declare namespace NodeJS {
  interface ProcessEnv {
    BYTEDANCE_OPENSPPECH_APPID: string;
    BYTEDANCE_OPENSPPECH_ACCESS_TOKEN: string;

    ROAD2ALL_API_URL: string;
    ROAD2ALL_API_KEY: string;

    COS_APPID: string;
    COS_SECRET_ID: string;
    COS_SECRET_KEY: string;
    COS_REGION: string;
    COS_BUCKET: string;

    VOD_SECRET_ID: string;
    VOD_SECRET_KEY: string;
    VOD_REGION: string;

    // Langfuse 服务配置
    LANGFUSE_BASEURL: string;
  }
}
