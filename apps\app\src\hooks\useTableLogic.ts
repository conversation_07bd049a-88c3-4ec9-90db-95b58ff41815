import { useState, useMemo, useEffect } from 'react';

type SortOrder = 'asc' | 'desc' | undefined;
interface SortState {
  key: string;
  order: SortOrder;
}

export function useTableLogic<T extends { [key: string]: any }>(
  dataSource: T[],
  idKey: string,
  initialSelectedRows: any[] = [],
) {
  const [selectedRows, setSelectedRows] = useState<any[]>(initialSelectedRows); // 使用初始值
  const [sortState, setSortState] = useState<SortState>({ key: '', order: undefined });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  useEffect(() => {
    if (initialSelectedRows.length === 0 && selectedRows.length) {
      setSelectedRows([]);
    }
  }, [initialSelectedRows.length]);

  const handleSelectAll = (checked: boolean, currentPageIds: any[]) => {
    if (checked) {
      setSelectedRows((prev) => [...new Set([...prev, ...currentPageIds])]);
    } else {
      setSelectedRows((prev) => prev.filter((id) => !currentPageIds.includes(id)));
    }
  };

  const handleSelect = (id: string | any, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id]);
    } else {
      setSelectedRows(selectedRows.filter((itemId) => itemId !== id));
    }
  };

  const handleSort = (key: string) => {
    setSortState((prev) => ({
      key,
      order: prev.key === key ? (prev.order === 'asc' ? 'desc' : prev.order === 'desc' ? undefined : 'asc') : 'asc',
    }));
  };

  const getSortValue = (record: T, key: string): number => {
    console.log(record.jsonDate);
    let value: any;
    // 根据不同的数据结构获取排序值
    if (record.jsonDate?.metrics) {
      switch (key) {
        case 'spend':
          value = record.jsonDate?.metrics?.spend;
          break;
        case 'cpc':
          value = record.jsonDate?.metrics?.cpc;
          break;
        case 'cpm':
          value = record.jsonDate?.metrics?.cpm;
          break;
        case 'impressions':
          value = record.jsonDate?.metrics?.impressions;
          break;
        case 'clicks':
          value = record.jsonDate?.metrics?.clicks;
          break;
        case 'purchases':
          value = record.jsonDate?.metrics?.onsiteShopping;
          break;
        case 'onsiteShoppingRate':
          value = record.jsonDate?.metrics?.onsiteShoppingRate;
          break;
        case 'onsiteShoppingRoas':
          value = record.jsonDate?.metrics?.onsiteShoppingRoas;
          break;
        case 'totalOnsiteShoppingValue':
          value = record.jsonDate?.metrics?.totalOnsiteShoppingValue;
          break;
        case 'onsiteOnWebCart':
          value = record.jsonDate?.metrics?.onsiteOnWebCart;
          break;
        case 'ctr':
          value = parseFloat((record.jsonDate?.metrics?.ctr || '0%').replace('%', ''));
          break;
        // case 'checkout':
        //   value = record.jsonDate?.metrics?.initiateCheckout;
        //   break;
        case 'orderValue':
          value = record.jsonDate?.metrics?.valuePerOnsiteShopping;
          break;
        default:
          value = record[key];
      }
    } else if (record.jsonDate) {
      switch (key) {
        case 'budget':
          value = record.jsonDate?.budget;
          break;
        default:
          value = record[key];
      }
    } else {
      switch (key) {
        case 'balance':
          value = record.balance;
          break;
        default:
          value = record[key];
      }
    }

    return isNaN(Number(value)) ? 0 : Number(value);
  };

  const sortedData = useMemo(() => {
    if (!sortState.key || !sortState.order) {
      return dataSource;
    }

    return [...dataSource].sort((a, b) => {
      const aValue = getSortValue(a, sortState.key);
      const bValue = getSortValue(b, sortState.key);

      return sortState.order === 'asc' ? aValue - bValue : bValue - aValue;
    });
  }, [dataSource, sortState]);
  return {
    selectedRows,
    sortState,
    pagination,
    sortedData,
    handleSelectAll,
    handleSelect,
    handleSort,
    setPagination,
    setSelectedRows,
  };
}
