import { ActionContext } from '@roasmax/serve';
import queryString from 'query-string';
import CryptoJS from 'crypto-js';

// 缓存链接 先临时存在内存中
const links: { [key: string]: { link: string; extract: string; expire: number } } = {};

/**
 * 获取分享链接
 * @param ctx
 * @returns
 */
export const fetchShareLink = async (
  ctx: ActionContext<{ resource: string; allow: 'readonly' | 'readwrite'; expire: Date }>,
) => {
  return await ctx.trx(async (ctx) => {
    // 资源路径
    const resource = `qcs::cos:ap-shanghai:uid/${process.env.COS_APPID}:${process.env.COS_BUCKET}/roasmax/${ctx.tenant.id}/${ctx.data.resource}/*`;
    // 尝试获取缓存链接 距离有效期还有半小时的可用
    const cache = links[ctx.data.resource];
    if (cache && cache.expire > Date.now() / 1000 + 60 * 30) {
      return cache;
    }

    // 获取临时密钥
    const federationToken = await ctx.sts.GetFederationToken({
      Name: '分享链接',
      DurationSeconds: 3600,
      Policy: JSON.stringify({
        version: '2.0',
        statement: [
          {
            action:
              ctx.data.allow === 'readwrite'
                ? [
                    'name/cos:GetBucket',
                    'name/cos:GetObject',
                    'name/cos:HeadObject',
                    'name/cos:PostObject',
                    'name/cos:PutObject',
                    'name/cos:PutObjectTagging',
                    'name/cos:InitiateMultipartUpload',
                    'name/cos:ListMultipartUploads',
                    'name/cos:ListParts',
                    'name/cos:UploadPart',
                    'name/cos:CompleteMultipartUpload',
                    'name/cos:UploadPartCopy',
                    'name/cos:AbortMultipartUpload',
                    'name/cos:DeleteObject',
                    'name/cos:DeleteMultipleObject',
                    'name/cos:GetBucketReplication',
                  ]
                : ['name/cos:GetBucket', 'name/cos:GetObject', 'name/cos:HeadObject'],
            effect: 'allow',
            principal: { qcs: ['*'] },
            resource,
          },
        ],
      }),
    });

    if (!federationToken || !federationToken.Credentials) {
      throw new Error('获取临时密钥失败');
    }

    // 最晚 1.5 小时后过期
    const expire = Math.floor(
      Math.min(ctx.data.expire.getTime() / 1000, Math.floor(Math.floor(Date.now() / 1000) + 60 * 60 * 1.5)),
    );

    const shareInfo = {
      Bucket: process.env.COS_BUCKET,
      Region: 'ap-shanghai',
      Prefix: `roasmax/${ctx.tenant.id}/`,
      action: encodeToBase64(
        ctx.data.allow === 'readwrite'
          ? [
              'GetBucket',
              'GetObject',
              'HeadObject',
              'PostObject',
              'PutObject',
              'PutObjectTagging',
              'InitiateMultipartUpload',
              'ListMultipartUploads',
              'ListParts',
              'UploadPart',
              'CompleteMultipartUpload',
              'UploadPartCopy',
              'AbortMultipartUpload',
              'DeleteObject',
              'DeleteMultipleObject',
              'GetBucketReplication',
            ]
          : ['GetBucket', 'GetObject', 'HeadObject'],
      ),
      expire: expire,
      files: encodeToBase64([{ name: `${encodeURIComponent(ctx.data.resource)}` }]),
      isMaz: '0',
      sid: federationToken.Credentials.TmpSecretId,
      token: federationToken.Credentials.Token,
    };

    // 随机生成一个6位数
    const extract = Math.random().toString(36).substring(2, 8);

    // 获取分享链接
    const response = await ctx.request.post<{ code: number; data: { shareId: string; accessCode: string } }>(
      'https://cosbrowser.cloud.tencent.com/api/share/save',
      { shareInfo: queryString.stringify(shareInfo), extract },
      { headers: { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' } },
    );

    // 获取分享链接
    const shareId = response.data.data.shareId;
    // 获取分享链接的token
    const shareToken = AESEncrypt(federationToken.Credentials.TmpSecretKey!, response.data.data.accessCode);
    // 拼接分享链接
    const query = queryString.stringify({ id: shareId, token: shareToken });

    const link = `https://cosbrowser.cloud.tencent.com/share/?${query}`;

    // 缓存链接
    links[resource] = { link, extract, expire };

    return { link, extract, expire };
  });
};

const encodeToBase64 = (arr: any[]) => {
  const rawStr = JSON.stringify(arr);
  const wordArray = CryptoJS.enc.Utf8.parse(rawStr);
  const base64 = CryptoJS.enc.Base64.stringify(wordArray);
  return base64;
};

const AESEncrypt = (word: string, key: string) => {
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const accessCode = CryptoJS.enc.Utf8.parse(key); // 密钥
  const encrypted = CryptoJS.AES.encrypt(srcs, accessCode, {
    iv: CryptoJS.enc.Utf8.parse('cosbrowser-share'),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const hexStr = encrypted.ciphertext.toString().toUpperCase();
  const oldHexStr = CryptoJS.enc.Hex.parse(hexStr);
  // 将密文转为Base64的字符串
  const base64Str = CryptoJS.enc.Base64.stringify(oldHexStr);
  return base64Str;
};
