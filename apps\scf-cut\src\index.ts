'use strict';
import { NodeScfWebServer } from '@roasmax/scf-web-server';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import path from 'path';
import SubtitleRecognizer from './libs/subtitle-recognizer';
import { SplitMode, VideoCutter } from './libs/video-cutter';
import { cos, downloadFileFromUrl } from './utils/cos';
import { prisma } from './utils/prisma';
import { snowflake } from './utils/snowflake';
import fs from 'fs';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 视频切割参数
 */
type VideoCutParams = {
  /**
   * 任务id
   */
  taskId: string;
};

const dispatcher = async (event: VideoCutParams) => {
  const task = await prisma.video_cut_tasks.findUnique({ where: { id: event.taskId } });
  if (!task) {
    throw new Error('任务不存在');
  }
  // 1. 视频下载
  console.log('开始下载视频...');
  let videoPath = '';
  if (task.origin_url.startsWith('/')) {
    videoPath = path.resolve(process.env.TMP_DIR!, `${snowflake.nextId()}.mp4`);
    // 斜杠开头默认为COS路径
    await cos.downloadFile({
      Bucket: process.env.COS_BUCKET!,
      Region: process.env.COS_REGION!,
      Key: `roasmax/${task.tenant_id}/直播录制/录制视频${task.origin_url}`,
      FilePath: videoPath,
      ChunkSize: 1024 * 1024 * 10,
      ParallelLimit: 5,
      RetryTimes: 3,
      onProgress: async (progress) => {
        const { loaded, total } = progress;
        console.log(`下载进度: ${Math.round((loaded * 100) / total)}%`);
        await prisma.video_cut_tasks.update({
          where: { id: task.id },
          data: { status: 'PROCESSING', status_desc: `下载进度: ${Math.round((loaded * 100) / total)}%` },
        });
      },
    });
  } else {
    videoPath = await downloadFileFromUrl(
      task.origin_url,
      path.resolve(process.env.TMP_DIR!, `${snowflake.nextId()}.mp4`),
      {
        onProgress: async (loaded, total) => {
          console.log(`下载进度: ${Math.round((loaded * 100) / total)}%`);
          await prisma.video_cut_tasks.update({
            where: { id: task.id },
            data: { status: 'PROCESSING', status_desc: `下载进度: ${Math.round((loaded * 100) / total)}%` },
          });
        },
      },
    );
  }
  await prisma.video_cut_tasks.update({
    where: { id: task.id },
    data: { status: 'PROCESSING', status_desc: '视频下载完成！' },
  });
  // 2. 字幕生成
  console.log('开始生成字幕...');
  const subtitleGenerator = new SubtitleRecognizer({
    appid: process.env.BYTEDANCE_OPENSPPECH_APPID,
    accessToken: process.env.BYTEDANCE_OPENSPPECH_ACCESS_TOKEN,
    language: task.language,
    segmentDuration: 20 * 60, // 20分钟切片 保证每次进入音频识别的时长不超过20分钟
  });
  const subtitleContent = await subtitleGenerator.recognize(videoPath);
  console.log('字幕生成完成！');
  await prisma.video_cut_tasks.update({
    where: { id: task.id },
    data: { status: 'PROCESSING', status_desc: '字幕生成完成！' },
  });
  // 3. 视频切割
  console.log('开始切割视频...');
  const splitter = new VideoCutter({
    apiKey: process.env.ROAD2ALL_API_KEY,
    apiUrl: process.env.ROAD2ALL_API_URL,
    mode: (task.cut_mode || 'general') as SplitMode,
    outputDir: (videoPath: string) => `output/${videoPath}`,
  });
  const result = await splitter.cut(videoPath, subtitleContent, task.goods_list);
  console.log('视频切割完成！，输出路径：', result.outputDir);
  await prisma.video_cut_tasks.update({
    where: { id: task.id },
    data: { status: 'PROCESSING', status_desc: '视频切割完成！' },
  });
  // 4. 视频上传
  console.log('开始上传视频...');
  const files: Parameters<typeof cos.uploadFiles>[0]['files'] = result.outputFiles.map((path) => {
    const baseKey = `roasmax/${task.tenant_id}/自动切分/切分产出`;
    const dirKey = `${dayjs.utc(task.tmp_created_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH-mm-ss')}-${task.name}`;
    const fileKey = `${path.replace(result.outputDir, '')}`;
    return {
      Bucket: process.env.COS_BUCKET!,
      Region: process.env.COS_REGION!,
      FilePath: path,
      Key: `${baseKey}/${dirKey}/${fileKey}`,
    };
  });
  const uploadResult = await cos.uploadFiles({
    files,
    onProgress: async (info) => {
      console.log(`上传进度: ${info.percent}%`);
      await prisma.video_cut_tasks.update({
        where: { id: task.id },
        data: { status: 'PROCESSING', status_desc: `上传进度: ${info.percent}%` },
      });
    },
  });
  console.log('视频上传完成！，输出路径：', uploadResult);
  await prisma.video_cut_tasks.update({
    where: { id: task.id },
    data: { status: 'SUCCESS', status_desc: '视频上传完成！' },
  });
  // 5. 清理临时文件
  fs.rmSync(videoPath, { recursive: true });
  fs.rmSync(result.outputDir, { recursive: true });
};

const server = new NodeScfWebServer(dispatcher);

server.start(Number(process.env.SCF_PORT) || 9000);
