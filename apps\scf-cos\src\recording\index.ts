import { EventPayload } from '..';
import { parseFilePath } from '../utils/path';
import { UploadFromCosForRecordingInputHandler } from './upload-cos-for-recording-input';

export const cutDispatcher = async (event: EventPayload) => {
  const promiseArr = event.Records.map(async (record) => {
    // 基础验证
    if (record.cos.cosBucket.appid !== process.env.COS_APPID) {
      console.log('appid 不匹配，不处理', record.cos.cosBucket.appid);
      return;
    }

    const fileInfo = parseFilePath(record);
    if (!fileInfo || fileInfo.appName !== 'roasmax' || fileInfo.workflowName !== '直播录制') {
      console.log('文件路径格式不正确或非目标文件，不处理');
      return;
    }

    // 创建事件 根据stageName分发到不同的handler
    if (record.event.eventName.startsWith('cos:ObjectCreated:')) {
      switch (fileInfo.stageName) {
        case '录制视频': {
          console.log('接收到 [录制视频:创建] 事件');
          const handler = new UploadFromCosForRecordingInputHandler();
          await handler.handle(record);
          break;
        }
        default:
          console.log('未知的处理阶段:[创建]:', fileInfo.stageName);
      }
    }
  });

  try {
    await Promise.all(promiseArr);
    return 'Success';
  } catch (e) {
    console.log(e);
    return 'Fail';
  }
};
