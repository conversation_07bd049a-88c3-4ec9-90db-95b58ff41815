import chalk from 'chalk';
import mysql from 'mysql2';
import { ActionContext, ActionContextPluginLoader } from '../../types';
import { snowflake } from '@roasmax/utils';
import { Prisma, PrismaClient } from '@roasmax/database';

const buildDatabaseApi = async (context: ActionContext<any>) => {
  const prisma = new PrismaClient({
    // https://www.prisma.io/docs/orm/prisma-client/queries/transactions#transaction-options
    transactionOptions: {
      isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
      maxWait: 5000,
      timeout: 100000,
    },
    log: [
      { emit: 'event', level: 'query' },
      { emit: 'event', level: 'info' },
      { emit: 'event', level: 'warn' },
      { emit: 'event', level: 'error' },
    ],
  });
  prisma.$on('query', (e) => {
    try {
      const params = JSON.parse(e.params || '[]') as any[];
      const query = e.query;
      context.logger.debug(chalk.yellow('[Prisma]'), `${e.duration}ms`, mysql.format(query, params));
    } catch (err) {
      context.logger.debug(chalk.yellow('[Prisma]'), `${e.duration}ms`, e.query, e.params);
    }
  });

  return prisma.$extends({
    query: {
      $allModels: {
        $allOperations: ({ model, args, query, operation }) => {
          if (operation === 'create') {
            return query({
              ...args,
              data: { id: snowflake.nextId() as any, ...args.data, tenant_id: context.tenant.id },
            });
          }
          if (operation === 'createMany') {
            const data = Array.isArray(args.data) ? args.data : [args.data];
            return query({
              ...args,
              data: data.map((d: any) => ({ id: snowflake.nextId(), ...d, tenant_id: context.tenant.id })),
            });
          }
          if ('tmp_deleted_at' in prisma[model].fields) {
            return query({
              ...args,
              where: { ...(args as any).where, tenant_id: context.tenant.id, tmp_deleted_at: null },
            });
          }
          return query({ ...args, where: { ...(args as any).where, tenant_id: context.tenant.id } });
        },
      },
    },
  });
};

const dbPlugin: ActionContextPluginLoader = async (context) => {
  return {
    name: 'db',
    plugin: await buildDatabaseApi(context),
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 数据库操作API
     */
    db: Awaited<ReturnType<typeof buildDatabaseApi>>;
  }
}

export default dbPlugin;
