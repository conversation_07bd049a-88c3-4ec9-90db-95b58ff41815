'use client';

import { ExcelButton } from '@/components/ExcelButton';
import React from 'react';
import { toast } from 'react-hot-toast';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import Cookies from 'js-cookie';

dayjs.extend(utc);
dayjs.extend(timezone);

interface IImportButton {
  headerMapper: {
    required: boolean;
    key: string;
    content: string;
    type: string;
  }[];
  api: string;
  title?: string;
  valueTransformer?: Record<string, (value: any) => any>;
  extraFields?: Record<string, any>;
  onSuccess?: (data: any[]) => void;
}

export const ImportButton: React.FC<IImportButton> = ({
  headerMapper,
  api,
  title = '导入Excel',
  valueTransformer = {},
  extraFields = {},
  onSuccess,
}) => {
  const excelMapper = Object.fromEntries((headerMapper ?? [])?.map((each) => [each.key, each.content]));

  const handleDataParsed = async (data: any[]) => {
    const loadingToast = toast.loading('正在导入数据...');

    try {
      const mapperKeys = headerMapper.map((each) => each.key);
      const requiredFields = headerMapper.filter((each) => each.required).map((each) => each.key);

      const validationErrors: string[] = [];

      const mappedData = data.map((item, index) => {
        const processedItem: any = {};
        const properties: any = {};

        // 处理字段
        Object.entries(item).forEach(([key, value]) => {
          if (mapperKeys.includes(key)) {
            const fieldConfig = headerMapper.find((h) => h.key === key);
            const transformedValue = valueTransformer[key] ? valueTransformer[key](value) : value;

            if (fieldConfig?.type === 'StringList') {
              processedItem[key] = transformedValue ? transformedValue.toString().split(' ').filter(Boolean) : [];
            } else if (fieldConfig?.type === 'date') {
              processedItem[key] = parseAndValidateDate(transformedValue, key, index, validationErrors);
            } else if (fieldConfig?.type === 'number') {
              processedItem[key] = parseAndValidateNumber(transformedValue, key, index, validationErrors);
            } else {
              processedItem[key] = transformedValue?.toString() || '';
            }

            // 验证必填字段
            if (requiredFields.includes(key) && processedItem[key] === undefined) {
              validationErrors.push(`第 ${index + 1} 行缺少必填字段: ${excelMapper[key]}`);
            }
          } else {
            properties[key] = value || '';
          }
        });

        return {
          ...processedItem,
          properties,
          ...extraFields,
        };
      });

      if (validationErrors.length > 0) {
        handleValidationErrors(validationErrors, loadingToast);
        return;
      }

      // 发送数据到服务器
      const response = await sendDataToServer(api, mappedData);

      if (!response.success) {
        toast.dismiss(loadingToast);
        toast.error(response.message || '导入失败');
        return;
      }

      toast.dismiss(loadingToast);
      toast.success('导入成功');

      // 调用成功回调
      onSuccess?.(mappedData);
    } catch (error) {
      console.error('导入失败:', error);
      toast.dismiss(loadingToast);
      toast.error('导入失败');
    }
  };

  // 辅助函数
  const parseAndValidateDate = (value: any, field: string, index: number, errors: string[]) => {
    if (value && value !== '-') {
      let parsedDate;
      if (typeof value === 'number') {
        const excelEpoch = new Date(1899, 11, 30);
        const millisecondsPerDay = 24 * 60 * 60 * 1000;
        parsedDate = dayjs(new Date(excelEpoch.getTime() + value * millisecondsPerDay)).toISOString();
      } else {
        parsedDate = dayjs(value.replace(/\//g, '-')).toISOString();
      }

      if (dayjs(parsedDate).isValid()) {
        return parsedDate;
      } else {
        errors.push(`第 ${index + 1} 行的${excelMapper[field]}格式不正确`);
      }
    }
    return undefined;
  };

  const parseAndValidateNumber = (value: any, field: string, index: number, errors: string[]) => {
    if (value !== undefined && value !== '') {
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        return numValue;
      } else {
        errors.push(`第 ${index + 1} 行的${excelMapper[field]}必须是数字`);
      }
    }
    return undefined;
  };

  const handleValidationErrors = (errors: string[], loadingToast: string) => {
    toast.dismiss(loadingToast);
    toast.error(
      <div>
        <div>数据格式错误：</div>
        {errors.map((error, index) => (
          <div key={index}>{error}</div>
        ))}
      </div>,
    );
  };

  const sendDataToServer = async (api: string, data: any[]) => {
    const response = await fetch(api, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        AccessToken: Cookies.get('Authorization') || '',
      },
      body: JSON.stringify({ data }),
    });
    return await response.json();
  };

  return (
    <ExcelButton
      className="h-[32px] text-[#050A1C] hover:text-[#050A1C]"
      title={title}
      mapper={excelMapper}
      onDataParsed={handleDataParsed}
      type="resolve"
    />
  );
};
