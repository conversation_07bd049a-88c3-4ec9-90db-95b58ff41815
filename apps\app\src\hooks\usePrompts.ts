import { listPrompts } from '@/services/actions/video-generation-task';
import { action, ActionResult } from '@/utils/server-action/action';
import { uniq } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

type PromptsStore = {
  prompts: ActionResult<typeof listPrompts>['data'];
  loading: boolean;
  fetch: () => Promise<void>;
};

const promptsStore = create<PromptsStore>()(
  devtools((set) => ({
    prompts: [],
    loading: false,
    fetch: async () => {
      set({ loading: true });
      const res = await action(listPrompts, {});
      set({ prompts: res?.data || [], loading: false });
    },
  })),
);

let hasFetched = false;

/**
 * prompts中包含了行业、时长、模式，需要根据行业和时长的选择，动态展示模式
 * 将平铺的prompts数据，按照行业、时长、模式进行分组转化为树状结构
 */
export const usePrompts = () => {
  const { prompts, loading, fetch } = promptsStore();

  useEffect(() => {
    if (!hasFetched) {
      fetch();
      hasFetched = true;
    }
  }, [fetch]);

  const tree = useMemo(() => {
    const industries = uniq(
      prompts
        .flatMap((prompt) => prompt.tags.filter((t) => t.startsWith('#行业-')))
        .map((t) => t.replace('#行业-', '')),
    );

    return industries.map((industry) => {
      const durations = uniq(
        prompts
          .flatMap((prompt) => {
            if (!prompt.tags.includes(`#行业-${industry}`)) return [];
            return prompt.tags.filter((t) => t.startsWith('#时长-'));
          })
          .map((t) => t.replace('#时长-', '')),
      );
      return {
        industry,
        durations: Array.from(new Set(durations)).map((duration) => {
          const model = prompts
            .filter((prompt) => prompt.tags.includes(`#行业-${industry}`) && prompt.tags.includes(`#时长-${duration}`))
            .map((prompt) => ({
              value: prompt.name,
              label: prompt.tags.find((tag) => tag.startsWith('#模式-'))?.replace('#模式-', '') || '',
              prompt: prompt,
            }));
          return { duration, model };
        }),
      };
    });
  }, [prompts]);

  const getByValue = useCallback(
    (v: string | undefined) => {
      const p = prompts.find((p) => p.name === v);
      const industry = p?.tags.find((t) => t.startsWith('#行业-'))?.replace('#行业-', '');
      const duration = p?.tags.find((t) => t.startsWith('#时长-'))?.replace('#时长-', '');
      return { industry, duration, prompt: p };
    },
    [prompts],
  );

  return useMemo(() => ({ list: prompts, tree, loading, getByValue }), [prompts, tree, loading, getByValue]);
};
