'use client';

import { ProFilter } from '@/components/pro/pro-filter';
import { DEFAULT_PAGINATION, ProPagination } from '@/components/pro/pro-pagination';
import ProTable from '@/components/pro/pro-table';
import { Panel } from '@/components/ui';
import { pageOrders } from '@/services/actions/orders';
import { ActionParams, useAction } from '@/utils/server-action/action';
import { useMemo, useState } from 'react';
import { createInbuiltColumns, filterColumns } from './config';

export default function Sales() {
  const [pagination, setPagination] = useState<{ page: number; pageSize: number }>(DEFAULT_PAGINATION);
  const [filters, setFilters] = useState<NonNullable<ActionParams<typeof pageOrders>['filters']>>({});

  const { data: dataSource, loading } = useAction(pageOrders, { pagination, filters });

  const columns = useMemo(() => {
    return createInbuiltColumns();
  }, []);

  return (
    <div className="h-[100%] p-4">
      <Panel className="flex h-[100%] flex-col p-4">
        <ProFilter value={filters} onSubmit={setFilters} columns={filterColumns} className="mb-2" />
        <div className="mb-2 flex items-center justify-between gap-4">
          <div className="text-base font-bold">销售列表</div>
        </div>
        <div className="flex-1 overflow-auto">
          <ProTable columns={columns} loading={loading} dataSource={dataSource?.list || []} />
        </div>
        <div className="mt-1">
          <ProPagination
            pagination={{ ...pagination, total: dataSource?.pagination.total }}
            onPaginationChange={setPagination}
          />
        </div>
      </Panel>
    </div>
  );
}
