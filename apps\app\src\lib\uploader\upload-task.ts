import { to } from '../to';
import type { Uploader } from '../vod';
import {
  type TaskEvent,
  type TaskEventInfo,
  type TaskJSON,
  TaskStatus,
  type UploadSuccessInfo,
} from './types';

/** 事件处理函数 */
type EventHandler<T extends TaskEvent> = (info: TaskEventInfo<T>) => void;

/**
 * 上传任务类 - 简化版本
 */
export class UploadTask {
  /** 任务ID */
  readonly id: string;
  /** 创建时间 */
  readonly createdAt: Date;
  /** 任务文件 */
  readonly file: File;
  /** 任务名称 */
  readonly name: string;
  /** 文件大小 */
  readonly fileSize: number;
  /** 文件类型 */
  readonly fileType: string;

  /** 任务状态 */
  private _status: TaskStatus = TaskStatus.PENDING;
  /** 上传进度 */
  private _progress = 0;
  /** 错误信息 */
  private _error: string | null = null;
  /** 是否取消 */
  private _canceled = false;
  /** 上传器实例 */
  private _uploader: Uploader | null = null;
  /** 事件处理器 */
  private _handlers: Partial<{
    [K in TaskEvent]: EventHandler<K>[];
  }> = {};

  constructor(file: File) {
    this.id = this._generateId();
    this.createdAt = new Date();
    this.file = file;
    this.name = file.name;
    this.fileSize = file.size;
    this.fileType = file.type;
  }

  /** 获取当前状态 */
  get status(): TaskStatus {
    return this._status;
  }

  /** 获取进度 */
  get progress(): number {
    return this._progress;
  }

  /** 获取错误信息 */
  get error(): string | null {
    return this._error;
  }

  /** 监听事件 */
  on<T extends TaskEvent>(event: T, handler: EventHandler<T>): void {
    if (!this._handlers[event]) {
      this._handlers[event] = [];
    }
    this._handlers[event]!.push(handler);
  }

  /** 触发事件 */
  private _emit<T extends TaskEvent>(event: T, info: TaskEventInfo<T>): void {
    const handlers = this._handlers[event];
    if (handlers) {
      handlers.forEach((handler) => handler(info));
    }
  }

  /** 开始上传 */
  async process(): Promise<void> {
    if (this._canceled) {
      this._setStatus(TaskStatus.CANCELED);
      return;
    }

    this._setStatus(TaskStatus.RUNNING);

    const [error] = await to(async () => {
      if (this._canceled) {
        throw new Error('任务已取消');
      }

      // 检查文件类型
      if (!this._isImageFile()) {
        throw new Error('仅支持图片文件');
      }

      // 导入上传模块
      const { vod, VOD_CONFIG } = await import('../vod');

      // 创建上传实例
      this._uploader = vod.upload({
        mediaFile: this.file,
      });

      // 监听上传进度
      this._uploader.on(
        'media_progress',
        ({ percent }: { percent: number }) => {
          if (this._canceled) {
            this._uploader?.cancel();
            return;
          }
          this._progress = Math.round(percent);
          this._emit('progress', { percent: Math.round(percent) });
        }
      );

      // 执行上传
      const result = await new Promise<UploadSuccessInfo>((resolve, reject) => {
        this._uploader!.on(
          'media_upload',
          (info: import('../vod').VodUploadResponse) => {
            if (this._canceled) {
              reject(new Error('任务已取消'));
              return;
            }

            console.log('上传成功:', info);

            // 从Key中提取fileId和appId
            const keyParts = info.Key.split('/');
            const fileId :any = keyParts.length > 1 ? keyParts[1] : info.UploadId;

            // 使用配置中的appId
            const appId = VOD_CONFIG.APP_ID;

            // 构建正确的CDN URL格式
            const cdnUrl = `https://${appId}.vod-qcloud.com/${keyParts[0]}/${fileId}/${keyParts[2]}`;

            resolve({
              url: cdnUrl,
              fileId: fileId || '',
              thumbnailUrl: cdnUrl, // 图片使用同一个URL作为缩略图
            });
          }
        );

        this._uploader!.on('error', (error: Error) => {
          const errorMessage =
            error instanceof Error
              ? error.message
              : typeof error === 'string'
                ? error
                : '上传失败';
          reject(new Error(errorMessage));
        });

        this._uploader!.start();
      });

      if (this._canceled) {
        throw new Error('任务已取消');
      }

      this._setStatus(TaskStatus.SUCCESS);
      this._emit('success', result);
    });

    if (error) {
      if (error.message === '任务已取消') {
        this._setStatus(TaskStatus.CANCELED);
        this._emit('canceled', undefined);
      } else {
        this._setStatus(TaskStatus.FAILED);
        this._error = error.message;
        this._emit('failed', { error: error.message });
      }
    }
  }

  /** 取消上传 */
  cancel(): void {
    this._canceled = true;
    if (this._uploader) {
      this._uploader.cancel();
    }
    this._setStatus(TaskStatus.CANCELED);
    this._emit('canceled', undefined);
  }

  /** 转换为JSON */
  toJSON(): TaskJSON {
    return {
      id: this.id,
      name: this.name,
      status: this._status,
      progress: this._progress,
      error: this._error,
      fileSize: this.fileSize,
      fileType: this.fileType,
      createdAt: this.createdAt.toISOString(),
    };
  }

  /** 从JSON恢复任务 */
  static fromJSON(json: TaskJSON): UploadTask | null {
    // 恢复的任务没有实际文件，只保留状态信息
    // 这里简化处理，只返回已完成的任务信息
    if (json.status !== TaskStatus.SUCCESS) {
      return null;
    }

    // 由于没有原始文件，创建一个占位文件
    const placeholderFile = new File([], json.name, { type: json.fileType });
    const task = new UploadTask(placeholderFile);

    // 恢复状态
    // @ts-expect-error eslint-disable-line
    task.id = json.id;
    // @ts-expect-error eslint-disable-line
    task.createdAt = new Date(json.createdAt);
    task._status = json.status;
    task._progress = json.progress;
    task._error = json.error;

    return task;
  }

  /** 设置状态 */
  private _setStatus(status: TaskStatus): void {
    this._status = status;
  }

  /** 生成任务ID */
  private _generateId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 7);
    return `${timestamp}-${random}`;
  }

  /** 检查是否为图片文件 */
  private _isImageFile(): boolean {
    const imageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    return imageTypes.includes(this.file.type);
  }
}
