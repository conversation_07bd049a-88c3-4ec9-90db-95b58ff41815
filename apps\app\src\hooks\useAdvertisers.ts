import useS<PERSON> from 'swr';
import tiktokService from '@/services/tiktokService';
import { useUserId } from '@/store/userStore';
import { ToCamelCase } from '@/utils/camel';
import { AdvertiserListResponse } from '@/services/interfaces/ads/res';
import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

// 抽离 fetcher 函数
export const advertiserFetcher = async (params: { page: number; pageSize: number; userId: string }) => {
  const { page, pageSize, userId } = params;
  const data: AdvertiserListResponse = await tiktokService.getAdvertiserList({
    uid: userId,
    page,
    page_size: pageSize,
  });

  // @ts-ignore
  return data as ToCamelCase<AdvertiserListResponse>;
};

interface PaginationState {
  page: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
}

export function useAdvertisers(initialPage = 1, initialPageSize = 10, startTime?: string, endTime?: string) {
  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
    startTime,
    endTime,
  });

  const userId = useUserId();
  const swrKey = userId ? [`/api/ads/advertisers`, { ...pagination, userId: userId.toString() }] : null;

  const { data, error, isLoading, isValidating, mutate } = useSWR<ToCamelCase<AdvertiserListResponse>>(
    swrKey,
    ([_, params]) => advertiserFetcher(params as { page: number; pageSize: number; userId: string }),
    {
      keepPreviousData: true,
      refreshInterval: 1000 * 60 * 5, // 5分钟刷新一次
      revalidateOnFocus: false,
      revalidateIfStale: false, // 当缓存过期时不自动重新验证
      revalidateOnMount: true, // 组件挂载时进行验证
      revalidateOnReconnect: false, // 重新连接时不自动重新验证
    },
  );

  return {
    advertisers: data,
    isLoading: isLoading || isValidating,
    isError: error,
    refresh: () => mutate(),
    mutate: (newPage?: number, newPageSize?: number) => {
      if (newPage !== undefined || newPageSize !== undefined) {
        setPagination((prev) => ({
          page: newPage ?? prev.page,
          pageSize: newPageSize ?? prev.pageSize,
          startTime,
          endTime,
        }));
      }
    },
  };
}
