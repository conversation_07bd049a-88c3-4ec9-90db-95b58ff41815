import { EventPayload } from '..';
import { parseFilePath } from '../utils/path';
import { RemoveForDistributedHandler } from './remove-for-distributed';
import { RemoveForWaitDistributeHandler } from './remove-for-wait-distribute';
import { UploadFromCosForDistributedHandler } from './upload-from-cos-for-distributed';
import { UploadFromLocalForNeedDistributeHandler } from './upload-from-local-for-need-distribute';
import { UploadFromVodForWaitDistributeHandler } from './upload-from-vod-for-wait-distribute';

export const distributionDispatcher = async (event: EventPayload) => {
  const promiseArr = event.Records.map(async (record) => {
    // 基础验证
    if (record.cos.cosBucket.appid !== process.env.COS_APPID) {
      console.log('appid 不匹配，不处理', record.cos.cosBucket.appid);
      return;
    }

    const fileInfo = parseFilePath(record);
    if (!fileInfo || fileInfo.appName !== 'roasmax' || fileInfo.workflowName !== '自动分发') {
      console.log('文件路径格式不正确或非目标文件，不处理');
      return;
    }

    // 创建事件 根据stageName分发到不同的handler
    if (record.event.eventName.startsWith('cos:ObjectCreated:')) {
      switch (fileInfo.stageName) {
        case '原始素材': {
          console.log('接收到 [原始素材:创建] 事件');
          const handler = new UploadFromLocalForNeedDistributeHandler();
          await handler.handle(record);
          break;
        }

        case '生成素材': {
          console.log('接收到 [生成素材:创建] 事件');
          const handler = new UploadFromVodForWaitDistributeHandler();
          await handler.handle(record);
          break;
        }

        case '分发素材': {
          console.log('接收到 [分发素材:创建] 事件');
          const handler = new UploadFromCosForDistributedHandler();
          await handler.handle(record);
          break;
        }

        default:
          console.log('未知的处理阶段:[创建]:', fileInfo.stageName);
      }
    } else if (record.event.eventName.startsWith('cos:ObjectRemove:')) {
      switch (fileInfo.stageName) {
        case '生成素材': {
          console.log('接收到 [生成素材:删除] 事件');
          const handler = new RemoveForWaitDistributeHandler();
          await handler.handle(record);
          break;
        }
        case '分发素材': {
          console.log('接收到 [分发素材:删除] 事件');
          const handler = new RemoveForDistributedHandler();
          await handler.handle(record);
          break;
        }

        default:
          console.log('未知的处理阶段:[删除]:', fileInfo.stageName);
      }
    }
  });

  try {
    await Promise.all(promiseArr);
    return 'Success';
  } catch (e) {
    console.log(e);
    return 'Fail';
  }
};
