import { cn } from '@/utils/cn';
import React from 'react';

export const UploadIcon = (props: { className?: string }) => {
  return (
    <svg
      width="33"
      height="33"
      viewBox="0 0 33 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(props.className)}
    >
      <path
        d="M16.6645 7.42082C13.2892 7.42082 10.4779 9.89348 9.94188 13.1475L9.79321 14.0475L8.89988 14.2355C6.48655 14.7435 4.66455 16.9008 4.66455 19.4902C4.66455 22.1355 6.56455 24.3288 9.05655 24.7748L10.3685 25.0102L9.89921 27.6348L8.58588 27.4002C4.83721 26.7288 1.99788 23.4402 1.99788 19.4902C1.99788 15.9348 4.29788 12.9148 7.49255 11.8582C8.55788 7.77415 12.2559 4.75415 16.6645 4.75415C21.0732 4.75415 24.7712 7.77415 25.8365 11.8575C27.4383 12.3911 28.8313 13.4158 29.8176 14.7861C30.804 16.1563 31.3336 17.8025 31.3312 19.4908C31.3312 23.4408 28.4925 26.7295 24.7425 27.4008L23.4299 27.6355L22.9605 25.0108L24.2725 24.7755C26.7645 24.3288 28.6645 22.1355 28.6645 19.4902C28.6645 16.9008 26.8432 14.7435 24.4285 14.2355L23.5352 14.0482L23.3872 13.1475C22.8519 9.89348 20.0399 7.42082 16.6645 7.42082Z"
        fill="white"
      />
      <path d="M17.9978 29.7542V20.4208H15.3311V29.7542H17.9978Z" fill="#00E1FF" />
      <path
        d="M15.722 19.478C15.972 19.228 16.3111 19.0876 16.6647 19.0876C17.0182 19.0876 17.3573 19.228 17.6073 19.478L21.2167 23.0873L19.3313 24.9727L16.6647 22.306L13.998 24.9727L12.1127 23.0873L15.722 19.478Z"
        fill="#00E1FF"
      />
    </svg>
  );
};
